{"settings": {"index": {"knn": true, "knn.algo_param.ef_search": 100}}, "mappings": {"properties": {"episode_id": {"type": "keyword"}, "type": {"type": "keyword"}, "name": {"type": "text", "analyzer": "standard"}, "PK": {"type": "keyword"}, "SK": {"type": "keyword"}, "embedding": {"type": "knn_vector", "dimension": 384, "method": {"name": "hnsw", "space_type": "cosinesimil", "engine": "nmslib", "parameters": {"ef_construction": 384, "m": 48}}}, "country": {"type": "keyword"}, "language": {"type": "keyword"}, "status": {"type": "keyword"}, "channel_id": {"type": "keyword"}, "owner_id": {"type": "keyword"}, "rate": {"type": "double"}, "quality": {"type": "double"}, "likes": {"type": "integer"}, "dislikes": {"type": "integer"}, "start_date": {"type": "long"}, "views": {"type": "integer"}, "contents": {"enabled": "false"}}}}