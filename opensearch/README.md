# Connecting to an OpenSearch Domain in a VPC

This guide explains how to securely connect to an OpenSearch domain deployed inside a VPC using `gimme-aws-creds` and `db-connect`.

## **Prerequisites**
Ensure you have the following installed and configured:

- [gimme-aws-creds](https://github.com/Nike-Inc/gimme-aws-creds)
- `db-connect` (your internal tool for establishing a secure connection)
- AWS credentials set up (e.g., via `aws configure` or SSO)

---

## Step 1: Authenticate with AWS using gimme-aws-creds
Run the following command to authenticate and obtain temporary AWS credentials:

```bash
gimme-aws-creds --open-browser
```
## Step 2: Create a Port Forward with db-connect
Use db-connect to forward traffic from your local machine to the OpenSearch domain inside the VPC.

```bash
./db-connect --env [stg|prod] --host <domainHost> --remotePort 443 --localPort 8443 --profile default
```

Parameters:
--env [stg|prod] → Select the environment (stg for staging, prod for production).

--host <domainHost> → Replace with the OpenSearch domain hostname.

--remotePort 443 → The port OpenSearch uses for HTTPS connections.

--localPort 8443 → The local port to forward traffic through.

--profile default → The AWS profile to use (modify if needed).

## Step 3: Access OpenSearch
Once the connection is established, open your browser and navigate to:

🔗 http://localhost:8443/_dashboards

You should now be connected to the OpenSearch domain running in your VPC. 🎉

# OpenSearch Reindex with New Mapping and Alias Switch

This guide explains how to:
1. Stop a Lambda function from indexing new documents.
2. Reindex data into a new index with updated mappings.
3. Switch the alias to the new index.
4. Restart the Lambda function.

## 1. Stop the Lambda Function
Temporarily stop the Lambda function to prevent new documents from being indexed.

1. Go to the AWS Management Console.
2. Navigate to **Lambda > Functions**.
3. Select the Lambda function responsible for indexing.
4. Click **Actions > Disable**.

Alternatively, you can disable triggers associated with the Lambda function (e.g., SQS, DynamoDB streams, API Gateway).

## 2. Create a New Index with Updated Mapping

1. Define the new mapping:
```bash
PUT new_index
{
  "mappings": {
    "properties": {
      "field1": { "type": "keyword" },
      "field2": { "type": "text" },
      "timestamp": { "type": "date" }
    }
  }
}
```

2. Verify the new index:
```bash
GET new_index
```

## 3. Reindex Data

Use the `_reindex` API to copy data from the old index to the new one:
```bash
POST _reindex
{
  "source": {
    "index": "old_index"
  },
  "dest": {
    "index": "new_index"
  }
}
```

Optionally, monitor the task:
```bash
POST _reindex?wait_for_completion=false
```
```bash
GET _tasks/<task_id>
```

## 4. Switch the Alias

1. Remove the alias from the old index and add it to the new index:
```bash
POST _aliases
{
  "actions": [
    { "remove": { "index": "old_index", "alias": "my_index_alias" } },
    { "add": { "index": "new_index", "alias": "my_index_alias" } }
  ]
}
```

2. Verify the alias points to the new index:
```bash
GET _cat/aliases
```

## 5. Restart the Lambda Function

1. Go back to the AWS Management Console.
2. Navigate to **Lambda > Functions**.
3. Select the Lambda function and click **Enable**.

Alternatively, re-enable any previously disabled triggers.

## Verification
Test the setup by sending a new document to the alias and ensuring it lands in the new index.
```bash
POST my_index_alias/_doc
{
  "field1": "test",
  "field2": "document",
  "timestamp": "2024-07-30T12:00:00Z"
}
```

---
You’re now ready to reindex with a new mapping and seamlessly switch to the updated index without losing any data! 🚀

