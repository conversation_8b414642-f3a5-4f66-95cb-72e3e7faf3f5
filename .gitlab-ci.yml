image: registry.gitlab.com/etermax/platform/util/k8s-deploy-cli:3.0.7-ci

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_HOST: tcp://docker:2375
  GIT_STRATEGY: fetch
  SERVICE_NAME: episode
  K8S_API_VERSION: apps/v1

services:
  - name: docker:20-dind
    command: ["--tls=false"]


workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG

include:
  - project: 'etermax/preguntados/templates'
    ref:  '1.1.18'
    file: 'ci-cd-template.yml'
  - project: 'etermax/security/infosec-sast'
    ref: main
    file: 'infosec.gitlab-ci.yml'

stages:
  - pre build
  - build and push
  - develop
  - production
  - post-production
  - staging
  - rollback
  - infosec

.testable_src_file_changes: &testable_src_file_changes
  changes:
    - src/**/*
    - .gitlab-ci.yml
    - Dockerfile
    - build.gradle
    - gradle.properties

validate k8s manifests:
  stage: pre build
  image: registry.gitlab.com/etermax/docker-images/ci-cd/kubectl:1.22
  script:
    - mkdir .cdrs-cache
    - >
      kubectl kustomize .k8s/develop | 
      kubeconform -strict -verbose -cache .cdrs-cache -debug -skip ExternalSecret,HorizontalPodAutoscaler --summary -schema-location default 
      -schema-location 'https://raw.githubusercontent.com/datreeio/CRDs-catalog/main/{{.Group}}/{{.ResourceKind}}_{{.ResourceAPIVersion}}.json'
    - >
      kubectl kustomize .k8s/staging |
      kubeconform -strict -verbose -cache .cdrs-cache -debug -skip ExternalSecret,HorizontalPodAutoscaler --summary -schema-location default 
      -schema-location 'https://raw.githubusercontent.com/datreeio/CRDs-catalog/main/{{.Group}}/{{.ResourceKind}}_{{.ResourceAPIVersion}}.json'
    - >
      kubectl kustomize .k8s/production | 
      kubeconform -strict -verbose -cache .cdrs-cache -debug -skip ExternalSecret,HorizontalPodAutoscaler --summary -schema-location default 
      -schema-location 'https://raw.githubusercontent.com/datreeio/CRDs-catalog/main/{{.Group}}/{{.ResourceKind}}_{{.ResourceAPIVersion}}.json'
  tags:
    - eterPIPE-Services
  only:
    changes:
      - .k8s/develop/**/*
      - .k8s/staging/**/*
      - .k8s/base/**/*
    refs:
      - main
      - branches
      - merge_requests

test:
  stage: build and push
  image: gradle:7.5.1-jdk17-focal
  coverage: '/coverage:.*?\d+\.\d+%/'
  script:
    - gradle test koverHtmlReport --stacktrace --info --gradle-user-home gradle-home/
    - coverage=$(head -100 build/reports/kover/html/index.html |  grep -P '\d*\.\d*%' | head -1)
    - echo "coverage:$coverage"
  artifacts:
    when: always
    paths:
      - build/test-results/test/TEST-*.xml
    reports:
      junit: build/test-results/test/TEST-*.xml
  variables:
    DOCKER_HOST: tcp://localhost:2375
  tags:
    - eterPIPE-Services
  only:
    <<: *testable_src_file_changes
    refs:
      - main
      - master
      - branches

test (merge request):
  stage: build and push
  image: gradle:7.5.1-jdk17-focal
  coverage: '/coverage:.*?\d+\.\d+%/'
  script:
    - gradle test jacocoTestReport koverHtmlReport --stacktrace --info --gradle-user-home gradle-home/
    - coverage=$(head -100 build/reports/kover/html/index.html |  grep -P '\d*\.\d*%' | head -1)
    - echo "coverage:$coverage"
  artifacts:
    when: always
    paths:
      - build/reports/jacoco/test/jacocoTestReport.xml
    reports:
      junit: build/test-results/test/TEST-*.xml
  variables:
    DOCKER_HOST: tcp://localhost:2375
  tags:
    - eterPIPE-Services
  only:
    <<: *testable_src_file_changes
    refs:
      - merge_requests

transform coverage:
  stage: build and push
  image: registry.gitlab.com/haynes/jacoco2cobertura:1.0.7
  script:
    - python /opt/cover2cover.py build/reports/jacoco/test/jacocoTestReport.xml $CI_PROJECT_DIR/src/main/kotlin/ > build/cobertura.xml
  needs: ["test (merge request)"]
  artifacts:
    paths:
      - build/cobertura.xml
    reports:
      coverage_report:
        coverage_format: cobertura
        path: build/cobertura.xml
  tags:
    - eterPIPE-Services
  only:
    refs:
      - merge_requests

lint:
  stage: pre build
  image: gradle:7.6-jdk8
  script: curl -sSLO https://github.com/pinterest/ktlint/releases/download/0.46.1/ktlint && chmod a+x ktlint && ./ktlint
  tags:
    - eterPIPE-Services
  only:
    changes:
      - src/**/*
      - build.gradle
      - gradle.properties
    refs:
      - main
      - branches
      - merge_requests

## BRANCH TO DEPLOY IN Develop
#deploy to dev:
#  only:
#    refs:
#      - main
#      - branches
#  when: manual
#
#to registry:
#  only:
#    refs:
#      - main
#      - branches

