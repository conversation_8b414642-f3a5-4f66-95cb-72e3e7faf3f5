# Episodes

This project enables the creation, exploration, and playback of episodes. Each episode consists of a stream of playable
content, allowing users to compete for the highest score.

# Administrative tasks

[[Opensearch] How to start local environment](docker/local/README.md)
 
[[Opensearch] How to reindex](opensearch/README.md)

# Development Setup

## Git Hooks

To set up the hooks, run:

```bash
git config core.hooksPath .githooks
chmod +x .githooks/pre-push
```

This will configure Git to use the project's hooks, including `pre-push` (ktlint)
