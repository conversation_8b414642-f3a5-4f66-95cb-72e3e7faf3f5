## BUILDER
FROM gradle:7.5.1-jdk17-focal AS builder
USER root
COPY src src
COPY build.gradle build.gradle
COPY settings.gradle settings.gradle
COPY dependencies.gradle.kts dependencies.gradle.kts
RUN gradle installDist
COPY src/main/resources/java.security  /home/<USER>/build/install/episode/bin/java.security

# RUNNER
FROM eclipse-temurin:17.0.7_7-jre-jammy
WORKDIR /home/<USER>
EXPOSE 8080
COPY --from=builder /home/<USER>/build/install/episode  /home/<USER>
RUN chmod +x /home/<USER>/bin/episode

ENTRYPOINT ["/home/<USER>/bin/episode"]
