#!/bin/sh

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
KTLINT_PATH="$SCRIPT_DIR/ktlint"

# Check if ktlint exists in the same directory as this script
if [ ! -f "$KTLINT_PATH" ]; then
    echo "ktlint not found in .githooks directory, downloading..."
    cd "$SCRIPT_DIR" && curl -sSL https://github.com/pinterest/ktlint/releases/download/0.46.1/ktlint -o ktlint
    chmod a+x "$KTLINT_PATH"
fi

echo "Running ktlint..."
"$KTLINT_PATH"
lint_status=$?

if [ $lint_status -ne 0 ]; then
    echo "\nPush canceled because lint is failing."
    exit 1
fi
echo "Lint check passed"
exit 0