app:
  image: "${DOCKER_IMAGE}"
  deployedAt: "${DEPLOYED_TIME}"
  environment: "${ENVIRONMENT:-local}"

  # Port number where this application will listen for requests
  port: 8081
  shutdown:
    gracePeriod: 0
    stopTimeout: 0
    timeUnit: "SECONDS"

http:
  # Max milliseconds to establish an HTTP connection - default 10 seconds.
  # A value of 0 represents infinite, while -1 represents system's default value.
  connectTimeoutMs: 1000

  # Max milliseconds for the connection manager to start a request - default 20 seconds.
  # A value of 0 represents infinite, while -1 represents system's default value.
  connectionRequestTimeoutMs: 1000

  # Maximum number of socket connections.
  maxConnTotal: 100

  # Maximum number of requests for a specific endpoint route.
  maxConnPerRoute: 100

platformHost: ${PLATFORM_HOST:-https://api.platform-staging.etermax.com:443}

platformGameId: ${PLATFORM_GAME_ID:-P2DEV}

p2ApiHost: ${P2_API_HOST:-https://api.dev.tc.etermax.com:443}

triviaPlaylistHost: ${TRIVIA_PLAYLIST_HOST:-https://api.dev.tc.etermax.com:443}

contentHost: ${CONTENT_HOST:-https://api.dev.tc.etermax.com:443}

biHost: ${BI_HOST:-https://api.bi-staging.etermax.com:443}

p2AdminPassword: ${P2_ADMIN_PASSWORD:-hola123}

clerk:
  host: "http://localhost"
  adminPassword: "${P2_ADMIN_PASSWORD:-hola123}"

resilienceConfig:
  circuitBreaker:
    waitSecondsInOpenState: 30
  retry:
    maxAttempts: 3
    initialIntervalMillis: 100
    multiplierFactor: 1.5

persistence:
  dynamo:
    endpoint: ${AWS_ENDPOINT:-http://localhost:8000}
    maxConnections: ${AWS_MAX_CONNECTIONS:-3}
    maxErrorRetries: ${AWS_MAX_ERROR_RETRIES:-3}
    region: ${AWS_REGION:-us-east-1}
    tablePrefix: ${AWS_TABLE_PREFIX:-dev}
    accessKey: ${AWS_ACCESS_KEY_ID:-access}
    secretKey: ${AWS_SECRET_ACCESS_KEY:-secret}
  cache:
    endpoint: ${CACHE_HOST:-localhost:6379}
    username: ${CACHE_USER:-default}
    password: ${CACHE_PASS:-local}
    useTLS: false
  ttl:
    veryShort: ${REDIS_TTL_SHORT:-PT30S}
    short: ${REDIS_TTL_SHORT:-PT5M}
    medium: ${REDIS_TTL_MEDIUM:-PT6H}
    large: ${REDIS_TTL_LARGE:-PT24H}

openSearch:
  endpoint: ${OS_ENDPOINT:-http://localhost:9200}
  episodesIndex: episodes-index
  channelsIndex: channels-index
  playerEpisodesIndex: player-episodes-index

delivery:
  requiredMinimumQueryLength: 2
  ## Top Rated
  requiredLikesForTopRated: 1
  requiredViewsForTopRated: 2
  ## Feed Search
  lastPlayedThreshold: 6
  ## Similar Search
  similarityScoreThreshold: 1.8
  useBlackList: false

pendingEpisodesJobConfig:
  isEnabled: false
  startDelayInSeconds: 15
  processDelayInSeconds: 60
  stopGracePeriodInSeconds: 2

notificationsConfiguration:
  played:
    isEnabled: true
    singleCanvasId: bfe58b9c-38b2-4940-8f48-093ec90c2252
    pluralCanvasId: 22eecbcb-065c-49dd-a03e-63ea58124033
  liked:
    isEnabled: true
    singleCanvasId: 30fc4500-8007-494c-95a3-5157c56826f2
    pluralCanvasId: 994620b7-0319-4af4-8a19-1a13510d6805
  inviterCanvasId: e249e7c4-a326-4138-bdd3-61b08dec4f61
  creatorToPlayersCanvasId: 6277b21d-07db-415c-a339-5dfd75adf29d

eventGroupsConfiguration:
  groups:
    LIKE:
      isEnabled: true
      minCountToGroup: 5
      groupSizes: [5,10,20]
      groupDuration: PT24H
    PLAY:
      isEnabled: true
      minCountToGroup: 5
      groupSizes: [5,30,50]
      groupDuration: PT24H

dynamoDBSearch:
  isEnabled: false
  chunkSize: 20

isUpdateRateCountersEnabled: true
isIncrementViewsEnabled: true
playersByOwnerExpirationTime: ${PLAYERS_BY_OWNER_EXPIRATION_TIME:-P30D}
isPlayersByOwnerRepositoryEnabled: ${IS_PLAYERS_BY_OWNER_REPOSITORY_ENABLED:-true}
isChannelEpisodesNormalizerEnabled: ${IS_CHANNEL_EPISODES_NORMALIZER_ENABLED:-false}

analytics:
  isEnabled: ${ANALYTICS_RELEASE_TOGGLE_ENABLED:-true}