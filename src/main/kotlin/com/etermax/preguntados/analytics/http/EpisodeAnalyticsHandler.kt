package com.etermax.preguntados.analytics.http

import com.etermax.preguntados.analytics.actions.GetEpisodeAnalytics
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.http.episodeId
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodeAnalyticsRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class EpisodeAnalyticsHandler(
    private val getEpisodeAnalytics: GetEpisodeAnalytics,
    private val clock: Clock
) : Handler {
    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode/{episodeId}/analytics") {
                get { analyticsHandler() }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.analyticsHandler() {
        val analytics = getEpisodeAnalytics(GetEpisodeAnalytics.ActionData(userId, episodeId))
        val representation = EpisodeAnalyticsRepresentation.Companion.from(analytics, clock)
        call.respond(HttpStatusCode.Companion.OK, representation)
    }
}
