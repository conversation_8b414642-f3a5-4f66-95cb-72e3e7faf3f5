package com.etermax.preguntados.analytics.service

import com.etermax.devices.domain.DeviceType
import com.etermax.preguntados.analytics.enums.MetricDeviceType
import com.etermax.preguntados.external.services.core.domain.api.profile.Country

data class PlayedEvent(
    val userId: Long,
    val episodeId: String,
    val ownerId: Long,
    val country: Country,
    private val deviceType: DeviceType,
    private val tablet: Boolean
) {
    val device: MetricDeviceType = when (deviceType) {
        DeviceType.ANDROID -> if (tablet) MetricDeviceType.ANDROID_TABLET else MetricDeviceType.ANDROID_MOBILE
        DeviceType.IPHONE -> if (tablet) MetricDeviceType.IOS_IPAD else MetricDeviceType.IOS_MOBILE
        DeviceType.BROWSER -> MetricDeviceType.WEB
        else -> MetricDeviceType.OTHER
    }
}

data class FollowEvent(val followedUserId: Long, val episodeId: String)

data class FinishedEvent(val userId: Long, val episodeId: String, val ownerId: Long)
