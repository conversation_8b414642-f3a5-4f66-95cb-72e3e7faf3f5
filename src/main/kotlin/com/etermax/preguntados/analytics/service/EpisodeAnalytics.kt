package com.etermax.preguntados.analytics.service

import com.etermax.preguntados.analytics.enums.MetricDeviceType
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.reports.action.EpisodeReportsSummary

data class EpisodeAnalytics(
    val playersByDay: List<Long>,
    val playConversionByDay: List<Long>,
    val followersByDay: List<Long>,
    val mostPlayedTimes: List<Double>,
    val feedbacks: Feedbacks?,
    val demography: Demography?,
    val finishRate: FinishRateData?,
    val reports: EpisodeReportsSummary
)

data class Feedbacks(val likes: Long, val dislikes: Long)

data class Demography(
    val countries: Map<Country, Double>,
    val devices: Map<MetricDeviceType, Double>,
    val userTypes: UserTypes
)

data class UserTypes(val followers: Double, val notFollowers: Double)

data class FinishRateData(
    val thisEpisode: Double,
    val creatorAverage: Double
)
