package com.etermax.preguntados.analytics.service

import java.time.OffsetDateTime
import java.util.concurrent.CompletionStage

interface HistogramAnalyticsRepository {
    suspend fun registerViewed(episodeId: String, userId: Long)
    suspend fun registerPlayed(episodeId: String, userId: Long)
    suspend fun registerFollowed(episodeId: String, followedUserId: Long)
    fun playersByDay(dates: List<OffsetDateTime>, episodeId: String): List<CompletionStage<Long>>
    fun viewsByDay(dates: List<OffsetDateTime>, episodeId: String): List<CompletionStage<Long>>
    fun followsByDay(dates: List<OffsetDateTime>, episodeId: String): List<CompletionStage<Long>>
    fun playsByHourOnLast5Days(sorted: List<OffsetDateTime>, episodeId: String): List<List<CompletionStage<Long>>>
}
