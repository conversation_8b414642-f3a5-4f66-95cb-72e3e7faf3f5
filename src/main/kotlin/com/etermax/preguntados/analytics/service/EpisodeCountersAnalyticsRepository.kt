package com.etermax.preguntados.analytics.service

import com.etermax.preguntados.analytics.enums.MetricDeviceType
import com.etermax.preguntados.analytics.enums.MetricUserType
import com.etermax.preguntados.analytics.repository.EpisodeMetricsItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Country

interface EpisodeCountersAnalyticsRepository {
    suspend fun registerPlayedFrom(id: String, country: Country, deviceType: MetricDeviceType, userType: MetricUserType)
    suspend fun registerEpisodeFinished(episodeId: String)
    suspend fun getContentMetrics(id: String): EpisodeMetricsItem?
}
