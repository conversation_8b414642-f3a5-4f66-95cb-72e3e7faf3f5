package com.etermax.preguntados.analytics.service

import com.etermax.preguntados.analytics.enums.MetricUserType
import com.etermax.preguntados.analytics.repository.DynamoDBMetricMapper
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.profile.PlayerFriendsService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.reports.action.GetEpisodeReports
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.future.await
import org.slf4j.Logger
import org.slf4j.LoggerFactory

data class ViewedEvent(val userId: Long, val episodeId: String)

class AnalyticsTracker(
    private val clock: Clock,
    private val episodeMetricsRepository: EpisodeCountersAnalyticsRepository,
    private val creatorMetricsRepository: CreatorsCountersAnalyticsRepository,
    private val histogramsRepository: HistogramAnalyticsRepository,
    private val getEpisodeReports: GetEpisodeReports,
    private val playerFriendsService: PlayerFriendsService
) {
    private val itemMapper = DynamoDBMetricMapper()
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend fun track(event: ViewedEvent) {
        try {
            histogramsRepository.registerViewed(event.episodeId, event.userId)
        } catch (e: Exception) {
            logger.error("Error tracking viewed event for user ${event.userId} and episode ${event.episodeId}", e)
        }
    }

    internal suspend fun track(event: PlayedEvent) {
        try {
            histogramsRepository.registerPlayed(event.episodeId, event.userId)

            val playerFollowers = playerFriendsService.findFollowedIds(event.userId)
            val userType = if (playerFollowers.contains(event.ownerId)) {
                MetricUserType.FOLLOWER
            } else {
                MetricUserType.NOT_FOLLOWER
            }

            episodeMetricsRepository.registerPlayedFrom(
                event.episodeId,
                event.country,
                event.device,
                userType
            )

            creatorMetricsRepository.registerPlayedContentOf(event.ownerId)
        } catch (e: Exception) {
            logger.error("Error tracking played event for user ${event.userId} and episode ${event.episodeId}", e)
        }
    }

    internal suspend fun track(event: FinishedEvent) {
        try {
            episodeMetricsRepository.registerEpisodeFinished(event.episodeId)
            creatorMetricsRepository.registerFinishedContentOf(event.ownerId)
        } catch (e: Exception) {
            logger.error("Error tracking finished event for user ${event.userId} and episode ${event.episodeId}", e)
        }
    }

    internal suspend fun track(event: FollowEvent) {
        val episodeId = event.episodeId
        val followedUserId = event.followedUserId
        try {
            histogramsRepository.registerFollowed(episodeId, followedUserId)
        } catch (e: Exception) {
            logger.error("Error tracking follow event for user $followedUserId and episode $episodeId", e)
        }
    }

    suspend fun getEpisodeAnalytics(creatorId: Long, episode: Episode): EpisodeAnalytics = coroutineScope {
        val now = clock.now()
        val last30Days = (0..29).map { daysAgo -> now.minusDays(daysAgo.toLong()) }.reversed()

        val playersByDayAsync = histogramsRepository.playersByDay(last30Days, episode.id)
        val viewsByDayAsync = histogramsRepository.viewsByDay(last30Days, episode.id)
        val followsByDayAsync = histogramsRepository.followsByDay(last30Days, episode.id)
        val playsByHourOnLast5DaysAsync = histogramsRepository.playsByHourOnLast5Days(last30Days, episode.id)

        val contentMetricsItemAsync = async { episodeMetricsRepository.getContentMetrics(episode.id) }
        val creatorFinishRateAsync = async { creatorMetricsRepository.getCreatorFinishRate(creatorId) }
        val reportsAsync = async { getEpisodeReports(GetEpisodeReports.ActionData(creatorId, episode.id)) }

        val playersByDay = playersByDayAsync.map { it.await() }
        val viewersByDay = viewsByDayAsync.map { it.await() }
        val followersByDay = followsByDayAsync.map { it.await() }

        val playConversionByDay = viewersByDay.zip(playersByDay) { views, plays ->
            calculateConversion(views, plays)
        }

        val playsByHourOnLast5Days: List<List<Long>> =
            playsByHourOnLast5DaysAsync.map { it.map { future -> future.await() } }

        val totalsByHour = playsByHourOnLast5Days.reduce { acc, list -> acc.zip(list) { a, b -> a + b } }
        val playedLast5DaysInTotal = totalsByHour.sum()
        val mostPlayedTimes =
            totalsByHour.map { if (playedLast5DaysInTotal == 0L) 0.0 else 100.0 * it / playedLast5DaysInTotal }

        val likes = episode.rate.likes
        val dislikes = episode.rate.dislikes
        val feedbacks = if (likes + dislikes > 0) Feedbacks(likes, dislikes) else null

        val contentMetricsItem = contentMetricsItemAsync.await()
        val demography = itemMapper.extractDemographics(contentMetricsItem)
        val episodeFinishRate = itemMapper.extractContentFinishedRate(contentMetricsItem)

        val creatorMetricsItem = creatorFinishRateAsync.await()
        val creatorFinishRate = itemMapper.extractCreatorFinishedRate(creatorMetricsItem)

        val finishRate = episodeFinishRate?.let {
            creatorFinishRate?.let {
                FinishRateData(episodeFinishRate, creatorFinishRate)
            }
        }

        val reports = reportsAsync.await()

        EpisodeAnalytics(
            playersByDay,
            playConversionByDay,
            followersByDay,
            mostPlayedTimes,
            feedbacks,
            demography,
            finishRate,
            reports
        )
    }

    private fun calculateConversion(views: Long, plays: Long): Long {
        if (views == 0L && plays == 0L) return 0 // Nothing
        if (views == 0L && plays > 0L) return 100 // Corner case
        return plays * 100 / views
    }
}
