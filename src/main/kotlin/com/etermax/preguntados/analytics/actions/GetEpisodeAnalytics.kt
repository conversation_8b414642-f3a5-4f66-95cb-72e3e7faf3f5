package com.etermax.preguntados.analytics.actions

import com.etermax.preguntados.analytics.service.AnalyticsTracker
import com.etermax.preguntados.analytics.service.EpisodeAnalytics
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeEntityNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.base.ForbiddenException

class GetEpisodeAnalytics(private val analyticsTracker: AnalyticsTracker, val episodesRepository: EpisodeRepository) {
    suspend operator fun invoke(actionData: ActionData): EpisodeAnalytics {
        val episode = episodesRepository.findById(actionData.episodeId)
        if (episode == null) throw EpisodeEntityNotFoundException(actionData.episodeId)

        if (actionData.userId != episode.ownerId) throw UserNotAllowedToSeeAnotherCreatorAnalytics(
            actionData.userId,
            episode.ownerId,
            actionData.episodeId
        )

        return analyticsTracker.getEpisodeAnalytics(actionData.userId, episode)
    }

    data class ActionData(
        val userId: Long,
        val episodeId: String
    )

    class UserNotAllowedToSeeAnotherCreatorAnalytics(userId: Long, ownerId: Long, episodeId: String) :
        ForbiddenException("User $userId tried to see episode $episodeId but it's created by $ownerId")
}
