package com.etermax.preguntados.analytics.actions

import com.etermax.preguntados.analytics.service.*
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class TrackEpisodeAnalytics(
    private val analyticsTracker: AnalyticsTracker,
    private val episodeRepository: EpisodeRepository,
    private val releaseToggle: Boolean = true
) {
    operator fun invoke(event: ViewedEvent, ownerId: Long? = null) {
        if (!releaseToggle) return

        fireAndForget {
            val ownerAux = ownerId ?: episodeRepository.findById(event.episodeId)?.ownerId
            if (ownerAux != null && event.userId != ownerAux) {
                analyticsTracker.track(event)
            }
        }
    }

    operator fun invoke(event: PlayedEvent) {
        if (!releaseToggle || event.userId == event.ownerId) return
        fireAndForget({ analyticsTracker.track(event) })
    }

    operator fun invoke(event: FinishedEvent) {
        if (!releaseToggle || event.userId == event.ownerId) return
        fireAndForget({ analyticsTracker.track(event) })
    }

    // This is not invoked, because we don't have a "Follow" button inside TriviaX yet.
    operator fun invoke(event: FollowEvent) {
        fireAndForget({ analyticsTracker.track(event) })
    }

    internal fun fireAndForget(block: suspend CoroutineScope.() -> Unit) {
        CoroutineScope(Dispatchers.IO).launch(block = block)
    }
}
