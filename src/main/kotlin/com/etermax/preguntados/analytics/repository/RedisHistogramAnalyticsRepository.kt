package com.etermax.preguntados.analytics.repository

import com.etermax.preguntados.analytics.service.HistogramAnalyticsRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.future.await
import java.time.Duration
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.concurrent.CompletionStage

class RedisHistogramAnalyticsRepository(
    private val redis: RedisAsyncCommands<String, String>,
    private val clock: Clock
) : HistogramAnalyticsRepository {
    private val dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd").withZone(ZoneOffset.UTC)
    private val duration31Days: Duration = Duration.ofDays(31)
    private val duration6Days: Duration = Duration.ofDays(6)

    override suspend fun registerViewed(episodeId: String, userId: Long) {
        val key = viewsDailyKey(episodeId, clock.now())
        redis.pfadd(key, userId.toString()).await()
        redis.expire(key, duration31Days)
    }

    override suspend fun registerPlayed(episodeId: String, userId: Long) {
        val now = clock.now()

        val playsByDayKey = playsDailyKey(episodeId, now)
        redis.pfadd(playsByDayKey, userId.toString()).await()
        redis.expire(playsByDayKey, duration31Days)

        val playsByHourKey = getPlaysByHourKey(episodeId, now)
        redis.hincrby(playsByHourKey, now.hour.toString(), 1).await()
        redis.expire(playsByHourKey, duration6Days)
    }

    override suspend fun registerFollowed(episodeId: String, followedUserId: Long) {
        val key = followsDailyKey(episodeId, clock.now())
        redis.pfadd(key, followedUserId.toString()).await()
        redis.expire(key, duration31Days)
    }

    override fun playersByDay(dates: List<OffsetDateTime>, episodeId: String): List<CompletionStage<Long>> =
        dates.map { redis.pfcount(playsDailyKey(episodeId, it)) }

    override fun viewsByDay(dates: List<OffsetDateTime>, episodeId: String): List<CompletionStage<Long>> =
        dates.map { redis.pfcount(viewsDailyKey(episodeId, it)) }

    override fun followsByDay(dates: List<OffsetDateTime>, episodeId: String): List<CompletionStage<Long>> =
        dates.map { redis.pfcount(followsDailyKey(episodeId, it)) }

    override fun playsByHourOnLast5Days(
        sorted: List<OffsetDateTime>,
        episodeId: String
    ): List<List<CompletionStage<Long>>> {
        val hours = (0..23).map { it.toString() }
        val redisPlaysByHourOnLast5Days: List<List<CompletionStage<Long>>> = sorted.takeLast(5).map {
            hours.map { hour ->
                val hget = redis.hget(getPlaysByHourKey(episodeId, it), hour)
                hget.thenApply { result -> result?.toLongOrNull() ?: 0 }
            }
        }
        return redisPlaysByHourOnLast5Days
    }

    private fun viewsDailyKey(episodeId: String, time: OffsetDateTime): String =
        "views:${toValidId(episodeId)}:${yyyyMMdd(time)}"

    private fun playsDailyKey(episodeId: String, time: OffsetDateTime): String =
        "plays:${toValidId(episodeId)}:${yyyyMMdd(time)}"

    private fun getPlaysByHourKey(episodeId: String, time: OffsetDateTime): String =
        "plays:${toValidId(episodeId)}:${yyyyMMdd(time)}:hours"

    private fun followsDailyKey(episodeId: String, time: OffsetDateTime): String =
        "follows:${toValidId(episodeId)}:${yyyyMMdd(time)}"

    private fun yyyyMMdd(date: OffsetDateTime): String = dateFormatter.format(date.toInstant())

    companion object {
        fun toValidId(episodeId: String): String =
            if (episodeId.startsWith("E#")) episodeId else "E#$episodeId"
    }
}
