package com.etermax.preguntados.analytics.repository

import com.etermax.preguntados.analytics.enums.MetricDeviceType
import com.etermax.preguntados.analytics.service.Demography
import com.etermax.preguntados.analytics.service.UserTypes
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import kotlin.math.min

class DynamoDBMetricMapper {
    fun extractDemographics(item: EpisodeMetricsItem?): Demography? {
        if (item == null) return null

        val countries = Country.values().associateWith { item.counterBySite(it) }
        val countriesRates = ratesList(countries)

        val devices = MetricDeviceType.values().associateWith { counterByDevice(it, item) }
        val devicesRates = ratesList(devices)

        val userTypesRates = extractUserTypes(item)

        return Demography(countriesRates, devicesRates, userTypesRates)
    }

    fun extractContentFinishedRate(item: EpisodeMetricsItem?): Double? {
        if (item == null || item.started == 0) return null

        val unboundedRate = 100.0 * item.finished / item.started
        return min(unboundedRate, 100.0)
    }

    fun extractCreatorFinishedRate(item: CreatorUserMetricsItem?): Double? {
        if (item == null || item.totalStarted == 0) return null

        val unboundedRate = 100.0 * item.totalFinished / item.totalStarted
        return min(unboundedRate, 100.0)
    }

    private fun extractUserTypes(item: EpisodeMetricsItem): UserTypes {
        val totals = item.userTypeFollower + item.userTypeNotFollower
        if (totals == 0) return UserTypes(0.0, 0.0)

        return UserTypes(
            100.0 * item.userTypeFollower / totals,
            100.0 * item.userTypeNotFollower / totals
        )
    }

    private fun counterByDevice(it: MetricDeviceType, item: EpisodeMetricsItem): Int = when (it) {
        MetricDeviceType.ANDROID_TABLET -> item.deviceAndroidTablet
        MetricDeviceType.ANDROID_MOBILE -> item.deviceAndroidMobile
        MetricDeviceType.IOS_MOBILE -> item.deviceIosMobile
        MetricDeviceType.IOS_IPAD -> item.deviceIosIpad
        MetricDeviceType.WEB -> item.deviceWeb
        MetricDeviceType.OTHER -> item.deviceOther
    }

    private fun <T> ratesList(counters: Map<T, Int>): Map<T, Double> {
        val countersWithData = counters.filter { it.value > 0 }
        val totalCountryPlays = countersWithData.values.sum()
        return if (totalCountryPlays > 0) {
            countersWithData.mapValues { 100.0 * it.value / totalCountryPlays }
        } else {
            emptyMap()
        }
    }
}
