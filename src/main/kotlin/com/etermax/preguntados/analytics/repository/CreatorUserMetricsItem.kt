package com.etermax.preguntados.analytics.repository

import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey

@DynamoDbBean
class CreatorUserMetricsItem() {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(CreatorMetricsAttributes.PK)
    var pk: String = ""

    @get:DynamoDbAttribute(CreatorMetricsAttributes.TOTAL_STARTED)
    var totalStarted: Int = 0

    @get:DynamoDbAttribute(CreatorMetricsAttributes.TOTAL_FINISHED)
    var totalFinished: Int = 0

    @get:DynamoDbAttribute(CreatorMetricsAttributes.EXPIRATION)
    var expiration: Long = 0

    companion object {
        fun buildPartitionKey(userId: Long) = "U#$userId"

        fun from(userId: Long, totalStarted: Int = 0, totalFinished: Int = 0) = CreatorUserMetricsItem().apply {
            pk = buildPartitionKey(userId)
            this.totalStarted = totalStarted
            this.totalFinished = totalFinished
        }
    }
}

object CreatorMetricsAttributes {
    const val PK = "PK"
    const val TOTAL_STARTED = "total_started"
    const val TOTAL_FINISHED = "total_finished"
    const val EXPIRATION = "expiration"
}
