package com.etermax.preguntados.analytics.repository

import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey

@DynamoDbBean
class EpisodeMetricsItem() {
    // Device metrics
    @get:DynamoDbAttribute(EpisodeMetricsAttributes.DEVICE_ANDROID_MOBILE)
    var deviceAndroidMobile: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.DEVICE_ANDROID_TABLET)
    var deviceAndroidTablet: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.DEVICE_IOS_MOBILE)
    var deviceIosMobile: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.DEVICE_IOS_IPAD)
    var deviceIosIpad: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.DEVICE_WEB)
    var deviceWeb: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.DEVICE_OTHER)
    var deviceOther: Int = 0

    // User type metrics
    @get:DynamoDbAttribute(EpisodeMetricsAttributes.USER_TYPE_FOLLOWER)
    var userTypeFollower: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.USER_TYPE_NOT_FOLLOWER)
    var userTypeNotFollower: Int = 0

    // Episode completion metrics
    @get:DynamoDbAttribute(EpisodeMetricsAttributes.STARTED)
    var started: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.FINISHED)
    var finished: Int = 0

    // Country metrics - ALL countries from Country enum
    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AD)
    var siteAD: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AE)
    var siteAE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AF)
    var siteAF: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AG)
    var siteAG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AI)
    var siteAI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AL)
    var siteAL: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AM)
    var siteAM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AN)
    var siteAN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AO)
    var siteAO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AR)
    var siteAR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AS)
    var siteAS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AT)
    var siteAT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AU)
    var siteAU: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AW)
    var siteAW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AX)
    var siteAX: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_AZ)
    var siteAZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BA)
    var siteBA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BB)
    var siteBB: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BD)
    var siteBD: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BE)
    var siteBE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BF)
    var siteBF: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BG)
    var siteBG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BH)
    var siteBH: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BI)
    var siteBI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BJ)
    var siteBJ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BL)
    var siteBL: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BM)
    var siteBM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BN)
    var siteBN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BO)
    var siteBO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BR)
    var siteBR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BS)
    var siteBS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BT)
    var siteBT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BV)
    var siteBV: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BW)
    var siteBW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BY)
    var siteBY: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_BZ)
    var siteBZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CA)
    var siteCA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CC)
    var siteCC: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CD)
    var siteCD: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CF)
    var siteCF: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CG)
    var siteCG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CH)
    var siteCH: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CI)
    var siteCI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CK)
    var siteCK: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CL)
    var siteCL: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CM)
    var siteCM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CN)
    var siteCN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CO)
    var siteCO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CR)
    var siteCR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CT)
    var siteCT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CU)
    var siteCU: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CV)
    var siteCV: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CW)
    var siteCW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CX)
    var siteCX: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CY)
    var siteCY: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_CZ)
    var siteCZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_DE)
    var siteDE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_DJ)
    var siteDJ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_DK)
    var siteDK: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_DM)
    var siteDM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_DO)
    var siteDO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_DZ)
    var siteDZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_EC)
    var siteEC: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_EE)
    var siteEE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_EG)
    var siteEG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_EH)
    var siteEH: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ER)
    var siteER: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ES)
    var siteES: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ET)
    var siteET: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_FI)
    var siteFI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_FJ)
    var siteFJ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_FK)
    var siteFK: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_FM)
    var siteFM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_FO)
    var siteFO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_FR)
    var siteFR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GA)
    var siteGA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GB)
    var siteGB: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GD)
    var siteGD: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GE)
    var siteGE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GF)
    var siteGF: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GG)
    var siteGG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GH)
    var siteGH: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GI)
    var siteGI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GL)
    var siteGL: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GM)
    var siteGM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GN)
    var siteGN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GP)
    var siteGP: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GQ)
    var siteGQ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GR)
    var siteGR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GS)
    var siteGS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GT)
    var siteGT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GU)
    var siteGU: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GW)
    var siteGW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GX)
    var siteGX: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_GY)
    var siteGY: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_HK)
    var siteHK: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_HM)
    var siteHM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_HN)
    var siteHN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_HR)
    var siteHR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_HT)
    var siteHT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_HU)
    var siteHU: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ID)
    var siteID: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_IE)
    var siteIE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_IL)
    var siteIL: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_IM)
    var siteIM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_IN)
    var siteIN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_IO)
    var siteIO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_IQ)
    var siteIQ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_IR)
    var siteIR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_IS)
    var siteIS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_IT)
    var siteIT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_JE)
    var siteJE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_JM)
    var siteJM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_JO)
    var siteJO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_JP)
    var siteJP: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KE)
    var siteKE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KG)
    var siteKG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KH)
    var siteKH: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KI)
    var siteKI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KM)
    var siteKM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KN)
    var siteKN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KP)
    var siteKP: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KR)
    var siteKR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KW)
    var siteKW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KY)
    var siteKY: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_KZ)
    var siteKZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LA)
    var siteLA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LB)
    var siteLB: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LC)
    var siteLC: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LI)
    var siteLI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LK)
    var siteLK: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LR)
    var siteLR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LS)
    var siteLS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LT)
    var siteLT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LU)
    var siteLU: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LV)
    var siteLV: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_LY)
    var siteLY: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MA)
    var siteMA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MC)
    var siteMC: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MD)
    var siteMD: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ME)
    var siteME: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MF)
    var siteMF: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MG)
    var siteMG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MH)
    var siteMH: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MK)
    var siteMK: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ML)
    var siteML: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MM)
    var siteMM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MN)
    var siteMN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MO)
    var siteMO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MP)
    var siteMP: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MQ)
    var siteMQ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MR)
    var siteMR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MS)
    var siteMS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MT)
    var siteMT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MU)
    var siteMU: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MV)
    var siteMV: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MW)
    var siteMW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MX)
    var siteMX: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MY)
    var siteMY: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_MZ)
    var siteMZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NA)
    var siteNA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NC)
    var siteNC: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NE)
    var siteNE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NF)
    var siteNF: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NG)
    var siteNG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NI)
    var siteNI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NL)
    var siteNL: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NO)
    var siteNO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NP)
    var siteNP: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NR)
    var siteNR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NU)
    var siteNU: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_NZ)
    var siteNZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_OM)
    var siteOM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_OT)
    var siteOT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PA)
    var sitePA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PE)
    var sitePE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PF)
    var sitePF: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PG)
    var sitePG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PH)
    var sitePH: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PK)
    var sitePK: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PL)
    var sitePL: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PM)
    var sitePM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PN)
    var sitePN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PR)
    var sitePR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PS)
    var sitePS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PT)
    var sitePT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PW)
    var sitePW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_PY)
    var sitePY: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_QA)
    var siteQA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_RE)
    var siteRE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_RO)
    var siteRO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_RS)
    var siteRS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_RU)
    var siteRU: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_RW)
    var siteRW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SA)
    var siteSA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SB)
    var siteSB: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SC)
    var siteSC: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SD)
    var siteSD: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SE)
    var siteSE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SG)
    var siteSG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SH)
    var siteSH: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SI)
    var siteSI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SJ)
    var siteSJ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SK)
    var siteSK: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SL)
    var siteSL: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SM)
    var siteSM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SN)
    var siteSN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SO)
    var siteSO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SR)
    var siteSR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SS)
    var siteSS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ST)
    var siteST: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SV)
    var siteSV: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SY)
    var siteSY: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_SZ)
    var siteSZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TC)
    var siteTC: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TD)
    var siteTD: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TF)
    var siteTF: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TG)
    var siteTG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TH)
    var siteTH: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TJ)
    var siteTJ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TK)
    var siteTK: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TL)
    var siteTL: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TM)
    var siteTM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TN)
    var siteTN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TO)
    var siteTO: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TR)
    var siteTR: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TT)
    var siteTT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TV)
    var siteTV: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TW)
    var siteTW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_TZ)
    var siteTZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_UA)
    var siteUA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_UG)
    var siteUG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_US)
    var siteUS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_UY)
    var siteUY: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_UZ)
    var siteUZ: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_VA)
    var siteVA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_VC)
    var siteVC: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_VE)
    var siteVE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_VG)
    var siteVG: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_VI)
    var siteVI: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_VN)
    var siteVN: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_VU)
    var siteVU: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_WF)
    var siteWF: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_WS)
    var siteWS: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_YE)
    var siteYE: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_YT)
    var siteYT: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ZA)
    var siteZA: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ZM)
    var siteZM: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.SITE_ZW)
    var siteZW: Int = 0

    @get:DynamoDbAttribute(EpisodeMetricsAttributes.EXPIRATION)
    var expiration: Long = 0

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = ""

    fun counterBySite(country: Country): Int = when (country) {
        Country.AD -> siteAD
        Country.AE -> siteAE
        Country.AF -> siteAF
        Country.AG -> siteAG
        Country.AI -> siteAI
        Country.AL -> siteAL
        Country.AM -> siteAM
        Country.AN -> siteAN
        Country.AO -> siteAO
        Country.AR -> siteAR
        Country.AS -> siteAS
        Country.AT -> siteAT
        Country.AU -> siteAU
        Country.AW -> siteAW
        Country.AX -> siteAX
        Country.AZ -> siteAZ
        Country.BA -> siteBA
        Country.BB -> siteBB
        Country.BD -> siteBD
        Country.BE -> siteBE
        Country.BF -> siteBF
        Country.BG -> siteBG
        Country.BH -> siteBH
        Country.BI -> siteBI
        Country.BJ -> siteBJ
        Country.BL -> siteBL
        Country.BM -> siteBM
        Country.BN -> siteBN
        Country.BO -> siteBO
        Country.BR -> siteBR
        Country.BS -> siteBS
        Country.BT -> siteBT
        Country.BV -> siteBV
        Country.BW -> siteBW
        Country.BY -> siteBY
        Country.BZ -> siteBZ
        Country.CA -> siteCA
        Country.CC -> siteCC
        Country.CD -> siteCD
        Country.CF -> siteCF
        Country.CG -> siteCG
        Country.CH -> siteCH
        Country.CI -> siteCI
        Country.CK -> siteCK
        Country.CL -> siteCL
        Country.CM -> siteCM
        Country.CN -> siteCN
        Country.CO -> siteCO
        Country.CR -> siteCR
        Country.CT -> siteCT
        Country.CU -> siteCU
        Country.CV -> siteCV
        Country.CW -> siteCW
        Country.CX -> siteCX
        Country.CY -> siteCY
        Country.CZ -> siteCZ
        Country.DE -> siteDE
        Country.DJ -> siteDJ
        Country.DK -> siteDK
        Country.DM -> siteDM
        Country.DO -> siteDO
        Country.DZ -> siteDZ
        Country.EC -> siteEC
        Country.EE -> siteEE
        Country.EG -> siteEG
        Country.EH -> siteEH
        Country.ER -> siteER
        Country.ES -> siteES
        Country.ET -> siteET
        Country.FI -> siteFI
        Country.FJ -> siteFJ
        Country.FK -> siteFK
        Country.FM -> siteFM
        Country.FO -> siteFO
        Country.FR -> siteFR
        Country.GA -> siteGA
        Country.GB -> siteGB
        Country.GD -> siteGD
        Country.GE -> siteGE
        Country.GF -> siteGF
        Country.GG -> siteGG
        Country.GH -> siteGH
        Country.GI -> siteGI
        Country.GL -> siteGL
        Country.GM -> siteGM
        Country.GN -> siteGN
        Country.GP -> siteGP
        Country.GQ -> siteGQ
        Country.GR -> siteGR
        Country.GS -> siteGS
        Country.GT -> siteGT
        Country.GU -> siteGU
        Country.GW -> siteGW
        Country.GX -> siteGX
        Country.GY -> siteGY
        Country.HK -> siteHK
        Country.HM -> siteHM
        Country.HN -> siteHN
        Country.HR -> siteHR
        Country.HT -> siteHT
        Country.HU -> siteHU
        Country.ID -> siteID
        Country.IE -> siteIE
        Country.IL -> siteIL
        Country.IM -> siteIM
        Country.IN -> siteIN
        Country.IO -> siteIO
        Country.IQ -> siteIQ
        Country.IR -> siteIR
        Country.IS -> siteIS
        Country.IT -> siteIT
        Country.JE -> siteJE
        Country.JM -> siteJM
        Country.JO -> siteJO
        Country.JP -> siteJP
        Country.KE -> siteKE
        Country.KG -> siteKG
        Country.KH -> siteKH
        Country.KI -> siteKI
        Country.KM -> siteKM
        Country.KN -> siteKN
        Country.KP -> siteKP
        Country.KR -> siteKR
        Country.KW -> siteKW
        Country.KY -> siteKY
        Country.KZ -> siteKZ
        Country.LA -> siteLA
        Country.LB -> siteLB
        Country.LC -> siteLC
        Country.LI -> siteLI
        Country.LK -> siteLK
        Country.LR -> siteLR
        Country.LS -> siteLS
        Country.LT -> siteLT
        Country.LU -> siteLU
        Country.LV -> siteLV
        Country.LY -> siteLY
        Country.MA -> siteMA
        Country.MC -> siteMC
        Country.MD -> siteMD
        Country.ME -> siteME
        Country.MF -> siteMF
        Country.MG -> siteMG
        Country.MH -> siteMH
        Country.MK -> siteMK
        Country.ML -> siteML
        Country.MM -> siteMM
        Country.MN -> siteMN
        Country.MO -> siteMO
        Country.MP -> siteMP
        Country.MQ -> siteMQ
        Country.MR -> siteMR
        Country.MS -> siteMS
        Country.MT -> siteMT
        Country.MU -> siteMU
        Country.MV -> siteMV
        Country.MW -> siteMW
        Country.MX -> siteMX
        Country.MY -> siteMY
        Country.MZ -> siteMZ
        Country.NA -> siteNA
        Country.NC -> siteNC
        Country.NE -> siteNE
        Country.NF -> siteNF
        Country.NG -> siteNG
        Country.NI -> siteNI
        Country.NL -> siteNL
        Country.NO -> siteNO
        Country.NP -> siteNP
        Country.NR -> siteNR
        Country.NU -> siteNU
        Country.NZ -> siteNZ
        Country.OM -> siteOM
        Country.OT -> siteOT
        Country.PA -> sitePA
        Country.PE -> sitePE
        Country.PF -> sitePF
        Country.PG -> sitePG
        Country.PH -> sitePH
        Country.PK -> sitePK
        Country.PL -> sitePL
        Country.PM -> sitePM
        Country.PN -> sitePN
        Country.PR -> sitePR
        Country.PS -> sitePS
        Country.PT -> sitePT
        Country.PW -> sitePW
        Country.PY -> sitePY
        Country.QA -> siteQA
        Country.RE -> siteRE
        Country.RO -> siteRO
        Country.RS -> siteRS
        Country.RU -> siteRU
        Country.RW -> siteRW
        Country.SA -> siteSA
        Country.SB -> siteSB
        Country.SC -> siteSC
        Country.SD -> siteSD
        Country.SE -> siteSE
        Country.SG -> siteSG
        Country.SH -> siteSH
        Country.SI -> siteSI
        Country.SJ -> siteSJ
        Country.SK -> siteSK
        Country.SL -> siteSL
        Country.SM -> siteSM
        Country.SN -> siteSN
        Country.SO -> siteSO
        Country.SR -> siteSR
        Country.SS -> siteSS
        Country.ST -> siteST
        Country.SV -> siteSV
        Country.SY -> siteSY
        Country.SZ -> siteSZ
        Country.TC -> siteTC
        Country.TD -> siteTD
        Country.TF -> siteTF
        Country.TG -> siteTG
        Country.TH -> siteTH
        Country.TJ -> siteTJ
        Country.TK -> siteTK
        Country.TL -> siteTL
        Country.TM -> siteTM
        Country.TN -> siteTN
        Country.TO -> siteTO
        Country.TR -> siteTR
        Country.TT -> siteTT
        Country.TV -> siteTV
        Country.TW -> siteTW
        Country.TZ -> siteTZ
        Country.UA -> siteUA
        Country.UG -> siteUG
        Country.US -> siteUS
        Country.UY -> siteUY
        Country.UZ -> siteUZ
        Country.VA -> siteVA
        Country.VC -> siteVC
        Country.VE -> siteVE
        Country.VG -> siteVG
        Country.VI -> siteVI
        Country.VN -> siteVN
        Country.VU -> siteVU
        Country.WF -> siteWF
        Country.WS -> siteWS
        Country.YE -> siteYE
        Country.YT -> siteYT
        Country.ZA -> siteZA
        Country.ZM -> siteZM
        Country.ZW -> siteZW
    }

    companion object {
        fun buildPartitionKey(episodeId: String): String =
            if (episodeId.startsWith("E#")) episodeId else "E#$episodeId"
    }
}
