package com.etermax.preguntados.analytics.repository

import com.etermax.preguntados.analytics.enums.MetricDeviceType
import com.etermax.preguntados.analytics.enums.MetricUserType
import com.etermax.preguntados.analytics.service.EpisodeCountersAnalyticsRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import kotlinx.coroutines.future.await
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.ReturnValue
import software.amazon.awssdk.services.dynamodb.model.UpdateItemRequest
import software.amazon.awssdk.services.dynamodb.model.UpdateItemResponse
import java.time.temporal.ChronoUnit

class DynamoDBEpisodeCountersAnalyticsRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<EpisodeMetricsItem>,
    clock: Clock
) : EpisodeCountersAnalyticsRepository, DynamoDBRepository<EpisodeMetricsItem>(client, table) {
    private val expiresIn6Months = clock.now().plus(6, ChronoUnit.MONTHS).toEpochSecond()

    override suspend fun registerPlayedFrom(
        id: String,
        country: Country,
        deviceType: MetricDeviceType,
        userType: MetricUserType
    ) {
        val counters = listOf(
            EpisodeMetricsAttributes.from(country),
            when (deviceType) {
                MetricDeviceType.ANDROID_MOBILE -> EpisodeMetricsAttributes.DEVICE_ANDROID_MOBILE
                MetricDeviceType.ANDROID_TABLET -> EpisodeMetricsAttributes.DEVICE_ANDROID_TABLET
                MetricDeviceType.IOS_MOBILE -> EpisodeMetricsAttributes.DEVICE_IOS_MOBILE
                MetricDeviceType.IOS_IPAD -> EpisodeMetricsAttributes.DEVICE_IOS_IPAD
                MetricDeviceType.WEB -> EpisodeMetricsAttributes.DEVICE_WEB
                MetricDeviceType.OTHER -> EpisodeMetricsAttributes.DEVICE_OTHER
            },
            when (userType) {
                MetricUserType.FOLLOWER -> EpisodeMetricsAttributes.USER_TYPE_FOLLOWER
                MetricUserType.NOT_FOLLOWER -> EpisodeMetricsAttributes.USER_TYPE_NOT_FOLLOWER
            },
            EpisodeMetricsAttributes.STARTED
        )

        val expressionValues = counters.associate { ":$it" to AttributeValue.builder().n("1").build() } + mapOf(
            ":${EpisodeMetricsAttributes.EXPIRATION}" to AttributeValue.builder().n(expiresIn6Months.toString()).build()
        )

        val updateExpression =
            "ADD ${counters.joinToString(", ") { "$it :$it" }} SET ${EpisodeMetricsAttributes.EXPIRATION} = :${EpisodeMetricsAttributes.EXPIRATION}"

        val episodeIdPK = EpisodeMetricsItem.buildPartitionKey(id)
        val updateRequest = UpdateItemRequest.builder()
            .tableName(table.tableName())
            .key(mapOf("PK" to AttributeValue.builder().s(episodeIdPK).build()))
            .updateExpression(updateExpression)
            .expressionAttributeValues(expressionValues)
            .returnValues(ReturnValue.ALL_NEW)
            .build()

        try {
            rawClient().updateItem(updateRequest).await()
        } catch (e: Exception) {
            throw RuntimeException("Error updating DynamoDB item for id $id: ${e.message}", e)
        }
    }

    override suspend fun registerEpisodeFinished(episodeId: String) {
        val episodeIdPK = EpisodeMetricsItem.buildPartitionKey(episodeId)
        val values = mapOf<String, AttributeValue>(
            ":increment" to AttributeValue.builder().n("1").build(),
            ":${EpisodeMetricsAttributes.EXPIRATION}" to AttributeValue.builder().n(expiresIn6Months.toString()).build()
        )
        val updateRequest = UpdateItemRequest.builder()
            .tableName(table.tableName())
            .key(mapOf("PK" to AttributeValue.builder().s(episodeIdPK).build()))
            .updateExpression("ADD ${EpisodeMetricsAttributes.FINISHED} :increment  SET ${EpisodeMetricsAttributes.EXPIRATION} = :${EpisodeMetricsAttributes.EXPIRATION}")
            .expressionAttributeValues(values)
            .returnValues(ReturnValue.ALL_NEW)
            .build()
        rawClient().updateItem(updateRequest).await<UpdateItemResponse>()
    }

    override suspend fun getContentMetrics(id: String): EpisodeMetricsItem? {
        val episodeIdPK = EpisodeMetricsItem.buildPartitionKey(id)
        return findItem(
            Key.builder()
                .partitionValue(episodeIdPK)
                .build()
        )
    }
}
