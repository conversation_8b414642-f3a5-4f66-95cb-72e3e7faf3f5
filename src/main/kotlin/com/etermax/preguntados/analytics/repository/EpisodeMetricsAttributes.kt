package com.etermax.preguntados.analytics.repository

import com.etermax.preguntados.external.services.core.domain.api.profile.Country

object EpisodeMetricsAttributes {
    const val DEVICE_ANDROID_MOBILE = "device_android_mobile"
    const val DEVICE_ANDROID_TABLET = "device_android_tablet"
    const val DEVICE_IOS_MOBILE = "device_ios_mobile"
    const val DEVICE_IOS_IPAD = "device_ios_ipad"
    const val DEVICE_WEB = "device_web"
    const val DEVICE_OTHER = "device_other"

    const val USER_TYPE_FOLLOWER = "user_type_follower"
    const val USER_TYPE_NOT_FOLLOWER = "user_type_not_follower"

    const val STARTED = "started"
    const val FINISHED = "finished"

    const val SITE_AD = "site_AD"
    const val SITE_AE = "site_AE"
    const val SITE_AF = "site_AF"
    const val SITE_AG = "site_AG"
    const val SITE_AI = "site_AI"
    const val SITE_AL = "site_AL"
    const val SITE_AM = "site_AM"
    const val SITE_AN = "site_AN"
    const val SITE_AO = "site_AO"
    const val SITE_AR = "site_AR"
    const val SITE_AS = "site_AS"
    const val SITE_AT = "site_AT"
    const val SITE_AU = "site_AU"
    const val SITE_AW = "site_AW"
    const val SITE_AX = "site_AX"
    const val SITE_AZ = "site_AZ"
    const val SITE_BA = "site_BA"
    const val SITE_BB = "site_BB"
    const val SITE_BD = "site_BD"
    const val SITE_BE = "site_BE"
    const val SITE_BF = "site_BF"
    const val SITE_BG = "site_BG"
    const val SITE_BH = "site_BH"
    const val SITE_BI = "site_BI"
    const val SITE_BJ = "site_BJ"
    const val SITE_BL = "site_BL"
    const val SITE_BM = "site_BM"
    const val SITE_BN = "site_BN"
    const val SITE_BO = "site_BO"
    const val SITE_BR = "site_BR"
    const val SITE_BS = "site_BS"
    const val SITE_BT = "site_BT"
    const val SITE_BV = "site_BV"
    const val SITE_BW = "site_BW"
    const val SITE_BY = "site_BY"
    const val SITE_BZ = "site_BZ"
    const val SITE_CA = "site_CA"
    const val SITE_CC = "site_CC"
    const val SITE_CD = "site_CD"
    const val SITE_CF = "site_CF"
    const val SITE_CG = "site_CG"
    const val SITE_CH = "site_CH"
    const val SITE_CI = "site_CI"
    const val SITE_CK = "site_CK"
    const val SITE_CL = "site_CL"
    const val SITE_CM = "site_CM"
    const val SITE_CN = "site_CN"
    const val SITE_CO = "site_CO"
    const val SITE_CR = "site_CR"
    const val SITE_CT = "site_CT"
    const val SITE_CU = "site_CU"
    const val SITE_CV = "site_CV"
    const val SITE_CW = "site_CW"
    const val SITE_CX = "site_CX"
    const val SITE_CY = "site_CY"
    const val SITE_CZ = "site_CZ"
    const val SITE_DE = "site_DE"
    const val SITE_DJ = "site_DJ"
    const val SITE_DK = "site_DK"
    const val SITE_DM = "site_DM"
    const val SITE_DO = "site_DO"
    const val SITE_DZ = "site_DZ"
    const val SITE_EC = "site_EC"
    const val SITE_EE = "site_EE"
    const val SITE_EG = "site_EG"
    const val SITE_EH = "site_EH"
    const val SITE_ER = "site_ER"
    const val SITE_ES = "site_ES"
    const val SITE_ET = "site_ET"
    const val SITE_FI = "site_FI"
    const val SITE_FJ = "site_FJ"
    const val SITE_FK = "site_FK"
    const val SITE_FM = "site_FM"
    const val SITE_FO = "site_FO"
    const val SITE_FR = "site_FR"
    const val SITE_GA = "site_GA"
    const val SITE_GB = "site_GB"
    const val SITE_GD = "site_GD"
    const val SITE_GE = "site_GE"
    const val SITE_GF = "site_GF"
    const val SITE_GG = "site_GG"
    const val SITE_GH = "site_GH"
    const val SITE_GI = "site_GI"
    const val SITE_GL = "site_GL"
    const val SITE_GM = "site_GM"
    const val SITE_GN = "site_GN"
    const val SITE_GP = "site_GP"
    const val SITE_GQ = "site_GQ"
    const val SITE_GR = "site_GR"
    const val SITE_GS = "site_GS"
    const val SITE_GT = "site_GT"
    const val SITE_GU = "site_GU"
    const val SITE_GW = "site_GW"
    const val SITE_GX = "site_GX"
    const val SITE_GY = "site_GY"
    const val SITE_HK = "site_HK"
    const val SITE_HM = "site_HM"
    const val SITE_HN = "site_HN"
    const val SITE_HR = "site_HR"
    const val SITE_HT = "site_HT"
    const val SITE_HU = "site_HU"
    const val SITE_ID = "site_ID"
    const val SITE_IE = "site_IE"
    const val SITE_IL = "site_IL"
    const val SITE_IM = "site_IM"
    const val SITE_IN = "site_IN"
    const val SITE_IO = "site_IO"
    const val SITE_IQ = "site_IQ"
    const val SITE_IR = "site_IR"
    const val SITE_IS = "site_IS"
    const val SITE_IT = "site_IT"
    const val SITE_JE = "site_JE"
    const val SITE_JM = "site_JM"
    const val SITE_JO = "site_JO"
    const val SITE_JP = "site_JP"
    const val SITE_KE = "site_KE"
    const val SITE_KG = "site_KG"
    const val SITE_KH = "site_KH"
    const val SITE_KI = "site_KI"
    const val SITE_KM = "site_KM"
    const val SITE_KN = "site_KN"
    const val SITE_KP = "site_KP"
    const val SITE_KR = "site_KR"
    const val SITE_KW = "site_KW"
    const val SITE_KY = "site_KY"
    const val SITE_KZ = "site_KZ"
    const val SITE_LA = "site_LA"
    const val SITE_LB = "site_LB"
    const val SITE_LC = "site_LC"
    const val SITE_LI = "site_LI"
    const val SITE_LK = "site_LK"
    const val SITE_LR = "site_LR"
    const val SITE_LS = "site_LS"
    const val SITE_LT = "site_LT"
    const val SITE_LU = "site_LU"
    const val SITE_LV = "site_LV"
    const val SITE_LY = "site_LY"
    const val SITE_MA = "site_MA"
    const val SITE_MC = "site_MC"
    const val SITE_MD = "site_MD"
    const val SITE_ME = "site_ME"
    const val SITE_MF = "site_MF"
    const val SITE_MG = "site_MG"
    const val SITE_MH = "site_MH"
    const val SITE_MK = "site_MK"
    const val SITE_ML = "site_ML"
    const val SITE_MM = "site_MM"
    const val SITE_MN = "site_MN"
    const val SITE_MO = "site_MO"
    const val SITE_MP = "site_MP"
    const val SITE_MQ = "site_MQ"
    const val SITE_MR = "site_MR"
    const val SITE_MS = "site_MS"
    const val SITE_MT = "site_MT"
    const val SITE_MU = "site_MU"
    const val SITE_MV = "site_MV"
    const val SITE_MW = "site_MW"
    const val SITE_MX = "site_MX"
    const val SITE_MY = "site_MY"
    const val SITE_MZ = "site_MZ"
    const val SITE_NA = "site_NA"
    const val SITE_NC = "site_NC"
    const val SITE_NE = "site_NE"
    const val SITE_NF = "site_NF"
    const val SITE_NG = "site_NG"
    const val SITE_NI = "site_NI"
    const val SITE_NL = "site_NL"
    const val SITE_NO = "site_NO"
    const val SITE_NP = "site_NP"
    const val SITE_NR = "site_NR"
    const val SITE_NU = "site_NU"
    const val SITE_NZ = "site_NZ"
    const val SITE_OM = "site_OM"
    const val SITE_OT = "site_OT"
    const val SITE_PA = "site_PA"
    const val SITE_PE = "site_PE"
    const val SITE_PF = "site_PF"
    const val SITE_PG = "site_PG"
    const val SITE_PH = "site_PH"
    const val SITE_PK = "site_PK"
    const val SITE_PL = "site_PL"
    const val SITE_PM = "site_PM"
    const val SITE_PN = "site_PN"
    const val SITE_PR = "site_PR"
    const val SITE_PS = "site_PS"
    const val SITE_PT = "site_PT"
    const val SITE_PW = "site_PW"
    const val SITE_PY = "site_PY"
    const val SITE_QA = "site_QA"
    const val SITE_RE = "site_RE"
    const val SITE_RO = "site_RO"
    const val SITE_RS = "site_RS"
    const val SITE_RU = "site_RU"
    const val SITE_RW = "site_RW"
    const val SITE_SA = "site_SA"
    const val SITE_SB = "site_SB"
    const val SITE_SC = "site_SC"
    const val SITE_SD = "site_SD"
    const val SITE_SE = "site_SE"
    const val SITE_SG = "site_SG"
    const val SITE_SH = "site_SH"
    const val SITE_SI = "site_SI"
    const val SITE_SJ = "site_SJ"
    const val SITE_SK = "site_SK"
    const val SITE_SL = "site_SL"
    const val SITE_SM = "site_SM"
    const val SITE_SN = "site_SN"
    const val SITE_SO = "site_SO"
    const val SITE_SR = "site_SR"
    const val SITE_SS = "site_SS"
    const val SITE_ST = "site_ST"
    const val SITE_SV = "site_SV"
    const val SITE_SY = "site_SY"
    const val SITE_SZ = "site_SZ"
    const val SITE_TC = "site_TC"
    const val SITE_TD = "site_TD"
    const val SITE_TF = "site_TF"
    const val SITE_TG = "site_TG"
    const val SITE_TH = "site_TH"
    const val SITE_TJ = "site_TJ"
    const val SITE_TK = "site_TK"
    const val SITE_TL = "site_TL"
    const val SITE_TM = "site_TM"
    const val SITE_TN = "site_TN"
    const val SITE_TO = "site_TO"
    const val SITE_TR = "site_TR"
    const val SITE_TT = "site_TT"
    const val SITE_TV = "site_TV"
    const val SITE_TW = "site_TW"
    const val SITE_TZ = "site_TZ"
    const val SITE_UA = "site_UA"
    const val SITE_UG = "site_UG"
    const val SITE_US = "site_US"
    const val SITE_UY = "site_UY"
    const val SITE_UZ = "site_UZ"
    const val SITE_VA = "site_VA"
    const val SITE_VC = "site_VC"
    const val SITE_VE = "site_VE"
    const val SITE_VG = "site_VG"
    const val SITE_VI = "site_VI"
    const val SITE_VN = "site_VN"
    const val SITE_VU = "site_VU"
    const val SITE_WF = "site_WF"
    const val SITE_WS = "site_WS"
    const val SITE_YE = "site_YE"
    const val SITE_YT = "site_YT"
    const val SITE_ZA = "site_ZA"
    const val SITE_ZM = "site_ZM"
    const val SITE_ZW = "site_ZW"

    const val EXPIRATION = "expiration"

    fun from(country: Country) = when (country) {
        Country.AD -> SITE_AD
        Country.AE -> SITE_AE
        Country.AF -> SITE_AF
        Country.AG -> SITE_AG
        Country.AI -> SITE_AI
        Country.AL -> SITE_AL
        Country.AM -> SITE_AM
        Country.AN -> SITE_AN
        Country.AO -> SITE_AO
        Country.AR -> SITE_AR
        Country.AS -> SITE_AS
        Country.AT -> SITE_AT
        Country.AU -> SITE_AU
        Country.AW -> SITE_AW
        Country.AX -> SITE_AX
        Country.AZ -> SITE_AZ
        Country.BA -> SITE_BA
        Country.BB -> SITE_BB
        Country.BD -> SITE_BD
        Country.BE -> SITE_BE
        Country.BF -> SITE_BF
        Country.BG -> SITE_BG
        Country.BH -> SITE_BH
        Country.BI -> SITE_BI
        Country.BJ -> SITE_BJ
        Country.BL -> SITE_BL
        Country.BM -> SITE_BM
        Country.BN -> SITE_BN
        Country.BO -> SITE_BO
        Country.BR -> SITE_BR
        Country.BS -> SITE_BS
        Country.BT -> SITE_BT
        Country.BV -> SITE_BV
        Country.BW -> SITE_BW
        Country.BY -> SITE_BY
        Country.BZ -> SITE_BZ
        Country.CA -> SITE_CA
        Country.CC -> SITE_CC
        Country.CD -> SITE_CD
        Country.CF -> SITE_CF
        Country.CG -> SITE_CG
        Country.CH -> SITE_CH
        Country.CI -> SITE_CI
        Country.CK -> SITE_CK
        Country.CL -> SITE_CL
        Country.CM -> SITE_CM
        Country.CN -> SITE_CN
        Country.CO -> SITE_CO
        Country.CR -> SITE_CR
        Country.CT -> SITE_CT
        Country.CU -> SITE_CU
        Country.CV -> SITE_CV
        Country.CW -> SITE_CW
        Country.CX -> SITE_CX
        Country.CY -> SITE_CY
        Country.CZ -> SITE_CZ
        Country.DE -> SITE_DE
        Country.DJ -> SITE_DJ
        Country.DK -> SITE_DK
        Country.DM -> SITE_DM
        Country.DO -> SITE_DO
        Country.DZ -> SITE_DZ
        Country.EC -> SITE_EC
        Country.EE -> SITE_EE
        Country.EG -> SITE_EG
        Country.EH -> SITE_EH
        Country.ER -> SITE_ER
        Country.ES -> SITE_ES
        Country.ET -> SITE_ET
        Country.FI -> SITE_FI
        Country.FJ -> SITE_FJ
        Country.FK -> SITE_FK
        Country.FM -> SITE_FM
        Country.FO -> SITE_FO
        Country.FR -> SITE_FR
        Country.GA -> SITE_GA
        Country.GB -> SITE_GB
        Country.GD -> SITE_GD
        Country.GE -> SITE_GE
        Country.GF -> SITE_GF
        Country.GG -> SITE_GG
        Country.GH -> SITE_GH
        Country.GI -> SITE_GI
        Country.GL -> SITE_GL
        Country.GM -> SITE_GM
        Country.GN -> SITE_GN
        Country.GP -> SITE_GP
        Country.GQ -> SITE_GQ
        Country.GR -> SITE_GR
        Country.GS -> SITE_GS
        Country.GT -> SITE_GT
        Country.GU -> SITE_GU
        Country.GW -> SITE_GW
        Country.GX -> SITE_GX
        Country.GY -> SITE_GY
        Country.HK -> SITE_HK
        Country.HM -> SITE_HM
        Country.HN -> SITE_HN
        Country.HR -> SITE_HR
        Country.HT -> SITE_HT
        Country.HU -> SITE_HU
        Country.ID -> SITE_ID
        Country.IE -> SITE_IE
        Country.IL -> SITE_IL
        Country.IM -> SITE_IM
        Country.IN -> SITE_IN
        Country.IO -> SITE_IO
        Country.IQ -> SITE_IQ
        Country.IR -> SITE_IR
        Country.IS -> SITE_IS
        Country.IT -> SITE_IT
        Country.JE -> SITE_JE
        Country.JM -> SITE_JM
        Country.JO -> SITE_JO
        Country.JP -> SITE_JP
        Country.KE -> SITE_KE
        Country.KG -> SITE_KG
        Country.KH -> SITE_KH
        Country.KI -> SITE_KI
        Country.KM -> SITE_KM
        Country.KN -> SITE_KN
        Country.KP -> SITE_KP
        Country.KR -> SITE_KR
        Country.KW -> SITE_KW
        Country.KY -> SITE_KY
        Country.KZ -> SITE_KZ
        Country.LA -> SITE_LA
        Country.LB -> SITE_LB
        Country.LC -> SITE_LC
        Country.LI -> SITE_LI
        Country.LK -> SITE_LK
        Country.LR -> SITE_LR
        Country.LS -> SITE_LS
        Country.LT -> SITE_LT
        Country.LU -> SITE_LU
        Country.LV -> SITE_LV
        Country.LY -> SITE_LY
        Country.MA -> SITE_MA
        Country.MC -> SITE_MC
        Country.MD -> SITE_MD
        Country.ME -> SITE_ME
        Country.MF -> SITE_MF
        Country.MG -> SITE_MG
        Country.MH -> SITE_MH
        Country.MK -> SITE_MK
        Country.ML -> SITE_ML
        Country.MM -> SITE_MM
        Country.MN -> SITE_MN
        Country.MO -> SITE_MO
        Country.MP -> SITE_MP
        Country.MQ -> SITE_MQ
        Country.MR -> SITE_MR
        Country.MS -> SITE_MS
        Country.MT -> SITE_MT
        Country.MU -> SITE_MU
        Country.MV -> SITE_MV
        Country.MW -> SITE_MW
        Country.MX -> SITE_MX
        Country.MY -> SITE_MY
        Country.MZ -> SITE_MZ
        Country.NA -> SITE_NA
        Country.NC -> SITE_NC
        Country.NE -> SITE_NE
        Country.NF -> SITE_NF
        Country.NG -> SITE_NG
        Country.NI -> SITE_NI
        Country.NL -> SITE_NL
        Country.NO -> SITE_NO
        Country.NP -> SITE_NP
        Country.NR -> SITE_NR
        Country.NU -> SITE_NU
        Country.NZ -> SITE_NZ
        Country.OM -> SITE_OM
        Country.OT -> SITE_OT
        Country.PA -> SITE_PA
        Country.PE -> SITE_PE
        Country.PF -> SITE_PF
        Country.PG -> SITE_PG
        Country.PH -> SITE_PH
        Country.PK -> SITE_PK
        Country.PL -> SITE_PL
        Country.PM -> SITE_PM
        Country.PN -> SITE_PN
        Country.PR -> SITE_PR
        Country.PS -> SITE_PS
        Country.PT -> SITE_PT
        Country.PW -> SITE_PW
        Country.PY -> SITE_PY
        Country.QA -> SITE_QA
        Country.RE -> SITE_RE
        Country.RO -> SITE_RO
        Country.RS -> SITE_RS
        Country.RU -> SITE_RU
        Country.RW -> SITE_RW
        Country.SA -> SITE_SA
        Country.SB -> SITE_SB
        Country.SC -> SITE_SC
        Country.SD -> SITE_SD
        Country.SE -> SITE_SE
        Country.SG -> SITE_SG
        Country.SH -> SITE_SH
        Country.SI -> SITE_SI
        Country.SJ -> SITE_SJ
        Country.SK -> SITE_SK
        Country.SL -> SITE_SL
        Country.SM -> SITE_SM
        Country.SN -> SITE_SN
        Country.SO -> SITE_SO
        Country.SR -> SITE_SR
        Country.SS -> SITE_SS
        Country.ST -> SITE_ST
        Country.SV -> SITE_SV
        Country.SY -> SITE_SY
        Country.SZ -> SITE_SZ
        Country.TC -> SITE_TC
        Country.TD -> SITE_TD
        Country.TF -> SITE_TF
        Country.TG -> SITE_TG
        Country.TH -> SITE_TH
        Country.TJ -> SITE_TJ
        Country.TK -> SITE_TK
        Country.TL -> SITE_TL
        Country.TM -> SITE_TM
        Country.TN -> SITE_TN
        Country.TO -> SITE_TO
        Country.TR -> SITE_TR
        Country.TT -> SITE_TT
        Country.TV -> SITE_TV
        Country.TW -> SITE_TW
        Country.TZ -> SITE_TZ
        Country.UA -> SITE_UA
        Country.UG -> SITE_UG
        Country.US -> SITE_US
        Country.UY -> SITE_UY
        Country.UZ -> SITE_UZ
        Country.VA -> SITE_VA
        Country.VC -> SITE_VC
        Country.VE -> SITE_VE
        Country.VG -> SITE_VG
        Country.VI -> SITE_VI
        Country.VN -> SITE_VN
        Country.VU -> SITE_VU
        Country.WF -> SITE_WF
        Country.WS -> SITE_WS
        Country.YE -> SITE_YE
        Country.YT -> SITE_YT
        Country.ZA -> SITE_ZA
        Country.ZM -> SITE_ZM
        Country.ZW -> SITE_ZW
    }
}
