package com.etermax.preguntados.analytics.repository

import com.etermax.preguntados.analytics.service.CreatorsCountersAnalyticsRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import kotlinx.coroutines.future.await
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.ReturnValue
import software.amazon.awssdk.services.dynamodb.model.UpdateItemRequest
import software.amazon.awssdk.services.dynamodb.model.UpdateItemResponse
import java.time.temporal.ChronoUnit

class DynamoDBCreatorUserAnalyticsRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<CreatorUserMetricsItem>,
    clock: Clock
) : CreatorsCountersAnalyticsRepository, DynamoDBRepository<CreatorUserMetricsItem>(client, table) {
    private val expiresIn6Months = clock.now().plus(6, ChronoUnit.MONTHS).toEpochSecond()

    override suspend fun registerPlayedContentOf(creatorId: Long) {
        try {
            val ownerUserPK = CreatorUserMetricsItem.buildPartitionKey(creatorId)
            val updateRequest = UpdateItemRequest.builder()
                .tableName(table.tableName())
                .key(mapOf<String, AttributeValue>("PK" to AttributeValue.builder().s(ownerUserPK).build()))
                .updateExpression("ADD ${"total_started"} :increment SET ${EpisodeMetricsAttributes.EXPIRATION} = :${CreatorMetricsAttributes.EXPIRATION}")
                .expressionAttributeValues(
                    mapOf<String, AttributeValue>(
                        ":increment" to AttributeValue.builder().n("1").build(),
                        ":${CreatorMetricsAttributes.EXPIRATION}" to AttributeValue.builder()
                            .n(expiresIn6Months.toString()).build()
                    )
                )
                .returnValues(ReturnValue.ALL_NEW)
                .build()
            rawClient().updateItem(updateRequest).await<UpdateItemResponse>()
        } catch (e: Exception) {
            throw RuntimeException("Error updating DynamoDB item for creator id $creatorId: ${e.message}", e)
        }
    }

    override suspend fun registerFinishedContentOf(creatorId: Long) {
        val ownerUserPK = CreatorUserMetricsItem.buildPartitionKey(creatorId)
        val updateRequest = UpdateItemRequest.builder()
            .tableName(table.tableName())
            .key(mapOf("PK" to AttributeValue.builder().s(ownerUserPK).build()))
            .updateExpression("ADD ${"total_finished"} :increment SET ${EpisodeMetricsAttributes.EXPIRATION} = :${CreatorMetricsAttributes.EXPIRATION}")
            .expressionAttributeValues(
                mapOf(
                    ":increment" to AttributeValue.builder().n("1").build(),
                    ":${CreatorMetricsAttributes.EXPIRATION}" to AttributeValue.builder().n(expiresIn6Months.toString())
                        .build()
                )
            )
            .returnValues(ReturnValue.ALL_NEW)
            .build()
        rawClient().updateItem(updateRequest).await<UpdateItemResponse>()
    }

    override suspend fun getCreatorFinishRate(creatorId: Long): CreatorUserMetricsItem? = findItem(
        Key.builder()
            .partitionValue(CreatorUserMetricsItem.buildPartitionKey(creatorId))
            .build()
    )
}
