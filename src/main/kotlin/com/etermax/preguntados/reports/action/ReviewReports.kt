package com.etermax.preguntados.reports.action

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeEntityNotFoundException
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.job.UpdateStatusService
import com.etermax.preguntados.reports.domain.ReportActionTaken
import com.etermax.preguntados.reports.domain.ReportRepository
import org.slf4j.LoggerFactory

class ReviewReports(
    private val getFullEpisodeReports: GetFullEpisodeReports,
    private val updateStatusService: UpdateStatusService,
    private val reportsRepository: ReportRepository,
    private val episodeRepository: EpisodeRepository,
    private val clock: Clock
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData) = with(actionData) {
        logger.info("[REPORTS] Executing review with data $actionData")
        val episode = episodeRepository.findById(actionData.episodeId)
            ?: throw EpisodeEntityNotFoundException(actionData.episodeId)

        if (actionData.actionTaken == ReportActionTaken.BANNED) updateStatusService.updateToRejected(episode)

        val actionDataForFullReports = GetFullEpisodeReports.ActionData(actionData.adminId, actionData.episodeId)
        val reportsResult = getFullEpisodeReports(actionDataForFullReports)
        val reports = reportsResult.episodeReports + reportsResult.contentToReports.values.flatten()
        reports.forEach {
            val updatedDate = clock.now().toInstant().toEpochMilli()
            val closedReport = it.close(adminId, actionTaken, comment, updatedDate)
            reportsRepository.save(closedReport)
        }
    }

    data class ActionData(
        val adminId: Long,
        val episodeId: String,
        val actionTaken: ReportActionTaken,
        val comment: String
    )
}
