package com.etermax.preguntados.reports.action

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeEntityNotFoundException
import com.etermax.preguntados.reports.domain.EpisodeReports
import com.etermax.preguntados.reports.domain.ReportEntityType
import com.etermax.preguntados.reports.domain.ReportRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope

class GetFullEpisodeReports(
    private val reportRepository: ReportRepository,
    private val episodeRepository: EpisodeRepository
) {
    suspend operator fun invoke(actionData: ActionData): EpisodeReports = coroutineScope {
        val episode = episodeRepository.findById(actionData.episodeId)
            ?: throw EpisodeEntityNotFoundException(actionData.episodeId)

        val episodeReportsAsync = async {
            reportRepository.getReportsMadeTo(ReportEntityType.EPISODE, actionData.episodeId)
        }

        val contentReportsAsync = episode.contents.map { contentId ->
            async {
                reportRepository.getReportsMadeTo(ReportEntityType.CONTENT, contentId)
            }
        }

        val episodeReports = episodeReportsAsync.await()
        val contentToReports = episode.contents.zip(contentReportsAsync.awaitAll()).toMap()
        EpisodeReports(episode, episodeReports, contentToReports)
    }

    data class ActionData(
        val adminId: Long,
        val episodeId: String
    )
}
