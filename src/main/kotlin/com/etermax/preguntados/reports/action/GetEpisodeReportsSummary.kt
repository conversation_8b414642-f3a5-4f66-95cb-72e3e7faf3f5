package com.etermax.preguntados.reports.action

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeEntityNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.base.ForbiddenException
import com.etermax.preguntados.reports.domain.Report
import com.etermax.preguntados.reports.domain.ReportEntityType
import com.etermax.preguntados.reports.domain.ReportReason
import com.etermax.preguntados.reports.domain.ReportRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class GetEpisodeReports(
    private val reportRepository: ReportRepository,
    private val episodeRepository: EpisodeRepository
) {
    data class ActionData(
        val ownerId: Long,
        val episodeId: String
    )

    suspend operator fun invoke(actionData: ActionData): EpisodeReportsSummary = coroutineScope {
        val episode = episodeRepository.findById(actionData.episodeId)
            ?: throw EpisodeEntityNotFoundException(actionData.episodeId)

        if (actionData.ownerId != episode.ownerId) {
            throw UserNotAllowedToSeeAnotherCreatorReports(
                actionData.ownerId,
                episode.ownerId,
                actionData.episodeId
            )
        }

        val episodeReportsAsync = async {
            reportRepository.getReportsMadeTo(ReportEntityType.EPISODE, actionData.episodeId)
        }

        // Get reports for each content ID concurrently
        val contentReportsAsync = episode.contents.map { contentId ->
            async {
                reportRepository.getReportsMadeTo(ReportEntityType.CONTENT, contentId)
            }
        }

        val episodeReports = episodeReportsAsync.await()
        val contentReports = contentReportsAsync.map { it.await() }

        val episodeReportsByReason = countByReason(episodeReports)
        val contentReportsByContentAndReason = contentReports.map { countByReason(it) }

        EpisodeReportsSummary(
            episode = episodeReportsByReason,
            contents = contentReportsByContentAndReason
        )
    }

    private fun countByReason(episodeReports: List<Report>): Map<ReportReason, Int> =
        episodeReports.groupBy { it.reason }.mapValues { it.value.size }

    class UserNotAllowedToSeeAnotherCreatorReports(userId: Long, ownerId: Long, episodeId: String) :
        ForbiddenException("User $userId tried to see reports for episode $episodeId but it's created by $ownerId")
}

data class EpisodeReportsSummary(
    val episode: Map<ReportReason, Int>,
    val contents: List<Map<ReportReason, Int>>
)
