package com.etermax.preguntados.episodes.http.handler.core.challenge.representation

import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeSummary
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodeSummaryRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.ranking.RankingRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChallengeSummaryRepresentation(
    @SerialName("id") val id: String,
    @SerialName("start_date") val startDate: Long,
    @SerialName("end_date") val endDate: Long,
    @SerialName("owner_id") val ownerId: Long,
    @SerialName("episode") val episode: EpisodeSummaryRepresentation,
    @SerialName("ranking") val ranking: RankingRepresentation,
    @SerialName("players") val players: ChallengePlayersSummaryRepresentation
) {
    companion object {
        fun from(challengeSummary: ChallengeSummary) = with(challengeSummary) {
            ChallengeSummaryRepresentation(
                id = id,
                startDate = startDate.toMillis(),
                endDate = endDate.toMillis(),
                ownerId = ownerId,
                episode = EpisodeSummaryRepresentation.from(episode),
                ranking = RankingRepresentation.from(ranking),
                players = ChallengePlayersSummaryRepresentation.from(players)
            )
        }
    }
}
