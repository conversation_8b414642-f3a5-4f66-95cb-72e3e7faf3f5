package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.episode.recommendation.RecommendedEpisode
import com.etermax.preguntados.episodes.http.handler.core.representation.profile.ProfileRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class EpisodeRecommendationRepresentation(
    @SerialName("episode") val episode: EpisodeSummaryRepresentation,
    @SerialName("friends") val friends: List<ProfileRepresentation>
) {
    companion object {
        fun from(recommendation: RecommendedEpisode): EpisodeRecommendationRepresentation {
            return EpisodeRecommendationRepresentation(
                episode = EpisodeSummaryRepresentation.from(recommendation.episode),
                friends = recommendation.players.map { ProfileRepresentation.from(it) }
            )
        }
    }
}
