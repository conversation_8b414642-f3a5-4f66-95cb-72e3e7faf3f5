package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.action.episode.UpdateEpisode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class UpdateEpisodeRepresentation(
    @SerialName("episode_id") val episodeId: String,
    @SerialName("name") val name: String? = null,
    @SerialName("cover") val cover: String? = null,
    @SerialName("banner") val banner: String? = null,
    @SerialName("contents") val contents: List<String>? = null,
    @SerialName("status") val status: String? = null,
    @SerialName("type") val type: String? = null,
    @SerialName("channel_id") val channelId: String? = null
) {

    fun to(playerId: Long) = UpdateEpisode.ActionData(
        playerId = playerId,
        episodeId = episodeId,
        name = name,
        cover = cover,
        banner = banner,
        contents = contents,
        status = status?.let { EpisodeStatus.valueOf(it) },
        type = type?.let { EpisodeType.valueOf(it) },
        _channelId = channelId
    )
}
