package com.etermax.preguntados.episodes.http.handler.core.channel.representation

import com.etermax.preguntados.episodes.core.domain.channel.ChannelReduced
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.ChannelOrderTypeRepresentation.Companion.asString
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.ChannelTypeRepresentation.Companion.asString
import com.etermax.preguntados.episodes.http.handler.core.representation.profile.ProfileRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChannelSummaryRepresentation(
    @SerialName("id") val id: String,
    @SerialName("name") val name: String,
    @SerialName("description") val description: String?,
    @SerialName("website") val website: String?,
    @SerialName("cover_url") val coverUrl: String,
    @SerialName("subscribed") val subscribed: Boolean,
    @SerialName("owner_id") val ownerId: Long,
    @SerialName("owner") val owner: ProfileRepresentation?,
    @SerialName("create_date_in_millis") val createDateInMillis: Long,
    @SerialName("last_modification_date_in_millis") val lastModificationDateInMillis: Long,
    @SerialName("statistics") val statistics: ChannelStatisticsRepresentation,
    @SerialName("type") val type: String,
    @SerialName("language") val language: String?,
    @SerialName("order_type") val orderType: String
) {
    companion object {
        fun from(summary: ChannelSummary) = with(summary) {
            ChannelSummaryRepresentation(
                id = id,
                name = name,
                description = description,
                website = website,
                coverUrl = coverUrl,
                subscribed = subscribed,
                ownerId = ownerId,
                owner = owner?.let { ProfileRepresentation.from(it) },
                createDateInMillis = creationDate.toMillis(),
                lastModificationDateInMillis = lastModificationDate.toMillis(),
                statistics = ChannelStatisticsRepresentation.from(statistics),
                type = type.asString(),
                language = language?.name?.uppercase(),
                orderType = orderType.asString()
            )
        }
    }
}

@Serializable
data class ChannelSummaryReducedRepresentation(
    @SerialName("id") val id: String,
    @SerialName("name") val name: String,
    @SerialName("cover_url") val coverUrl: String
) {
    companion object {
        fun from(summary: ChannelReduced) = with(summary) {
            ChannelSummaryReducedRepresentation(
                id = id,
                name = name,
                coverUrl = coverUrl
            )
        }
    }
}
