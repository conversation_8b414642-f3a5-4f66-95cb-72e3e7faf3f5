package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.GetFeed
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.FeedRequestRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.FeedResponseRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class GetFeedHandler(private val getFeed: GetFeed) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode") {
                route("/feed") {
                    post { handler(userId) }
                }
            }
        }

        a.routing {
            route("/api/public/episode/feed") {
                post { handler() }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.handler(userId: Long? = null) {
        val request = call.receive<FeedRequestRepresentation>().to(userId)
        val response = getFeed(request)
        call.respond(HttpStatusCode.OK, FeedResponseRepresentation.from(response))
    }
}
