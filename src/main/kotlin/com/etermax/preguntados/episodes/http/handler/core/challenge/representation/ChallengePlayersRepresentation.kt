package com.etermax.preguntados.episodes.http.handler.core.challenge.representation

import com.etermax.preguntados.episodes.core.action.challenge.ChallengePlayers
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChallengePlayersRepresentation(
    @SerialName("user_ids") val userIds: Set<Long>
) {
    fun to(playerId: Long, challengeId: String) = ChallengePlayers.ActionData(playerId, challengeId, userIds)
}
