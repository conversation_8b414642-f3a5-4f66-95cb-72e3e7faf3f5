package com.etermax.preguntados.episodes.http.handler.core.challenge.representation

import com.etermax.preguntados.episodes.core.action.challenge.FindSortedChallenges.PlayerSortedChallengeSummary
import com.etermax.preguntados.episodes.core.action.challenge.FindSortedChallenges.SortedChallengeSummary
import com.etermax.preguntados.episodes.core.domain.challenge.Challenge
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PlayerSortedChallengeSummaryRepresentation(
    @SerialName("matches") val matches: List<SortedChallengeSummaryRepresentation>,
    @SerialName("pending_matches") val pendingMatches: List<SortedChallengeSummaryRepresentation>
) {
    companion object {
        fun from(summary: PlayerSortedChallengeSummary) = with(summary) {
            PlayerSortedChallengeSummaryRepresentation(
                matches = challenges.map { SortedChallengeSummaryRepresentation.from(it) },
                pendingMatches = pendingChallenges.map { SortedChallengeSummaryRepresentation.from(it) }
            )
        }
    }
}

@Serializable
data class SortedChallengeSummaryRepresentation(
    @SerialName("id") val id: String,
    @SerialName("start_date") val startDate: Long,
    @SerialName("end_date") val endDate: Long,
    @SerialName("owner_id") val ownerId: Long,
    @SerialName("position") val position: Int,
    @SerialName("episode_name") val episodeName: String,
    @SerialName("image_url") val imageUrl: String,
    @SerialName("status") val status: Challenge.Status
) {
    companion object {
        fun from(summary: SortedChallengeSummary) = with(summary) {
            SortedChallengeSummaryRepresentation(
                id = id,
                startDate = startDate,
                endDate = endDate,
                ownerId = ownerId,
                position = position,
                episodeName = episodeName,
                imageUrl = imageUrl,
                status = status
            )
        }
    }
}
