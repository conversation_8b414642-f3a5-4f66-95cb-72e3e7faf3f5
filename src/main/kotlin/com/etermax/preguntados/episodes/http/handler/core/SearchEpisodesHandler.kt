package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.SearchEpisodes
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodeSummaryRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.FilterEpisodeRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class SearchEpisodesHandler(private val searchEpisodes: SearchEpisodes) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode") {
                route("/search") {
                    post { searchHandler(userId) }
                }
            }

            route("/api/public/episode") {
                route("/search") {
                    post { searchHandler() }
                }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.searchHandler(userId: Long? = null) {
        val actionData = call.receive<FilterEpisodeRepresentation>().toActionData(userId)
        val summaries = searchEpisodes(actionData)
        call.respond(HttpStatusCode.OK, summaries.map { EpisodeSummaryRepresentation.from(it) })
    }
}
