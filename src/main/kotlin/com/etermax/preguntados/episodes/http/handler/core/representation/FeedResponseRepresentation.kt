package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedResponseSummary
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.ChannelSummaryRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FeedResponseRepresentation(
    @SerialName("items") val items: List<FeedItemRepresentation> = emptyList(),
    @SerialName("pagination_token") val paginationToken: String? = null
) {
    companion object {
        fun from(response: FeedResponseSummary) = with(response) {
            FeedResponseRepresentation(
                paginationToken = paginationToken,
                items = response.items.map { item ->
                    when (item) {
                        is EpisodeSummary -> EpisodeFeedItemRepresentation.from(item)
                        is ChannelSummary -> ChannelFeedItemRepresentation.from(item)
                        else -> throw IllegalArgumentException("Unknown item type")
                    }
                }
            )
        }
    }
}

@Serializable
sealed class FeedItemRepresentation

@Serializable
@SerialName("episode")
data class EpisodeFeedItemRepresentation(val data: EpisodeSummaryRepresentation) :
    FeedItemRepresentation() {
    companion object {
        fun from(episode: EpisodeSummary) = EpisodeFeedItemRepresentation(EpisodeSummaryRepresentation.from(episode))
    }
}

@Serializable
@SerialName("channel")
data class ChannelFeedItemRepresentation(val data: ChannelSummaryRepresentation) :
    FeedItemRepresentation() {
    companion object {
        fun from(channel: ChannelSummary) = ChannelFeedItemRepresentation(ChannelSummaryRepresentation.from(channel))
    }
}
