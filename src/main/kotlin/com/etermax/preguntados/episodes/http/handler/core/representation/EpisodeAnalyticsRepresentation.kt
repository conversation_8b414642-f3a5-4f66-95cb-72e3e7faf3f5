package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.analytics.service.EpisodeAnalytics
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.reports.http.representation.EpisodeReportsSummaryRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.time.temporal.ChronoUnit

@Serializable
data class EpisodeAnalyticsRepresentation(
    @SerialName("players_by_day") val playersByDay: List<DayMetricRepresentation>,
    @SerialName("followers_by_day") val followersByDay: List<DayMetricRepresentation>,
    @SerialName("conversion_by_day") val conversionByDay: List<DayRateRepresentation>,
    @SerialName("most_played_times") val mostPlayedTimes: List<HourRateRepresentation>,
    @SerialName("feedbacks") val feedbacks: FeedbacksRepresentation?,
    @SerialName("demography") val demography: DemographyRepresentation?,
    @SerialName("finish_rate") val finishRate: FinishRateRepresentation?,
    @SerialName("reports") val reports: EpisodeReportsSummaryRepresentation?
) {
    companion object {
        fun from(episodeAnalytics: EpisodeAnalytics, clock: Clock): EpisodeAnalyticsRepresentation {
            val now = clock.now().truncatedTo(ChronoUnit.DAYS)

            // Generate last 30 days timestamps
            val dates = (29 downTo 0).map { daysAgo ->
                val date = now.minusDays(daysAgo.toLong())
                date.toInstant().toEpochMilli()
            }

            // Map players by day with timestamps
            val playersByDay = dates.mapIndexed { index, timestamp ->
                DayMetricRepresentation(
                    date = timestamp,
                    count = if (index < episodeAnalytics.playersByDay.size) episodeAnalytics.playersByDay[index] else 0L
                )
            }

            // Map followers by day with timestamps
            val followersByDay = dates.mapIndexed { index, timestamp ->
                DayMetricRepresentation(
                    date = timestamp,
                    count = if (index < episodeAnalytics.followersByDay.size) episodeAnalytics.followersByDay[index] else 0L
                )
            }

            // Map conversion rates by day with timestamps
            val conversionByDay = dates.mapIndexed { index, timestamp ->
                DayRateRepresentation(
                    date = timestamp,
                    rate = if (index < episodeAnalytics.playConversionByDay.size) episodeAnalytics.playConversionByDay[index].toDouble() else 0.0
                )
            }

            val mostPlayedTimes = episodeAnalytics.mostPlayedTimes.mapIndexed { hour, rate ->
                HourRateRepresentation(
                    hour = hour,
                    rate = rate
                )
            }

            val feedbacks = episodeAnalytics.feedbacks?.let {
                FeedbacksRepresentation(
                    likes = it.likes,
                    dislikes = it.dislikes,
                    reports = 0 // Deprecated here. Client should not use this anymore
                )
            }

            val demography = episodeAnalytics.demography?.let {
                val devices = it.devices.map { (device, rate) ->
                    LabelRateRepresentation(device.name.lowercase(), rate)
                }

                val sites = it.countries.map { (country, rate) ->
                    LabelRateRepresentation(country.name, rate)
                }

                val userTypes = UserTypes(it.userTypes.followers, it.userTypes.notFollowers)

                DemographyRepresentation(devices, sites, userTypes)
            }

            val finishRate = episodeAnalytics.finishRate?.let {
                FinishRateRepresentation(
                    thisEpisode = episodeAnalytics.finishRate.thisEpisode,
                    creatorAverage = episodeAnalytics.finishRate.creatorAverage
                )
            }

            val reports = EpisodeReportsSummaryRepresentation.from(episodeAnalytics.reports)

            return EpisodeAnalyticsRepresentation(
                playersByDay = playersByDay,
                followersByDay = followersByDay,
                conversionByDay = conversionByDay,
                mostPlayedTimes = mostPlayedTimes,
                feedbacks = feedbacks,
                demography = demography,
                finishRate = finishRate,
                reports = reports
            )
        }
    }
}

@Serializable
data class DayMetricRepresentation(
    @SerialName("date") val date: Long,
    @SerialName("count") val count: Long
)

@Serializable
data class DayRateRepresentation(
    @SerialName("date") val date: Long,
    @SerialName("rate") val rate: Double
)

@Serializable
data class HourRateRepresentation(
    @SerialName("hour") val hour: Int,
    @SerialName("rate") val rate: Double
)

@Serializable
data class LabelRateRepresentation(
    @SerialName("label") val label: String,
    @SerialName("rate") val rate: Double
)

@Serializable
data class FeedbacksRepresentation(
    @SerialName("likes") val likes: Long,
    @SerialName("dislikes") val dislikes: Long,
    @SerialName("reports") val reports: Long
)

@Serializable
data class DemographyRepresentation(
    @SerialName("devices") val devices: List<LabelRateRepresentation>,
    @SerialName("sites") val sites: List<LabelRateRepresentation>,
    @SerialName("user_types") val userTypes: UserTypes
)

@Serializable
data class UserTypes(
    @SerialName("followers") val followers: Double,
    @SerialName("not_followers") val notFollowers: Double
)

@Serializable
data class FinishRateRepresentation(
    @SerialName("this_episode") val thisEpisode: Double,
    @SerialName("creator_average") val creatorAverage: Double
)
