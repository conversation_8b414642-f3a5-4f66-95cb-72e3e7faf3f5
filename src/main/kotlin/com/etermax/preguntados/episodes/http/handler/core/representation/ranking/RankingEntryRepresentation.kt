package com.etermax.preguntados.episodes.http.handler.core.representation.ranking

import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingEntry
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class RankingEntryRepresentation(
    @SerialName("position") val position: Int?,
    @SerialName("points") val points: Long
) {
    companion object {
        fun from(rankingEntry: RankingEntry) = with(rankingEntry) {
            RankingEntryRepresentation(position, points)
        }
    }
}
