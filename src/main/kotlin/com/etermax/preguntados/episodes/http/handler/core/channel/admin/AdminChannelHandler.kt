package com.etermax.preguntados.episodes.http.handler.core.channel.admin

import com.etermax.preguntados.episodes.core.action.channel.admin.AdminDeleteChannels
import com.etermax.preguntados.episodes.core.action.channel.admin.AdminGetChannel
import com.etermax.preguntados.episodes.http.channelId
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.channel.admin.representation.DeleteChannelsRepresentation
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.ChannelSummaryRepresentation
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*
import org.slf4j.LoggerFactory

class AdminChannelHandler(private val deleteChannels: AdminDeleteChannels, private val getChannel: AdminGetChannel) :
    Handler {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun routing(a: Application) {
        a.routing {
            route("/api/admins/channel-episodes") {
                route("/{channelId}") {
                    get { getHandler() }
                }

                route("/delete") {
                    post { deleteHandler() }
                }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.getHandler() {
        getChannel(AdminGetChannel.ActionData(channelId = channelId))
            ?.let { call.respond(HttpStatusCode.OK, ChannelSummaryRepresentation.from(it)) }
            ?: noContent()
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.deleteHandler() {
        val actionData = call.receive<DeleteChannelsRepresentation>().let {
            AdminDeleteChannels.ActionData(it.channelsIds)
        }
        deleteChannels(actionData)
        call.respond(HttpStatusCode.OK)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.noContent() {
        logger.info("[CH] Get channel id '$channelId not found!!'")
        call.respond(HttpStatusCode.NoContent)
    }
}
