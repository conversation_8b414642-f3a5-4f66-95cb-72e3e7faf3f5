package com.etermax.preguntados.episodes.http.handler.core.representation.ranking

import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRankedPlayer
import com.etermax.preguntados.episodes.http.handler.core.representation.profile.ProfileRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class RankedPlayerRepresentation(
    @SerialName("profile") val profile: ProfileRepresentation,
    @SerialName("entry") val entry: RankingEntryRepresentation
) {
    companion object {
        fun from(rankedPlayer: DeliveryRankedPlayer) = with(rankedPlayer) {
            RankedPlayerRepresentation(
                ProfileRepresentation.from(profile),
                RankingEntryRepresentation.from(rankingEntry)
            )
        }
    }
}
