package com.etermax.preguntados.episodes.http.plugins.eteragent

import com.etermax.apps.error.EterAgentApplicationException
import com.etermax.preguntados.episodes.core.domain.eteragent.EterAgentRepository
import com.etermax.users.eteragent.domain.EterAgent
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.util.AttributeKey
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class EterAgentPluginFactory(private val eterAgentRepository: EterAgentRepository) {

    fun create(): RouteScopedPlugin<Any> {
        return createRouteScopedPlugin("EterAgentPlugin", {}) {
            onCall { call ->
                val eterAgent = try {
                    val eterAgentHeader = call.request.header("eter-agent")
                    EterAgent.parseAgentString(eterAgentHeader)
                } catch (e: EterAgentApplicationException) {
                    null
                }
                if (eterAgent == null) return@onCall

                call.attributes.put(EterAgentAttributeKey, eterAgent)

                val regex = Regex("""^/api/users/(\d+)/.*$""")
                val matchResult = regex.matchEntire(call.request.uri)
                val userId = matchResult?.groups?.get(1)?.value?.toLong()
                saveEterAgent(userId, eterAgent.toStringPipeFormat())
            }
        }
    }

    private fun saveEterAgent(userId: Long?, eterAgent: String?) {
        coroutineScope.launch {
            if (userId != null && eterAgent != null) {
                eterAgentRepository.save(userId, eterAgent)
            }
        }
    }
}

private val coroutineScope = CoroutineScope(
    context = CoroutineName(name = "EterAgentPlugin") + Dispatchers.IO
)

// Clave para el atributo de la aplicación
val EterAgentAttributeKey = AttributeKey<EterAgent>("EterAgent")
