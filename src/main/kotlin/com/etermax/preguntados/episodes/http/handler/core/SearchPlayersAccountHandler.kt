package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.SearchPlayersAccount
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.FilterPlayerAccountRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.SearchPlayersAccountRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class SearchPlayersAccountHandler(
    private val searchPlayersAccount: SearchPlayersAccount
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode/accounts") {
                post { searchAccountsHandler() }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.searchAccountsHandler() {
        val actionData = call.receive<FilterPlayerAccountRepresentation>().to(userId)
        val result = searchPlayersAccount(actionData)
        call.respond(
            status = HttpStatusCode.OK,
            message = SearchPlayersAccountRepresentation.from(playerAccountsToken = result)
        )
    }
}
