package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.RegisterQuality
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.RegisterQualityRepresentation
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class RegisterQualityHandler(
    private val registerQuality: RegisterQuality
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/admins/episode/register/quality") {
                post { registerQualityHandler() }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.registerQualityHandler() {
        val actionData = call.receive<RegisterQualityRepresentation>().to()
        registerQuality(actionData)
        call.respond(HttpStatusCode.OK)
    }
}
