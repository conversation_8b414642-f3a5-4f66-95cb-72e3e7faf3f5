package com.etermax.preguntados.episodes.http.handler.core.representation.profile

import com.etermax.preguntados.episodes.core.domain.profile.Profile
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ProfileRepresentation(
    @SerialName("id") val id: Long,
    @SerialName("name") val name: String,
    @SerialName("photo_url") val photoUrl: String? = null,
    @SerialName("social_data") val socialData: SocialProfileRepresentation? = null,
    @SerialName("restriction") val restriction: RestrictionRepresentation? = null
) {

    companion object {
        fun from(profile: Profile) = with(profile) {
            ProfileRepresentation(
                id = playerId,
                name = name,
                photoUrl = photoUrl?.ifEmpty { null },
                socialData = socialProfile?.let { SocialProfileRepresentation.from(it) },
                restriction = restriction?.let { RestrictionRepresentation(type = it) }
            )
        }
    }
}
