package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.action.GetFeed
import com.etermax.preguntados.episodes.utils.LanguageUtils
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FeedRequestRepresentation(
    @SerialName("language") val language: String?,
    @SerialName("country") val country: String?,
    @SerialName("limit") val limit: Int,
    @SerialName("pagination_token") val paginationToken: String? = null,
    @SerialName("layout") val layout: String? = null,
    @SerialName("debug") val debug: Boolean = false
) {
    fun to(userId: Long?) = GetFeed.ActionData(
        userId = userId,
        language = LanguageUtils.from(language),
        country = country?.let { Country.valueOf(it.uppercase()) },
        limit = limit,
        paginationToken = paginationToken,
        layout = layout,
        debug = debug
    )
}
