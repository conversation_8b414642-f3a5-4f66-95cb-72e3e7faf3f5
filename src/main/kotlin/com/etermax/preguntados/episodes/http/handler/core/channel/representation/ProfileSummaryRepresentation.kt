package com.etermax.preguntados.episodes.http.handler.core.channel.representation

import com.etermax.preguntados.episodes.core.domain.profile.ProfileSummary
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ProfileSummaryRepresentation(
    @SerialName("has_episodes") val hasEpisodes: <PERSON>olean,
    @SerialName("has_channels") val hasChannels: <PERSON>olean
) {
    companion object {
        fun from(domain: ProfileSummary) = with(domain) {
            ProfileSummaryRepresentation(hasEpisodes, hasChannels)
        }
    }
}
