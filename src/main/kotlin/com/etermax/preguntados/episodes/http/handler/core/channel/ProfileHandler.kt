package com.etermax.preguntados.episodes.http.handler.core.channel

import com.etermax.preguntados.episodes.core.action.profile.GetProfileSummary
import com.etermax.preguntados.episodes.http.getFromPath
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.ProfileSummaryRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class ProfileHandler(private val getProfileSummary: GetProfileSummary) : Handler {
    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/channel-episodes/profile") {
                route("/{profileOwnerId}/summary") {
                    get { getSummary() }
                }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.getSummary() {
        val profileOwnerId = getFromPath("profileOwnerId").toLong()
        val actionData = GetProfileSummary.ActionData(userId, profileOwnerId)
        val summary = getProfileSummary(actionData)
        call.respond(HttpStatusCode.OK, ProfileSummaryRepresentation.from(summary))
    }
}
