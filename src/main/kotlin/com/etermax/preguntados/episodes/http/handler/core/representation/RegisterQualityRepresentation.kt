package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.action.RegisterQuality
import com.etermax.preguntados.episodes.core.domain.quality.ChannelQuality
import com.etermax.preguntados.episodes.core.domain.quality.EpisodeQuality
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RegisterQualityRepresentation(
    @SerialName("episodes") val episodes: List<ChannelQualityRepresentation> = emptyList(),
    @SerialName("channels") val channels: List<EpisodeQualityRepresentation> = emptyList()
) {

    fun to() = RegisterQuality.ActionData(
        episodes = episodes.map { it.to() },
        channels = channels.map { it.to() }
    )
}

@Serializable
data class ChannelQualityRepresentation(
    @SerialName("id") val id: String,
    @SerialName("quality") val quality: Int
) {
    fun to() = ChannelQuality(id = id, quality = quality)
}

@Serializable
data class EpisodeQualityRepresentation(
    @SerialName("id") val id: String,
    @SerialName("quality") val quality: Int
) {
    fun to() = EpisodeQuality(id = id, quality = quality)
}
