package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.ChannelStatisticsRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.profile.ProfileRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OverviewChannelRepresentation(
    @SerialName("id") val id: String,
    @SerialName("name") val name: String,
    @SerialName("cover_url") val coverUrl: String,
    @SerialName("owner_id") val ownerId: Long,
    @SerialName("owner") val owner: ProfileRepresentation?,
    @SerialName("description") val description: String?,
    @SerialName("language") val language: String?,
    @SerialName("statistics") val statistics: ChannelStatisticsRepresentation
) {
    companion object {
        fun from(summary: ChannelSummary) = with(summary) {
            OverviewChannelRepresentation(
                id = id,
                name = name,
                coverUrl = coverUrl,
                ownerId = ownerId,
                owner = owner?.let { ProfileRepresentation.from(it) },
                description = description,
                language = language?.name,
                statistics = ChannelStatisticsRepresentation.from(statistics)
            )
        }
    }
}
