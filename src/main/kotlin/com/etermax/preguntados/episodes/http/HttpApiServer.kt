@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "WildcardImport", "MaxLine<PERSON>ength")

package com.etermax.preguntados.episodes.http

import com.etermax.ktor.plugins.metrics.Prometheus
import com.etermax.ktor.plugins.sentry.SentryPlugin
import com.etermax.preguntados.episodes.core.infrastructure.http.error.ErrorResponse
import com.etermax.preguntados.episodes.core.infrastructure.http.error.ExceptionMapper
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.plugins.eteragent.EterAgentPluginFactory
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.engine.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.callid.*
import io.ktor.server.plugins.callloging.*
import io.ktor.server.plugins.compression.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.plugins.cors.*
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.plugins.defaultheaders.*
import io.ktor.server.plugins.doublereceive.*
import io.ktor.server.plugins.forwardedheaders.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.sentry.Sentry
import io.sentry.SpanStatus
import kotlinx.serialization.json.Json
import org.slf4j.LoggerFactory
import org.slf4j.event.Level
import java.time.Duration
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * This class exposed the services provided by this app as HTTP REST requests
 */
class HttpApiServer(
    private val config: AppConfig,
    private val exceptionMapper: ExceptionMapper,
    private val eterAgetPlugin: EterAgentPluginFactory,
    private vararg val handlers: Handler
) {
    data class AppConfig(
        val image: String,
        val deployedAt: String,
        val environment: String,
        val port: Int,
        val shutdown: ShutDownConfig
    ) {
        val name = image.splitToSequence(':').elementAtOrElse(0) { "N/A" }
        val version = image.splitToSequence(':').elementAtOrElse(1) { "N/A" }
    }

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val port = config.port
    private val userIdRegex = """users/([\w-]+)/""".toRegex()
    private lateinit var server: ApplicationEngine

    data class ShutDownConfig(
        val gracePeriod: Long,
        val stopTimeout: Long,
        val timeUnit: TimeUnit
    )

    fun start(wait: Boolean = true) {
        logger.info("Starting ${config.name} in port $port")

        server = embeddedServer(Netty, port = port) {
            main()
        }

        server.start(wait)
    }

    fun stop() {
        with(config.shutdown) {
            logger.info("Stopping application engine using {}", this)
            server.stop(gracePeriod, stopTimeout, timeUnit)
        }
    }

    private fun Application.main() {
        installFeatures()

        routing {
            if (logger.isTraceEnabled) {
                trace { logger.trace(it.buildText()) }
            }
        }

        handlers.forEach { it.routing(this) }
    }

    private val requestTracePlugin = createRouteScopedPlugin("RequestTracePlugin", { }) {
        onCall { call ->
            if (!shouldLogRequest(call.request.path())) {
                return@onCall
            }
            logger.debug("Processing call: ${call.request.uri}")
        }
    }

    private fun shouldLogRequest(path: String) = path != "/live" && path != "/ready" && path != "/metrics"

    private fun CallLoggingConfig.addMdcEntries() {
        callIdMdc("call-id")
        mdc("user-id") {
            retrieveUserIdFromPath(it)
        }
        mdc("eter_agent") {
            it.request.headers["eter-agent"]
        }
        mdc("raw_path") {
            it.request.path()
        }
    }

    private fun Application.installFeatures() {
        install(DefaultHeaders)
        install(Compression)
        installContentNegotiation()
        install(XForwardedHeaders)
        installCORS()
        install(Prometheus)
        install(CallLogging) {
            level = Level.INFO
            filter { ignoreInternalEndpoints(it.request.path()) }
            addMdcEntries()
        }
        install(requestTracePlugin)
        install(CallId) {
            generate { UUID.randomUUID().toString() }
            verify { callId: String ->
                callId.isNotEmpty()
            }
        }
        install(DoubleReceive)
        install(SentryPlugin) {
            sendRequestInformation()
            sendRequestBody()
            sendPerformanceInformation()
        }
        install(StatusPages) {
            addExceptionHandlers()
        }

        install(eterAgetPlugin.create())
    }

    private fun ignoreInternalEndpoints(path: String) = path != "/live" && path != "/ready" && path != "/metrics"

    private fun retrieveUserIdFromPath(it: ApplicationCall) =
        userIdRegex.find(it.request.path())?.groups?.get(1)?.value ?: "n/a"

    private fun Application.installContentNegotiation() {
        install(ContentNegotiation) {
            json(
                Json {
                    ignoreUnknownKeys = true
                    explicitNulls = false
                    encodeDefaults = true
                }
            )
        }
    }

    private fun Application.installCORS() {
        install(CORS) {
            allowedHosts.forEach { allowHost(it, schemes = SCHEMES) }
            allowCredentials = true
            allowNonSimpleContentTypes = true
            maxAgeInSeconds = Duration.ofDays(1).seconds
            allowCORSInModificationMethods()
            allowHeaders { true }
        }
    }

    private fun CORSConfig.allowCORSInModificationMethods() {
        allowMethod(HttpMethod.Patch)
        allowMethod(HttpMethod.Delete)
        allowMethod(HttpMethod.Put)
        allowMethod(HttpMethod.Post)
        allowMethod(HttpMethod.Options)
        allowMethod(HttpMethod.Get)
    }

    private fun StatusPagesConfig.addExceptionHandlers() {
        exception<Throwable> { call, cause ->
            Sentry.getSpan()?.run {
                throwable = cause
                finish(SpanStatus.INTERNAL_ERROR)
            }

            logger.error(cause::class.java.simpleName + ": " + cause.message, cause)
            call.respond(
                exceptionMapper.toHttpCode(cause),
                ErrorResponse(exceptionMapper.toDomainCode(cause), cause.localizedMessage)
            )
        }
    }

    companion object {
        private val allowedHosts = listOf(
            "flutter.dev.tc.etermax.com",
            "flutter.dev.tc.etermax.com:8080",
            "staging.triviacrack.com",
            "triviacrack.com",
            "online.triviacrack.com",
            "staging.preguntados.com",
            "preguntados.com",
            "proxy.etermax.com",
            "backoffice.preguntados.etermax.com",
            "localhost:4200"
        )
        private val SCHEMES = listOf("http", "https")
    }
}
