package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.http.handler.core.representation.profile.ProfileRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class EpisodeSummaryRepresentation(
    @SerialName("id") val id: String,
    @SerialName("name") val name: String,
    @SerialName("language") val language: String,
    @SerialName("country") val country: String,
    @SerialName("type") val type: String,
    @SerialName("start_date") val startDate: Long?,
    @SerialName("cover") val cover: String,
    @SerialName("banner") val banner: String,
    @SerialName("owner_id") val ownerId: Long,
    @SerialName("owner") val owner: ProfileRepresentation?,
    @SerialName("contents") val contents: List<String>,
    @SerialName("views") val views: Long,
    @SerialName("rate") val rate: RateRepresentation,
    @SerialName("status") val status: String,
    @SerialName("channel_id") val channelId: String?
) {

    companion object {
        fun from(episodeSummary: EpisodeSummary) = with(episodeSummary) {
            EpisodeSummaryRepresentation(
                id = id,
                name = name,
                language = language.name,
                country = country.name,
                type = type.name,
                startDate = startDate?.toMillis(),
                cover = cover,
                banner = banner,
                ownerId = ownerId,
                owner = owner?.let { ProfileRepresentation.from(owner) },
                contents = contents,
                views = views,
                rate = RateRepresentation.from(rate),
                status = status.name,
                channelId = channelId
            )
        }
    }
}
