package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.gameplay.*
import com.etermax.preguntados.episodes.http.episodeId
import com.etermax.preguntados.episodes.http.eterAgent
import com.etermax.preguntados.episodes.http.getFromPath
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.*
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class GamePlayHandler(
    private val playEpisode: PlayEpisode,
    private val progressEpisode: RegisterContentProgress,
    private val answerEpisode: AnswerContent,
    private val finishEpisode: FinishEpisode,
    private val findFriendsPlayedEpisode: FindFriendsPlayedEpisode
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode/{episodeId}") {
                route("/play") {
                    post { playHandler() }
                }
                route("/finish") {
                    get { finishHandler() }
                }
                route("/friends") {
                    get { findFriendsPlayedEpisodeHandler() }
                }
                route("/progress/{contentId}") {
                    post { registerProgressHandler() }
                }
                route("/answer/{contentId}") {
                    post { answerHandler() }
                }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.playHandler() {
        val requestBody = getPlayRequestBody()
        val inviterNotificationData = requestBody?.inviterNotificationData?.toInviterNotificationData()
        val agent = eterAgent
        val from = From(agent?.osCountry, agent?.deviceType, agent?.isTablet)
        val remainingContent = playEpisode(PlayEpisode.ActionData(userId, episodeId, from, inviterNotificationData))
        call.respond(HttpStatusCode.OK, RemainingContentRepresentation.from(remainingContent))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.getPlayRequestBody() =
        if ((call.request.contentLength() ?: 0) > 0L) {
            call.receive<PlayEpisodeRequestRepresentation>()
        } else null

    private suspend fun PipelineContext<Unit, ApplicationCall>.finishHandler() {
        val finishEpisodeInfo = finishEpisode(FinishEpisode.ActionData(userId, episodeId))
        call.respond(HttpStatusCode.OK, FinishEpisodeRepresentation.from(finishEpisodeInfo))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.findFriendsPlayedEpisodeHandler() {
        val finishEpisodeInfo = findFriendsPlayedEpisode(FindFriendsPlayedEpisode.ActionData(userId, episodeId))
        call.respond(HttpStatusCode.OK, FriendsRepresentation(finishEpisodeInfo))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.registerProgressHandler() {
        val contentId = getFromPath("contentId")
        val episode = progressEpisode(RegisterContentProgress.ActionData(userId, episodeId, contentId))
        call.respond(HttpStatusCode.OK, ProgressEpisodeRepresentation.from(EpisodeSummaryRepresentation.from(episode)))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.answerHandler() {
        val contentId = getFromPath("contentId")
        val request = call.receive<AnswerRepresentation>()
        val actionData = AnswerContent.ActionData(
            playerId = userId,
            episodeId = episodeId,
            contentId = contentId,
            isCorrect = request.isCorrect,
            elapsedTime = request.elapsedTime,
            totalTime = request.totalTime
        )
        answerEpisode(actionData)
        call.respond(HttpStatusCode.OK)
    }
}
