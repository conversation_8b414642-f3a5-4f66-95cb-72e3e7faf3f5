package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OverviewChannelsRepresentation(
    @SerialName("channels") val channels: List<OverviewChannelRepresentation>
) {

    companion object {
        fun from(channels: List<ChannelSummary>) = with(channels) {
            OverviewChannelsRepresentation(
                channels = channels.map { OverviewChannelRepresentation.from(it) }
            )
        }
    }
}
