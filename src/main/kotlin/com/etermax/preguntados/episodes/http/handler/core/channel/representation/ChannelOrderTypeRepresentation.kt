package com.etermax.preguntados.episodes.http.handler.core.channel.representation

import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType

class ChannelOrderTypeRepresentation {
    companion object {
        fun ChannelOrderType.asString(): String {
            return when (this) {
                ChannelOrderType.DATE_ADDED -> "DATE_ADDED"
                ChannelOrderType.CUSTOM_ORDER -> "CUSTOM_ORDER"
            }
        }
    }
}
