package com.etermax.preguntados.episodes.http.handler.administrative

import com.etermax.preguntados.episodes.http.handler.Handler
import io.ktor.http.ContentType
import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.response.respondText
import io.ktor.server.routing.get
import io.ktor.server.routing.routing

/**
 * Provides endpoints for liveness, readiness, and started probes.
 */
class StatusHandler : Handler {
    override fun routing(a: Application) {
        a.routing {
            get("/started") {
                call.respondText("Ok", ContentType.Text.Plain)
            }

            get("/ready") {
                call.respondText("Ok", ContentType.Text.Plain)
            }

            get("/live") {
                call.respondText("Ok", ContentType.Text.Plain)
            }
        }
    }
}
