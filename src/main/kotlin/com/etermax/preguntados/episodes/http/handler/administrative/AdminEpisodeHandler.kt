package com.etermax.preguntados.episodes.http.handler.administrative

import com.etermax.preguntados.episodes.core.action.episode.FindAllEpisodes
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodeSummaryRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.FilterAllEpisodeRepresentation
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class AdminEpisodeHandler(
    private val findAllEpisodes: FindAllEpisodes
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/admins/episode/all") {
                post { findAllHandler() }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.findAllHandler() {
        val actionData = call.receive<FilterAllEpisodeRepresentation>().let {
            FindAllEpisodes.ActionData(it.ownerId, it.toLanguage(), it.name, it.toCountry())
        }
        val summaries = findAllEpisodes(actionData)
        call.respond(HttpStatusCode.OK, summaries.map { EpisodeSummaryRepresentation.from(it) })
    }
}
