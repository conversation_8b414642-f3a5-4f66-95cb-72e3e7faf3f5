package com.etermax.preguntados.episodes.http.handler.core.channel.representation

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CreateChannelRepresentation(
    @SerialName("name") val name: String,
    @SerialName("description") val description: String?,
    @SerialName("website") val website: String?,
    @SerialName("type") val type: String?,
    @SerialName("cover_url") val coverUrl: String,
    @SerialName("language") val moderationLanguage: String
)
