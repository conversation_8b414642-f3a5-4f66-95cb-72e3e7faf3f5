package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.gameplay.RecommendEpisodes
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodeRecommendationRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class EpisodeRecommendationHandler(
    private val recommendEpisodes: RecommendEpisodes
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode/recommendation") {
                get { recommendationHandler() }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.recommendationHandler() {
        val recommendations = recommendEpisodes(RecommendEpisodes.ActionData(userId))
        call.respond(
            status = HttpStatusCode.OK,
            message = recommendations.map { EpisodeRecommendationRepresentation.from(it) }
        )
    }
}
