package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountsToken
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SearchPlayersAccountRepresentation(
    @SerialName("accounts") val accounts: List<PlayerAccountRepresentation>,
    @SerialName("token") val token: String
) {

    companion object {
        fun from(playerAccountsToken: PlayerAccountsToken) = with(playerAccountsToken) {
            SearchPlayersAccountRepresentation(
                accounts = playerAccounts.map { PlayerAccountRepresentation.from(it) },
                token = token
            )
        }
    }
}
