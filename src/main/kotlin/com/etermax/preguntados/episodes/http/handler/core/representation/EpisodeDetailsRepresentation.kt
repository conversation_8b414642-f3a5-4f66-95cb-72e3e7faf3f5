package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeDetails
import com.etermax.preguntados.episodes.http.handler.core.representation.ranking.RankingRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class EpisodeDetailsRepresentation(
    @SerialName("ranking") val ranking: RankingRepresentation,
    @SerialName("ranking_with_friends") val rankingWithFriends: RankingRepresentation,
    @SerialName("rate") val rate: String?,
    @SerialName("has_played") val hasPlayed: <PERSON><PERSON><PERSON>,
    @SerialName("progress") val progress: ProgressRepresentation
) {

    companion object {
        fun from(episodeDetails: EpisodeDetails) = with(episodeDetails) {
            EpisodeDetailsRepresentation(
                ranking = RankingRepresentation.from(ranking),
                rankingWithFriends = RankingRepresentation.from(rankingWithFriends),
                rate = rate?.name,
                hasPlayed = hasPlayed,
                progress = ProgressRepresentation.from(deliveryProgress)
            )
        }
    }
}
