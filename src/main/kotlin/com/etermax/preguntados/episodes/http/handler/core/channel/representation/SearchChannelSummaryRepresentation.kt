package com.etermax.preguntados.episodes.http.handler.core.channel.representation

import com.etermax.preguntados.episodes.core.domain.channel.SearchChannelSummary
import com.etermax.preguntados.episodes.http.handler.core.pagination.PaginatedItemsRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SearchChannelSummariesRepresentation(
    @SerialName("channels") val id: PaginatedItemsRepresentation<SearchChannelSummaryRepresentation>
)

@Serializable
data class SearchChannelSummaryRepresentation(
    @SerialName("channel") val channel: ChannelSummaryRepresentation,
    @SerialName("episodes_covers_url") val episodesCoversUrl: List<String>
) {
    companion object {
        fun from(summary: SearchChannelSummary) = with(summary) {
            SearchChannelSummaryRepresentation(
                channel = ChannelSummaryRepresentation.from(summary.channelSummary),
                episodesCoversUrl = episodeCovers
            )
        }
    }
}
