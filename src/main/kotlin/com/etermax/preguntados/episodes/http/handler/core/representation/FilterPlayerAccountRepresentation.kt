package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.action.SearchPlayersAccount
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FilterPlayerAccountRepresentation(
    @SerialName("query") val query: String,
    @SerialName("amount") val amount: Int,
    @SerialName("skip") val skip: Int,
    @SerialName("skip_restricted") val skipRestricted: Boolean? = null,
    @SerialName("fields") val fields: String? = null,
    @SerialName("token") val token: String? = null
) {

    fun to(playerId: Long) = SearchPlayersAccount.ActionData(
        playerId = playerId,
        query = query,
        amount = amount,
        skip = skip,
        skipRestricted = skipRestricted,
        fields = fields,
        token = token
    )
}
