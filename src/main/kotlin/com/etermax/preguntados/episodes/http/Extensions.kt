package com.etermax.preguntados.episodes.http

import com.etermax.preguntados.episodes.http.plugins.eteragent.EterAgentAttributeKey
import com.etermax.users.eteragent.domain.EterAgent
import io.ktor.server.application.*
import io.ktor.util.pipeline.*

val PipelineContext<Unit, ApplicationCall>.userId: Long
    get() = getFromPath("userId").toLong()

val PipelineContext<Unit, ApplicationCall>.adminId: Long
    get() = getFromPath("adminId").toLong()

val PipelineContext<Unit, ApplicationCall>.episodeId: String
    get() = getFromPath("episodeId")

val PipelineContext<Unit, ApplicationCall>.channelId: String
    get() = getFromPath("channelId")

val PipelineContext<Unit, ApplicationCall>.challengeId: String
    get() = getFromPath("challengeId")

val PipelineContext<Unit, ApplicationCall>.lastEvaluatedKey: String?
    get() = this.queryParameter("lastEvaluatedKey")

val PipelineContext<Unit, ApplicationCall>.profileOwnerIdQueryParam: Long?
    get() = this.queryParameter("profileOwnerId")?.toLong()

val PipelineContext<Unit, ApplicationCall>.section: String?
    get() = this.queryParameter("section")

val PipelineContext<Unit, ApplicationCall>.matchesAmount: Int?
    get() = this.queryParameter("matches.amount")?.toInt()

val PipelineContext<Unit, ApplicationCall>.matchesSkip: Int?
    get() = this.queryParameter("matches.skip")?.toInt()

val PipelineContext<Unit, ApplicationCall>.pendingMatchesAmount: Int?
    get() = this.queryParameter("pending_matches.amount")?.toInt()

val PipelineContext<Unit, ApplicationCall>.pendingMatchesSkip: Int?
    get() = this.queryParameter("pending_matches.skip")?.toInt()

// Extensión para acceder al EterAgent desde los atributos de la aplicación
val PipelineContext<Unit, ApplicationCall>.eterAgent: EterAgent?
    get() = call.attributes.getOrNull(EterAgentAttributeKey)

fun PipelineContext<Unit, ApplicationCall>.getFromPath(key: String): String {
    return context.parameters[key]!!
}

fun PipelineContext<Unit, ApplicationCall>.queryParameter(key: String): String? =
    call.request.queryParameters[key]
