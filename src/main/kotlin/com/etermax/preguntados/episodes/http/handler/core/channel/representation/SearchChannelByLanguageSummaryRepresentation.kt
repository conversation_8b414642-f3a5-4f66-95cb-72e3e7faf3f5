package com.etermax.preguntados.episodes.http.handler.core.channel.representation

import com.etermax.preguntados.episodes.http.handler.core.pagination.PaginatedItemsRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SearchChannelByLanguageSummaryRepresentation(
    @SerialName("channels") val id: PaginatedItemsRepresentation<ChannelSummaryReducedRepresentation>
)
