package com.etermax.preguntados.episodes.http.handler.core.representation.ranking

import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class RankingRepresentation(
    @SerialName("players") val players: List<RankedPlayerRepresentation>,
    @SerialName("my_entry") val myEntry: RankingEntryRepresentation?
) {

    companion object {
        private val EMPTY_RANKING = RankingRepresentation(emptyList(), null)

        fun from(ranking: DeliveryRanking?): RankingRepresentation {
            return ranking?.let {
                RankingRepresentation(
                    players = it.players.map { player -> RankedPlayerRepresentation.from(player) },
                    myEntry = it.myRankingEntry?.let { rankingEntry -> RankingEntryRepresentation.from(rankingEntry) }
                )
            } ?: EMPTY_RANKING
        }
    }
}
