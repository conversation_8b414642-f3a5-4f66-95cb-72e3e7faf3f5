package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.episode.progress.DeliveryProgress
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ProgressRepresentation(
    @SerialName("last_content_id") val lastContentId: String? = null,
    @SerialName("has_finished_episode") val hasFinishedEpisode: Boolean
) {

    companion object {
        fun from(progress: DeliveryProgress) = with(progress) {
            ProgressRepresentation(
                lastContentId = progress.lastContentId,
                hasFinishedEpisode = progress.hasFinishedEpisode
            )
        }
    }
}
