package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.episode.finish.FinishEpisodeDetails
import com.etermax.preguntados.episodes.http.handler.core.representation.ranking.RankingRepresentation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FinishEpisodeRepresentation(
    @SerialName("episode") val episode: EpisodeSummaryRepresentation,
    @SerialName("ranking") val ranking: RankingRepresentation,
    @SerialName("ranking_with_friends") val rankingWithFriends: RankingRepresentation,
    @SerialName("rate") val rate: String?
) {

    companion object {
        fun from(finishEpisodeDetails: FinishEpisodeDetails) = with(finishEpisodeDetails) {
            FinishEpisodeRepresentation(
                episode = EpisodeSummaryRepresentation.from(episodeSummary),
                ranking = RankingRepresentation.from(ranking),
                rankingWithFriends = RankingRepresentation.from(rankingWithFriends),
                rate = rate?.name
            )
        }
    }
}
