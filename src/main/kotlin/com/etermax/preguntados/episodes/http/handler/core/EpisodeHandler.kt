package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.episode.*
import com.etermax.preguntados.episodes.http.episodeId
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.CreateEpisodeRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodeSummaryRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodesIdRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.UpdateEpisodeRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class EpisodeHandler(
    private val createEpisode: CreateEpisode,
    private val deleteEpisode: DeleteEpisode,
    private val findEpisode: FindEpisode,
    private val findEpisodesByIds: FindEpisodesByIds,
    private val copyEpisode: CopyEpisode,
    private val updateEpisode: UpdateEpisode
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode") {
                route("/create") {
                    post { createHandler() }
                }
                route("/update") {
                    post { updateHandler() }
                }
                route("/{episodeId}") {
                    get { findHandler() }
                    delete { deleteHandler() }
                }
                route("/ids") {
                    post { findByIdsHandler() }
                }
                route("/copy/{episodeId}") {
                    post { copyHandler() }
                }
            }

            route("/api/admins/episode") {
                route("/{episodeId}") {
                    get { findHandler() }
                }
            }

            route("/api/public/episode") {
                route("/{episodeId}") {
                    get { findHandler() }
                }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.createHandler() {
        val actionData = call.receive<CreateEpisodeRepresentation>().to(userId)
        val summary = createEpisode(actionData)
        call.respond(HttpStatusCode.Created, EpisodeSummaryRepresentation.from(summary))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.updateHandler() {
        val actionData = call.receive<UpdateEpisodeRepresentation>().to(userId)
        val summary = updateEpisode(actionData)
        call.respond(HttpStatusCode.Created, EpisodeSummaryRepresentation.from(summary))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.findHandler() {
        findEpisode(FindEpisode.ActionData(episodeId))?.let {
            call.respond(HttpStatusCode.OK, EpisodeSummaryRepresentation.from(it))
        } ?: call.respond(HttpStatusCode.NoContent)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.findByIdsHandler() {
        val actionData = call.receive<EpisodesIdRepresentation>().to()
        val episodes = findEpisodesByIds(actionData)
        call.respond(
            status = HttpStatusCode.OK,
            message = episodes.map { EpisodeSummaryRepresentation.from(it) }
        )
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.deleteHandler() {
        deleteEpisode(DeleteEpisode.ActionData(userId, episodeId))
        call.respond(HttpStatusCode.NoContent)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.copyHandler() {
        val actionData = CopyEpisode.ActionData(userId, episodeId)
        val summary = copyEpisode(actionData)
        call.respond(HttpStatusCode.OK, EpisodeSummaryRepresentation.from(summary))
    }
}
