package com.etermax.preguntados.episodes.http.handler.core.channel.representation

import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummaryStatistics
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChannelStatisticsRepresentation(
    @SerialName("subscribers") val subscribers: Int,
    @SerialName("episodes") val episodes: Int,
    @SerialName("unpublished_episodes") val unpublishedEpisodes: Int
) {
    companion object {
        fun from(domain: ChannelSummaryStatistics): ChannelStatisticsRepresentation = with(domain) {
            return ChannelStatisticsRepresentation(subscribers, episodes, unpublishedEpisodes)
        }
    }
}
