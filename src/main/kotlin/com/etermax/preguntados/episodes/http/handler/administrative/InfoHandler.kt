package com.etermax.preguntados.episodes.http.handler.administrative

import com.etermax.preguntados.episodes.http.HttpApiServer
import com.etermax.preguntados.episodes.http.handler.Handler
import io.ktor.server.application.*
import io.ktor.server.response.respond
import io.ktor.server.routing.*
import io.ktor.util.pipeline.PipelineContext
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

class InfoHandler(private val config: HttpApiServer.AppConfig) : Handler {
    lateinit var application: Application

    override fun routing(a: Application) {
        application = a

        a.routing {
            get("/episode/info") { handleInfo() }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.handleInfo() {
        val root = application.plugin(Routing)

        val allRoutes = allRoutes(root)

        val allRoutesWithMethod = allRoutes.filter { it.selector is HttpMethodRouteSelector }

        val info = JsonArray(
            listOf(
                JsonObject(
                    mapOf(
                        "name" to config.name.toJson(),
                        "version" to config.version.toJson(),
                        "image" to config.image.toJson(),
                        "deployedAt" to config.deployedAt.toJson()
                    )
                ),
                JsonObject(
                    mapOf(
                        "routes" to JsonArray(allRoutesWithMethod.map { it.toString().toJson() })
                    )
                )
            )
        )

        arrayOf(
            mapOf(
                "name" to config.name,
                "version" to config.version,
                "image" to config.image,
                "deployedAt" to config.deployedAt
            ),
            mapOf(
                "routes" to allRoutesWithMethod.map { it.toString() }
            )
        )

        call.respond(info)
    }
    private fun allRoutes(root: Route): List<Route> {
        return listOf(root) + root.children.flatMap { allRoutes(it) }
    }

    private fun String.toJson(): JsonPrimitive = JsonPrimitive(this)
}
