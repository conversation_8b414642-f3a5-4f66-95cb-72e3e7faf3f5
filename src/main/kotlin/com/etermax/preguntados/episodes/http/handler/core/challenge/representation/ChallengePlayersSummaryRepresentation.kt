package com.etermax.preguntados.episodes.http.handler.core.challenge.representation

import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayerSummary
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayersSummary
import com.etermax.preguntados.episodes.http.handler.core.representation.profile.ProfileRepresentation
import kotlinx.serialization.Serializable

@Serializable
data class ChallengePlayersSummaryRepresentation(
    val players: List<ChallengePlayerSummaryRepresentation>
) {

    companion object {
        fun from(challengePlayersSummary: ChallengePlayersSummary) = with(challengePlayersSummary) {
            ChallengePlayersSummaryRepresentation(
                players.map { ChallengePlayerSummaryRepresentation.from(it) }
            )
        }
    }
}

@Serializable
data class ChallengePlayerSummaryRepresentation(val profile: ProfileRepresentation, val status: String) {
    companion object {
        fun from(challengePlayerSummary: ChallengePlayerSummary) = with(challengePlayerSummary) {
            ChallengePlayerSummaryRepresentation(ProfileRepresentation.from(profile), status.name)
        }
    }
}
