package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.search.OverviewInfo
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OverviewInfoRepresentation(
    @SerialName("accounts") val accounts: List<PlayerAccountRepresentation>,
    @SerialName("episodes") val episodes: List<EpisodeSummaryRepresentation>,
    @SerialName("channels") val channels: List<OverviewChannelRepresentation>
) {

    companion object {
        fun from(overviewInfo: OverviewInfo) = with(overviewInfo) {
            OverviewInfoRepresentation(
                accounts = playerAccounts.map { PlayerAccountRepresentation.from(it) },
                episodes = episodes.map { EpisodeSummaryRepresentation.from(it) },
                channels = channels.map { OverviewChannelRepresentation.from(it) }
            )
        }
    }
}
