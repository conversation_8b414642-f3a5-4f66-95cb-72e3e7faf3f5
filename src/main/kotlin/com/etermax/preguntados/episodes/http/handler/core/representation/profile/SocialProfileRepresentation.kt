package com.etermax.preguntados.episodes.http.handler.core.representation.profile

import com.etermax.preguntados.episodes.core.domain.profile.social.SocialProfile
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SocialProfileRepresentation(
    @SerialName("network") val network: String,
    @SerialName("id") val id: String? = null,
    @SerialName("name") val name: String? = null
) {

    companion object {
        fun from(socialProfile: SocialProfile) = with(socialProfile) {
            SocialProfileRepresentation(
                network = network.name,
                id = id,
                name = name
            )
        }
    }
}
