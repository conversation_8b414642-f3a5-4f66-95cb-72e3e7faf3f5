package com.etermax.preguntados.episodes.http.handler.core.representation

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class ProgressEpisodeRepresentation(@SerialName("episode") val episode: EpisodeSummaryRepresentation) {
    companion object {
        fun from(episode: EpisodeSummaryRepresentation) = ProgressEpisodeRepresentation(episode)
    }
}
