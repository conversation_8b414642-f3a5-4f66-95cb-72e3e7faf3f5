package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.action.OverviewChannelsSearch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FilterOverviewChannelsRepresentation(
    @SerialName("name") val name: String,
    @SerialName("language") val language: String? = null,
    @SerialName("offset") val offset: Int,
    @SerialName("limit") val limit: Int
) {

    fun to(playerId: Long) = OverviewChannelsSearch.ActionData(
        playerId = playerId,
        name = name,
        language = language,
        offset = offset,
        limit = limit
    )
}
