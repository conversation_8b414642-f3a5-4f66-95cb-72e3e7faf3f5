package com.etermax.preguntados.episodes.http.handler.core.channel.representation

import com.etermax.preguntados.episodes.core.domain.channel.ChannelType

class ChannelTypeRepresentation {
    companion object {
        fun ChannelType.asString(): String {
            return when (this) {
                ChannelType.PUBLIC -> "PUBLIC"
                ChannelType.PRIVATE -> "PRIVATE"
            }
        }
    }
}
