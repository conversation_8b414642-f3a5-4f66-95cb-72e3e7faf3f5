package com.etermax.preguntados.episodes.http.handler.core.channel

import com.etermax.preguntados.episodes.core.action.channel.*
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.episodes.http.*
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.*
import com.etermax.preguntados.episodes.http.handler.core.pagination.PaginatedItemsRepresentation
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class ChannelHandler(
    private val createChannel: CreateChannel,
    private val searchChannels: SearchChannels,
    private val searchChannelsByLanguage: SearchChannelsByLanguage,
    private val findChannelById: FindChannelById,
    private val findReducedChannelById: FindReducedChannelById,
    private val updateChannel: UpdateChannel,
    private val deleteChannel: DeleteChannel,
    private val updateChannelOrderType: UpdateChannelOrderType
) : Handler {
    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/channel-episodes") {
                route("/create") {
                    post { createHandler() }
                }
                route("/search") {
                    get { searchHandler() }
                    route("/by-language/{language}") {
                        get { searchByLanguageHandler() }
                    }
                }
                route("/{channelId}") {
                    get { findByIdHandler() }
                    put { updateChannel() }
                    delete { deleteChannel() }
                    route("/reduced") {
                        get { findReducedByIdHandler() }
                    }
                    route("/order-type") {
                        put { updateChannelOrderType() }
                    }
                }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.createHandler() {
        val actionData = call.receive<CreateChannelRepresentation>().toActionData(userId)
        val summary = createChannel(actionData)
        val response = ChannelSummaryRepresentation.from(summary)
        call.respond(HttpStatusCode.Created, response)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.searchHandler() {
        val channels = searchChannels(SearchChannels.ActionData(userId, profileOwnerIdQueryParam, lastEvaluatedKey))

        val items = channels.items.map { SearchChannelSummaryRepresentation.from(it) }
        val paginatedItems = PaginatedItemsRepresentation(channels.lastEvaluatedKey, items)
        val response = SearchChannelSummariesRepresentation(paginatedItems)

        call.respond(HttpStatusCode.OK, response)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.searchByLanguageHandler() {
        val language = Language.valueOf(getFromPath("language").uppercase())
        val includeWithNoLanguage = queryParameter("includeWithNoLanguage")?.toBoolean() ?: true
        val channels = searchChannelsByLanguage(
            SearchChannelsByLanguage.ActionData(
                userId,
                language,
                lastEvaluatedKey,
                includeWithNoLanguage
            )
        )

        val items = channels.items.map { ChannelSummaryReducedRepresentation.from(it) }
        val response = PaginatedItemsRepresentation(channels.lastEvaluatedKey, items)
        call.respond(HttpStatusCode.OK, SearchChannelByLanguageSummaryRepresentation(response))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.findByIdHandler() {
        val channel = findChannelById(channelId)

        if (channel == null) {
            call.respond(HttpStatusCode.NoContent)
        } else {
            call.respond(HttpStatusCode.OK, ChannelSummaryRepresentation.from(channel))
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.updateChannel() {
        val data = call.receive<UpdateChannelRepresentation>().toActionData(userId, channelId)
        val channel = updateChannel(data)

        call.respond(HttpStatusCode.OK, ChannelSummaryRepresentation.from(channel))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.deleteChannel() {
        deleteChannel(userId, channelId)
        call.respond(HttpStatusCode.OK)
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.findReducedByIdHandler() {
        val channel = findReducedChannelById(channelId)

        if (channel == null) {
            call.respond(HttpStatusCode.NoContent)
        } else {
            call.respond(HttpStatusCode.OK, ChannelSummaryReducedRepresentation.from(channel))
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.updateChannelOrderType() {
        val request = call.receive<UpdateChannelOrderTypeRepresentation>()
        val orderType = ChannelOrderType.valueOf(request.orderType.uppercase())
        val data = UpdateChannelOrderType.ActionData(userId, channelId, orderType)
        updateChannelOrderType(data)
        call.respond(HttpStatusCode.OK)
    }

    private fun CreateChannelRepresentation.toActionData(playerId: Long) = ActionData(
        playerId = playerId,
        _name = name,
        _description = description,
        _website = website,
        type = type?.let { ChannelType.valueOf(type) },
        coverUrl = coverUrl,
        moderationLanguage = Language.valueOf(moderationLanguage.uppercase())
    )

    private fun UpdateChannelRepresentation.toActionData(playerId: Long, channelId: String) = ActionData(
        playerId = playerId,
        channelId = channelId,
        _name = name,
        _description = description,
        coverUrl = coverUrl,
        _website = website,
        type = type?.let { ChannelType.valueOf(type) },
        moderationLanguage = Language.valueOf(moderationLanguage.uppercase())
    )
}
