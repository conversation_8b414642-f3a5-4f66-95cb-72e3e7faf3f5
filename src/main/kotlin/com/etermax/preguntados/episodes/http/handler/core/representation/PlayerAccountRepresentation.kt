package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.account.PlayerAccount
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PlayerAccountRepresentation(
    @SerialName("id") val id: Long,
    @SerialName("username") val username: String,
    @SerialName("name") val name: String?,
    @SerialName("facebook_id") val facebookId: String?,
    @SerialName("photo_url") val photoUrl: String?,
    @SerialName("is_followed") val isFollowed: Boolean?
) {

    companion object {
        fun from(account: PlayerAccount) = with(account) {
            PlayerAccountRepresentation(
                id = id,
                username = username,
                name = name,
                facebookId = facebookId,
                photoUrl = photoUrl,
                isFollowed = isFollowed
            )
        }
    }
}
