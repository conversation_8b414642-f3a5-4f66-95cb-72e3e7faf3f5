package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.gameplay.FindEpisodeDetails
import com.etermax.preguntados.episodes.http.episodeId
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodeDetailsRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class EpisodeDetailsHandler(
    private val findEpisodeDetails: FindEpisodeDetails
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode/{episodeId}/details") {
                get { detailHandler() }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.detailHandler() {
        val details = findEpisodeDetails(FindEpisodeDetails.ActionData(userId, episodeId))
        call.respond(HttpStatusCode.OK, EpisodeDetailsRepresentation.from(details))
    }
}
