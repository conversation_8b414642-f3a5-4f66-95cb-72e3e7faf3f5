package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.episode.Rate
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RateRepresentation(
    @SerialName("likes") val likes: Long,
    @SerialName("dislikes") val dislikes: Long
) {
    companion object {
        fun from(rate: Rate) = with(rate) {
            RateRepresentation(likes, dislikes)
        }
    }
}
