package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.action.SearchEpisodes
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.utils.LanguageUtils
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FilterEpisodeRepresentation(
    @SerialName("player_id") val playerId: Long? = null,
    @SerialName("language") val language: String? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("episode_id") val episodeId: String? = null,
    @SerialName("channel_id") val channelId: String? = null,
    @SerialName("sort") val sort: String? = null,
    @SerialName("country") val country: String? = null,
    @SerialName("offset") val offset: Int? = null,
    @SerialName("limit") val limit: Int? = null
) {

    fun toActionData(userId: Long? = null) = SearchEpisodes.ActionData(
        playerId = playerId ?: userId,
        language = LanguageUtils.from(language),
        name = name,
        episodeId = episodeId,
        channelId = channelId,
        sort = mapSorting(userId),
        country = country?.let { Country.valueOf(it.uppercase()) },
        isRestricted = playerId != null,
        offset = offset ?: 0,
        limit = limit ?: 12
    )

    private fun mapSorting(userId: Long?): SortEpisode? {
        return when {
            isAnonymousFeedRequest(userId) -> {
                if (sort == SortEpisode.EPISODES_FOR_CHANNEL.toString()) {
                    SortEpisode.EPISODES_FOR_CHANNEL
                } else {
                    SortEpisode.TRENDING
                }
            }
            else -> sort?.uppercase()?.let { SortEpisode.valueOf(it) }
        }
    }

    private fun isAnonymousFeedRequest(userId: Long?) = sort == null && userId == null && name.isNullOrBlank()
}
