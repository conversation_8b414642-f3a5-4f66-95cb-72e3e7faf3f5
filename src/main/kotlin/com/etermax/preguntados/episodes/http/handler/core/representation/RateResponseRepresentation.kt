package com.etermax.preguntados.episodes.http.handler.core.representation

import kotlinx.serialization.Serializable

@Serializable
data class RateResponseRepresentation(val episode: EpisodeSummaryRepresentation) {

    companion object {
        fun from(episodeSummary: EpisodeSummaryRepresentation): RateResponseRepresentation {
            return RateResponseRepresentation(episodeSummary)
        }
    }
}
