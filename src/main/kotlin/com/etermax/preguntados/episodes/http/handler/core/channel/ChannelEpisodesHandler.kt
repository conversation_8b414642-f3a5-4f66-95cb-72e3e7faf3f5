package com.etermax.preguntados.episodes.http.handler.core.channel

import com.etermax.preguntados.episodes.core.action.channel.episodes.AddEpisodesToChannel
import com.etermax.preguntados.episodes.core.action.channel.episodes.RemoveEpisodesFromChannel
import com.etermax.preguntados.episodes.core.action.channel.episodes.UpdateEpisodesOrderInChannel
import com.etermax.preguntados.episodes.http.channelId
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.AddEpisodesToChannelRepresentation
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.ChannelSummaryRepresentation
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.RemoveEpisodesChannelRepresentation
import com.etermax.preguntados.episodes.http.handler.core.channel.representation.UpdateChannelEpisodesOrderRepresentation
import com.etermax.preguntados.episodes.http.userId
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class ChannelEpisodesHandler(
    private val addEpisodesToChannel: AddEpisodesToChannel,
    private val removeEpisodesFromChannel: RemoveEpisodesFromChannel,
    private val updateEpisodesOrderInChannel: UpdateEpisodesOrderInChannel
) : Handler {
    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/channel-episodes/{channelId}/episodes") {
                route("/add") {
                    post { addEpisodesHandler() }
                }
                route("/remove") {
                    post { removeEpisodesHandler() }
                }
                route("/order") {
                    put { updateOrder() }
                }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.addEpisodesHandler() {
        val requestBody = call.receive<AddEpisodesToChannelRepresentation>()
        val actionData = requestBody.toActionData(userId, channelId)
        val summary = addEpisodesToChannel(actionData)
        call.respond(HttpStatusCode.OK, ChannelSummaryRepresentation.from(summary))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.removeEpisodesHandler() {
        val requestBody = call.receive<RemoveEpisodesChannelRepresentation>()
        val actionData = requestBody.toActionData(userId, channelId)
        val summary = removeEpisodesFromChannel(actionData)
        call.respond(HttpStatusCode.OK, ChannelSummaryRepresentation.from(summary))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.updateOrder() {
        val requestBody = call.receive<UpdateChannelEpisodesOrderRepresentation>()
        val actionData = requestBody.toActionData(userId, channelId)
        updateEpisodesOrderInChannel(actionData)
        call.respond(HttpStatusCode.OK)
    }

    private fun AddEpisodesToChannelRepresentation.toActionData(
        playerId: Long,
        channelId: String
    ): AddEpisodesToChannel.ActionData {
        return AddEpisodesToChannel.ActionData(
            playerId,
            channelId,
            Language.valueOf(language.uppercase()),
            episodesIds.toSet()
        )
    }

    private fun RemoveEpisodesChannelRepresentation.toActionData(
        playerId: Long,
        channelId: String
    ): RemoveEpisodesFromChannel.ActionData {
        return RemoveEpisodesFromChannel.ActionData(
            playerId,
            channelId,
            episodesIds.toSet()
        )
    }

    private fun UpdateChannelEpisodesOrderRepresentation.toActionData(
        playerId: Long,
        channelId: String
    ): UpdateEpisodesOrderInChannel.ActionData {
        val map = this.newOrder.mapKeys { (key, _) -> key.toInt() }
        return UpdateEpisodesOrderInChannel.ActionData(playerId, channelId, map)
    }
}
