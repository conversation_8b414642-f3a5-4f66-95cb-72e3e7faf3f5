package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.gameplay.RateEpisode
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.http.episodeId
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.EpisodeSummaryRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.RateResponseRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class RateEpisodeHandler(
    private val rateEpisode: RateEpisode
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode/{episodeId}") {
                route("/like") {
                    post { rateHandler(Rate.Type.LIKE) }
                }
                route("/dislike") {
                    post { rateHandler(Rate.Type.DISLIKE) }
                }
                route("/unrate") {
                    post { rateHandler() }
                }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.rateHandler(rateType: Rate.Type? = null) {
        val episodeSummary = rateEpisode(RateEpisode.ActionData(userId, episodeId, rateType))
        call.respond(HttpStatusCode.OK, RateResponseRepresentation(EpisodeSummaryRepresentation.from(episodeSummary)))
    }
}
