package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.action.OverviewInfoSearch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FilterOverviewInfoRepresentation(
    @SerialName("name") val name: String,
    @SerialName("language") val language: String? = null
) {

    fun to(playerId: Long) = OverviewInfoSearch.ActionData(
        playerId = playerId,
        name = name.trim(),
        language = language
    )
}
