package com.etermax.preguntados.episodes.http.handler.core.challenge.representation

import com.etermax.preguntados.episodes.core.action.challenge.CreateChallenge
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CreateChallengeRepresentation(
    @SerialName("episode_id") val episodeId: String,
    @SerialName("duration") val durationInSeconds: Long
) {
    fun to(playerId: Long) = CreateChallenge.ActionData(playerId, episodeId, durationInSeconds)
}
