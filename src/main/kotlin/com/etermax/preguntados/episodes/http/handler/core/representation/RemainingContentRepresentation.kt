package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.core.domain.episode.RemainingContent
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RemainingContentRepresentation(
    @SerialName("contents") val contents: List<String>,
    @SerialName("episode") val episode: EpisodeSummaryRepresentation
) {

    companion object {
        fun from(episodeContent: RemainingContent) = RemainingContentRepresentation(
            contents = episodeContent.contents,
            episode = EpisodeSummaryRepresentation.from(episodeContent.episode)
        )
    }
}
