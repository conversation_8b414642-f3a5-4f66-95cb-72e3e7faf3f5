package com.etermax.preguntados.episodes.http.handler.core

import com.etermax.preguntados.episodes.core.action.OverviewChannelsSearch
import com.etermax.preguntados.episodes.core.action.OverviewInfoSearch
import com.etermax.preguntados.episodes.http.handler.Handler
import com.etermax.preguntados.episodes.http.handler.core.representation.FilterOverviewChannelsRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.FilterOverviewInfoRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.OverviewChannelsRepresentation
import com.etermax.preguntados.episodes.http.handler.core.representation.OverviewInfoRepresentation
import com.etermax.preguntados.episodes.http.userId
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*

class OverviewHandler(
    private val overviewInfoSearch: OverviewInfoSearch,
    private val overviewChannelsSearch: OverviewChannelsSearch
) : Handler {

    override fun routing(a: Application) {
        a.routing {
            route("/api/users/{userId}/episode/overview") {
                post { searchOverviewHandler() }
            }
            route("/api/users/{userId}/episode/overview/channels") {
                post { searchOverviewChannelsHandler() }
            }
        }
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.searchOverviewHandler() {
        val actionData = call.receive<FilterOverviewInfoRepresentation>().to(userId)
        val overviewInfo = overviewInfoSearch(actionData)
        call.respond(HttpStatusCode.OK, OverviewInfoRepresentation.from(overviewInfo))
    }

    private suspend fun PipelineContext<Unit, ApplicationCall>.searchOverviewChannelsHandler() {
        val actionData = call.receive<FilterOverviewChannelsRepresentation>().to(userId)
        val channels = overviewChannelsSearch(actionData)
        call.respond(HttpStatusCode.OK, OverviewChannelsRepresentation.from(channels))
    }
}
