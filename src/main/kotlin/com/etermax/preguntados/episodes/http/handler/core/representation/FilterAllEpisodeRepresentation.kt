package com.etermax.preguntados.episodes.http.handler.core.representation

import com.etermax.preguntados.episodes.utils.LanguageUtils
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FilterAllEpisodeRepresentation(
    @SerialName("owner_id") val ownerId: Long? = null,
    @SerialName("language") val language: String? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("country") val country: String? = null
) {

    fun toLanguage() = LanguageUtils.from(language)

    fun toCountry() = country?.let { Country.valueOf(it.uppercase()) }
}
