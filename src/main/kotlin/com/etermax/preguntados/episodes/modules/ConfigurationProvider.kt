package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.episodes.core.domain.episode.notification.EventGroupsConfiguration
import com.etermax.preguntados.episodes.core.domain.notification.NotificationsConfiguration
import com.etermax.preguntados.episodes.http.HttpApiServer.AppConfig
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.ResilienceConfiguration
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.DynamoConfiguration
import com.etermax.preguntados.episodes.modules.GatewaysProvider.HttpEngineConfiguration
import com.etermax.preguntados.episodes.modules.OpenSearchProvider.OpenSearchConfiguration
import com.etermax.preguntados.episodes.modules.RedisProvider.RedisConfiguration
import com.etermax.preguntados.episodes.modules.RedisProvider.RedisTtlConfiguration
import com.sksamuel.hoplite.ConfigLoader
import com.sksamuel.hoplite.addEnvironmentSource
import com.sksamuel.hoplite.yaml.YamlParser
import java.time.Duration

/**
 * Load configuration data from `config.yaml`
 */
object ConfigurationProvider {

    private const val CONFIGURATION_FILE_NAME = "/config.yaml"

    data class Configuration(
        val app: AppConfig,
        val http: HttpEngineConfiguration,
        val persistence: PersistenceConfiguration,
        val openSearch: OpenSearchConfiguration,
        val resilienceConfig: ResilienceConfiguration,
        val delivery: DeliveryConfiguration,
        val pendingEpisodesJobConfig: PendingEpisodesJobConfiguration,
        val p2ApiHost: String,
        val triviaPlaylistHost: String,
        val contentHost: String,
        val biHost: String,
        val p2AdminPassword: String,
        val clerk: ClerkConfiguration,
        val platformHost: String,
        val platformGameId: String,
        val notificationsConfiguration: NotificationsConfiguration,
        val eventGroupsConfiguration: EventGroupsConfiguration,
        val dynamoDBSearch: DynamoDBSearchConfiguration,
        val isUpdateRateCountersEnabled: Boolean,
        val isIncrementViewsEnabled: Boolean,
        val playersByOwnerExpirationTime: String,
        val isPlayersByOwnerRepositoryEnabled: Boolean,
        val analytics: AnalyticsConfiguration,
        val isChannelEpisodesNormalizerEnabled: Boolean
    )

    data class PersistenceConfiguration(
        val dynamo: DynamoConfiguration,
        val cache: RedisConfiguration,
        val ttl: RedisTtlConfiguration
    ) {
        val veryShortTtl: Duration by lazy { Duration.parse(ttl.veryShort) }
        val shortTtl: Duration by lazy { Duration.parse(ttl.short) }
        val mediumTtl: Duration by lazy { Duration.parse(ttl.medium) }
        val largeTtl: Duration by lazy { Duration.parse(ttl.large) }
    }

    data class DeliveryConfiguration(
        val requiredLikesForTopRated: Int,
        val requiredViewsForTopRated: Int,
        val requiredMinimumQueryLength: Int,
        val lastPlayedThreshold: Int,
        val similarityScoreThreshold: Double,
        val isSortedByQuality: Boolean
    )

    data class PendingEpisodesJobConfiguration(
        val isEnabled: Boolean,
        val startDelayInSeconds: Int,
        val processDelayInSeconds: Int,
        val stopGracePeriodInSeconds: Int
    )

    data class DynamoDBSearchConfiguration(
        val isEnabled: Boolean,
        val chunkSize: Int
    )

    data class AnalyticsConfiguration(
        val isEnabled: Boolean
    )

    /**
     * In charge of obtaining the configuration values and setting them in the corresponding variables.
     */
    val config: Configuration = ConfigLoader.builder()
        .allowUnresolvedSubstitutions()
        .addFileExtensionMapping("yaml", YamlParser())
        .addEnvironmentSource()
        .build()
        .loadConfigOrThrow(CONFIGURATION_FILE_NAME)
}

data class ClerkConfiguration(val host: String, val adminPassword: String)
