package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import io.lettuce.core.RedisClient
import io.lettuce.core.RedisURI
import io.lettuce.core.StaticCredentialsProvider
import io.lettuce.core.api.async.RedisAsyncCommands
import org.slf4j.Logger
import org.slf4j.LoggerFactory

object RedisProvider {
    private val logger: Logger = LoggerFactory.getLogger("DynamoDbProvider")

    data class RedisConfiguration(
        val endpoint: String,
        val password: String,
        val username: String,
        val useTLS: Boolean
    )

    data class RedisTtlConfiguration(
        val veryShort: String,
        val short: String,
        val medium: String,
        val large: String
    )

    val redis: RedisAsyncCommands<String, String> by lazy {
        logger.info("Connecting to redis for cache instance...")
        redisClient.connect().async()
            .also { logger.info("Connection async with redis for caches established") }
    }

    private val redisClient: RedisClient by lazy {
        buildRedisClientFrom(config.persistence.cache)
    }

    private fun buildRedisClientFrom(redisConfiguration: RedisConfiguration): RedisClient {
        return with(redisConfiguration) {
            val uriEndpoint = RedisURI.create("redis://$endpoint")
            val username = username.let {
                if (it.contentEquals("default")) null else it
            }
            val uri = RedisURI.builder(uriEndpoint)
                .withAuthentication(StaticCredentialsProvider(username, password.toCharArray()))
                .withSsl(useTLS)
                .withClientName("episodes")
                .build()
            RedisClient.create(uri)
        }
    }

    fun shutdownClient() {
        redisClient.shutdown()
    }
}
