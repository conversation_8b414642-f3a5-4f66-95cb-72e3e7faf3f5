package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.analytics.repository.CreatorUserMetricsItem
import com.etermax.preguntados.analytics.repository.EpisodeMetricsItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables.ChallengeIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables.ChallengeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables.ChallengePlayerItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ByOwnerChannelReducedItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.*
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeByOwnerItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodesIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.UpdateEpisodeQualityItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.eteragent.EterAgentItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.notification.EventGroupsItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.tables.ProgressItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.rate.tables.RateItem
import com.etermax.preguntados.episodes.core.infrastructure.search.filter.DynamoDBFilterDataItem
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import com.etermax.preguntados.reports.repository.ReportItem
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import software.amazon.awssdk.auth.credentials.*
import software.amazon.awssdk.core.client.config.ClientAsyncConfiguration
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration
import software.amazon.awssdk.core.retry.RetryPolicy
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClientBuilder
import java.net.URI

object DynamoDBProvider {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    data class DynamoConfiguration(
        val endpoint: String,
        val maxConnections: Int,
        val maxErrorRetries: Int,
        val tablePrefix: String,
        val accessKey: String,
        val secretKey: String
    )

    val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient by lazy {
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbClient)
            .build()
    }

    val episodeTable: DynamoDbAsyncTable<EpisodeItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(EpisodeItem::class.java))
    }

    val byOwnerIndex: DynamoDbAsyncIndex<EpisodeByOwnerItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(EpisodeByOwnerItem::class.java))
            .index(EpisodesIndexes.BY_OWNER)
    }

    val byOwnerAndLastModificationIndex: DynamoDbAsyncIndex<ByOwnerChannelReducedItem> by lazy {
        dynamoDbEnhancedClient.table(channelsTableName, TableSchema.fromBean(ByOwnerChannelReducedItem::class.java))
            .index(ChannelIndexes.BY_OWNER_AND_LAST_MODIFICATION)
    }

    val assignChannelIdToEpisodeItem: DynamoDbAsyncTable<AssignChannelIdToEpisodeItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(AssignChannelIdToEpisodeItem::class.java))
    }

    val updateChannelEpisodesCountTable: DynamoDbAsyncTable<UpdateChannelEpisodesCountItem> by lazy {
        dynamoDbEnhancedClient.table(
            channelsTableName,
            TableSchema.fromBean(UpdateChannelEpisodesCountItem::class.java)
        )
    }

    val removeChannelIdFromChannelTable: DynamoDbAsyncTable<RemoveChannelFromEpisodeItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(RemoveChannelFromEpisodeItem::class.java))
    }

    val eterAgentTable: DynamoDbAsyncTable<EterAgentItem> by lazy {
        dynamoDbEnhancedClient.table(eterAgentTableName, TableSchema.fromBean(EterAgentItem::class.java))
    }

    val eventGroupsTable: DynamoDbAsyncTable<EventGroupsItem> by lazy {
        dynamoDbEnhancedClient.table(eventGroupsTableName, TableSchema.fromBean(EventGroupsItem::class.java))
    }

    val progressTable: DynamoDbAsyncTable<ProgressItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(ProgressItem::class.java))
    }

    val rateTable: DynamoDbAsyncTable<RateItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(RateItem::class.java))
    }

    val coreReportTable: DynamoDbAsyncTable<ReportItem> by lazy {
        dynamoDbEnhancedClient.table(reportsTableName, TableSchema.fromBean(ReportItem::class.java))
    }

    val channelsTable: DynamoDbAsyncTable<ChannelItem> by lazy {
        dynamoDbEnhancedClient.table(channelsTableName, TableSchema.fromBean(ChannelItem::class.java))
    }

    val channelsEpisodesTable: DynamoDbAsyncTable<ChannelEpisodeItem> by lazy {
        dynamoDbEnhancedClient.table(channelsTableName, TableSchema.fromBean(ChannelEpisodeItem::class.java))
    }

    val channelsEpisodesOrderTable: DynamoDbAsyncTable<ChannelEpisodeOrderItem> by lazy {
        dynamoDbEnhancedClient.table(channelsTableName, TableSchema.fromBean(ChannelEpisodeOrderItem::class.java))
    }

    val filtersDataTable: DynamoDbAsyncTable<DynamoDBFilterDataItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(DynamoDBFilterDataItem::class.java))
    }

    val channelScoreTable: DynamoDbAsyncTable<UpdateChannelScoreItem> by lazy {
        dynamoDbEnhancedClient.table(channelsTableName, TableSchema.fromBean(UpdateChannelScoreItem::class.java))
    }

    val channelQualityTable: DynamoDbAsyncTable<UpdateChannelQualityItem> by lazy {
        dynamoDbEnhancedClient.table(channelsTableName, TableSchema.fromBean(UpdateChannelQualityItem::class.java))
    }

    val episodeScoreTable: DynamoDbAsyncTable<UpdateEpisodeQualityItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(UpdateEpisodeQualityItem::class.java))
    }

    val contentMetricsTable: DynamoDbAsyncTable<EpisodeMetricsItem> by lazy {
        dynamoDbEnhancedClient.table(metricsTableName, TableSchema.fromBean(EpisodeMetricsItem::class.java))
    }

    val creatorMetricsTable: DynamoDbAsyncTable<CreatorUserMetricsItem> by lazy {
        dynamoDbEnhancedClient.table(metricsTableName, TableSchema.fromBean(CreatorUserMetricsItem::class.java))
    }

    val challengesTable: DynamoDbAsyncTable<ChallengeItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(ChallengeItem::class.java))
    }

    val challengePlayersTable: DynamoDbAsyncTable<ChallengePlayerItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(ChallengePlayerItem::class.java))
    }

    val challengePlayersByPlayerIndex: DynamoDbAsyncIndex<ChallengePlayerItem> by lazy {
        dynamoDbEnhancedClient.table(episodesTableName, TableSchema.fromBean(ChallengePlayerItem::class.java))
            .index(ChallengeIndexes.BY_PLAYER)
    }

    val updateChannelOrderTypeTable: DynamoDbAsyncTable<UpdateChannelOrderTypeItem> by lazy {
        dynamoDbEnhancedClient.table(channelsTableName, TableSchema.fromBean(UpdateChannelOrderTypeItem::class.java))
    }

    private val episodesTableName by lazy {
        if (config.persistence.dynamo.tablePrefix.isBlank()) {
            "episodes"
        } else {
            "${config.persistence.dynamo.tablePrefix.lowercase()}_episodes"
        }
    }

    private val eterAgentTableName by lazy {
        val tableName = "eteragents"
        if (config.persistence.dynamo.tablePrefix.isBlank()) {
            tableName
        } else {
            "${config.persistence.dynamo.tablePrefix.lowercase()}_$tableName"
        }
    }

    private val eventGroupsTableName by lazy {
        val tableName = "event_groups"
        if (config.persistence.dynamo.tablePrefix.isBlank()) {
            tableName
        } else {
            "${config.persistence.dynamo.tablePrefix.lowercase()}_$tableName"
        }
    }

    private val channelsTableName by lazy {
        if (config.persistence.dynamo.tablePrefix.isBlank()) {
            "channels"
        } else {
            "${config.persistence.dynamo.tablePrefix.lowercase()}_channels"
        }
    }

    private val metricsTableName by lazy {
        if (config.persistence.dynamo.tablePrefix.isBlank()) {
            "trivia_metrics"
        } else {
            "${config.persistence.dynamo.tablePrefix.lowercase()}_trivia_metrics"
        }
    }

    private val reportsTableName by lazy {
        if (config.persistence.dynamo.tablePrefix.isBlank()) {
            "trivia_reports"
        } else {
            "${config.persistence.dynamo.tablePrefix.lowercase()}_trivia_reports"
        }
    }

    val dynamoDbClient: DynamoDbAsyncClient by lazy {
        logger.info("Creating DynamoDB client")

        val client = DynamoDbAsyncClient.builder()
            .setHttpClient()
            .setConfiguration()
            .setRegion()
            .setEndpoint()
            .setCredentials()
            .build()

        logger.info("DynamoDB client created")
        client
    }

    private fun DynamoDbAsyncClientBuilder.setRegion(): DynamoDbAsyncClientBuilder {
        region(Region.US_EAST_1)
        return this
    }

    private fun DynamoDbAsyncClientBuilder.setEndpoint(): DynamoDbAsyncClientBuilder {
        if (config.app.environment.equals("local", ignoreCase = true)) {
            endpointOverride(URI.create(config.persistence.dynamo.endpoint))
        }
        return this
    }

    private fun DynamoDbAsyncClientBuilder.setCredentials(): DynamoDbAsyncClientBuilder {
        credentialsProvider(
            AwsCredentialsProviderChain.builder()
                .addCredentialsProvider(WebIdentityTokenFileCredentialsProvider.create())
                .addCredentialsProvider(EnvironmentVariableCredentialsProvider.create())
                .addCredentialsProvider(
                    StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(
                            config.persistence.dynamo.accessKey,
                            config.persistence.dynamo.secretKey
                        )
                    )
                )
                .build()
        )
        return this
    }

    private fun DynamoDbAsyncClientBuilder.setHttpClient(): DynamoDbAsyncClientBuilder {
        val httpClientBuilder = NettyNioAsyncHttpClient.builder()
            .maxConcurrency(config.persistence.dynamo.maxConnections)

        httpClientBuilder(httpClientBuilder)
        asyncConfiguration(ClientAsyncConfiguration.builder().build())

        return this
    }

    private fun DynamoDbAsyncClientBuilder.setConfiguration(): DynamoDbAsyncClientBuilder {
        val overrideConfig = ClientOverrideConfiguration.builder()
            .retryPolicy(
                RetryPolicy.builder()
                    .numRetries(config.persistence.dynamo.maxErrorRetries)
                    .build()
            )

        overrideConfiguration(overrideConfig.build())
        return this
    }

    fun shutdownClient() {
        dynamoDbClient.close()
    }
}
