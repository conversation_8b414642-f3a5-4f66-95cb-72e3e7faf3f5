package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.analytics.actions.GetEpisodeAnalytics
import com.etermax.preguntados.analytics.actions.TrackEpisodeAnalytics
import com.etermax.preguntados.episodes.core.action.*
import com.etermax.preguntados.episodes.core.action.challenge.ChallengePlayers
import com.etermax.preguntados.episodes.core.action.challenge.CreateChallenge
import com.etermax.preguntados.episodes.core.action.challenge.FindChallenge
import com.etermax.preguntados.episodes.core.action.challenge.FindSortedChallenges
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.AnswerChallengeContent
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.FinishChallenge
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.PlayChallenge
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.RegisterChallengeProgress
import com.etermax.preguntados.episodes.core.action.channel.*
import com.etermax.preguntados.episodes.core.action.channel.admin.AdminDeleteChannels
import com.etermax.preguntados.episodes.core.action.channel.admin.AdminGetChannel
import com.etermax.preguntados.episodes.core.action.channel.episodes.AddEpisodesToChannel
import com.etermax.preguntados.episodes.core.action.channel.episodes.RemoveEpisodesFromChannel
import com.etermax.preguntados.episodes.core.action.channel.episodes.UpdateEpisodesOrderInChannel
import com.etermax.preguntados.episodes.core.action.episode.*
import com.etermax.preguntados.episodes.core.action.gameplay.*
import com.etermax.preguntados.episodes.core.action.profile.GetProfileSummary
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import com.etermax.preguntados.episodes.modules.GatewaysProvider.answerContentHistoryService
import com.etermax.preguntados.episodes.modules.GatewaysProvider.channelValidatorService
import com.etermax.preguntados.episodes.modules.GatewaysProvider.friendsRecommendationService
import com.etermax.preguntados.episodes.modules.GatewaysProvider.moderationService
import com.etermax.preguntados.episodes.modules.GatewaysProvider.playerAccountService
import com.etermax.preguntados.episodes.modules.GatewaysProvider.rankingRepository
import com.etermax.preguntados.episodes.modules.PostActionsProvider.updateChannelEpisodePostActionsSingleExecutor
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.asyncRedisSortedGames
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.challengePlayerRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.challengesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelByLanguageRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelDeleteRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelEpisodesOrderRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelEpisodesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelRemoveEpisodeRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelScoreRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelUpdateOrderTypeRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.episodePlayersByOwnerRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.episodeScoreRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.episodesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.progressContentRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.rateRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.reportRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.updatedEpisodeRepository
import com.etermax.preguntados.episodes.modules.SearchProvider.allSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.channelsSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.friendsEpisodeSearch
import com.etermax.preguntados.episodes.modules.ServicesProvider.analyticsTracker
import com.etermax.preguntados.episodes.modules.ServicesProvider.cachePlayerFriendsService
import com.etermax.preguntados.episodes.modules.ServicesProvider.cacheProfileService
import com.etermax.preguntados.episodes.modules.ServicesProvider.challengeService
import com.etermax.preguntados.episodes.modules.ServicesProvider.channelEpisodesService
import com.etermax.preguntados.episodes.modules.ServicesProvider.clock
import com.etermax.preguntados.episodes.modules.ServicesProvider.deliveryService
import com.etermax.preguntados.episodes.modules.ServicesProvider.descendingOrderItemCalculator
import com.etermax.preguntados.episodes.modules.ServicesProvider.episodeCreationNotifier
import com.etermax.preguntados.episodes.modules.ServicesProvider.episodeDeleteService
import com.etermax.preguntados.episodes.modules.ServicesProvider.episodeNotificationService
import com.etermax.preguntados.episodes.modules.ServicesProvider.feedServiceProxy
import com.etermax.preguntados.episodes.modules.ServicesProvider.progressContentService
import com.etermax.preguntados.episodes.modules.ServicesProvider.qualityService
import com.etermax.preguntados.episodes.modules.ServicesProvider.rankingPointsCalculator
import com.etermax.preguntados.episodes.modules.ServicesProvider.rankingService
import com.etermax.preguntados.episodes.modules.ServicesProvider.summaryService
import com.etermax.preguntados.episodes.modules.ServicesProvider.updateStatusService
import com.etermax.preguntados.episodes.modules.ServicesProvider.uuidSequencer
import com.etermax.preguntados.reports.action.*

object ActionsProvider {

    val createEpisode by lazy {
        CreateEpisode(
            episodesRepository,
            uuidSequencer,
            cacheProfileService,
            moderationService,
            updatedEpisodeRepository,
            channelEpisodesService,
            clock
        )
    }

    val updateEpisode by lazy {
        UpdateEpisode(
            episodesRepository,
            cacheProfileService,
            moderationService,
            clock,
            updateChannelEpisodePostActionsSingleExecutor,
            episodeCreationNotifier
        )
    }

    val deleteEpisode by lazy {
        DeleteEpisode(episodesRepository, episodeDeleteService)
    }

    val findEpisode by lazy {
        FindEpisode(episodesRepository, summaryService)
    }

    val findEpisodesByIds by lazy {
        FindEpisodesByIds(episodesRepository, summaryService)
    }

    val findAllEpisodes by lazy {
        FindAllEpisodes(episodesRepository)
    }

    val copyEpisode by lazy {
        CopyEpisode(episodesRepository, uuidSequencer, cacheProfileService, clock)
    }

    val searchEpisodes by lazy {
        SearchEpisodes(summaryService, deliveryService, updatedEpisodeRepository)
    }

    val getFeed by lazy {
        GetFeed(feedServiceProxy, summaryService)
    }

    val playEpisode by lazy {
        PlayEpisode(
            episodesRepository,
            progressContentService,
            episodeNotificationService,
            cacheProfileService,
            rankingRepository,
            trackMetric,
            episodePlayersByOwnerRepository
        )
    }

    val registerContentProgress by lazy {
        RegisterContentProgress(progressContentService, episodesRepository, cacheProfileService, qualityService) {
            config.isIncrementViewsEnabled
        }
    }

    val rateEpisode by lazy {
        RateEpisode(
            rateRepository = rateRepository,
            episodeRepository = episodesRepository,
            episodeNotificationService = episodeNotificationService,
            profileService = cacheProfileService,
            isUpdateRateCountersEnabled = config.isUpdateRateCountersEnabled,
            qualityService = qualityService
        )
    }

    val reportEpisode by lazy {
        ReportEpisode(reportRepository, episodesRepository, clock)
    }

    val getEpisodeReports by lazy {
        GetEpisodeReports(reportRepository, episodesRepository)
    }

    val getFullEpisodeReports by lazy {
        GetFullEpisodeReports(reportRepository, episodesRepository)
    }

    val reviewReports by lazy {
        ReviewReports(getFullEpisodeReports, updateStatusService, reportRepository, episodesRepository, clock)
    }

    val getMyReports by lazy {
        GetMyReports(reportRepository)
    }

    val findEpisodeDetails by lazy {
        FindEpisodeDetails(rateRepository, progressContentRepository, rankingService, trackMetric)
    }

    val answerContent by lazy {
        AnswerContent(
            rankingRepository,
            rankingPointsCalculator,
            progressContentService,
            episodesRepository,
            answerContentHistoryService
        )
    }

    val finishEpisode by lazy {
        FinishEpisode(episodesRepository, cacheProfileService, rateRepository, rankingService, trackMetric)
    }

    val findFriendsPlayedEpisode by lazy {
        FindFriendsPlayedEpisode(cachePlayerFriendsService, friendsEpisodeSearch)
    }

    val recommendEpisodes by lazy {
        RecommendEpisodes(episodesRepository, cacheProfileService, friendsRecommendationService)
    }

    val createChannel by lazy {
        CreateChannel(
            uuidSequencer,
            summaryService,
            channelRepository,
            descendingOrderItemCalculator,
            channelValidatorService,
            clock
        )
    }

    val updateChannel by lazy {
        UpdateChannel(
            repository = channelRepository,
            summaryService = summaryService,
            channelValidatorService = channelValidatorService,
            clock = clock
        )
    }

    val deleteChannel by lazy {
        DeleteChannel(
            channelRepository,
            channelDeleteRepository,
            channelUnpublishedEpisodesRepository
        )
    }

    val searchChannels by lazy {
        SearchChannels(
            channelRepository,
            channelEpisodesRepository,
            episodesRepository,
            summaryService,
            paginationSize = 25
        )
    }

    val searchChannelsByLanguage by lazy {
        SearchChannelsByLanguage(
            channelByLanguageRepository,
            paginationSize = 30
        )
    }

    val findChannelById by lazy {
        FindChannelById(channelRepository, summaryService)
    }

    val findReducedChannelById by lazy {
        FindReducedChannelById(channelRepository)
    }

    val addEpisodesToChannel by lazy {
        AddEpisodesToChannel(
            episodesRepository,
            channelRepository,
            channelEpisodesService,
            summaryService,
            qualityService
        )
    }

    val removeEpisodesFromChannel by lazy {
        RemoveEpisodesFromChannel(
            episodesRepository,
            channelRepository,
            channelRemoveEpisodeRepository,
            summaryService,
            clock,
            qualityService
        )
    }

    val updateEpisodesOrderInChannel by lazy {
        UpdateEpisodesOrderInChannel(
            episodesRepository,
            channelEpisodesRepository,
            channelEpisodesOrderRepository,
            channelRepository,
            descendingOrderItemCalculator
        )
    }

    val getProfileSummary by lazy {
        GetProfileSummary(
            episodesRepository,
            channelRepository
        )
    }

    val adminDeleteChannel by lazy {
        AdminDeleteChannels(channelDeleteRepository)
    }

    val adminGetChannel by lazy {
        AdminGetChannel(channelRepository, summaryService)
    }
    val overviewInfoSearch by lazy {
        OverviewInfoSearch(playerAccountService, allSearch, summaryService, channelsSearch)
    }

    val overviewChannelsSearch by lazy {
        OverviewChannelsSearch(summaryService, channelsSearch)
    }

    val searchPlayersAccount by lazy {
        SearchPlayersAccount(playerAccountService)
    }

    val trackMetric by lazy {
        TrackEpisodeAnalytics(analyticsTracker, episodesRepository, config.analytics.isEnabled)
    }

    val getEpisodeAnalytics by lazy {
        GetEpisodeAnalytics(analyticsTracker, episodesRepository)
    }

    val createChallenge by lazy {
        CreateChallenge(
            challengesRepository,
            challengePlayerRepository,
            episodesRepository,
            uuidSequencer,
            summaryService,
            clock,
            config.persistence.largeTtl
        )
    }

    val findChallenge by lazy {
        FindChallenge(challengeService, summaryService)
    }

    val findSortedChallenges by lazy {
        FindSortedChallenges(asyncRedisSortedGames, challengeService, summaryService)
    }

    val challengePlayers by lazy {
        ChallengePlayers(challengeService, summaryService)
    }

    val playChallenge by lazy {
        PlayChallenge(challengeService, summaryService, clock)
    }

    val answerChallenge by lazy {
        AnswerChallengeContent(
            challengeService,
            rankingRepository,
            rankingPointsCalculator,
            answerContentHistoryService,
            clock
        )
    }

    val progressChallenge by lazy {
        RegisterChallengeProgress(
            challengeService,
            summaryService,
            episodesRepository
        )
    }

    val finishChallenge by lazy {
        FinishChallenge(
            challengeService,
            summaryService,
            rankingService
        )
    }

    val updateChannelOrderType by lazy {
        UpdateChannelOrderType(channelRepository, channelUpdateOrderTypeRepository)
    }

    val registerQuality by lazy {
        RegisterQuality(episodeScoreRepository, channelScoreRepository)
    }
}
