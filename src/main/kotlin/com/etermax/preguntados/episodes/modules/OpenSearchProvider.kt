package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.OpenSearchPlayedRepository
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import org.apache.hc.core5.http.HttpHost
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.transport.aws.AwsSdk2Transport
import org.opensearch.client.transport.aws.AwsSdk2TransportOptions
import org.opensearch.client.transport.httpclient5.ApacheHttpClient5TransportBuilder
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import software.amazon.awssdk.auth.credentials.AwsCredentialsProviderChain
import software.amazon.awssdk.auth.credentials.EnvironmentVariableCredentialsProvider
import software.amazon.awssdk.auth.credentials.ProfileCredentialsProvider
import software.amazon.awssdk.auth.credentials.WebIdentityTokenFileCredentialsProvider
import software.amazon.awssdk.http.async.SdkAsyncHttpClient
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient
import software.amazon.awssdk.regions.Region

object OpenSearchProvider {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    private const val SERVICE_NAME = "es"

    data class OpenSearchConfiguration(
        val endpoint: String,
        val episodesIndex: String,
        val playerEpisodesIndex: String,
        val channelsIndex: String
    )

    val openSearchClient by lazy {
        logger.info("Creating OpenSearch client")
        val host = HttpHost.create(config.openSearch.endpoint)

        if (config.app.environment.equals("local", ignoreCase = true)) {
            OpenSearchAsyncClient(ApacheHttpClient5TransportBuilder.builder(host).build())
        } else {
            val httpClient: SdkAsyncHttpClient = NettyNioAsyncHttpClient.builder().build()
            OpenSearchAsyncClient(
                AwsSdk2Transport(
                    httpClient,
                    host.hostName,
                    SERVICE_NAME,
                    Region.US_EAST_1,
                    AwsSdk2TransportOptions.builder().setCredentials(buildCredentialProvider()).build()
                )
            )
        }.also {
            logger.info("OpenSearch client created")
        }
    }

    val playedSearchRepository by lazy {
        OpenSearchPlayedRepository(openSearchClient, config.openSearch.playerEpisodesIndex)
    }

    fun shutdownClient() {
        openSearchClient.shutdown()
    }

    private fun buildCredentialProvider() =
        AwsCredentialsProviderChain.builder().credentialsProviders(
            listOf(
                WebIdentityTokenFileCredentialsProvider.create(),
                EnvironmentVariableCredentialsProvider.create(),
                ProfileCredentialsProvider.create()
            )
        ).build()
}
