package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.infrastructure.search.*
import com.etermax.preguntados.episodes.core.infrastructure.search.filter.DefaultFilterFactory
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import com.etermax.preguntados.episodes.modules.OpenSearchProvider.playedSearchRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelEpisodesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.episodesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.filterDataRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.playerRecentSearchRepository
import com.etermax.preguntados.episodes.modules.ServicesProvider.cachePlayerFriendsService
import com.etermax.preguntados.episodes.modules.ServicesProvider.clock

object SearchProvider {

    private fun withDynamoDBSearch(baseSearch: Search): Search {
        return DynamoDBSearch(
            baseSearch = baseSearch,
            episodeRepository = episodesRepository,
            chunkSize = config.dynamoDBSearch.chunkSize,
            isDynamoDBSearchEnabled = {
                config.dynamoDBSearch.isEnabled
            }
        )
    }

    val feedSearch by lazy {
        withDynamoDBSearch(
            FeedSearch(
                RepositoriesProvider.tokenizer,
                playedSearchRepository,
                recentSearch,
                feedFallbackSearch,
                filterProvider,
                playerRecentSearchRepository,
                OpenSearchProvider.openSearchClient,
                config.openSearch.episodesIndex,
                config.delivery.lastPlayedThreshold
            )
        )
    }

    val pendingSearch by lazy {
        withDynamoDBSearch(
            PendingSearch(
                client = OpenSearchProvider.openSearchClient,
                indexName = config.openSearch.episodesIndex,
                tokenizer = RepositoriesProvider.tokenizer
            )
        )
    }

    private val feedFallbackSearch by lazy {
        FeedFallbackSearch(
            client = OpenSearchProvider.openSearchClient,
            indexName = config.openSearch.episodesIndex,
            requiredLikesForTopRated = config.delivery.requiredLikesForTopRated,
            requiredViewsForTopRated = config.delivery.requiredViewsForTopRated,
            tokenizer = RepositoriesProvider.tokenizer
        )
    }

    val allSearch by lazy {
        withDynamoDBSearch(
            AllSearch(
                client = OpenSearchProvider.openSearchClient,
                indexName = config.openSearch.episodesIndex,
                tokenizer = RepositoriesProvider.tokenizer
            )
        )
    }

    val channelsSearch by lazy {
        SearchChannelsByName(
            repository = RepositoriesProvider.channelRepository,
            searchClient = OpenSearchProvider.openSearchClient,
            indexName = config.openSearch.channelsIndex,
            tokenizer = RepositoriesProvider.tokenizer
        )
    }

    val topRatedSearch by lazy {
        withDynamoDBSearch(
            TopRatedSearch(
                client = OpenSearchProvider.openSearchClient,
                indexName = config.openSearch.episodesIndex,
                requiredLikesForTopRated = config.delivery.requiredLikesForTopRated,
                requiredViewsForTopRated = config.delivery.requiredViewsForTopRated,
                tokenizer = RepositoriesProvider.tokenizer
            )
        )
    }

    val popularSearch by lazy {
        withDynamoDBSearch(
            PopularSearch(
                client = OpenSearchProvider.openSearchClient,
                indexName = config.openSearch.episodesIndex,
                tokenizer = RepositoriesProvider.tokenizer
            )
        )
    }

    val channelCreationSearch by lazy {
        ChannelCreationSearch(
            episodeRepository = episodesRepository
        )
    }

    val channelCreationSearchV2 by lazy {
        ChannelCreationSearchV2(
            episodeRepository = episodesRepository,
            episodesIndexName = config.openSearch.episodesIndex,
            client = OpenSearchProvider.openSearchClient
        )
    }

    val byChannelSearch by lazy {
        ByChannelSearch(
            episodeRepository = episodesRepository,
            channelEpisodesRepository = channelEpisodesRepository
        )
    }

    val byChannelCustomOrderSearch by lazy {
        ByChannelCustomOrderSearch(
            episodeRepository = episodesRepository,
            channelEpisodesRepository = channelEpisodesRepository
        )
    }

    val mineSearch by lazy {
        withDynamoDBSearch(
            MineSearch(
                client = OpenSearchProvider.openSearchClient,
                indexName = config.openSearch.episodesIndex,
                tokenizer = RepositoriesProvider.tokenizer
            )
        )
    }

    val viewerSearch by lazy {
        withDynamoDBSearch(
            ViewerSearch(
                client = OpenSearchProvider.openSearchClient,
                indexName = config.openSearch.episodesIndex,
                tokenizer = RepositoriesProvider.tokenizer
            )
        )
    }

    val recentSearch by lazy {
        RecentSearch(
            client = OpenSearchProvider.openSearchClient,
            indexName = config.openSearch.episodesIndex,
            tokenizer = RepositoriesProvider.tokenizer
        )
    }

    val playedSearch by lazy {
        PlayedSearch(
            playedRepository = playedSearchRepository,
            episodeRepository = episodesRepository
        )
    }

    val playedByMeSearch by lazy {
        PlayedByMeSearch(
            playedRepository = playedSearchRepository,
            episodeRepository = episodesRepository
        )
    }

    val playedByOtherSearch by lazy {
        PlayedByOtherSearch(
            playedRepository = playedSearchRepository,
            episodeRepository = episodesRepository
        )
    }

    val trendingSearch by lazy {
        TrendingSearch(
            client = OpenSearchProvider.openSearchClient,
            indexName = config.openSearch.playerEpisodesIndex,
            episodeRepository = episodesRepository,
            clock = clock
        )
    }

    val similarSearch by lazy {
        withDynamoDBSearch(
            SimilarSearch(
                client = OpenSearchProvider.openSearchClient,
                indexName = config.openSearch.episodesIndex,
                tokenizer = RepositoriesProvider.tokenizer,
                playedRepository = playedSearchRepository,
                similarityScoreThreshold = config.delivery.similarityScoreThreshold
            )
        )
    }

    val recommendedEpisodesSearch by lazy {
        RecommendedEpisodesSearch(
            client = OpenSearchProvider.openSearchClient,
            indexName = config.openSearch.playerEpisodesIndex,
            clock = clock,
            episodeRepository = episodesRepository,
            playerFriendsService = cachePlayerFriendsService
        )
    }

    val friendsEpisodeSearch by lazy {
        FriendsEpisodeSearch(
            client = OpenSearchProvider.openSearchClient,
            indexName = config.openSearch.playerEpisodesIndex
        )
    }

    val filterProvider by lazy {
        DefaultFilterFactory(
            repository = filterDataRepository,
            osClient = OpenSearchProvider.openSearchClient,
            episodesIndexName = config.openSearch.playerEpisodesIndex
        )
    }
}
