package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.episodes.core.domain.episode.pending.PendingEpisodeConfiguration
import com.etermax.preguntados.episodes.job.PendingEpisodesJob
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import com.etermax.preguntados.episodes.modules.GatewaysProvider.contentValidationService
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.pendingEpisodeRepository
import com.etermax.preguntados.episodes.modules.SearchProvider.pendingSearch
import com.etermax.preguntados.episodes.modules.ServicesProvider.updateStatusService

object JobsProvider {

    val pendingEpisodesJob by lazy {
        PendingEpisodesJob(
            config = PendingEpisodeConfiguration(
                isEnabled = config.pendingEpisodesJobConfig.isEnabled,
                startDelayInSeconds = config.pendingEpisodesJobConfig.startDelayInSeconds,
                stopGracePeriodInSeconds = config.pendingEpisodesJobConfig.stopGracePeriodInSeconds,
                processDelayInSeconds = config.pendingEpisodesJobConfig.processDelayInSeconds
            ),
            contentValidationService = contentValidationService,
            pendingEpisodeRepository = pendingEpisodeRepository,
            pendingSearch = pendingSearch,
            updateStatusService = updateStatusService
        )
    }
}
