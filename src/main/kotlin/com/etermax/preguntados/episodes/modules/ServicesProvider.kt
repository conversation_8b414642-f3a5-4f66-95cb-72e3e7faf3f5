package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.analytics.service.AnalyticsTracker
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelEpisodesService
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeCreationNotifier
import com.etermax.preguntados.episodes.core.domain.episode.delete.EpisodeDeleteService
import com.etermax.preguntados.episodes.core.domain.episode.notification.EpisodeNotificationService
import com.etermax.preguntados.episodes.core.domain.episode.notification.EventGroupService
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentService
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRankingService
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingService
import com.etermax.preguntados.episodes.core.domain.profile.CachePlayerFriendsService
import com.etermax.preguntados.episodes.core.domain.profile.CacheProfileService
import com.etermax.preguntados.episodes.core.domain.quality.likerate.EpisodeLikeRateAvgCalculator
import com.etermax.preguntados.episodes.core.domain.quality.service.AsyncDefaultQualityService
import com.etermax.preguntados.episodes.core.domain.quality.service.DefaultQualityService
import com.etermax.preguntados.episodes.core.domain.search.DeliveryService
import com.etermax.preguntados.episodes.core.infrastructure.moderation.RegexUrlValidatorService
import com.etermax.preguntados.episodes.core.infrastructure.order.DynamoDBDescendingOrderItemCalculator
import com.etermax.preguntados.episodes.core.infrastructure.profile.ApiProfileService
import com.etermax.preguntados.episodes.core.infrastructure.ranking.TimeRankingPointsCalculator
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.service.AnonymousFeedService
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.service.DefaultFeedService
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.service.DefaultFeedServiceConfiguration
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.service.FeedServiceProxy
import com.etermax.preguntados.episodes.core.infrastructure.search.service.PlayedEpisodeSearchService
import com.etermax.preguntados.episodes.core.infrastructure.search.service.RecentEpisodeSearchService
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer
import com.etermax.preguntados.episodes.core.infrastructure.time.DefaultClock
import com.etermax.preguntados.episodes.job.UpdateStatusService
import com.etermax.preguntados.episodes.modules.ActionsProvider.getEpisodeReports
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import com.etermax.preguntados.episodes.modules.GatewaysProvider.apiExternalService
import com.etermax.preguntados.episodes.modules.GatewaysProvider.blackListRecommendationService
import com.etermax.preguntados.episodes.modules.GatewaysProvider.clerkExternalService
import com.etermax.preguntados.episodes.modules.GatewaysProvider.playerFriendsService
import com.etermax.preguntados.episodes.modules.GatewaysProvider.rankingRepository
import com.etermax.preguntados.episodes.modules.GatewaysProvider.userBasedRepository
import com.etermax.preguntados.episodes.modules.MetricsProvider.cacheProfileMetric
import com.etermax.preguntados.episodes.modules.OpenSearchProvider.openSearchClient
import com.etermax.preguntados.episodes.modules.OpenSearchProvider.playedSearchRepository
import com.etermax.preguntados.episodes.modules.PostActionsProvider.updateChannelEpisodePostActionsSingleExecutor
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.addEpisodesToChannelRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.challengePlayerRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.challengesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelEpisodeDeleteRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelEpisodesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelScoreRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.contentAnalyticsRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.creatorAnalyticsRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.episodePlayersByOwnerRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.episodesRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.eventGroupRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.histogramsRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.playedEpisodeSearchRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.playerFriendsRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.profileRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.progressContentRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.recentEpisodeRepository
import com.etermax.preguntados.episodes.modules.SearchProvider.allSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.byChannelCustomOrderSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.byChannelSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.channelCreationSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.channelCreationSearchV2
import com.etermax.preguntados.episodes.modules.SearchProvider.feedSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.filterProvider
import com.etermax.preguntados.episodes.modules.SearchProvider.mineSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.playedSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.popularSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.recentSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.recommendedEpisodesSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.similarSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.topRatedSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.trendingSearch
import com.etermax.preguntados.episodes.modules.SearchProvider.viewerSearch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers

object ServicesProvider {

    val uuidSequencer by lazy {
        UUIDSequencer()
    }

    val progressContentService by lazy {
        ProgressContentService(progressContentRepository)
    }

    val cacheProfileService by lazy {
        CacheProfileService(profileService, profileRepository, cacheProfileMetric)
    }

    val analyticsTracker by lazy {
        AnalyticsTracker(
            clock,
            contentAnalyticsRepository,
            creatorAnalyticsRepository,
            histogramsRepository,
            getEpisodeReports,
            cachePlayerFriendsService
        )
    }

    val cachePlayerFriendsService by lazy {
        CachePlayerFriendsService(playerFriendsService, playerFriendsRepository)
    }

    val clock by lazy {
        DefaultClock()
    }

    val episodeNotificationService by lazy {
        EpisodeNotificationService(
            cacheProfileService,
            eventGroupService,
            clerkExternalService
        ) { config.notificationsConfiguration }
    }

    val rankingPointsCalculator by lazy {
        TimeRankingPointsCalculator()
    }

    val rankingService by lazy {
        RankingService(rankingRepository, deliveryRankingService, playerFriendsService)
    }

    private val deliveryRankingService by lazy {
        DeliveryRankingService(cacheProfileService)
    }

    private val eventGroupService by lazy {
        EventGroupService(eventGroupRepository, DefaultClock(), config.eventGroupsConfiguration)
    }

    val summaryService by lazy {
        SummaryService(cacheProfileService, channelUnpublishedEpisodesService)
    }

    val challengeService by lazy {
        ChallengeService(
            progressContentService,
            rankingService,
            challengesRepository,
            challengePlayerRepository,
            episodesRepository
        )
    }

    val playedEpisodeSearchService by lazy {
        PlayedEpisodeSearchService(playedSearchRepository, playedEpisodeSearchRepository, episodesRepository, clock)
    }

    private val recentEpisodeSearchService by lazy {
        RecentEpisodeSearchService(
            client = openSearchClient,
            episodesIndexName = config.openSearch.episodesIndex,
            recentEpisodeRepository = recentEpisodeRepository,
            clock = clock
        )
    }

    val deliveryService by lazy {
        DeliveryService(
            listOf(
                feedSearch,
                allSearch,
                topRatedSearch,
                playedSearch,
                recentSearch,
                mineSearch,
                viewerSearch,
                popularSearch,
                similarSearch,
                trendingSearch,
                channelCreationSearch,
                channelCreationSearchV2,
                byChannelSearch,
                byChannelCustomOrderSearch,
                recommendedEpisodesSearch
            )
        )
    }

    val feedServiceProxy by lazy {
        FeedServiceProxy(defaultFeedService, anonymousFeedService)
    }

    val descendingOrderItemCalculator by lazy {
        DynamoDBDescendingOrderItemCalculator(
            getCurrentDate = { clock.now() }
        )
    }

    val urlValidatorService by lazy {
        RegexUrlValidatorService()
    }

    val channelEpisodesService by lazy {
        ChannelEpisodesService(
            episodesRepository,
            channelRepository,
            descendingOrderItemCalculator,
            addEpisodesToChannelRepository,
            clock
        )
    }

    val episodeDeleteService by lazy {
        EpisodeDeleteService(
            channelRepository,
            channelEpisodeDeleteRepository,
            channelUnpublishedEpisodesRepository,
            blackListRecommendationService,
            clock
        )
    }

    val qualityService by lazy {
        val defaultQualityService = DefaultQualityService(
            channelEpisodesRepository = channelEpisodesRepository,
            episodeRepository = episodesRepository,
            channelRepository = channelRepository,
            episodeLikeRateAvgCalculator = EpisodeLikeRateAvgCalculator(),
            channelScoreRepository = channelScoreRepository,
            minimumRates = config.delivery.requiredLikesForTopRated,
            minimumViews = config.delivery.requiredViewsForTopRated
        )
        AsyncDefaultQualityService(defaultQualityService, coroutineScope = CoroutineScope(Dispatchers.IO))
    }

    val episodeCreationNotifier by lazy {
        EpisodeCreationNotifier(
            episodeNotificationService,
            episodePlayersByOwnerRepository,
            cacheProfileService
        )
    }

    private val channelUnpublishedEpisodesService by lazy {
        ChannelUnpublishedEpisodesService(channelUnpublishedEpisodesRepository)
    }

    val updateStatusService by lazy {
        UpdateStatusService(
            episodesRepository,
            clock,
            updateChannelEpisodePostActionsSingleExecutor,
            episodeCreationNotifier
        )
    }

    private val profileService by lazy {
        ApiProfileService(apiExternalService)
    }

    private val defaultFeedService by lazy {
        DefaultFeedService(
            filterFactory = filterProvider,
            osClient = openSearchClient,
            episodesIndexName = config.openSearch.episodesIndex,
            channelIndexName = config.openSearch.channelsIndex,
            playedRepository = playedSearchRepository,
            episodesRepository = episodesRepository,
            channelRepository = channelRepository,
            configuration = DefaultFeedServiceConfiguration(
                userHistoryThreshold = config.delivery.lastPlayedThreshold,
                requiredLikesForHighQuality = config.delivery.requiredLikesForTopRated,
                requiredViewsForHighQuality = config.delivery.requiredViewsForTopRated,
                isSortedByQuality = config.delivery.isSortedByQuality
            ),
            userBasedRepository = userBasedRepository,
            playedEpisodeSearchService = playedEpisodeSearchService,
            recentEpisodeSearchService = recentEpisodeSearchService,
            blackListRecommendationService = blackListRecommendationService
        )
    }

    private val anonymousFeedService by lazy {
        AnonymousFeedService(
            osClient = openSearchClient,
            episodesIndexName = config.openSearch.episodesIndex,
            channelIndexName = config.openSearch.channelsIndex,
            episodesRepository = episodesRepository,
            channelRepository = channelRepository,
            configuration = DefaultFeedServiceConfiguration(
                userHistoryThreshold = config.delivery.lastPlayedThreshold,
                requiredLikesForHighQuality = config.delivery.requiredLikesForTopRated,
                requiredViewsForHighQuality = config.delivery.requiredViewsForTopRated,
                isSortedByQuality = config.delivery.isSortedByQuality
            )
        )
    }
}
