package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.episodes.core.action.channel.ChannelValidatorService
import com.etermax.preguntados.episodes.core.infrastructure.account.HttpPlayerAccountService
import com.etermax.preguntados.episodes.core.infrastructure.content.HttpContentValidationService
import com.etermax.preguntados.episodes.core.infrastructure.episode.history.HttpAnswerContentHistoryService
import com.etermax.preguntados.episodes.core.infrastructure.http.ClientFactory
import com.etermax.preguntados.episodes.core.infrastructure.http.ClientFactory.closeWithEngine
import com.etermax.preguntados.episodes.core.infrastructure.moderation.PlatformModerationService
import com.etermax.preguntados.episodes.core.infrastructure.notification.PlatformNotificationService
import com.etermax.preguntados.episodes.core.infrastructure.profile.HttpPlayerFriendsService
import com.etermax.preguntados.episodes.core.infrastructure.ranking.HttpRankingRepository
import com.etermax.preguntados.episodes.core.infrastructure.recommendation.HttpFriendsRecommendationService
import com.etermax.preguntados.episodes.core.infrastructure.search.repository.HttpUserBasedRepository
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.apiResilienceBundle
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.biResilienceBundle
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.circuitBreakerRegistry
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.contentResilienceBundle
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.historyResilienceBundle
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.rankingResilienceBundle
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.retryRegistry
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.userBasedResilienceBundle
import com.etermax.preguntados.episodes.modules.CircuitBreakerProvider.userProfileResilienceBundle
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import com.etermax.preguntados.external.services.core.infrastructure.ExternalServiceFactory
import io.ktor.client.engine.apache.*
import org.slf4j.LoggerFactory

object GatewaysProvider {

    private val logger = LoggerFactory.getLogger(this::class.java)

    data class HttpEngineConfiguration(
        val connectTimeoutMs: Int,
        val connectionRequestTimeoutMs: Int,
        val maxConnTotal: Int,
        val maxConnPerRoute: Int
    )

    private val platformClient by lazy {
        logger.info("Connecting to platform. host: {}", config.platformHost)
        val config = ClientFactory.Config(baseUrl = config.platformHost)
        ClientFactory.makeClient(config, engine)
    }

    private val contentClient by lazy {
        logger.info("Connecting to content. host: {}", config.contentHost)
        val config = ClientFactory.Config(baseUrl = config.contentHost)
        ClientFactory.makeClient(config, engine)
    }

    private val apiClient by lazy {
        logger.info("Connecting to api. host: {}", config.p2ApiHost)
        val config = ClientFactory.Config(baseUrl = config.p2ApiHost)
        ClientFactory.makeClient(config, engine)
    }

    private val historyClient by lazy {
        logger.info("Connecting to history. host: {}", config.triviaPlaylistHost)
        val config = ClientFactory.Config(baseUrl = config.triviaPlaylistHost)
        ClientFactory.makeClient(config, engine)
    }

    private val biClient by lazy {
        logger.info("Connecting to bi. host: {}", config.biHost)
        val config = ClientFactory.Config(baseUrl = config.biHost)
        ClientFactory.makeClient(config, engine)
    }

    val apiExternalService by lazy {
        ExternalServiceFactory.createApiService(
            config.p2ApiHost,
            config.p2AdminPassword,
            circuitBreakerRegistry = circuitBreakerRegistry,
            retryRegistry = retryRegistry
        )
    }

    val clerkExternalService by lazy {
        ExternalServiceFactory.createClerkService(
            host = config.clerk.host,
            adminPassword = config.clerk.adminPassword,
            circuitBreakerRegistry = circuitBreakerRegistry,
            retryRegistry = retryRegistry
        )
    }

    val rankingRepository by lazy {
        HttpRankingRepository(platformClient, rankingResilienceBundle, config.platformGameId)
    }

    val playerFriendsService by lazy {
        HttpPlayerFriendsService(apiClient, userProfileResilienceBundle, config.p2AdminPassword)
    }

    val friendsRecommendationService by lazy {
        HttpFriendsRecommendationService(biClient, biResilienceBundle)
    }

    val playerAccountService by lazy {
        HttpPlayerAccountService(apiClient, apiResilienceBundle, config.p2AdminPassword)
    }

    val contentValidationService by lazy {
        HttpContentValidationService(contentClient, contentResilienceBundle, config.p2AdminPassword)
    }

    val answerContentHistoryService by lazy {
        HttpAnswerContentHistoryService(historyClient, historyResilienceBundle, config.p2AdminPassword)
    }

    val moderationService by lazy {
        PlatformModerationService(platformClient, config.platformGameId)
    }

    val userBasedRepository by lazy {
        HttpUserBasedRepository(biClient, userBasedResilienceBundle)
    }

    val channelValidatorService by lazy {
        ChannelValidatorService(
            moderationService = moderationService,
            urlValidatorService = ServicesProvider.urlValidatorService
        )
    }

    val notificationService by lazy {
        PlatformNotificationService(
            platformClient,
            config.platformGameId
        )
    }

    private val engine by lazy {
        val cfg = config.http

        Apache.create {
            connectTimeout = cfg.connectTimeoutMs
            connectionRequestTimeout = cfg.connectionRequestTimeoutMs

            customizeClient {
                // Maximum number of socket connections.
                setMaxConnTotal(cfg.maxConnTotal)

                // Maximum number of requests for a specific endpoint route.
                setMaxConnPerRoute(cfg.maxConnPerRoute)
            }
        }
    }

    fun closeClients() {
        logger.info("Stopping http clients")
        platformClient.closeWithEngine()
        logger.info("Http clients stopped")
    }
}
