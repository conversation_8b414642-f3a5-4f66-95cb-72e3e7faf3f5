package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.episodes.core.infrastructure.http.error.GatewayUnexpectedException
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import com.etermax.preguntados.episodes.modules.ConfigurationProvider.config
import com.sksamuel.hoplite.ConfigAlias
import io.github.resilience4j.circuitbreaker.CircuitBreaker
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry
import io.github.resilience4j.core.IntervalFunction
import io.github.resilience4j.kotlin.retry.RetryConfig
import io.github.resilience4j.kotlin.retry.RetryRegistry
import io.github.resilience4j.prometheus.collectors.CircuitBreakerMetricsCollector
import io.github.resilience4j.prometheus.collectors.RetryMetricsCollector
import io.github.resilience4j.retry.Retry
import io.ktor.client.statement.*
import io.prometheus.client.CollectorRegistry
import java.time.Duration

object CircuitBreakerProvider {

    val circuitBreakerRegistry: CircuitBreakerRegistry by lazy {
        val circuitBreakerConfig = CircuitBreakerConfig.custom()
            .waitDurationInOpenState(Duration.ofSeconds(config.resilienceConfig.circuitBreakerConfiguration.waitSecondsInOpenState))
            .recordResult { filterResult(it as HttpResponse) }
            .build()

        val cbRegistry = CircuitBreakerRegistry.of(circuitBreakerConfig)
        CollectorRegistry.defaultRegistry.register(CircuitBreakerMetricsCollector.ofCircuitBreakerRegistry(cbRegistry))

        cbRegistry
    }

    val retryRegistry by lazy {
        val registry = RetryRegistry { }
        CollectorRegistry.defaultRegistry.register(RetryMetricsCollector.ofRetryRegistry(registry))
        registry
    }

    val rankingResilienceBundle by lazy {
        buildResilienceBundle("platform-ranking", config.resilienceConfig)
    }

    val userProfileResilienceBundle by lazy {
        buildResilienceBundle("user-profile", config.resilienceConfig)
    }

    val contentResilienceBundle by lazy {
        buildResilienceBundle("api-content", config.resilienceConfig)
    }

    val historyResilienceBundle by lazy {
        buildResilienceBundle("trivia-playlist", config.resilienceConfig)
    }

    val biResilienceBundle by lazy {
        buildResilienceBundle("bi", config.resilienceConfig)
    }

    val userBasedResilienceBundle by lazy {
        buildResilienceBundle("user-based", config.resilienceConfig)
    }

    val blackListResilienceBundle by lazy {
        buildResilienceBundle("bi", config.resilienceConfig)
    }

    val apiResilienceBundle by lazy {
        buildResilienceBundle("api", config.resilienceConfig)
    }

    private fun buildResilienceBundle(metricsServiceName: String, config: ResilienceConfiguration) =
        with(config) {
            val circuitBreaker = buildCircuitBreaker(metricsServiceName, circuitBreakerConfiguration)
            val retry = buildRetry(metricsServiceName, retryConfiguration)
            EndpointResilienceBundle(circuitBreaker, retry)
        }

    private fun buildRetry(metricsServiceName: String, config: RetryConfiguration): Retry {
        val retryConfig = RetryConfig {
            maxAttempts(config.maxAttempts)
            intervalFunction(
                IntervalFunction.ofExponentialRandomBackoff(
                    config.initialIntervalMillis,
                    config.multiplierFactor
                )
            )
            retryExceptions(GatewayUnexpectedException::class.java)
        }
        return retryRegistry.retry(metricsServiceName, retryConfig)
    }

    private fun buildCircuitBreaker(metricsServiceName: String, config: CircuitBreakerConfiguration): CircuitBreaker {
        val circuitBreakerConfig = CircuitBreakerConfig.custom()
            .waitDurationInOpenState(Duration.ofSeconds(config.waitSecondsInOpenState))
            .recordException {
                when (it) {
                    is GatewayUnexpectedException -> it.isServerError
                    else -> false
                }
            }
            .build()
        return circuitBreakerRegistry.circuitBreaker(metricsServiceName, circuitBreakerConfig)
    }

    private fun filterResult(httpResponse: HttpResponse): Boolean {
        return httpResponse.isError()
    }

    private fun HttpResponse.isError() = status.value >= 300

    data class CircuitBreakerConfiguration(
        val waitSecondsInOpenState: Long
    )

    data class RetryConfiguration(
        val maxAttempts: Int,
        val initialIntervalMillis: Long,
        val multiplierFactor: Double
    )

    data class ResilienceConfiguration(
        @ConfigAlias("retry") val retryConfiguration: RetryConfiguration,
        @ConfigAlias("circuitBreaker") val circuitBreakerConfiguration: CircuitBreakerConfiguration
    )
}
