package com.etermax.preguntados.episodes.modules

import com.etermax.preguntados.episodes.core.domain.channel.episode.UpdateChannelEpisodeProcessDraftPostAction
import com.etermax.preguntados.episodes.core.domain.channel.episode.UpdateChannelEpisodeProcessPendingPostAction
import com.etermax.preguntados.episodes.core.domain.channel.episode.UpdateChannelEpisodeProcessPublishedPostAction
import com.etermax.preguntados.episodes.core.domain.channel.episode.UpdateEpisodeProcessRejectedChannelEpisodePostAction
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionSingleExecutor
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelRepository
import com.etermax.preguntados.episodes.modules.RepositoriesProvider.channelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.modules.ServicesProvider.channelEpisodesService

object PostActionsProvider {

    val updateChannelEpisodePostActionsSingleExecutor by lazy {
        UpdateChannelEpisodePostActionSingleExecutor(updateChannelEpisodePostActions)
    }

    private val updateChannelEpisodePostActions by lazy {
        listOf(
            updateChannelEpisodeProcessPublishedPostAction,
            updateChannelEpisodeProcessRejectedPostAction,
            updateChannelEpisodeProcessPendingPostAction,
            updateChannelEpisodeProcessDraftPostAction
        )
    }

    private val updateChannelEpisodeProcessPublishedPostAction by lazy {
        UpdateChannelEpisodeProcessPublishedPostAction(
            channelRepository,
            channelEpisodesService,
            channelUnpublishedEpisodesRepository
        )
    }

    private val updateChannelEpisodeProcessRejectedPostAction by lazy {
        UpdateEpisodeProcessRejectedChannelEpisodePostAction(channelUnpublishedEpisodesRepository, channelEpisodesService)
    }

    private val updateChannelEpisodeProcessPendingPostAction by lazy {
        UpdateChannelEpisodeProcessPendingPostAction(channelUnpublishedEpisodesRepository)
    }

    private val updateChannelEpisodeProcessDraftPostAction by lazy {
        UpdateChannelEpisodeProcessDraftPostAction(channelUnpublishedEpisodesRepository)
    }
}
