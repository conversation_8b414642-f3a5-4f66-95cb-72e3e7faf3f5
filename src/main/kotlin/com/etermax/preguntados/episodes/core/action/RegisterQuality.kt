package com.etermax.preguntados.episodes.core.action

import com.etermax.preguntados.episodes.core.domain.episode.update.QualityRepository
import com.etermax.preguntados.episodes.core.domain.quality.Quality
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class RegisterQuality(
    private val episodeQualityRepository: QualityRepository,
    private val channelQualityRepository: QualityRepository
) {

    suspend operator fun invoke(actionData: ActionData) = coroutineScope {
        with(actionData) {
            async { episodeQualityRepository.updateQuality(episodes) }
            async { channelQualityRepository.updateQuality(channels) }
        }
    }

    data class ActionData(
        val episodes: List<Quality>,
        val channels: List<Quality>
    )
}
