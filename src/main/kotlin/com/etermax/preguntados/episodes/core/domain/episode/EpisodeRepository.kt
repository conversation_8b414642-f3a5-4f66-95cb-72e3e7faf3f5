package com.etermax.preguntados.episodes.core.domain.episode

import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedRequest
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedResponse
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language

interface EpisodeRepository {
    suspend fun findById(episodeId: String): Episode?
    suspend fun findByIds(episodeIds: List<String>): List<Episode>
    suspend fun findBy(
        ownerId: Long,
        language: Language? = null,
        type: EpisodeType? = null,
        status: EpisodeStatus? = null,
        channelId: String? = null
    ): List<String>

    suspend fun findBy(
        paginatedRequest: PaginatedRequest,
        ownerId: Long,
        language: Language? = null,
        type: EpisodeType? = null,
        status: EpisodeStatus? = null,
        channelId: String? = null
    ): PaginatedResponse<String>

    suspend fun findAll(ownerId: Long?, language: Language?, name: String?, country: Country?): List<Episode>
    suspend fun delete(episodeId: String)
    suspend fun save(episode: Episode)
    suspend fun save(episodes: List<Episode>)
    suspend fun updateItem(episode: Episode)
    suspend fun hasEpisodes(ownerId: Long): Boolean
    suspend fun updateRate(episodeId: String, like: Int, dislike: Int)
    suspend fun plusOneReport(episodeId: String)
    suspend fun updateView(episodeId: String, views: Int = 1)
}
