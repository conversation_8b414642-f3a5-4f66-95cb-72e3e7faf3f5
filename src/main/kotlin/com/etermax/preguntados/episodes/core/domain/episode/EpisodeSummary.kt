package com.etermax.preguntados.episodes.core.domain.episode

import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import java.time.OffsetDateTime

data class EpisodeSummary(
    val id: String,
    val name: String,
    val language: Language,
    val country: Country,
    val type: EpisodeType,
    val startDate: OffsetDateTime?,
    val cover: String,
    val banner: String,
    val ownerId: Long,
    val owner: Profile?,
    val contents: List<String>,
    val views: Long,
    val rate: Rate,
    val status: EpisodeStatus,
    val channelId: String?
) {

    companion object {
        fun from(episode: Episode, profile: Profile?) =
            with(episode) {
                EpisodeSummary(
                    id = id,
                    name = name,
                    language = language,
                    country = country,
                    type = type,
                    startDate = startDate,
                    cover = cover,
                    banner = banner,
                    ownerId = ownerId,
                    owner = profile,
                    contents = contents,
                    views = views,
                    rate = rate,
                    status = status,
                    channelId = channelId
                )
            }
    }
}
