package com.etermax.preguntados.episodes.core.infrastructure.search

import org.apache.lucene.analysis.TokenStream
import org.apache.lucene.analysis.core.KeywordTokenizerFactory
import org.apache.lucene.analysis.core.LetterTokenizerFactory
import org.apache.lucene.analysis.core.LowerCaseFilterFactory
import org.apache.lucene.analysis.custom.CustomAnalyzer
import org.apache.lucene.analysis.miscellaneous.ASCIIFoldingFilterFactory
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute
import java.io.StringReader

class Tokenizer {

    fun tokenizeForExactUsername(query: String): List<String> {
        userNameAnalyzer.tokenStream(null, StringReader(query)).use { stream ->
            return extractTokens(stream)
        }
    }

    fun tokenizeForPrefixes(query: String): List<String> {
        prefixesAnalyzer.tokenStream(null, StringReader(query)).use { stream ->
            return extractTokens(stream)
        }
    }

    private val userNameAnalyzer = CustomAnalyzer.builder()
        .withTokenizer(KeywordTokenizerFactory.NAME)
        .addTokenFilter(ASCIIFoldingFilterFactory.NAME)
        .addTokenFilter(LowerCaseFilterFactory.NAME)
        .build()

    private val prefixesAnalyzer = CustomAnalyzer.builder()
        .withTokenizer(LetterTokenizerFactory.NAME)
        .addTokenFilter(ASCIIFoldingFilterFactory.NAME)
        .addTokenFilter(LowerCaseFilterFactory.NAME)
        .build()

    private fun extractTokens(
        tokenStream: TokenStream
    ): List<String> {
        val tokens = mutableListOf<String>()
        val termAttribute = tokenStream.addAttribute(CharTermAttribute::class.java)

        tokenStream.reset()
        while (tokenStream.incrementToken()) {
            tokens.add(termAttribute.toString())
        }
        tokenStream.end()
        return tokens
    }
}
