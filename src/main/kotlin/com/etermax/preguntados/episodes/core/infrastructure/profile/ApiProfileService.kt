package com.etermax.preguntados.episodes.core.infrastructure.profile

import com.etermax.preguntados.episodes.core.domain.exception.InvalidProfileException
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.external.services.core.domain.api.APIService
import com.etermax.preguntados.external.services.core.domain.api.profile.SlimProfile as ApiSlimProfile

class ApiProfileService(
    private val apiService: APIService
) : ProfileService {

    override suspend fun find(playerId: Long): Profile {
        try {
            val playerProfile = apiService.findSlimProfile(playerId)
            return buildProfile(playerProfile)
        } catch (e: Exception) {
            throw InvalidProfileException(playerId)
        }
    }

    private fun buildProfile(playerProfile: ApiSlimProfile): Profile {
        return with(playerProfile) {
            val facebookData = Profile.FacebookData(facebookId, facebookName, showFacebookPicture, showFacebookName)
            Profile.build(userId, username, country, photoUrl, facebookData, joinDate, restriction)
        }
    }

    override suspend fun findMany(playerIds: List<Long>): List<Profile> {
        return apiService.findSlimProfiles(playerIds).map { buildProfile(it) }
    }
}
