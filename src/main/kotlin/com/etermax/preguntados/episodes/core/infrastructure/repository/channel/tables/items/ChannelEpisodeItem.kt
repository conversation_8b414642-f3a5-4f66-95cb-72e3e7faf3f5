package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items

import com.etermax.preguntados.episodes.core.domain.channel.ChannelEpisode
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.EpisodeChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItemUtils.isEpisodeOrderNormalized
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItemUtils.toNormalizedLength
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@DynamoDbBean
class ChannelEpisodeItem(

    @get:DynamoDbAttribute("episode_id") var episodeId: String = "",

    @get:DynamoDbAttribute(ChannelItemAttributes.CHANNEL_ID) var channelId: String = "",

    @get:DynamoDbSecondarySortKey(indexNames = [ChannelIndexes.BY_ID_AND_EPISODES_ADDED_DATE])
    @get:DynamoDbAttribute(EpisodeChannelItemAttributes.EPISODE_DATE_ADDED)
    var dateAdded: Long = 0,

    @get:DynamoDbSecondarySortKey(indexNames = [ChannelIndexes.BY_ID_AND_EPISODES_CUSTOM_ORDER])
    @get:DynamoDbAttribute("ep_order")
    var episodeOrder: String = ""
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = buildPartitionKey(channelId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = buildSortedKey(episodeId)

    fun isNormalized(): Boolean = this.isEpisodeOrderNormalized()

    fun toDomain(): ChannelEpisode? {
        try {
            return ChannelEpisode(
                channelId = channelId,
                episodeId = episodeId,
                dateAdded = OffsetDateTime.ofInstant(Instant.ofEpochMilli(dateAdded), ZoneOffset.UTC),
                episodeOrder = episodeOrder.toLong()
            )
        } catch (e: Exception) {
            logger.error("Error mapping channel episode for [$channelId]-[$episodeId] to item", e)
            return null
        }
    }

    fun channelIdFromPartitionKey(): String {
        return pk.substring(CHANNEL_PREFIX.length)
    }

    companion object {
        private const val EPISODE_PREFIX = "E#"
        private const val CHANNEL_PREFIX = "C#"

        fun buildPartitionKey(channelId: String) = "$CHANNEL_PREFIX$channelId"
        fun buildSortedKey(episodeId: String) = "$EPISODE_PREFIX$episodeId"
        fun from(episode: ChannelEpisode) = with(episode) {
            ChannelEpisodeItem(
                channelId = channelId,
                episodeId = episodeId,
                dateAdded = dateAdded.toMillis(loseMillis = false),
                episodeOrder = episodeOrder.toNormalizedLength()
            )
        }
    }
}
