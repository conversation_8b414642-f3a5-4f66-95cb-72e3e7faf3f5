package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeSummary
import kotlinx.coroutines.coroutineScope

class FindChallenge(
    val challengeService: ChallengeService,
    val summaryService: SummaryService
) {
    suspend operator fun invoke(actionData: ActionData): ChallengeSummary? = coroutineScope {
        with(actionData) {
            val detail = challengeService.getChallengeDetails(playerId, challengeId)
            summaryService.toChallengeSummary(detail)
        }
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String
    )
}
