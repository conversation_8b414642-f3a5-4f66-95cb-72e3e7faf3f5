package com.etermax.preguntados.episodes.core.domain.channel

data class ChannelSummaryStatistics(val subscribers: Int, val episodes: Int, val unpublishedEpisodes: Int) {
    companion object {
        fun from(statistics: ChannelStatistics, unpublishedEpisodes: Int): ChannelSummaryStatistics {
            return ChannelSummaryStatistics(statistics.subscribers, statistics.episodes, unpublishedEpisodes)
        }
    }
}
