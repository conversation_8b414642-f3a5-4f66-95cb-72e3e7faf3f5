package com.etermax.preguntados.episodes.core.infrastructure.content

import com.etermax.preguntados.episodes.core.domain.content.Content
import com.etermax.preguntados.episodes.core.domain.content.ContentValidationService
import com.etermax.preguntados.episodes.core.infrastructure.content.representation.ContentsRequest
import com.etermax.preguntados.episodes.core.infrastructure.content.representation.ContentsResponse
import com.etermax.preguntados.episodes.core.infrastructure.http.resilientPost
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import org.slf4j.LoggerFactory

class HttpContentValidationService(
    private val client: HttpClient,
    private val resilienceBundle: EndpointResilienceBundle,
    private val adminPassword: String
) : ContentValidationService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun validate(playerId: Long, contents: List<String>): List<Content> {
        return try {
            val body = ContentsRequest(contents)
            client.resilientPost(
                urlString = URL,
                resilienceBundle = resilienceBundle,
                requestBuilder = {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    header("god-password", adminPassword)
                    setBody(body)
                    expectSuccess = true
                }
            ) {
                return@resilientPost makeContent(it)
            }
        } catch (e: Exception) {
            logger.error("Cannot get status for contents $contents of player $playerId", e)
            return emptyList()
        }
    }

    private suspend fun makeContent(response: HttpResponse): List<Content> {
        if (response.status != HttpStatusCode.OK) {
            logger.info("Invalid status ${response.status} for content: $URL")
            return emptyList()
        }
        return response.body<ContentsResponse>().to()
    }

    private companion object {
        const val URL = "/api/content-creation/content-moderation-statuses"
    }
}
