package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.search.data.EpisodeData
import kotlinx.coroutines.future.await
import org.opensearch.client.json.JsonData
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder

/**
 * @deprecated Search based feed will be removed.
 * @see com.etermax.preguntados.episodes.core.domain.search.feed.FeedService
 */
@Deprecated("Search based feed will be removed")
class FeedFallbackSearch(
    private val client: OpenSearchAsyncClient,
    private val indexName: String,
    private val requiredLikesForTopRated: Int,
    private val requiredViewsForTopRated: Int,
    tokenizer: Tokenizer
) : Search, BaseSearch(tokenizer) {

    override suspend fun match(parameters: SearchParameters) = with(parameters) {
        false
    }

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        with(parameters) {
            var request = OpenSearchRequestFactory(indexName, offset, limit)
                .queryByName(name)
                .queryByCountry(country?.name)
                .filterByLanguage(language?.name)
                .filterByType(EpisodeType.PUBLIC)
                .filterByStatus(EpisodeStatus.PUBLISHED)
                .filterByLikesAndViews()
                .sortByRate()
                .excludeEmbedding()
                .build()

            var response = client.search(request, EpisodeData::class.java).await()

            // In countries with small number of episodes remote filterByLikesAndViews
            // FIXME Remove this hardcoded value
            if (response.hits().total().value() < 20) {
                request = OpenSearchRequestFactory(indexName, offset, limit)
                    .queryByName(name)
                    .queryByCountry(country?.name)
                    .filterByLanguage(language?.name)
                    .filterByType(EpisodeType.PUBLIC)
                    .filterByStatus(EpisodeStatus.PUBLISHED)
                    .sortByRate()
                    .excludeEmbedding()
                    .build()

                response = client.search(request, EpisodeData::class.java).await()
            }

            return response.toEpisodes()
        }
    }

    private fun OpenSearchRequestFactory.sortByRate() = this.also {
        sortBy(RATE_FIELD, SortOrder.Desc)
    }

    private fun OpenSearchRequestFactory.filterByLikesAndViews() = this.also {
        val script = listOf(
            "doc['likes'].value + doc['dislikes'].value >= params.target",
            "(doc['views'].size() > 0 ? doc['views'].value : 0) >= params.minViews"
        ).joinToString(" && ")
        filterScript(
            script = script,
            params = mapOf(
                "target" to JsonData.of(requiredLikesForTopRated),
                "minViews" to JsonData.of(requiredViewsForTopRated)
            )
        )
    }
}
