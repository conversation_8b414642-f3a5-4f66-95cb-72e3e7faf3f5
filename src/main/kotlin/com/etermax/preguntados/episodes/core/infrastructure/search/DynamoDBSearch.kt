package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import kotlinx.coroutines.*
import org.slf4j.LoggerFactory

class DynamoDBSearch(
    private val baseSearch: Search,
    private val episodeRepository: EpisodeRepository,
    private val chunkSize: Int,
    private val isDynamoDBSearchEnabled: () -> <PERSON><PERSON><PERSON>,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : Search {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val errorHandler = CoroutineExceptionHandler { _, exception ->
        logger.error("There was an error getting episodes from DynamoDB", exception)
    }

    override suspend fun match(parameters: SearchParameters): <PERSON><PERSON><PERSON> {
        return baseSearch.match(parameters)
    }

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        val result = baseSearch.search(parameters)
        val dynamoDBResult = withContext(dispatcher + SupervisorJob() + errorHandler) {
            result
                .chunked(chunkSize)
                .map { episodes ->
                    async {
                        logger.info(
                            "[DYNAMODB] Searching in DynamoDB for episodes: ${episodes.joinToString(", ") { it.id }}"
                        )
                        if (isDynamoDBSearchEnabled()) {
                            findInDynamoDB(episodes)
                        } else {
                            episodes
                        }
                    }
                }
                .awaitAll()
                .flatten()
                .associateBy { it.id }
        }

        return result.mapNotNull { dynamoDBResult[it.id] }
    }

    private suspend fun findInDynamoDB(episodes: List<Episode>): List<Episode> {
        val findByIds = episodeRepository.findByIds(episodes.map { it.id }.distinct())
        logger.info("[DYNAMODB] Found ${findByIds.size} episodes in DynamoDB: ${findByIds.joinToString(", ") { it.id }}")
        return findByIds
    }
}
