package com.etermax.preguntados.episodes.core.infrastructure.search.filter

import com.etermax.preguntados.episodes.core.domain.search.filter.FilterDataRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key

class DynamoDBFilterDataRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<DynamoDBFilterDataItem>
) : FilterDataRepository, DynamoDBRepository<DynamoDBFilterDataItem>(client, table) {

    override suspend fun save(scope: String, data: ByteArray) {
        val item = DynamoDBFilterDataItem(scope, SdkBytes.fromByteArray(data))
        saveItem(item, DynamoDBFilterDataItem::class.java)
    }

    override suspend fun getByScope(scope: String): ByteArray {
        val item = findItem(
            Key.builder()
                .partitionValue(DynamoDBFilterDataItem.Companion.buildPartitionKey(scope))
                .sortValue(DynamoDBFilterDataItem.Companion.buildSortedKey())
                .build()
        )

        return item?.data?.asByteArray() ?: ByteArray(0)
    }
}
