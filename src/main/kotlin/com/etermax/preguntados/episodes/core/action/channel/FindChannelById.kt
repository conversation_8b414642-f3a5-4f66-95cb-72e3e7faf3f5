package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository

class FindChannelById(
    private val repository: ChannelRepository,
    private val summaryService: SummaryService
) {
    suspend operator fun invoke(channelId: String): ChannelSummary? {
        val channel = repository.findById(channelId) ?: return null
        return summaryService.toChannelSummary(channel)
    }
}
