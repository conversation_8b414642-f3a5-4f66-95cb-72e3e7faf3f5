package com.etermax.preguntados.episodes.core.infrastructure.repository.episode.persistence.episode

import com.etermax.preguntados.episodes.core.domain.episode.Rate
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class RatePersistence(
    @SerialName("l") val likes: Long,
    @SerialName("d") val dislikes: Long
) {
    fun to() = Rate(likes, dislikes)

    companion object {
        fun from(rate: Rate) = RatePersistence(rate.likes, rate.dislikes)
    }
}
