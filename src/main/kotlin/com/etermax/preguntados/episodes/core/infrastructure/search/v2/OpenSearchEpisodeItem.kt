package com.etermax.preguntados.episodes.core.infrastructure.search.v2

import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItemAttributes
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class OpenSearchEpisodeItem(
    @JsonProperty(EpisodeItemAttributes.EPISODE_ID)
    val episodeId: String? = null
)
