package com.etermax.preguntados.episodes.core.infrastructure.notification.representation

import com.etermax.preguntados.episodes.core.domain.notification.Notification
import com.etermax.preguntados.episodes.core.domain.notification.NotificationWithHoursLeft
import com.etermax.preguntados.episodes.core.domain.notification.SimpleNotification
import com.etermax.preguntados.episodes.core.infrastructure.notification.representation.factory.NotificationRequestFactory
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

@Serializable
data class NotificationRequest(
    @SerialName("notification") val notification: NotificationPayloadRequest
) {
    companion object {
        fun from(notification: Notification): NotificationRequest {
            return NotificationRequestFactory.create(notification)
        }

        fun from(notification: NotificationWithHoursLeft): NotificationRequest {
            return NotificationRequestFactory.create(notification)
        }

        fun from(notification: SimpleNotification): NotificationRequest {
            return NotificationRequest(
                NotificationPayloadRequest(
                    localization = LocalizationRequest(
                        title = notification.title,
                        titleArgs = notification.titleArgs,
                        body = notification.body,
                        bodyArgs = notification.bodyArgs
                    ),
                    dataAsString = buildPayload(notification)
                )
            )
        }

        private fun buildPayload(notification: SimpleNotification): String? {
            val payload = notification.payload?.toMutableMap() ?: mutableMapOf()
            notification.title?.let { payload.put("TITLE", it) }
            notification.body?.let { payload.put("BODY", it) }
            notification.titleArgs?.let { payload.put("TITLE_ARGS", it.joinToString(",", "[", "]") { arg -> "\"$arg\"" }) }
            notification.bodyArgs?.let { payload.put("BODY_ARGS", it.joinToString(",", "[", "]") { arg -> "\"$arg\"" }) }
            notification.deeplink?.let {
                val encodedDeeplink = URLEncoder.encode(it, StandardCharsets.UTF_8.toString())
                payload.put("DL", "preguntados://$encodedDeeplink")
            }
            if (payload.isEmpty()) return null
            return payload.entries.joinToString("&") { "data.${it.key}=${it.value}" }
        }
    }
}
