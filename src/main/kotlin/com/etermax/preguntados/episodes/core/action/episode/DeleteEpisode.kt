package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.delete.EpisodeDeleteService
import com.etermax.preguntados.episodes.core.domain.exception.PlayerNotOwnEpisodeException
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class DeleteEpisode(
    private val episodeRepository: EpisodeRepository,
    private val deleteService: EpisodeDeleteService
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData) {
        return with(actionData) {
            val episode = episodeRepository.findById(episodeId)

            if (episode == null) {
                logger.warn("Trying to delete not existing Episode='$episodeId'")
                return
            }

            if (episode.ownerId != playerId) {
                throw PlayerNotOwnEpisodeException(playerId, episode.id)
            }

            logger.info("Delete $episodeId")
            deleteService.delete(episode)
        }
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String
    )
}
