package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.search.data.EpisodeData
import kotlinx.coroutines.future.await
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder

class PopularSearch(
    private val client: OpenSearchAsyncClient,
    private val indexName: String,
    tokenizer: Tokenizer
) : Search, BaseSearch(tokenizer) {

    override suspend fun match(parameters: SearchParameters) = parameters.sort == SortEpisode.PLAYERS

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        with(parameters) {
            val request = OpenSearchRequestFactory(indexName, offset, limit)
                .queryByName(name)
                .queryByCountry(country?.name)
                .filterByLanguage(language?.name)
                .filterByType(EpisodeType.PUBLIC)
                .filterByStatus(EpisodeStatus.PUBLISHED)
                .sortByViews()
                .excludeEmbedding()
                .build()

            val response = client.search(request, EpisodeData::class.java).await()
            return response.toEpisodes()
        }
    }

    private fun OpenSearchRequestFactory.sortByViews() = this.also {
        sortBy(VIEWS_FIELD, SortOrder.Desc)
    }
}
