package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.OpenSearchPlayedRepository

class PlayedSearch(
    private val playedRepository: OpenSearchPlayedRepository,
    private val episodeRepository: EpisodeRepository
) : Search {

    override suspend fun match(parameters: SearchParameters) = parameters.sort == SortEpisode.PLAYED

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        val playerId = parameters.playerId
        if (playerId == null) return emptyList()

        val offset = parameters.offset
        val limit = parameters.limit

        val ids = playedRepository.lastPlayedEpisodesIds(playerId, offset, limit)

        val episodesById = episodeRepository.findByIds(ids).associateBy { it.id }

        return ids.mapNotNull { episodesById[it.removePrefix("E#")] }
    }
}
