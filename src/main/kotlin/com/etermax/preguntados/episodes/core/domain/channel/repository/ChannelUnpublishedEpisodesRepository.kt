package com.etermax.preguntados.episodes.core.domain.channel.repository

import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode

interface ChannelUnpublishedEpisodesRepository {
    suspend fun add(episode: ChannelUnpublishedEpisode)
    suspend fun delete(episode: ChannelUnpublishedEpisode)
    suspend fun deleteAll(channelId: String)
    suspend fun count(channelId: String): Int
}
