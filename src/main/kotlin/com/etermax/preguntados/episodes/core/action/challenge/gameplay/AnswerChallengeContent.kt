package com.etermax.preguntados.episodes.core.action.challenge.gameplay

import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService.ChallengeContext
import com.etermax.preguntados.episodes.core.domain.episode.history.AnswerContentHistoryService
import com.etermax.preguntados.episodes.core.domain.episode.ranking.CalculatorInfo
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingPointsCalculator
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class AnswerChallengeContent(
    private val challengeService: ChallengeService,
    private val rankingRepository: RankingRepository,
    private val calculator: RankingPointsCalculator,
    private val answerContentHistoryService: AnswerContentHistoryService,
    private val clock: Clock
) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)
    private val scope = CoroutineScope(Dispatchers.IO)

    suspend operator fun invoke(actionData: ActionData) {
        val answerLog = if (actionData.isCorrect) "correctly" else "incorrectly"
        logger.info("Player $actionData.playerId answered $answerLog $actionData.contentId from challenge $actionData.challengeId")
        val context = getChallengeContext(actionData)

        if (isContentAlreadyFinished(context)) return

        incrementScore(actionData, context)
        finishChallenge(actionData, context)

        addAnsweredContentToHistory(actionData)
    }

    private suspend fun getChallengeContext(actionData: ActionData): ChallengeContext {
        return challengeService.getChallengeContext(actionData.challengeId, actionData.playerId)
    }

    private fun isContentAlreadyFinished(context: ChallengeContext): Boolean {
        return context.progress?.hasFinishedEpisode ?: false
    }

    private fun isChallengeFinished(context: ChallengeContext): Boolean {
        return context.challenge.endDate < clock.now()
    }

    private suspend fun finishChallenge(
        actionData: ActionData,
        context: ChallengeContext
    ) {
        val isLastContent = context.episode.contents.last() == actionData.contentId
        if (isLastContent) {
            challengeService.finishChallenge(actionData.challengeId, actionData.playerId, context)
        }
    }

    private suspend fun incrementScore(actionData: ActionData, context: ChallengeContext) {
        if (isChallengeFinished(context)) return

        if (actionData.isCorrect) {
            rankingRepository.incrementScore(
                playerId = actionData.playerId,
                domain = Domain(context.scope),
                score = calculateScore(actionData)
            )
        }
    }

    private fun calculateScore(actionData: ActionData): Int {
        return POINTS_PER_CORRECT_ANSWER + getExtraPoints(actionData.elapsedTime, actionData.totalTime)
    }

    private fun getExtraPoints(elapsedTime: Int, totalTime: Int?): Int {
        val info = CalculatorInfo(elapsedTime, totalTime)
        return calculator.calculate(info).coerceAtLeast(ZERO_SCORE)
    }

    private fun addAnsweredContentToHistory(actionData: ActionData) = scope.launch {
        try {
            answerContentHistoryService.addAnsweredContentToHistory(
                actionData.playerId,
                actionData.contentId,
                "episode"
            )
        } catch (e: Exception) {
            logger.error(
                "Error in fire and forget answer content history for player ${actionData.playerId} and content ${actionData.contentId}",
                e
            )
        }
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String,
        val contentId: String,
        val isCorrect: Boolean,
        val elapsedTime: Int,
        val totalTime: Int?
    )

    private companion object {
        const val POINTS_PER_CORRECT_ANSWER = 50
        const val ZERO_SCORE = 0
    }
}
