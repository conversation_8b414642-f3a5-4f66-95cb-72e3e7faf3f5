package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.search.feed.Source
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceRequest
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem.Companion.EPISODE_PREFIX
import org.slf4j.LoggerFactory
import kotlin.time.ExperimentalTime
import kotlin.time.measureTimedValue

@OptIn(ExperimentalTime::class)
class EpisodeHydratorSource(
    private val source: Source<String>,
    private val repository: EpisodeRepository
) : Source<Episode> {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val prefix = EPISODE_PREFIX

    override suspend fun fetch(request: SourceRequest): SourceResponse<Episode> {
        logger.debug(
            "[FEED] Fetching episode IDs with range: offset={}, limit={}",
            request.range.offset,
            request.range.limit
        )

        val rawResponse = source.fetch(request)
        val ids = rawResponse.items.map { prefix + it }
        logger.debug("[FEED] Raw IDs: {}. Hydrated IDs: {}", rawResponse.items, ids)

        val (episodes, findByTime) = measureTimedValue {
            repository.findByIds(ids)
        }
        logger.info("[FEED] Hydrated {} episodes from {} IDs for player {}", episodes.size, ids.size, request.userId)
        logger.debug("[LATENCY] episodes findByTime={}ms", findByTime)
        logger.debug("[FEED] Hydrated {} episodes from {} IDs", episodes.size, ids.size)

        return SourceResponse(episodes, rawResponse.fetchCursor)
    }
}
