package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.analytics.actions.TrackEpisodeAnalytics
import com.etermax.preguntados.analytics.service.ViewedEvent
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeDetails
import com.etermax.preguntados.episodes.core.domain.episode.progress.DeliveryProgress
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentRepository
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingService
import com.etermax.preguntados.episodes.core.domain.episode.rate.RateRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class FindEpisodeDetails(
    private val rateRepository: RateRepository,
    private val progressContentRepository: ProgressContentRepository,
    private val rankingService: RankingService,
    private val trackMetric: TrackEpisodeAnalytics
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): EpisodeDetails = coroutineScope {
        with(actionData) {
            logger.info("Player $playerId find details from episode $episodeId")

            val rate = async { rateRepository.findBy(playerId, episodeId) }
            val content = async { progressContentRepository.findBy(episodeId, playerId) }
            val ranking = async { rankingService.findRanking(playerId, domain) }
            val rankingWithFriends = async { rankingService.findRankingWithFriends(playerId, domain) }

            val progressContent = content.await()

            trackMetric(ViewedEvent(playerId, episodeId))

            EpisodeDetails(
                ranking = ranking.await(),
                rankingWithFriends = rankingWithFriends.await(),
                rate = rate.await(),
                hasPlayed = progressContent?.lastContentId != null,
                deliveryProgress = progressContent?.let { DeliveryProgress.from(it) } ?: DeliveryProgress.EMPTY
            )
        }
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String
    ) {
        val domain = Domain(episodeId)
    }
}
