package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.*
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNameEmptyException
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNameNotAllowedException
import com.etermax.preguntados.episodes.core.domain.exception.MandatoryAttributeValidateException
import com.etermax.preguntados.episodes.core.domain.moderation.ModerationService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import java.time.OffsetDateTime

class CreateEpisode(
    private val episodeRepository: EpisodeRepository,
    private val uuidSequencer: UUIDSequencer,
    private val profileService: ProfileService,
    private val moderationService: ModerationService,
    private val newEpisodeRepository: NewEpisodeRepository,
    private val channelEpisodesService: ChannelEpisodesService,
    private val clock: Clock
) {
    suspend operator fun invoke(actionData: ActionData): EpisodeSummary {
        with(actionData) {
            validate(actionData)

            var episode = Episode(
                id = uuidSequencer.next(),
                name = name,
                language = language,
                country = country,
                type = type ?: EpisodeType.PUBLIC,
                startDate = getStartDate(),
                cover = cover ?: EMPTY,
                banner = banner ?: EMPTY,
                ownerId = playerId,
                contents = contents,
                channelId = null,
                rate = Rate.empty(),
                reports = NO_REPORTS,
                views = NO_VIEWS,
                status = status,
                quality = NO_QUALITY
            )

            if (channelId != null) {
                val canAddToChannel = episode.isDraft && channelEpisodesService.canAddToChannel(channelId, episode)
                if (canAddToChannel) {
                    episode = episode.assignChannel(channelId)
                }
            }

            persist(episode)
            return episode.toSummary()
        }
    }

    private fun ActionData.getStartDate() =
        (startDate?.let { OffsetDateTime.parse(it) } ?: clock.now())
            .takeIf { status != EpisodeStatus.DRAFT }

    private suspend fun validate(actionData: ActionData) {
        with(actionData) {
            if (name.trim().isEmpty()) {
                throw EpisodeNameEmptyException(name)
            }

            if (!moderationService.isTextAllowed(playerId, language, name)) {
                throw EpisodeNameNotAllowedException(name)
            }

            if (attributeMandatoryFails(cover)) {
                throw MandatoryAttributeValidateException("cover")
            }

            if (attributeMandatoryFails(banner)) {
                throw MandatoryAttributeValidateException("banner")
            }
        }
    }

    private suspend fun ActionData.persist(episode: Episode) {
        episodeRepository.save(episode)
        newEpisodeRepository.add(playerId, episode)
    }

    private suspend fun Episode.toSummary(): EpisodeSummary {
        val profile = profileService.find(ownerId)
        return EpisodeSummary.from(this, profile)
    }

    private fun ActionData.attributeMandatoryFails(cover: String?) =
        cover.isNullOrBlank() && status != EpisodeStatus.DRAFT

    data class ActionData(
        val playerId: Long,
        val name: String,
        val language: Language,
        val country: Country,
        val type: EpisodeType?,
        val startDate: String?,
        val cover: String?,
        val banner: String?,
        val contents: List<String>,
        val status: EpisodeStatus,
        val channelId: String?
    )

    private companion object {
        const val NO_REPORTS = 0L
        const val NO_VIEWS = 0L
        const val NO_QUALITY = 0
        const val EMPTY = ""
    }
}
