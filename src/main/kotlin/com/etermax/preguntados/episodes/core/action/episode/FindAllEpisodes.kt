package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language

class FindAllEpisodes(
    private val episodeRepository: EpisodeRepository
) {

    suspend operator fun invoke(actionData: ActionData): List<EpisodeSummary> {
        return with(actionData) {
            if (!atLeastOneFilter()) {
                return emptyList()
            }

            episodeRepository.findAll(ownerId, language, name, country)
                .map { EpisodeSummary.from(it, null) }
        }
    }

    private fun ActionData.atLeastOneFilter() = listOf(ownerId, language, name, country).any { it != null }

    data class ActionData(
        val ownerId: Long? = null,
        val language: Language? = null,
        val name: String? = null,
        val country: Country? = null
    )
}
