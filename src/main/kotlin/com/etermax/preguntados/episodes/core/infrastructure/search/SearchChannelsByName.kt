package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.search.data.ChannelData
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.future.await
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder
import org.opensearch.client.opensearch.core.SearchRequest
import org.opensearch.client.opensearch.core.SearchResponse

class SearchChannelsByName(
    private val searchClient: OpenSearchAsyncClient,
    val repository: ChannelRepository,
    private val indexName: String,
    private val tokenizer: Tokenizer
) {
    suspend fun search(name: String, language: Language?, offset: Int, limit: Int): List<Channel> {
        val request = buildRequest(name, language, offset, limit)
        val response = searchClient.search(request, ChannelData::class.java).await()
        val ids = response.toIds()
        val channels = repository.findById(ids)
        return channels
    }

    fun buildRequest(name: String, language: Language?, offset: Int, limit: Int): SearchRequest =
        OpenSearchRequestFactory(indexName, offset, limit)
            .mustPrefixesFuzziness(ChannelItemAttributes.NAME, tokenizer.tokenizeForPrefixes(name))
            .filterByLanguage(language)
            .filterTerm(ChannelItemAttributes.TYPE, ChannelType.PUBLIC.name)
            .sortBy(ChannelItemAttributes.CREATION_DATE, SortOrder.Desc)
            .excludeSources()
            .filterGreaterThanOrEqual(ChannelItemAttributes.EPISODES_COUNT, 2)
            .build()

    private fun OpenSearchRequestFactory.filterByLanguage(language: Language?) = this.also {
        language?.let {
            filterTerm(ChannelItemAttributes.LANGUAGE, it.name)
        }
    }

    fun SearchResponse<ChannelData>.toIds(): List<String> {
        return hits().hits().mapNotNull { hit ->
            hit.id()
        }
    }
}
