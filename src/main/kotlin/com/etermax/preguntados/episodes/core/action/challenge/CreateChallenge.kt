package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.*
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer.Status
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer
import java.time.Duration

class CreateChallenge(
    private val challengesRepository: ChallengeRepository,
    private val challengePlayersRepository: ChallengePlayerRepository,
    private val episodesRepository: EpisodeRepository,
    private val uuidSequencer: UUIDSequencer,
    private val summaryService: SummaryService,
    val clock: Clock,
    val ttl: Duration
) {
    suspend operator fun invoke(actionData: ActionData): ChallengeSummary {
        return with(actionData) {
            val episode = episodesRepository.findById(episodeId) ?: throw EpisodeNotFoundException(episodeId)

            val now = clock.now()
            val challenge = Challenge(
                id = uuidSequencer.next(),
                createDate = now,
                startDate = now,
                endDate = now.plusSeconds(durationInSeconds),
                expireDate = now.plusSeconds(durationInSeconds).plusSeconds(ttl.toSeconds()),
                ownerId = playerId,
                reference = EpisodeReference(episode.id, episode.name, episode.cover, episode.contents)
            )

            challengesRepository.save(challenge)
            val owner = ChallengePlayer(
                playerId = playerId,
                challengeId = challenge.id,
                status = Status.PENDING,
                challenge.expireDate
            )
            challengePlayersRepository.save(owner)

            ChallengeSummary(
                id = challenge.id,
                episode = summaryService.toEpisodeSummary(episode)!!,
                startDate = challenge.startDate,
                endDate = challenge.endDate,
                expireDate = challenge.expireDate,
                ownerId = challenge.ownerId,
                ranking = DeliveryRanking.EMPTY,
                players = summaryService.toChallengePlayersSummary(listOf(owner))
            )
        }
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String,
        val durationInSeconds: Long
    )
}
