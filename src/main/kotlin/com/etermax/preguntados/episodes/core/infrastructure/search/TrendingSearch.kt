package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.search.data.PlayerEpisodeData
import kotlinx.coroutines.future.await
import org.opensearch.client.json.JsonData
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder
import org.opensearch.client.opensearch._types.aggregations.Aggregation
import org.opensearch.client.opensearch._types.aggregations.TermsAggregation
import org.opensearch.client.opensearch.core.SearchResponse
import java.time.temporal.ChronoUnit.DAYS

class TrendingSearch(
    private val client: OpenSearchAsyncClient,
    private val indexName: String,
    private val episodeRepository: EpisodeRepository,
    private val clock: Clock
) : Search {

    override suspend fun match(parameters: SearchParameters) = parameters.sort == SortEpisode.TRENDING

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        with(parameters) {
            val oneWeekBefore = clock.now().minus(SEVEN, DAYS).toMillis()
            val request = OpenSearchRequestFactory(indexName, offset, NO_LIMIT)
                .filterByLanguage(language?.name)
                .filterGreaterOrEqualsThan(oneWeekBefore)
                .addAggregation(POPULAR) { aggregation(parameters) }
                .build()

            val response = client.search(request, PlayerEpisodeData::class.java).await()
            val ids = response.toEpisodeIds()

            if (ids.isEmpty()) return emptyList()

            val episodesById = episodeRepository.findByIds(ids).associateBy { it.id }
            return ids.mapNotNull { episodesById[it.removePrefix("E#")] }
        }
    }

    private fun OpenSearchRequestFactory.filterGreaterOrEqualsThan(since: Long) = this.also {
        filterRange(TIMESTAMP) {
            it.gte(JsonData.of(since))
        }
    }

    private fun OpenSearchRequestFactory.filterByLanguage(language: String?) = this.also {
        language?.let {
            filterTerm(LANGUAGE_FIELD, it)
        }
    }

    private fun aggregation(parameters: SearchParameters): Aggregation =
        Aggregation.Builder().terms(
            TermsAggregation
                .Builder()
                .field("episode_id")
                .size(if (parameters.playerId == null) parameters.limit else 10)
                .order(mapOf("_count" to SortOrder.Desc))
                .build()
        ).build()

    private fun SearchResponse<PlayerEpisodeData>.toEpisodeIds(): List<String> {
        return aggregations()[POPULAR]?.sterms()?.buckets()?.array()?.mapNotNull {
            it.key()
        } ?: emptyList()
    }

    private companion object {
        const val NO_LIMIT = 0
        const val SEVEN = 7L
        const val POPULAR = "popular"
        const val TIMESTAMP = "timestamp"
        const val LANGUAGE_FIELD = "language"
    }
}
