package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import java.time.Duration

class RedisChannelUnpublishedEpisodesRepository(
    private val redis: RedisAsyncCommands<String, String>,
    private val scoreCalculator: () -> Double,
    private val ttl: Duration,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : ChannelUnpublishedEpisodesRepository {

    override suspend fun add(episode: ChannelUnpublishedEpisode) {
        withContext(dispatcher) {
            val key = buildKey(episode.channelId)
            val score = scoreCalculator()
            redis.zadd(key, score, episode.episodeId).await()
            redis.expire(key, ttl)
        }
    }

    override suspend fun delete(episode: ChannelUnpublishedEpisode) {
        withContext(dispatcher) {
            val key = buildKey(episode.channelId)
            redis.zrem(key, episode.episodeId).await()
        }
    }

    override suspend fun deleteAll(channelId: String) {
        withContext(dispatcher) {
            val key = buildKey(channelId)
            redis.del(key).await()
        }
    }

    override suspend fun count(channelId: String): Int {
        return withContext(dispatcher) {
            val key = buildKey(channelId)
            redis.zcard(key).await().toInt()
        }
    }

    private fun buildKey(channelId: String): String {
        return "$KEY:$channelId"
    }

    private companion object {
        const val KEY = "pr:e:c:ue"
    }
}
