package com.etermax.preguntados.episodes.core.domain.challenge

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentService
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingService
import com.etermax.preguntados.episodes.core.domain.exception.ChallengeNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope

class ChallengeService(
    val progressContentService: ProgressContentService,
    val rankingService: RankingService,
    val challengesRepository: ChallengeRepository,
    val challengePlayerRepository: ChallengePlayerRepository,
    val episodesRepository: EpisodeRepository
) {

    @Suppress("UNCHECKED_CAST")
    suspend fun getChallengeDetails(playerId: Long, challengeId: String): ChallengeDetails = coroutineScope {
        val challenge =
            challengesRepository.find(challengeId) ?: throw ChallengeNotFoundException(challengeId)
        val episode =
            episodesRepository.findById(challenge.reference.episodeId) ?: throw EpisodeNotFoundException(
                challenge.reference.episodeId
            )

        val scope = "${episode.id}_$challengeId"
        val (players, ranking) = awaitAll(
            async { challengePlayerRepository.findAllByChallengeId(challengeId) },
            async { rankingService.findRanking(playerId, Domain(scope)) }
        )

        ChallengeDetails(
            playerId = playerId,
            challenge = challenge,
            episode = episode,
            players = players as List<ChallengePlayer>,
            ranking = ranking as DeliveryRanking
        )
    }

    @Suppress("UNCHECKED_CAST")
    suspend fun getChallengeDetailsByUser(playerId: Long): List<ChallengeDetails> = coroutineScope {
        challengePlayerRepository.findAllByPlayerId(playerId).map {
            async { getChallengeDetails(playerId, it.challengeId) }
        }.awaitAll()
    }

    suspend fun getChallengeContext(
        challengeId: String,
        playerId: Long
    ): ChallengeContext {
        val challenge = challengesRepository.find(challengeId) ?: throw ChallengeNotFoundException(challengeId)
        val episode = episodesRepository.findById(challenge.reference.episodeId) ?: throw EpisodeNotFoundException(
            challenge.reference.episodeId
        )

        episode.overrideWith(challenge)

        val scope = "${episode.id}_$challengeId"
        val progress = progressContentService.findProgress(scope, playerId)

        return ChallengeContext(scope, episode, challenge, progress)
    }

    suspend fun ensurePlayerInitialized(challengeId: String, playerId: Long, context: ChallengeContext) {
        if (context.progress == null) {
            rankingService.incrementScore(playerId, Domain(context.scope), INITIAL_SCORE)
            challengePlayerRepository.save(ChallengePlayer(playerId, challengeId, ChallengePlayer.Status.PLAYING))
        }
    }

    fun calculateRemainingContent(context: ChallengeContext): List<String> {
        return if (context.progress == null || context.progress.hasFinishedEpisode) {
            context.episode.contents
        } else {
            val lastContentIndex = context.episode.contents.indexOf(context.progress.lastContentId)
            val remainingContent = context.episode.contents.drop(lastContentIndex + 1)
            return remainingContent
        }
    }

    suspend fun registerProgress(playerId: Long, contentId: String, context: ChallengeContext) {
        progressContentService.registerProgress(
            context.scope,
            playerId,
            contentId,
            context.episode.language.name,
            context.progress?.hasFinishedEpisode ?: false
        )
    }

    suspend fun finishChallenge(challengeId: String, playerId: Long, context: ChallengeContext) {
        progressContentService.registerProgress(
            context.scope,
            playerId,
            context.episode.contents.last(),
            context.episode.language.name,
            true
        )
        challengePlayerRepository.save(ChallengePlayer(playerId, challengeId, ChallengePlayer.Status.FINISHED))
    }

    private companion object {
        const val INITIAL_SCORE = 0
    }

    data class ChallengeContext(
        val scope: String,
        val episode: Episode,
        val challenge: Challenge,
        val progress: ProgressContent?
    )
}
