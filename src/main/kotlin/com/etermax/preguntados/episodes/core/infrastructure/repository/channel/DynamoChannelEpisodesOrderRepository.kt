package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.episode.ChannelEpisodeOrder
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesOrderRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.ChannelEpisodeOrderItem
import kotlinx.coroutines.future.await
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.model.IgnoreNullsMode
import software.amazon.awssdk.enhanced.dynamodb.model.TransactUpdateItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest

class DynamoChannelEpisodesOrderRepository(
    private val client: DynamoDbEnhancedAsyncClient,
    private val table: DynamoDbAsyncTable<ChannelEpisodeOrderItem>
) : ChannelEpisodesOrderRepository {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun put(channelEpisodesOrder: List<ChannelEpisodeOrder>) {
        channelEpisodesOrder.chunked(25).map { chunk ->
            try {
                val request = TransactWriteItemsEnhancedRequest.builder()

                chunk.forEach {
                    val item = ChannelEpisodeOrderItem.from(it)
                    val transactItem = TransactUpdateItemEnhancedRequest
                        .builder(ChannelEpisodeOrderItem::class.java)
                        .ignoreNullsMode(IgnoreNullsMode.DEFAULT)
                        .item(item)
                        .build()

                    request.addUpdateItem(table, transactItem)
                }

                client.transactWriteItems(request.build()).await()
            } catch (e: Exception) {
                logger.warn("Error updating channel episodes order: ${e.message}", e)
            }
        }
    }
}
