package com.etermax.preguntados.episodes.core.infrastructure.repository.episode

import com.etermax.preguntados.episodes.core.domain.episode.EpisodePlayersByOwnerRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import io.lettuce.core.Range
import io.lettuce.core.cluster.api.async.RedisClusterAsyncCommands
import kotlinx.coroutines.future.await
import java.time.Duration

class RedisEpisodePlayersByOwnerRepository(
    private val redisConnection: RedisClusterAsyncCommands<String, String>,
    private val clock: Clock,
    private val expirationTime: Duration,
    private val isEnabled: Boolean
) : EpisodePlayersByOwnerRepository {
    override suspend fun get(ownerId: Long): List<Long> {
        if (!isEnabled) return emptyList()

        val now = clock.now()
        val key = buildKey(ownerId)
        return redisConnection.zrevrangebyscore(
            key,
            Range.create(now.minus(expirationTime).toEpochSecond(), now.toEpochSecond())
        ).await()
            ?.map { it.toLong() } ?: emptyList()
    }

    override suspend fun addPlayer(ownerId: Long, playerId: Long) {
        if (!isEnabled) return

        val now = clock.now()
        val key = buildKey(ownerId)
        redisConnection.zadd(key, now.toEpochSecond().toDouble(), playerId.toString()).await()
        redisConnection.zremrangebyscore(key, Range.create(0, now.minus(expirationTime).toEpochSecond())).await()
        redisConnection.expire(key, expirationTime).await()
    }

    private fun buildKey(ownerId: Long): String {
        return "pr:e:pbo:$ownerId"
    }
}
