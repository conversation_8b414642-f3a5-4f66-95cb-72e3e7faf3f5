package com.etermax.preguntados.episodes.core.domain.episode.delete

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.episode.ChannelEpisodeDeleteRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.time.Clock
import org.slf4j.LoggerFactory

class EpisodeDeleteService(
    private val channelsRepository: ChannelRepository,
    private val channelEpisodeDeleteRepository: ChannelEpisodeDeleteRepository,
    private val channelUnpublishedEpisodesRepository: ChannelUnpublishedEpisodesRepository,
    private val clock: Clock
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    suspend fun delete(episode: Episode) {
        val channel = findChannelFor(episode)?.let { removeEpisodeFrom(it) }
        channelEpisodeDeleteRepository.delete(episode.id, channel)
        removeFromUnpublished(episode)
    }

    private suspend fun findChannelFor(episode: Episode): Channel? {
        if (!episode.isPublished) return null

        return episode.channelId?.let { channelId ->
            val channel = channelsRepository.findById(channelId)
            if (channel == null) {
                logger.warn("Episode '${episode.id}' has channel '$channelId' but channel not found")
            }
            channel
        }
    }

    private fun removeEpisodeFrom(channel: Channel): Channel {
        return channel.removeEpisodes(1).setLastModificationDate(clock.now())
    }

    private suspend fun removeFromUnpublished(episode: Episode) {
        episode.channelId?.let { channelId ->
            val item = ChannelUnpublishedEpisode(channelId, episode.id)
            channelUnpublishedEpisodesRepository.delete(item)
        }
    }
}
