package com.etermax.preguntados.episodes.core.domain.episode.progress

class ProgressContentService(
    private val progressContentRepository: ProgressContentRepository
) {

    suspend fun registerProgress(
        episodeId: String,
        playerId: Long,
        contentId: String,
        language: String,
        isEpisodeFinished: Boolean
    ) {
        val progressContent = ProgressContent(
            episodeId = episodeId,
            playerId = playerId,
            lastContentId = contentId,
            language = language,
            hasFinishedEpisode = isEpisodeFinished
        )
        progressContentRepository.save(progressContent)
    }

    suspend fun findProgress(episodeId: String, playerId: Long) = progressContentRepository.findBy(episodeId, playerId)
}
