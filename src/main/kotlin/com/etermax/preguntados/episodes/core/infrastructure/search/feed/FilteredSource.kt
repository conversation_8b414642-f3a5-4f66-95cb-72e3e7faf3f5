package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import com.etermax.preguntados.episodes.core.domain.search.filter.Filter
import org.slf4j.LoggerFactory

class FilteredSource(
    private val source: Source<String>,
    private val filter: Filter
) : Source<String> {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<String> {
        val originalOffset = request.range.offset
        val originalLimit = request.range.limit

        logger.debug("[FEED] Starting fetch with offset={}, limit={}", originalOffset, originalLimit)

        if (originalLimit <= 0) {
            logger.debug("[FEED] Limit is 0 or less, returning empty response")
            return emptyResponse(request.fetchCursor)
        }

        if (request.fetchCursor != null && request.fetchCursor.exhausted) {
            logger.debug("[FEED] Fetch called with exhausted cursor, returning empty response")
            return emptyResponse(request.fetchCursor)
        }

        var sourceFetchCursor = (request.fetchCursor as? FilteredFetchCursor)?.delegate

        val result = mutableListOf<String>()
        var offset = originalOffset
        var remainingLimit = originalLimit
        var isSourceExhausted = false

        while (result.size < originalLimit && !isSourceExhausted) {
            val batchRequest =
                request.copy(range = OffsetLimit(offset, remainingLimit), fetchCursor = sourceFetchCursor)

            logger.debug("[FEED] Fetching from source with offset={}, limit={}", offset, remainingLimit)

            val sourceResponse = source.fetch(batchRequest)
            isSourceExhausted = sourceResponse.isEmpty()

            logger.debug("[FEED] Source returned {} items", sourceResponse.items.size)

            if (!isSourceExhausted) {
                val filteredItems = filter.apply(sourceResponse.items)
                logger.debug("[FEED] {} items passed the filter", filteredItems.size)

                result.addAll(filteredItems)

                offset += sourceResponse.size()
                remainingLimit = originalLimit - result.size
                sourceFetchCursor = sourceResponse.fetchCursor
            }
        }

        logger.debug("[FEED] Returning {} filtered items (exhausted={})", result.size, isSourceExhausted)

        return SourceResponse(result, FilteredFetchCursor(sourceFetchCursor, isSourceExhausted))
    }
}
