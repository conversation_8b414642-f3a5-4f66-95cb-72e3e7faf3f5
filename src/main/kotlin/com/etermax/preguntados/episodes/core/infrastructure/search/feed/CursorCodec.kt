package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.modules.SerializersModule
import kotlinx.serialization.modules.polymorphic
import kotlinx.serialization.modules.subclass
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.util.*
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream

class CursorCodec {
    private val json = Json {
        serializersModule = SerializersModule {
            polymorphic(FetchCursor::class) {
                subclass(OffsetFetchCursor::class)
                subclass(FilteredFetchCursor::class)
                subclass(MixedSimilarityFetchCursor::class)
                subclass(DistributedFetchCursor::class)
                subclass(FourByOneFetchCursor::class)
            }
        }
        classDiscriminator = "type" // injects "type" field into JSON
    }

    fun decode(token: String?, debug: Boolean): FetchCursor? {
        var paginationToken = token
        if (!debug) {
            paginationToken = token?.let { gzipDecompress(it) }
        }

        return paginationToken?.let { json.decodeFromString(FetchCursor.serializer(), it) }
    }

    fun encode(cursor: FetchCursor?, debug: Boolean): String? {
        var paginationToken = cursor?.let {
            json.encodeToString(FetchCursor.serializer(), it)
        }

        if (!debug) {
            paginationToken = paginationToken?.let { gzipCompress(it) }
        }

        return paginationToken
    }

    private fun gzipCompress(json: String): String {
        val byteStream = ByteArrayOutputStream()
        GZIPOutputStream(byteStream).bufferedWriter(Charsets.UTF_8).use { it.write(json) }
        return Base64.getEncoder().encodeToString(byteStream.toByteArray())
    }

    private fun gzipDecompress(compressed: String): String {
        val bytes = Base64.getDecoder().decode(compressed)
        return GZIPInputStream(ByteArrayInputStream(bytes)).bufferedReader(Charsets.UTF_8).use { it.readText() }
    }
}
