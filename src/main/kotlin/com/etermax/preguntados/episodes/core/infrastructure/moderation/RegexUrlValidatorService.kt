package com.etermax.preguntados.episodes.core.infrastructure.moderation

import com.etermax.preguntados.episodes.core.domain.moderation.UrlValidatorService

class RegexUrlValidatorService : UrlValidatorService {
    override fun isValid(url: String): <PERSON><PERSON>an {
        return REGEX.matches(url)
    }

    private companion object {
        val REGEX = Regex("^(https?://)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+([/?#].*)?$")
    }
}
