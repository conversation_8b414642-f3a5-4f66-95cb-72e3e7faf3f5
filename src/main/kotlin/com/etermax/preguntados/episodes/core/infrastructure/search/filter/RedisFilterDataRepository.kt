package com.etermax.preguntados.episodes.core.infrastructure.search.filter

import com.etermax.preguntados.episodes.core.domain.search.filter.FilterDataRepository
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import java.time.Duration
import java.util.*

class RedisFilterDataRepository(
    private val redis: RedisAsyncCommands<String, String>,
    private val ttl: Duration,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : FilterDataRepository {
    override suspend fun save(scope: String, data: ByteArray) {
        withContext(dispatcher) {
            val key = createKey(scope)
            redis.setex(key, ttl.seconds, encode(data)).await()
        }
    }

    override suspend fun getByScope(scope: String): ByteArray {
        return withContext(dispatcher) {
            val key = createKey(scope)
            redis.get(key).await()?.let { decode(it) } ?: ByteArray(0)
        }
    }

    private fun decode(data: String): ByteArray {
        return Base64.getDecoder().decode(data)
    }

    private fun encode(data: ByteArray): String {
        return Base64.getEncoder().encodeToString(data)
    }

    private fun createKey(scope: String) = "$KEY:$scope"

    private companion object {
        const val KEY = "fr:e:f"
    }
}
