package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.exception.SearchException
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.search.BaseSearch.Companion.VIEWS_FIELD
import com.etermax.preguntados.episodes.core.infrastructure.search.OpenSearchRequestFactory
import com.etermax.preguntados.episodes.core.infrastructure.search.v2.OpenSearchEpisodeEmbeddingItem
import com.etermax.preguntados.episodes.core.infrastructure.search.v2.OpenSearchEpisodeItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.future.await
import org.opensearch.client.json.JsonData
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.OpenSearchException
import org.opensearch.client.opensearch.core.GetRequest
import org.opensearch.client.opensearch.core.GetResponse
import org.opensearch.client.opensearch.core.SearchRequest
import org.opensearch.client.opensearch.core.SearchResponse
import org.slf4j.LoggerFactory
import java.time.Instant
import java.time.temporal.ChronoUnit

class SimilarityEpisodeSource(
    private val client: OpenSearchAsyncClient,
    private val episodesIndexName: String
) : Source<String> {

    private val logger = LoggerFactory.getLogger(SimilarityEpisodeSource::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<String> {
        request as SimilarityEpisodeSourceRequest

        if (request.range.limit <= 0) {
            logger.debug("[FEED] Fetch called with non-positive limit: {}", request.range.limit)
            return emptyResponse(request.fetchCursor)
        }

        val offsetCursor = request.fetchCursor as? OffsetFetchCursor
        if (offsetCursor != null && offsetCursor.exhausted) {
            logger.debug("[FEED] Fetch called with exhausted cursor, returning empty response")
            return emptyResponse(offsetCursor)
        }

        val effectiveRange = applyFetchCursor(request.range, offsetCursor)
        logger.debug(
            "[FEED] Effective range calculated: offset={}, limit={}",
            effectiveRange.offset,
            effectiveRange.limit
        )

        val embedding = fetchEmbeddingForReference(request.reference)
        if (embedding == null) {
            logger.debug("[FEED] No embedding found for reference id: {}", request.reference)
            return emptyResponse(request.fetchCursor)
        }

        logger.debug(
            "[FEED] Fetching similar episodes for reference '{}' with offset {} and limit {}",
            request.reference,
            effectiveRange.offset,
            effectiveRange.limit
        )

        val searchRequest =
            buildSearchRequest(request, embedding, request.context, effectiveRange.offset, effectiveRange.limit)

        val searchResponse = try {
            client.search(searchRequest, OpenSearchEpisodeItem::class.java).await()
        } catch (ex: OpenSearchException) {
            logger.error("[FEED] OpenSearchException during search for reference '{}'", request.reference, ex)
            throw SearchException("Error during similarity search", ex)
        }

        val items = searchResponse.extractEpisodeIds()
        logger.debug("[FEED] Search returned {} episode IDs for reference '{}'", items.size, request.reference)
        logger.info("[FEED] Similarity returned {} episodes IDs for player {}", items.size, request.userId)

        val nextOffset = effectiveRange.offset + items.size

        val exhausted = items.size < effectiveRange.limit
        return SourceResponse(items, OffsetFetchCursor(nextOffset, exhausted))
    }

    private fun applyFetchCursor(range: OffsetLimit, offsetCursor: OffsetFetchCursor?): OffsetLimit {
        return if (offsetCursor != null) {
            OffsetLimit(offsetCursor.offset, range.limit).also {
                logger.debug("[FEED] Applied fetch cursor offset: {}", it.offset)
            }
        } else {
            range.also {
                logger.debug("[FEED] No fetch cursor applied, using original range offset: {}", it.offset)
            }
        }
    }

    private suspend fun fetchEmbeddingForReference(referenceId: String): FloatArray? {
        val request = GetRequest.Builder()
            .id(referenceId)
            .index(episodesIndexName)
            .sourceIncludes(listOf(EpisodeItemAttributes.EPISODE_ID, EpisodeItemAttributes.EMBEDDING))
            .build()

        return try {
            val response: GetResponse<OpenSearchEpisodeEmbeddingItem> =
                client.get(request, OpenSearchEpisodeEmbeddingItem::class.java).await()
            response.source()?.embedding.also {
                if (it != null) {
                    logger.debug("[FEED] Fetched embedding for reference ID '{}'", referenceId)
                } else {
                    logger.debug("[FEED] No embedding found in source document for reference ID '{}'", referenceId)
                }
            }
        } catch (ex: OpenSearchException) {
            logger.error("[FEED] Failed to fetch embedding for reference ID '{}'", referenceId, ex)
            throw SearchException("Error fetching embeddings", ex)
        }
    }

    private fun buildSearchRequest(
        request: SimilarityEpisodeSourceRequest,
        embedding: FloatArray,
        excludedIds: List<String>,
        offset: Int,
        limit: Int
    ): SearchRequest {
        return OpenSearchRequestFactory(episodesIndexName, offset, limit)
            .filterByLanguage(request.language)
            .filterByType(EpisodeType.PUBLIC)
            .filterByStatus(EpisodeStatus.PUBLISHED)
            .filterByMinViews(5)
            .filterOlderThan(sevenDaysAgo())
            .filterNotById(excludedIds)
            .filterByEmbedding(embedding, 100)
            .includeEpisodeIdOnly()
            .build()
    }

    private fun OpenSearchRequestFactory.filterByLanguage(language: Language?) = this.also {
        language?.let { filterTerm(EpisodeItemAttributes.LANGUAGE, it.name) }
    }

    private fun OpenSearchRequestFactory.filterByStatus(status: EpisodeStatus?) = this.also {
        status?.let { filterTerm(EpisodeItemAttributes.STATUS, it.name.lowercase()) }
    }

    private fun OpenSearchRequestFactory.filterByType(type: EpisodeType?) = this.also {
        type?.let { filterTerm(EpisodeItemAttributes.TYPE, it.name) }
    }

    private fun OpenSearchRequestFactory.filterNotById(ids: List<String>) = this.also {
        mustNotIds(ids)
    }

    private fun OpenSearchRequestFactory.filterByEmbedding(embedding: FloatArray, limit: Int) = this.also {
        shouldKnn(EpisodeItemAttributes.EMBEDDING, embedding, limit)
    }

    private fun OpenSearchRequestFactory.includeEpisodeIdOnly() = this.also {
        includeSourceFields(EpisodeItemAttributes.EPISODE_ID)
    }

    private fun OpenSearchRequestFactory.filterOlderThan(cutoffMillis: Long) = this.also {
        filterRange(EpisodeItemAttributes.START_DATE) { it.lt(JsonData.of(cutoffMillis)) }
    }

    private fun OpenSearchRequestFactory.filterByMinViews(value: Int): OpenSearchRequestFactory =
        filterGreaterThanOrEqual(VIEWS_FIELD, value)

    private fun SearchResponse<OpenSearchEpisodeItem>.extractEpisodeIds(): List<String> {
        return hits().hits().mapNotNull { it.source()?.episodeId }
    }

    private fun sevenDaysAgo(): Long = Instant.now().minus(7, ChronoUnit.DAYS).toEpochMilli()
}

class SimilarityEpisodeSourceRequest(
    range: OffsetLimit,
    userId: Long,
    language: Language? = null,
    country: Country? = null,
    val reference: String,
    val context: List<String>,
    fetchCursor: FetchCursor? = null
) : SourceRequest(range, userId, language, country, fetchCursor) {
    override fun copy(range: OffsetLimit, fetchCursor: FetchCursor?) = SimilarityEpisodeSourceRequest(
        range = range,
        userId = userId,
        language = language,
        country = country,
        reference = reference,
        context = context,
        fetchCursor = fetchCursor
    )
}
