package com.etermax.preguntados.episodes.core.action.challenge.gameplay

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService.ChallengeContext
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.RemainingContent
import com.etermax.preguntados.episodes.core.domain.exception.ChallengeAlreadyFinishedException
import com.etermax.preguntados.episodes.core.domain.time.Clock
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class PlayChallenge(val challengeService: ChallengeService, val summaryService: SummaryService, val clock: Clock) {

    val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): RemainingContent {
        logger.info("Player ${actionData.playerId} played challenge ${actionData.challengeId}")
        val context = getChallengeContext(actionData)

        validateChallengeStatus(context)

        ensurePlayerInitialized(actionData, context)

        val content = resolveRemainingContentAndFinalizeIfNeeded(actionData, context)
        val summary = getEpisodeSummary(context)

        return RemainingContent(content, summary)
    }

    private fun validateChallengeStatus(context: ChallengeContext) {
        if (context.challenge.endDate < clock.now()) {
            throw ChallengeAlreadyFinishedException(context.challenge.id)
        }
    }

    private suspend fun getChallengeContext(actionData: ActionData): ChallengeContext {
        return challengeService.getChallengeContext(actionData.challengeId, actionData.playerId)
    }

    private suspend fun ensurePlayerInitialized(actionData: ActionData, context: ChallengeContext) {
        challengeService.ensurePlayerInitialized(actionData.challengeId, actionData.playerId, context)
    }

    private suspend fun resolveRemainingContentAndFinalizeIfNeeded(
        actionData: ActionData,
        context: ChallengeContext
    ): List<String> {
        val remaining = challengeService.calculateRemainingContent(context)
        if (answerWasNotCalledForLastContent(remaining, context)) {
            challengeService.finishChallenge(actionData.challengeId, actionData.playerId, context)
        }
        return remaining.ifEmpty { context.episode.contents }
    }

    private fun answerWasNotCalledForLastContent(remaining: List<String>, context: ChallengeContext): Boolean {
        return remaining.isEmpty() && context.progress?.hasFinishedEpisode == false
    }

    private suspend fun getEpisodeSummary(context: ChallengeContext): EpisodeSummary {
        return summaryService.toEpisodeSummary(context.episode)!!
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String
    )
}
