package com.etermax.preguntados.episodes.core.domain.quality.likerate

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class EpisodeLikeRateAvgCalculator {

    val logger: Logger = LoggerFactory.getLogger(this::class.java)

    fun calculateAverage(channelId: String, validEpisodes: List<Episode>): Int {
        if (validEpisodes.isEmpty()) {
            return 0
        }

        // calculate like rate for each episode
        val episodeLikeRates = validEpisodes.map { episode ->
            val likes = Math.max(episode.rate.likes, 0)
            val dislikes = Math.max(episode.rate.dislikes, 0)
            val totalRates = likes + dislikes
            if (totalRates == 0L) {
                0
            } else {
                // calculate the rate of likes and round to integer
                ((likes.toDouble() / totalRates) * 100).toInt()
            }
        }

        // calculate the avg of all episode like rates
        val likeRateAvg = episodeLikeRates.average().toInt()

        if (likeRateAvg < 0) {
            logger.warn("[QUALITY] the like rate avg calculated is negative for channel id $channelId")
        }

        val roundedLikeRateAvg = likeRateAvg.coerceIn(0, 100)
        logger.info("[QUALITY] Channel $channelId has an average like rate of $roundedLikeRateAvg% based on ${validEpisodes.size} valid episodes.")

        return roundedLikeRateAvg
    }
}
