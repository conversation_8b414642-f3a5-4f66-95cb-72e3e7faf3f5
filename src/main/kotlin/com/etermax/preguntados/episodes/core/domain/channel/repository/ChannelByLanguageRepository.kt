package com.etermax.preguntados.episodes.core.domain.channel.repository

import com.etermax.preguntados.episodes.core.domain.channel.ChannelReduced
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelByLanguageFilters
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter

interface ChannelByLanguageRepository {
    suspend fun search(filters: ChannelByLanguageFilters, pagination: PaginationFilter): PaginatedItems<ChannelReduced>
}
