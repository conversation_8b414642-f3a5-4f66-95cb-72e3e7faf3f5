package com.etermax.preguntados.episodes.core.infrastructure.moderation

import com.etermax.preguntados.episodes.core.domain.moderation.ModerationService
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import com.etermax.preguntados.external.services.core.infrastructure.http.GatewayUnexpectedException
import com.etermax.preguntados.external.services.core.infrastructure.http.safeReceive
import io.ktor.client.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

class PlatformModerationService(private val client: HttpClient, private val gameId: String) : ModerationService {
    override suspend fun isTextAllowed(userId: Long, language: Language, phrase: String): Boolean {
        val url = URL.format(gameId, userId)
        val response = client.post(url) {
            contentType(ContentType.Application.Json)
            accept(ContentType.Application.Json)
            setBody(PlatformModerationRequest(language.name, phrase))
            expectSuccess = true
        }

        if (response.status != HttpStatusCode.OK) {
            throw GatewayUnexpectedException.buildFrom(response)
        }

        return response.safeReceive<PlatformModerationResponse>().valid
    }

    private companion object {
        const val URL = "games/%s/users/%s/content-moderator-service/texts"
    }
}

@Serializable
data class PlatformModerationRequest(
    @SerialName("language_code") val language: String,
    @SerialName("phrase") val phrase: String
)

@Serializable
data class PlatformModerationResponse(
    @SerialName("is_valid") val valid: Boolean
)
