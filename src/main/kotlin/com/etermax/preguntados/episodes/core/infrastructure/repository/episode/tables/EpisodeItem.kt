package com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@DynamoDbBean
class EpisodeItem(
    @get:DynamoDbAttribute(EpisodeItemAttributes.EPISODE_ID) var episodeId: String = "",
    @get:DynamoDbAttribute(EpisodeItemAttributes.OWNER_ID) var ownerId: Long = 0,
    @get:DynamoDbAttribute(EpisodeItemAttributes.LANGUAGE) var language: String? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.COUNTRY) var country: String? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.TYPE) var type: String? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.NAME) var name: String? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.START_DATE) var startDate: Long? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.COVER) var cover: String? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.BANNER) var banner: String? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.CONTENTS) var contents: List<String>? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.CHANNEL_ID) var channelId: String? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.LIKES) var likes: Long? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.DISLIKES) var dislikes: Long? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.REPORTS) var reports: Long? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.RATE) var rate: Double? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.VIEWS) var views: Long? = null,
    @get:DynamoDbAttribute(EpisodeItemAttributes.STATUS) var status: String = EpisodeStatus.PUBLISHED.name,
    @get:DynamoDbAttribute(EpisodeItemAttributes.QUALITY) var quality: Int? = null
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = buildPartitionKey(episodeId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = EPISODE_SK

    fun to(): Episode? {
        try {
            return Episode(
                id = episodeId,
                name = name!!,
                language = Language.valueOf(language!!),
                country = Country.valueOf(country!!),
                type = type?.let { EpisodeType.valueOf(it) } ?: EpisodeType.PUBLIC,
                startDate = startDate?.let { OffsetDateTime.ofInstant(Instant.ofEpochMilli(it), ZoneOffset.UTC) },
                cover = cover!!,
                banner = banner!!,
                ownerId = ownerId,
                contents = contents ?: emptyList(),
                channelId = channelId,
                rate = Rate(likes ?: 0, dislikes ?: 0),
                reports = reports ?: 0,
                views = views ?: 0,
                status = EpisodeStatus.valueOf(status),
                quality = quality ?: 0
            )
        } catch (e: Exception) {
            // TODO workaround while fix race condition
            logger.error("Error mapping episode [$episodeId] to item", e)
            return null
        }
    }

    companion object {
        const val EPISODE_PREFIX = "E#"
        const val EPISODE_SK = "EPISODE"

        fun buildPartitionKey(episodeId: String) = "$EPISODE_PREFIX$episodeId"

        fun from(episode: Episode) = with(episode) {
            EpisodeItem(
                episodeId = id,
                language = language.name,
                country = country.name,
                type = type.name,
                ownerId = ownerId,
                name = name,
                startDate = startDate?.toMillis(),
                cover = cover,
                banner = banner,
                contents = contents,
                likes = rate.likes,
                dislikes = rate.dislikes,
                reports = reports,
                views = views,
                status = status.name,
                rate = rate.calculate(),
                channelId = channelId,
                quality = quality
            )
        }
    }
}
