package com.etermax.preguntados.episodes.core.domain.episode.progress

data class DeliveryProgress(
    val lastContentId: String?,
    val hasFinishedEpisode: Boolean
) {

    companion object {
        val EMPTY = DeliveryProgress(lastContentId = null, hasFinishedEpisode = false)

        fun from(content: ProgressContent) = with(content) {
            DeliveryProgress(lastContentId, hasFinishedEpisode)
        }
    }
}
