package com.etermax.preguntados.episodes.core.infrastructure.search.feed.service

import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.delete.BlackListRecommendationService
import com.etermax.preguntados.episodes.core.domain.search.UserBasedRepository
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.filter.Filter
import com.etermax.preguntados.episodes.core.domain.search.filter.FilterFactory
import com.etermax.preguntados.episodes.core.domain.search.filter.FilterType.PLAYED_AND_DUPLICATE
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.OpenSearchPlayedRepository
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.*
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.layout.EightByTwoFeedSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.layout.FourByOneFeedSource
import com.etermax.preguntados.episodes.core.infrastructure.search.service.PlayedEpisodeSearchService
import com.etermax.preguntados.episodes.core.infrastructure.search.service.RecentEpisodeSearchService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.slf4j.MDCContext
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.slf4j.LoggerFactory
import kotlin.time.ExperimentalTime
import kotlin.time.measureTime
import kotlin.time.measureTimedValue

class DefaultFeedService(
    val filterFactory: FilterFactory,
    val osClient: OpenSearchAsyncClient,
    val episodesIndexName: String,
    val channelIndexName: String,
    val playedRepository: OpenSearchPlayedRepository,
    val episodesRepository: EpisodeRepository,
    val channelRepository: ChannelRepository,
    val configuration: DefaultFeedServiceConfiguration,
    private val userBasedRepository: UserBasedRepository,
    private val playedEpisodeSearchService: PlayedEpisodeSearchService,
    private val recentEpisodeSearchService: RecentEpisodeSearchService,
    private val blackListRecommendationService: BlackListRecommendationService
) : FeedService {
    private val codec = CursorCodec()

    private val logger = LoggerFactory.getLogger(this::class.java)

    @OptIn(ExperimentalTime::class)
    override suspend fun fetch(feedRequest: FeedRequest): FeedResponse {
        val userId = feedRequest.userId ?: throw RuntimeException("User is required in default feed service")
        val fetchCursor = codec.decode(feedRequest.paginationToken, feedRequest.debug)

        val isFirstPage = feedRequest.paginationToken.isNullOrEmpty()
        val layout = feedRequest.layout

        val sourceRequest = SourceRequest(
            range = OffsetLimit(0, feedRequest.limit),
            userId = userId,
            language = feedRequest.language,
            country = feedRequest.country,
            fetchCursor = fetchCursor
        )

        val history = lastPlayedEpisodesIds(userId)

        logger.debug(
            "[FEED] Creating filter for fetchCursor? {}, token: {}, cursor: {}",
            isFirstPage,
            feedRequest.paginationToken,
            fetchCursor
        )

        val (filter, loadFilterTime) = measureTimedValue {
            filterFactory.create(userId, PLAYED_AND_DUPLICATE, isFirstPage)
        }
        logger.info("[LATENCY] loadFilter=${loadFilterTime}ms")

        val source = buildEarlyCustomFeed(history, layout, filter)

        val (sourceResponse, fetchTime) = measureTimedValue {
            source.fetch(sourceRequest)
        }

        logger.info("[LATENCY] feedFetch=${fetchTime}ms")

        // Fire-and-forget save
        CoroutineScope(Dispatchers.IO).launch(MDCContext()) {
            try {
                val saveFilterTime = measureTime {
                    filter.save()
                }

                logger.info("[LATENCY] saveFilter=${saveFilterTime}ms")
            } catch (e: Exception) {
                logger.warn("Failed to save filter: ${e.message}", e)
            }
        }

        return if (sourceResponse.fetchCursor != null && sourceResponse.fetchCursor.exhausted) {
            logger.info("Feed exhausted, returning empty pagination token")
            FeedResponse(sourceResponse.items, null)
        } else {
            FeedResponse(sourceResponse.items, codec.encode(sourceResponse.fetchCursor, feedRequest.debug))
        }
    }

    private suspend fun lastPlayedEpisodesIds(playerId: Long): List<String> {
        return playedRepository.lastPlayedEpisodesIds(playerId, 0, configuration.userHistoryThreshold)
    }

    private fun buildEarlyCustomFeed(history: List<String>, layout: String?, filter: Filter): Source<Any> {
        return when (history.size) {
            0 -> buildFTUEFeed(layout, filter)
            1 -> buildEarlyBehaviourBasedFeed(history, layout, filter, weights = listOf(25, 25, 50))
            2 -> buildEarlyBehaviourBasedFeed(history, layout, filter, weights = listOf(33, 25, 42))
            3 -> buildEarlyBehaviourBasedFeed(history, layout, filter, weights = listOf(50, 25, 25))
            4, 5 -> buildEarlyBehaviourBasedFeed(history.take(3), layout, filter, weights = listOf(50, 25, 25))
            else -> buildBehaviourBasedFeed(layout, filter, history)
        }
    }

    private fun buildEarlyBehaviourBasedFeed(history: List<String>, layout: String?, filter: Filter, weights: List<Int>): Source<Any> {
        val episodes = DistributedSource(
            sources = listOf(
                MergedSimilaritySource(
                    source = FilteredSource(SimilarityEpisodeSource(osClient, episodesIndexName), filter = filter),
                    history = history
                ),
                FilteredSource(source = UserBasedEpisodeSource(userBasedRepository, playedEpisodeSearchService, episodesRepository, blackListRecommendationService), filter = filter),
                FilteredSource(
                    source = getHighQualitySource(),
                    filter = filter
                )
            ),
            distributionStrategy = WeightedDistributionStrategy(weights)
        )

        val channels = if (configuration.isSortedByQuality) {
            QualityChannelSource(osClient, channelIndexName)
        } else {
            ScoreChannelSource(osClient, channelIndexName)
        }

        return buildLayoutFeed(
            layout,
            primarySource = EpisodeHydratorSource(episodes, episodesRepository),
            secondarySource = ChannelHydratorSource(channels, channelRepository)
        )
    }

    private fun buildBehaviourBasedFeed(layout: String?, filter: Filter, history: List<String>): Source<Any> {
        val episodes = getUserBasedDistributedEpisodes(filter, history)

        val channels = if (configuration.isSortedByQuality) {
            QualityChannelSource(osClient, channelIndexName)
        } else {
            ScoreChannelSource(osClient, channelIndexName)
        }

        return buildLayoutFeed(
            layout,
            primarySource = EpisodeHydratorSource(episodes, episodesRepository),
            secondarySource = ChannelHydratorSource(channels, channelRepository)
        )
    }

    private fun buildFTUEFeed(layout: String?, filter: Filter): Source<Any> {
        val episodes =
            FilteredSource(
                source = getHighQualitySource(),
                filter = filter
            )

        val channels = if (configuration.isSortedByQuality) {
            QualityChannelSource(osClient, channelIndexName)
        } else {
            ScoreChannelSource(osClient, channelIndexName)
        }

        return buildLayoutFeed(
            layout,
            primarySource = EpisodeHydratorSource(episodes, episodesRepository),
            secondarySource = ChannelHydratorSource(channels, channelRepository)
        )
    }

    private fun getHighQualitySource(): Source<String> {
        return if (configuration.isSortedByQuality) {
            HighQualityEpisodeSource(osClient, episodesIndexName)
        } else {
            HighQualityRecentEpisodeSource(
                client = osClient,
                episodesIndexName = episodesIndexName,
                requiredLikesForTopRated = configuration.requiredLikesForHighQuality,
                requiredViewsForTopRated = configuration.requiredViewsForHighQuality
            )
        }
    }

    private fun <E, C> buildLayoutFeed(
        layout: String?,
        primarySource: Source<E>,
        secondarySource: Source<C>
    ): Source<Any> {
        return when {
            layout == null -> FourByOneFeedSource(primarySource, secondarySource)
            layout.startsWith("C3") -> FourByOneFeedSource(primarySource, secondarySource)
            layout.startsWith("C6") -> EightByTwoFeedSource(primarySource, secondarySource)
            else -> FourByOneFeedSource(primarySource, secondarySource)
        }
    }

    private fun getUserBasedDistributedEpisodes(filter: Filter, history: List<String>): DistributedSource {
        return DistributedSource(
            sources = listOf(
                MergedSimilaritySource(
                    source = FilteredSource(SimilarityEpisodeSource(osClient, episodesIndexName), filter = filter),
                    history = history
                ),
                FilteredSource(source = UserBasedEpisodeSource(userBasedRepository, playedEpisodeSearchService, episodesRepository, blackListRecommendationService), filter = filter),
                FilteredSource(source = RandomRecentEpisodeSource(recentEpisodeSearchService), filter = filter)
            ),
            distributionStrategy = WeightedDistributionStrategy(listOf(50, 25, 25))
        )
    }
}

data class DefaultFeedServiceConfiguration(
    val userHistoryThreshold: Int,
    val requiredLikesForHighQuality: Int,
    val requiredViewsForHighQuality: Int,
    val isSortedByQuality: Boolean
)
