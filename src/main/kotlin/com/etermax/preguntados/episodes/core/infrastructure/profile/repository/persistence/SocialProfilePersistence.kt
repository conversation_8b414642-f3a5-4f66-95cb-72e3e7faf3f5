package com.etermax.preguntados.episodes.core.infrastructure.profile.repository.persistence

import com.etermax.preguntados.episodes.core.domain.profile.social.SocialNetwork
import com.etermax.preguntados.episodes.core.domain.profile.social.SocialProfile
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SocialProfilePersistence(
    @SerialName("w") val network: String,
    @SerialName("i") val id: String,
    @SerialName("n") val name: String? = null
) {

    fun to() = SocialProfile(network.toSocialNetwork(), id, name)

    private fun String.toSocialNetwork() = when (this) {
        FACEBOOK -> SocialNetwork.FACEBOOK
        else -> throw RuntimeException("SocialNetwork $this not recognized.")
    }

    companion object {
        private const val FACEBOOK = "FACEBOOK"

        fun from(profile: SocialProfile) = with(profile) {
            SocialProfilePersistence(network.toPersistence(), id, name)
        }

        private fun SocialNetwork.toPersistence() = when (this) {
            SocialNetwork.FACEBOOK -> FACEBOOK
        }
    }
}
