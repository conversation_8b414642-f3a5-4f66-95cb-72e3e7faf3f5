package com.etermax.preguntados.episodes.core.infrastructure.search.feed.layout

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import org.slf4j.LoggerFactory

/**
 * Composes a feed by interleaving items from primary and secondary sources
 * in an 8:2 pattern (1P, 1S, 1P, 1S, 6P and 1S, 1P, 1S, 1P, 6P).
 * Each secondary item counts as 2 units of size.
 */
class EightByTwoFeedSource<E, C>(
    private val primarySource: Source<E>,
    private val secondarySource: Source<C>
) : Source<Any> {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<Any> {
        val totalSize = request.range.limit
        if (totalSize <= 0) {
            logger.debug("[FEED] Empty response requested (limit = $totalSize).")
            return emptyResponse(request.fetchCursor)
        }

        if (request.fetchCursor?.exhausted == true) {
            logger.debug("[FEED] Fetch called with exhausted cursor, returning empty response")
            return emptyResponse(request.fetchCursor)
        }

        val cursors = request.fetchCursor as? EightByTwoFetchCursor
        logger.debug("[FEED] Received fetch request with cursor: {}", cursors)

        if (cursors?.primary?.exhausted == true) {
            logger.debug("[FEED] Fetch called with exhausted primary cursor, returning empty response")
            return emptyResponse(request.fetchCursor)
        }

        val (primaryLimit, secondaryLimit) = calculateItemLimits(totalSize)
        logger.debug("[FEED] Composing feed of size $totalSize → primary: $primaryLimit, secondary: $secondaryLimit")

        val primaryResponse = primarySource.fetch(
            request.copy(range = OffsetLimit(0, primaryLimit), fetchCursor = cursors?.primary)
        )
        if (primaryResponse.items.isEmpty()) {
            logger.debug("[FEED] No primary items found. Returning empty response.")
            return emptyResponse(buildCursor(primaryResponse.fetchCursor, cursors?.secondary))
        }
        logger.debug("[FEED] Fetched {} primary items, cursor: {}", primaryResponse.items.size, primaryResponse.fetchCursor)

        val secondaryResponse = secondarySource.fetch(
            request.copy(range = OffsetLimit(0, secondaryLimit), fetchCursor = cursors?.secondary)
        )
        logger.debug(
            "[FEED] Fetched {} secondary items, cursor: {}",
            secondaryResponse.items.size,
            secondaryResponse.fetchCursor
        )

        val primaryItems = primaryResponse.items
        val secondaryItems = secondaryResponse.items
        val primaryCursor = primaryResponse.fetchCursor
        val secondaryCursor = secondaryResponse.fetchCursor

        val extraPrimaryNeeded = calculateExtraPrimaryItems(secondaryItems.size, secondaryLimit)
        logger.debug("[FEED] Extra primary items needed due to secondary shortfall: {}", extraPrimaryNeeded)

        val (finalPrimaryItems, finalPrimaryCursor) = if (extraPrimaryNeeded > 0) {
            val extraResponse = primarySource.fetch(
                request.copy(
                    range = OffsetLimit(primaryItems.size, extraPrimaryNeeded),
                    fetchCursor = primaryCursor
                )
            )
            logger.debug("[FEED] Fetched ${extraResponse.items.size} additional primary items")
            Pair(primaryItems + extraResponse.items, extraResponse.fetchCursor)
        } else {
            Pair(primaryItems, primaryCursor)
        }

        val composedItems = composeFeed(finalPrimaryItems, secondaryItems)
        logger.debug("[FEED] Composed final feed with {} items", composedItems.size)

        val finalCursor = buildCursor(finalPrimaryCursor, secondaryCursor)
        return SourceResponse(composedItems, finalCursor)
    }

    private fun calculateItemLimits(size: Int): Pair<Int, Int> {
        // Each pattern = 8 primary units (8x1) + 2 secondary units (2x2) = 12 units
        val completePatterns = size / 12
        val remainingUnits = size % 12

        // Each pattern consumes 2 secondary items → 2 * completePatterns
        var secondaryCount = completePatterns * 2
        var primaryCount = completePatterns * 8

        // Handle remaining units — prioritize preserving pattern shape
        // Try to fit an additional half-pattern (1P + 1S + 1P + 1S + up to 6P = 8 units)
        // That’s 2 secondary items (4 units) and 4+ primary (1 each)
        when {
            remainingUnits >= 6 -> {
                secondaryCount += 2
                primaryCount += remainingUnits - 4 // subtract 4 units used by 2 secondary items
            }

            remainingUnits >= 4 -> {
                secondaryCount += 1
                primaryCount += remainingUnits - 2 // 1 secondary = 2 units
            }

            else -> {
                primaryCount += remainingUnits
            }
        }

        return primaryCount to secondaryCount
    }

    private fun calculateExtraPrimaryItems(actualSecondary: Int, expectedSecondary: Int): Int {
        val missing = expectedSecondary - actualSecondary
        return missing * 2
    }

    private fun composeFeed(primaryItems: List<E>, secondaryItems: List<C>): List<Any> {
        val result = mutableListOf<Any>()
        var pIndex = 0
        var sIndex = 0
        var alternate = true

        while (pIndex < primaryItems.size || sIndex < secondaryItems.size) {
            if (alternate) {
                // Pattern A: 1P, 1S, 1P, 1S, 6P
                if (pIndex < primaryItems.size) result += primaryItems[pIndex++]!!
                if (sIndex < secondaryItems.size) result += secondaryItems[sIndex++]!!
                if (pIndex < primaryItems.size) result += primaryItems[pIndex++]!!
                if (sIndex < secondaryItems.size) result += secondaryItems[sIndex++]!!
                repeat(6) { if (pIndex < primaryItems.size) result += primaryItems[pIndex++]!! }
            } else {
                // Pattern B: 1S, 1P, 1S, 1P, 6P
                if (sIndex < secondaryItems.size) result += secondaryItems[sIndex++]!!
                if (pIndex < primaryItems.size) result += primaryItems[pIndex++]!!
                if (sIndex < secondaryItems.size) result += secondaryItems[sIndex++]!!
                if (pIndex < primaryItems.size) result += primaryItems[pIndex++]!!
                repeat(6) { if (pIndex < primaryItems.size) result += primaryItems[pIndex++]!! }
            }
            alternate = !alternate
        }

        // Fill remaining slots with primary items if needed
        while (pIndex < primaryItems.size) {
            result += primaryItems[pIndex++]!!
        }

        return result
    }

    private fun buildCursor(primary: FetchCursor?, secondary: FetchCursor?): FetchCursor? {
        return EightByTwoFetchCursor(
            primary,
            secondary,
            primary?.exhausted ?: false
        )
    }
}
