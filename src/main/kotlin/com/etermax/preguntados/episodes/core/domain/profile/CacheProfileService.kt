package com.etermax.preguntados.episodes.core.domain.profile

import com.etermax.preguntados.episodes.core.domain.metric.CounterMetric
import com.etermax.preguntados.episodes.core.domain.profile.repository.ProfileRepository
import com.etermax.preguntados.episodes.core.infrastructure.profile.metric.PrometheusCacheProfileMetric.Companion.HIT
import com.etermax.preguntados.episodes.core.infrastructure.profile.metric.PrometheusCacheProfileMetric.Companion.MISS

class CacheProfileService(
    private val profileService: ProfileService,
    private val cacheRepository: ProfileRepository,
    private val metric: CounterMetric
) : ProfileService {

    override suspend fun find(playerId: Long) =
        findCachedProfile(playerId) ?: findAndCacheProfile(playerId)

    override suspend fun findMany(playerIds: List<Long>): List<Profile> {
        val cachedProfiles = findCachedProfiles(playerIds)
        val missingIds = playerIds.filter { cachedProfiles.contains<PERSON>ey(it).not() }
        if (missingIds.isEmpty()) {
            return cachedProfiles.values.toList()
        }
        val missingProfiles = findAndCacheProfiles(missingIds)
        return missingProfiles.plus(cachedProfiles.values.toList())
    }

    private suspend fun findCachedProfile(playerId: Long): Profile? {
        return cacheRepository.find(playerId)?.also { track(HIT) }
    }

    private suspend fun findCachedProfiles(playerIds: List<Long>): Map<Long, Profile> {
        val cachedProfiles = cacheRepository.find(playerIds).onEach { track(HIT) }
        playerIds.filter { cachedProfiles.containsKey(it).not() }.onEach { track(MISS) }
        return cachedProfiles
    }

    private suspend fun findAndCacheProfile(playerId: Long): Profile {
        return profileService.find(playerId).also {
            track(MISS)
            cacheRepository.save(it)
        }
    }

    private suspend fun findAndCacheProfiles(playerIds: List<Long>): List<Profile> {
        return profileService.findMany(playerIds).onEach {
            track(MISS)
            // redis doesn't allow to multi set with an expiration time
            cacheRepository.save(it)
        }
    }

    private fun track(label: String) {
        metric.count(label)
    }
}
