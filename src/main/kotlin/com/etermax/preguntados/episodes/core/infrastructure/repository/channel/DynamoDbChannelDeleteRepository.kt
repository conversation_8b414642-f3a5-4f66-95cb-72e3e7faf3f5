package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelDeleteRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.TransactionBuilder
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.RemoveChannelFromEpisodeItem
import kotlinx.coroutines.future.await
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.TransactDeleteItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactUpdateItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest

class DynamoDbChannelDeleteRepository(
    dynamoDbClient: DynamoDbEnhancedAsyncClient,
    private val channelTable: DynamoDbAsyncTable<ChannelItem>,
    private val channelEpisodesTable: DynamoDbAsyncTable<ChannelEpisodeItem>,
    private val removeChannelIdFromChannelItemTable: DynamoDbAsyncTable<RemoveChannelFromEpisodeItem>,
    private val channelEpisodesRepository: DynamoChannelEpisodesRepository
) : ChannelDeleteRepository, DynamoDBRepository<ChannelItem>(dynamoDbClient, channelTable) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun delete(channelId: String) {
        logger.info("ChannelId='$channelId' - Deleting items")
        val channelEpisodesIds = channelEpisodesRepository.findAllEpisodeIdsFrom(channelId)

        if (channelEpisodesIds.isEmpty()) {
            deleteChannelSingleItem(channelId)
            return
        }

        deleteAllItems(channelEpisodesIds, channelId)
    }

    private suspend fun deleteChannelSingleItem(channelId: String) {
        runCatching {
            logger.info("ChannelId='$channelId' - Has no channel episodes.")
            val transactionBuilder = TransactWriteItemsEnhancedRequest.builder()
            transactionBuilder.addDeleteChannel(channelId)
            client.transactWriteItems(transactionBuilder.build()).await()
            logger.info("ChannelId='$channelId' - Finish deleting channel")
        }.onFailure {
            logger.error("ChannelId='$channelId' - Fail to delete channel without items", it)
        }
    }

    private suspend fun deleteAllItems(channelEpisodesIds: List<String>, channelId: String) {
        val chunkedChannelEpisodesIds = channelEpisodesIds.chunked(MAX_ITEMS_PER_TRANSACTION / 2)
        chunkedChannelEpisodesIds.forEachIndexed { index, chunk ->
            runCatching {
                logger.info("ChannelId='$channelId' - Deleting chunk='$index'. EpisodesIds='$chunk'.")
                val transactionBuilder = TransactWriteItemsEnhancedRequest.builder()

                if (index == 0) {
                    transactionBuilder.addDeleteChannel(channelId)
                }

                chunk.forEach { episodeId ->
                    transactionBuilder.addDeleteChannelEpisode(channelId, episodeId)
                    transactionBuilder.addRemoveChannelIdFromEpisode(episodeId)
                }

                client.transactWriteItems(transactionBuilder.build()).await()
                logger.info("ChannelId='$channelId' - Finish deleting chunk='$index'. EpisodesIds='$chunk'.")
            }.onFailure {
                val transactionDeletesChannelItem = index == 0
                if (transactionDeletesChannelItem) {
                    logger.error(
                        "ChannelId='$channelId' - Fail deleting chunk='0'. STOP PROCESSING. EpisodesIds='$chunk'.",
                        it
                    )
                    throw it
                } else {
                    logger.error(
                        "ChannelId='$channelId' - Fail deleting chunk='$index'. CONTINUE PROCESSING. EpisodesIds='$chunk'.",
                        it
                    )
                }
            }
        }
    }

    private fun TransactionBuilder.addDeleteChannel(channelId: String) {
        val key =
            Key.builder().partitionValue(ChannelItem.buildPartitionKey(channelId)).sortValue(ChannelItem.CHANNEL_SK)
                .build()

        this.addDeleteItem(
            channelTable,
            TransactDeleteItemEnhancedRequest.builder().key(key).build()
        )
    }

    private fun TransactionBuilder.addDeleteChannelEpisode(
        channelId: String,
        episodeId: String
    ) {
        val channelEpisodeKey = Key.builder().partitionValue(ChannelEpisodeItem.buildPartitionKey(channelId))
            .sortValue(ChannelEpisodeItem.buildSortedKey(episodeId)).build()

        this.addDeleteItem(
            channelEpisodesTable,
            TransactDeleteItemEnhancedRequest.builder().key(channelEpisodeKey).build()
        )
    }

    private fun TransactionBuilder.addRemoveChannelIdFromEpisode(episodeId: String) {
        val removeChannelIdItem = RemoveChannelFromEpisodeItem(episodeId)

        this.addUpdateItem(
            removeChannelIdFromChannelItemTable,
            TransactUpdateItemEnhancedRequest.builder(RemoveChannelFromEpisodeItem::class.java)
                .item(removeChannelIdItem).build()
        )
    }

    private companion object {
        const val MAX_ITEMS_PER_TRANSACTION = 24
    }
}
