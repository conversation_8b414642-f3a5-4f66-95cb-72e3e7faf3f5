package com.etermax.preguntados.episodes.core.infrastructure.search.data

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class EpisodeData(
    @JsonProperty("PK") val pk: String? = null,
    @JsonProperty("SK") val sk: String? = null,
    @JsonProperty("country") val country: String? = null,
    @JsonProperty("type") val type: String? = null,
    @JsonProperty("owner_id") val ownerId: Long? = null,
    @JsonProperty("contents") val contents: List<String>? = null,
    @JsonProperty("channel_id") val channelId: String? = null,
    @JsonProperty("banner") val banner: String? = null,
    @JsonProperty("language") val language: String? = null,
    @JsonProperty("cover") val cover: String? = null,
    @JsonProperty("episode_id") val episodeId: String? = null,
    @JsonProperty("name") val name: String? = null,
    @JsonProperty("start_date") val startDate: Long? = null,
    @JsonProperty("likes") val likes: Long? = null,
    @JsonProperty("dislikes") val dislikes: Long? = null,
    @JsonProperty("reports") val reports: Long? = null,
    @JsonProperty("views") val views: Long? = null,
    @JsonProperty("rate") val rate: Long? = null,
    @JsonProperty("status") val status: String? = null,
    @JsonProperty("quality") val quality: Int? = null
) {

    fun to() = Episode(
        id = episodeId!!,
        name = name!!,
        language = Language.valueOf(language!!),
        country = Country.valueOf(country!!),
        type = type?.let { EpisodeType.valueOf(it) } ?: EpisodeType.PUBLIC,
        startDate = startDate?.let { OffsetDateTime.ofInstant(Instant.ofEpochMilli(it), ZoneOffset.UTC) },
        cover = cover!!,
        banner = banner!!,
        ownerId = ownerId!!,
        contents = contents!!,
        channelId = channelId,
        rate = Rate(likes ?: 0, dislikes ?: 0),
        reports = reports ?: 0,
        views = views ?: 0,
        status = status?.let { EpisodeStatus.valueOf(it) } ?: EpisodeStatus.PUBLISHED,
        quality = quality ?: 0
    )
}
