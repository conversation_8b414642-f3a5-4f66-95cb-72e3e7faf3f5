package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.profile.PlayerFriendsService
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.search.data.PlayerEpisodeData
import kotlinx.coroutines.future.await
import org.opensearch.client.json.JsonData
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder
import org.opensearch.client.opensearch._types.aggregations.Aggregation
import org.opensearch.client.opensearch._types.aggregations.StringTermsBucket
import org.opensearch.client.opensearch._types.aggregations.TermsAggregation
import org.opensearch.client.opensearch._types.aggregations.TopHitsAggregation
import org.opensearch.client.opensearch.core.SearchResponse
import java.time.temporal.ChronoUnit.MONTHS

class RecommendedEpisodesSearch(
    private val client: OpenSearchAsyncClient,
    private val indexName: String,
    private val clock: Clock,
    private val episodeRepository: EpisodeRepository,
    private val playerFriendsService: PlayerFriendsService
) : Search {

    override suspend fun match(parameters: SearchParameters) = parameters.sort == SortEpisode.RECOMMENDED_EPISODES

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        val followedPlayers = playerFriendsService.findFollowedIds(parameters.playerId!!)
        if (followedPlayers.isEmpty()) return emptyList()

        val oneMonthBefore = clock.now().minus(ONE_MONTH, MONTHS).toMillis()
        val request = OpenSearchRequestFactory(indexName, 0, 10)
            .filterByPlayers(followedPlayers)
            .filterGreaterOrEqualsThan(oneMonthBefore)
            .addAggregation(TOP_EPISODES) { aggregation() }
            .build()

        val response = client.search(request, PlayerEpisodeData::class.java).await()

        val playerEpisodes = response.toPlayerEpisode()
        return playerEpisodes.toEpisodes()
    }

    private fun OpenSearchRequestFactory.filterByPlayers(friends: List<Long>) = this.also {
        mustTerms(PLAYER_ID_FIELD, friends)
    }

    private fun OpenSearchRequestFactory.filterGreaterOrEqualsThan(since: Long) = this.also {
        filterRange(TIMESTAMP) {
            it.gte(JsonData.of(since))
        }
    }

    private fun aggregation(): Aggregation {
        return Aggregation.Builder()
            .terms(
                TermsAggregation
                    .Builder()
                    .field(EPISODE_ID_FIELD)
                    .size(10)
                    .order(mapOf("_count" to SortOrder.Desc))
                    .build()
            )
            .aggregations(TOP_HITS_PER_EPISODE) {
                it.topHits(TopHitsAggregation.Builder().build())
            }.build()
    }

    private fun SearchResponse<PlayerEpisodeData>.toPlayerEpisode(): List<String> {
        return aggregations()[TOP_EPISODES]?.sterms()?.buckets()?.array()
            ?.mapNotNull(StringTermsBucket::key)
            ?: emptyList()
    }

    private suspend fun List<String>.toEpisodes(): List<Episode> {
        val episodeIds = map { it }
        return episodeRepository.findByIds(episodeIds)
    }

    private companion object {
        const val PLAYER_ID_FIELD = "player_id"
        const val EPISODE_ID_FIELD = "episode_id"
        const val TIMESTAMP = "timestamp"
        const val ONE_MONTH = 1L
        const val TOP_EPISODES = "top_episodes"
        const val TOP_HITS_PER_EPISODE = "top_hits_per_episode"
    }
}
