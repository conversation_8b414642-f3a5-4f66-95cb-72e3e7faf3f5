package com.etermax.preguntados.episodes.core.infrastructure.account.representation

import com.etermax.preguntados.episodes.core.domain.account.PlayerAccount
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class UserSearchResponse(
    @SerialName("id") val id: Long,
    @SerialName("username") val username: String,
    @SerialName("name") val name: String? = null,
    @SerialName("facebook_id") val facebookId: String? = null,
    @SerialName("photo_url") val photoUrl: String? = null,
    @SerialName("is_followed") val isFollowed: Boolean? = null
) {
    fun to() = PlayerAccount(
        id = id,
        username = username,
        name = name,
        facebookId = facebookId,
        photoUrl = photoUrl,
        isFollowed = isFollowed
    )
}
