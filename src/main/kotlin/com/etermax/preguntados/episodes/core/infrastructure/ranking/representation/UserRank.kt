package com.etermax.preguntados.episodes.core.infrastructure.ranking.representation

import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankedPlayer
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingEntry
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class UserRank(
    @SerialName("position") val position: Int,
    @SerialName("userData") val userData: UserData,
    @SerialName("score") val score: Int
) {

    fun to() = RankedPlayer(
        playerId = userData.userId.toLong(),
        rankingEntry = RankingEntry(position, score.toLong())
    )
}
