package com.etermax.preguntados.episodes.core.infrastructure.repository.episode.persistence.episode

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.serialization.Serializable
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset

@Serializable
data class EpisodePersistence(
    val id: String,
    val name: String,
    val language: String,
    val country: String,
    val type: EpisodeType,
    val startDateEpochSeconds: Long?,
    val cover: String,
    val banner: String,
    val ownerId: Long,
    val contents: List<String>,
    val channelId: String?,
    val rate: RatePersistence,
    val reports: Long,
    val views: Long,
    val status: String,
    val quality: Int = 0
) {
    fun to() = Episode(
        id = id,
        name = name,
        language = Language.valueOf(language),
        country = Country.valueOf(country),
        type = type,
        startDate = startDateEpochSeconds?.let {
            OffsetDateTime.of(
                LocalDateTime.ofEpochSecond(
                    startDateEpochSeconds,
                    0,
                    ZoneOffset.UTC
                ),
                ZoneOffset.UTC
            )
        },
        cover = cover,
        banner = banner,
        ownerId = ownerId,
        contents = contents,
        channelId = channelId,
        rate = rate.to(),
        reports = reports,
        views = views,
        status = EpisodeStatus.valueOf(status),
        quality = quality
    )

    companion object {
        fun from(episode: Episode) = EpisodePersistence(
            id = episode.id,
            name = episode.name,
            language = episode.language.name,
            country = episode.country.name,
            type = episode.type,
            startDateEpochSeconds = episode.startDate?.toEpochSecond(),
            cover = episode.cover,
            banner = episode.banner,
            ownerId = episode.ownerId,
            contents = episode.contents,
            channelId = episode.channelId,
            rate = RatePersistence.from(episode.rate),
            reports = episode.reports,
            views = episode.views,
            status = episode.status.name,
            quality = episode.quality
        )
    }
}
