package com.etermax.preguntados.episodes.core.infrastructure.repository.challenge

import com.etermax.preguntados.episodes.core.domain.challenge.Challenge
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables.ChallengeItem
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key

class DynamoDBChallengeRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<ChallengeItem>
) : ChallengeRepository, DynamoDBRepository<ChallengeItem>(client, table) {
    override suspend fun save(challenge: Challenge) {
        val item = ChallengeItem.from(challenge)
        saveItem(item, ChallengeItem::class.java)
    }

    override suspend fun find(challengeId: String): Challenge? {
        return findItem(
            Key.builder()
                .partitionValue(ChallengeItem.buildPartitionKey(challengeId))
                .sortValue(ChallengeItem.CHALLENGE_SK)
                .build()
        )?.toDomain()
    }
}
