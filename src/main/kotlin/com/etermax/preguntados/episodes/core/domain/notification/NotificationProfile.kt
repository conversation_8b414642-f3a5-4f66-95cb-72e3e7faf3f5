package com.etermax.preguntados.episodes.core.domain.notification

import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.social.SocialNetwork

data class NotificationProfile(val id: Long, val userName: String, val facebookId: String?) {
    companion object {
        fun from(profile: Profile) = NotificationProfile(
            profile.playerId,
            profile.name,
            profile.socialProfile?.let {
                if (it.network == SocialNetwork.FACEBOOK) it.id else null
            }
        )
    }
}
