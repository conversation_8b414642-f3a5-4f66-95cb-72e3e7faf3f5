package com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables

object EpisodeItemAttributes {
    const val PK = "PK"
    const val EPISODE_ID = "episode_id"
    const val OWNER_ID = "owner_id"
    const val LANGUAGE = "language"
    const val COUNTRY = "country"
    const val TYPE = "type"
    const val NAME = "name"
    const val START_DATE = "start_date"
    const val COVER = "cover"
    const val BANNER = "banner"
    const val CONTENTS = "contents"
    const val CHANNEL_ID = "channel_id"
    const val LIKES = "likes"
    const val DISLIKES = "dislikes"
    const val REPORTS = "reports"
    const val VIEWS = "views"
    const val STATUS = "status"
    const val RATE = "rate"
    const val EMBEDDING = "embedding"
    const val QUALITY = "quality"
}

object PlayedEpisodeItemAttributes {
    const val USER_ID = "player_id"
    const val TIMESTAMP = "timestamp"
    const val EPISODE_ID = "episode_id"
    const val LANGUAGE = "language"
}
