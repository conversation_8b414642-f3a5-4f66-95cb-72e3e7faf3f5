package com.etermax.preguntados.episodes.core.infrastructure.search.repository

import com.etermax.preguntados.episodes.core.domain.search.RecentEpisodeRepository
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import java.time.Duration

class RedisRecentEpisodeRepository(
    private val redis: RedisAsyncCommands<String, String>,
    private val ttl: Duration,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : RecentEpisodeRepository {

    override suspend fun save(language: String?, episodesId: List<String>) {
        return withContext(dispatcher) {
            val key = createKey(language)
            redis.rpush(key, *episodesId.toTypedArray()).await()
            redis.expire(key, ttl.seconds).await()
        }
    }

    override suspend fun find(language: String?, offset: Int, limit: Int): List<String>? {
        return withContext(dispatcher) {
            val key = createKey(language)
            if (redis.exists(key).await() <= 0) {
                return@withContext null
            }
            return@withContext redis.lrange(key, offset.toLong(), (offset + limit - 1).toLong()).await()
        }
    }

    override suspend fun isProcessing(language: String?): Boolean {
        return withContext(dispatcher) {
            val key = createProcessedKey(language)
            val result = redis.incr(key).await()
            if (result > 1) {
                true
            } else {
                redis.expire(key, 60)
                false
            }
        }
    }

    private fun createKey(language: String?) = "$KEY:${language ?: DEFAULT}"

    private fun createProcessedKey(language: String?) =
        "$PROCESSED_KEY:${language ?: DEFAULT}"

    private companion object {
        const val KEY = "pr:e:re"
        const val PROCESSED_KEY = "pr:e:re:pk"
        const val DEFAULT = "vne"
    }
}
