package com.etermax.preguntados.episodes.core.action.profile

import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelSearchFilters
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.profile.ProfileSummary
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

// TODO: Add tests
class GetProfileSummary(
    private val episodeRepository: EpisodeRepository,
    private val channelRepository: ChannelRepository
) {
    suspend operator fun invoke(data: ActionData): ProfileSummary = coroutineScope {
        ProfileSummary(
            hasEpisodes = hasEpisodesAsync(data).await(),
            hasChannels = hasChannelsAsync(data).await()
        )
    }

    private fun CoroutineScope.hasEpisodesAsync(data: ActionData): Deferred<Boolean> {
        return async {
            episodeRepository.hasEpisodes(data.profileOwnerId)
        }
    }

    private fun CoroutineScope.hasChannelsAsync(data: ActionData): Deferred<Boolean> {
        return async {
            val filter = ChannelSearchFilters(data.profileOwnerId, data.onlyWithEpisodes)
            channelRepository.hasChannels(filter)
        }
    }

    data class ActionData(val playerId: Long, val profileOwnerId: Long) {
        val onlyWithEpisodes = playerId != profileOwnerId
    }
}
