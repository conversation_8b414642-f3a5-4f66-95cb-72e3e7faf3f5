package com.etermax.preguntados.episodes.core.infrastructure.repository.episode

import com.etermax.preguntados.episodes.core.domain.episode.pending.PendingEpisodeRepository
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import java.time.Duration

class RedisPendingEpisodeRepository(
    private val redis: RedisAsyncCommands<String, String>,
    private val ttlDuration: Duration,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : PendingEpisodeRepository {

    override suspend fun markAsProcessed(episodeId: String): Boolean {
        return withContext(dispatcher) {
            val key = buildKey(episodeId)
            val result = redis.incr(key).await()
            if (result > 1) {
                false
            } else {
                redis.expire(key, ttlDuration)
                true
            }
        }
    }

    private fun buildKey(episodeId: String): String {
        return "$KEY:$episodeId"
    }

    companion object {
        private const val KEY = "pr:e:pe:p"
    }
}
