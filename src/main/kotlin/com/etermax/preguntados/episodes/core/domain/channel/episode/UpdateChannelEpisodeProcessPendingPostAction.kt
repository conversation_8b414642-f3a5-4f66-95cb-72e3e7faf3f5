package com.etermax.preguntados.episodes.core.domain.channel.episode

import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostAction
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import org.slf4j.LoggerFactory

class UpdateChannelEpisodeProcessPendingPostAction(
    private val repository: ChannelUnpublishedEpisodesRepository
) : UpdateChannelEpisodePostAction {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun applies(oldEpisode: Episode, updateData: UpdateChannelEpisodePostActionData): Boolean {
        if (updateData.status != EpisodeStatus.PENDING) return false
        if (updateData.channelId == null && !oldEpisode.hasChannel) return false

        return true
    }

    override suspend fun execute(
        oldEpisode: Episode,
        updateData: UpdateChannelEpisodePostActionData,
        alreadyCheckIfApplies: Boolean
    ) {
        runCatching {
            if (!alreadyCheckIfApplies && !applies(oldEpisode, updateData)) return

            if (oldEpisode.channelId != null && oldEpisode.channelId != updateData.channelId) {
                val item = ChannelUnpublishedEpisode(oldEpisode.channelId, oldEpisode.id)
                repository.delete(item)
            }

            val channelId = updateData.channelId ?: oldEpisode.channelId!!

            if (channelId.isBlank()) return

            logger.info("Marking episode '${oldEpisode.id}' as unpublished to channel='$channelId'")
            val item = ChannelUnpublishedEpisode(channelId, oldEpisode.id)
            repository.add(item)
        }.onFailure {
            logger.error(
                "Fail to mark episode '${oldEpisode.id}' as unpublished to channel='${updateData.channelId}'",
                it
            )
        }
    }
}
