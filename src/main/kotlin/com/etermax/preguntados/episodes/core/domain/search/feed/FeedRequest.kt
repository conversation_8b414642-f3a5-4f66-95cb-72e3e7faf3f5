package com.etermax.preguntados.episodes.core.domain.search.feed

import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language

class FeedRequest(
    val userId: Long?,
    val language: Language?,
    val country: Country?,
    val limit: Int,
    val layout: String?,
    val paginationToken: String? = null,
    val debug: Boolean = false
)
