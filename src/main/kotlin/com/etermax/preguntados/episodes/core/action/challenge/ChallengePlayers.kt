package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayersSummary
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService

class ChallengePlayers(
    private val challengeService: ChallengeService,
    private val summaryService: SummaryService
) {
    suspend operator fun invoke(actionData: ActionData): ChallengePlayersSummary {
        return with(actionData) {
            if (userIds.isEmpty()) {
                return ChallengePlayersSummary(players = emptyList())
            }

            val context = challengeService.getChallengeContext(challengeId, playerId)
            val result = challengeService.ensurePlayersInitialized(userIds, context)

            summaryService.toChallengePlayersSummary(result)
        }
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String,
        val userIds: Set<Long>
    )
}
