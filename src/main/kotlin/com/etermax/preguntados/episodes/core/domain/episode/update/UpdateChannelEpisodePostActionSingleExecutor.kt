package com.etermax.preguntados.episodes.core.domain.episode.update

import com.etermax.preguntados.episodes.core.domain.episode.Episode

class UpdateChannelEpisodePostActionSingleExecutor(private val postActions: List<UpdateChannelEpisodePostAction>) {
    suspend fun execute(oldEpisode: Episode, updateData: UpdateChannelEpisodePostActionData) {
        val postAction = postActions.firstOrNull { it.applies(oldEpisode, updateData) }
        postAction?.execute(oldEpisode, updateData, alreadyCheckIfApplies = true)
    }
}
