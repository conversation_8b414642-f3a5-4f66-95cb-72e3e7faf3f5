package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.episode.ChannelEpisodeDeleteRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.TransactionBuilder
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelEpisodesCountItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.DynamoDBEpisodeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import kotlinx.coroutines.future.await
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.TransactDeleteItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactUpdateItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest

class DynamoDbChannelEpisodeDeleteRepository(
    private val client: DynamoDbEnhancedAsyncClient,
    private val episodeTable: DynamoDbAsyncTable<EpisodeItem>,
    private val channelTable: DynamoDbAsyncTable<UpdateChannelEpisodesCountItem>,
    private val channelEpisodesTable: DynamoDbAsyncTable<ChannelEpisodeItem>,
    private val episodesRepository: DynamoDBEpisodeRepository
) : ChannelEpisodeDeleteRepository {

    override suspend fun delete(episodeId: String, channel: Channel?) {
        if (channel != null) {
            handleTransactionFor(episodeId, channel)
        }

        deleteRelatedItemsFor(episodeId)
    }

    private suspend fun handleTransactionFor(episodeId: String, channel: Channel) {
        val transactionBuilder = TransactWriteItemsEnhancedRequest.builder()

        transactionBuilder.addDeleteEpisode(episodeId)
        transactionBuilder.addUpdateChannel(channel)
        transactionBuilder.addDeleteChannelEpisode(channel, episodeId)

        client.transactWriteItems(transactionBuilder.build()).await()
    }

    private fun TransactionBuilder.addDeleteEpisode(episodeId: String) {
        val episodeKey = Key.builder()
            .episodePk(episodeId)
            .sortValue(EpisodeItem.EPISODE_SK)
            .build()

        this.addDeleteItem(
            episodeTable,
            TransactDeleteItemEnhancedRequest.builder().key(episodeKey).build()
        )
    }

    private fun TransactionBuilder.addUpdateChannel(channel: Channel) {
        this.addUpdateItem(
            channelTable,
            TransactUpdateItemEnhancedRequest.builder(UpdateChannelEpisodesCountItem::class.java)
                .item(UpdateChannelEpisodesCountItem.from(channel))
                .build()
        )
    }

    private fun TransactionBuilder.addDeleteChannelEpisode(
        channel: Channel,
        episodeId: String
    ) {
        val channelEpisodeKey = Key.builder()
            .partitionValue(ChannelEpisodeItem.buildPartitionKey(channel.id))
            .sortValue(ChannelEpisodeItem.buildSortedKey(episodeId))
            .build()

        this.addDeleteItem(
            channelEpisodesTable,
            TransactDeleteItemEnhancedRequest.builder().key(channelEpisodeKey).build()
        )
    }

    private suspend fun deleteRelatedItemsFor(episodeId: String) {
        episodesRepository.delete(episodeId)
    }

    private fun Key.Builder.episodePk(episodeId: String): Key.Builder {
        // FIXME This can create problems, we should always search with the formal id
        val value = if (episodeId.startsWith(EpisodeItem.EPISODE_PREFIX)) {
            episodeId
        } else {
            EpisodeItem.buildPartitionKey(episodeId)
        }
        partitionValue(value)
        return this
    }
}
