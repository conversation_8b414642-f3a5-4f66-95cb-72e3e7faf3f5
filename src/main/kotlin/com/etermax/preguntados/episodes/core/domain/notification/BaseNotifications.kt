package com.etermax.preguntados.episodes.core.domain.notification

abstract class Notification(
    open val type: NotificationType,
    open val gameId: Long,
    open val senderProfile: NotificationProfile,
    open val receiverId: Long
)

abstract class NotificationWithHoursLeft(
    type: NotificationType,
    gameId: Long,
    senderProfile: NotificationProfile,
    receiverId: Long,
    open val hoursLeft: Int
) : Notification(type, gameId, senderProfile, receiverId)

data class SimpleNotification(
    val senderProfile: NotificationProfile,
    val receiverId: Long,
    val deeplink: String? = null,
    val payload: Map<String, String>?,
    val title: String? = null,
    val titleArgs: List<String>? = null,
    val body: String? = null,
    val bodyArgs: List<String>? = null
)
