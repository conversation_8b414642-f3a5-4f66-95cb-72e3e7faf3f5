package com.etermax.preguntados.episodes.core.infrastructure.repository.episode.persistence

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.NewEpisode
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.persistence.episode.EpisodePersistence
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class NewEpisodePersistence(@SerialName("e") val episode: EpisodePersistence) {

    fun to() = NewEpisode(episode.to())

    companion object {
        fun from(episode: Episode) = NewEpisodePersistence(EpisodePersistence.from(episode))
    }
}
