package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRemoveEpisodeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.TransactionBuilder
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.RemoveChannelFromEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelEpisodesCountItem
import kotlinx.coroutines.future.await
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.TransactDeleteItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactUpdateItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest

class DynamoDbChannelRemoveEpisodeRepository(
    private val client: DynamoDbEnhancedAsyncClient,
    private val removeChannelIdFromChannelTable: DynamoDbAsyncTable<RemoveChannelFromEpisodeItem>,
    private val updateChannelEpisodesCountTable: DynamoDbAsyncTable<UpdateChannelEpisodesCountItem>,
    private val channelEpisodesTable: DynamoDbAsyncTable<ChannelEpisodeItem>
) : ChannelRemoveEpisodeRepository {
    override suspend fun remove(episodeId: String, channel: Channel) {
        val transactionBuilder = TransactWriteItemsEnhancedRequest.builder()

        transactionBuilder.addRemoveChannelIdFromEpisode(episodeId)
        transactionBuilder.updateChannelEpisodesCount(channel)
        transactionBuilder.removeChannelEpisode(channel, episodeId)

        client.transactWriteItems(transactionBuilder.build()).await()
    }

    private fun TransactionBuilder.addRemoveChannelIdFromEpisode(episodeId: String) {
        val removeChannelIdItem = RemoveChannelFromEpisodeItem(episodeId)

        this.addUpdateItem(
            removeChannelIdFromChannelTable,
            TransactUpdateItemEnhancedRequest.builder(RemoveChannelFromEpisodeItem::class.java)
                .item(removeChannelIdItem).build()
        )
    }

    private fun TransactionBuilder.updateChannelEpisodesCount(channel: Channel) {
        val item = UpdateChannelEpisodesCountItem.from(channel)
        this.addUpdateItem(
            updateChannelEpisodesCountTable,
            TransactUpdateItemEnhancedRequest.builder(UpdateChannelEpisodesCountItem::class.java).item(item).build()
        )
    }

    private fun TransactionBuilder.removeChannelEpisode(channel: Channel, episodeId: String) {
        val key = Key
            .builder()
            .partitionValue(ChannelEpisodeItem.buildPartitionKey(channel.id))
            .sortValue(ChannelEpisodeItem.buildSortedKey(episodeId))
            .build()

        this.addDeleteItem(
            channelEpisodesTable,
            TransactDeleteItemEnhancedRequest.builder().key(key).build()
        )
    }
}
