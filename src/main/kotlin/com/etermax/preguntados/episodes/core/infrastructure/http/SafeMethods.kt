package com.etermax.preguntados.episodes.core.infrastructure.http

import com.etermax.preguntados.episodes.core.infrastructure.http.error.GatewayProtocolException
import com.etermax.preguntados.episodes.core.infrastructure.http.error.GatewayUnexpectedException
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import io.github.resilience4j.kotlin.circuitbreaker.executeSuspendFunction
import io.github.resilience4j.kotlin.retry.decorateSuspendFunction
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.serialization.*
import io.ktor.util.reflect.*
import io.ktor.utils.io.errors.*

suspend inline fun <reified T> HttpResponse.safeReceive(): T {
    try {
        return call.body(typeInfo<T>()) as T
    } catch (exception: NullPointerException) {
        throw GatewayProtocolException(request.url, status, exception)
    } catch (exception: JsonConvertException) {
        throw GatewayProtocolException(request.url, status, exception)
    }
}

suspend inline fun HttpClient.safeGet(
    urlString: String,
    block: HttpRequestBuilder.() -> Unit = {}
): HttpResponse {
    try {
        return get(urlString, block)
    } catch (exception: IOException) {
        throw GatewayUnexpectedException("Failed GET $urlString", exception)
    }
}

suspend inline fun HttpClient.safePost(
    urlString: String,
    block: HttpRequestBuilder.() -> Unit = {}
): HttpResponse {
    try {
        return post(urlString, block)
    } catch (exception: IOException) {
        throw GatewayUnexpectedException("Failed POST $urlString", exception)
    }
}

suspend inline fun HttpClient.safePut(
    urlString: String,
    block: HttpRequestBuilder.() -> Unit = {}
): HttpResponse {
    try {
        return put(urlString, block)
    } catch (exception: IOException) {
        throw GatewayUnexpectedException("Failed PUT $urlString", exception)
    }
}

suspend inline fun <reified T> HttpClient.resilientPost(
    urlString: String,
    resilienceBundle: EndpointResilienceBundle,
    crossinline requestBuilder: HttpRequestBuilder.() -> Unit = {},
    crossinline responseBuilder: suspend (HttpResponse) -> T
): T = with(resilienceBundle) {
    circuitBreaker.executeSuspendFunction(
        retry.decorateSuspendFunction {
            val response = safePost(urlString, requestBuilder)
            responseBuilder(response)
        }
    )
}

suspend inline fun <reified T> HttpClient.resilientPut(
    urlString: String,
    resilienceBundle: EndpointResilienceBundle,
    crossinline requestBuilder: HttpRequestBuilder.() -> Unit = {},
    crossinline responseBuilder: suspend (HttpResponse) -> T
): T = with(resilienceBundle) {
    circuitBreaker.executeSuspendFunction(
        retry.decorateSuspendFunction {
            val response = safePut(urlString, requestBuilder)
            responseBuilder(response)
        }
    )
}

suspend inline fun <reified T> HttpClient.resilientGet(
    urlString: String,
    resilienceBundle: EndpointResilienceBundle,
    crossinline requestBuilder: HttpRequestBuilder.() -> Unit = {},
    crossinline responseBuilder: suspend (HttpResponse) -> T
): T = with(resilienceBundle) {
    circuitBreaker.executeSuspendFunction(
        retry.decorateSuspendFunction {
            val response = safeGet(urlString, requestBuilder)
            responseBuilder(response)
        }
    )
}
