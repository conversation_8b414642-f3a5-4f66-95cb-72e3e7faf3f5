package com.etermax.preguntados.episodes.core.domain.channel

import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.OffsetDateTime
import kotlin.math.max

data class Channel(
    val id: String,
    val name: String,
    val description: String?,
    val website: String?,
    val coverUrl: String,
    val subscribed: Boolean,
    val ownerId: Long,
    val statistics: ChannelStatistics,
    val creationDate: OffsetDateTime,
    val lastModificationDate: OffsetDateTime,
    val type: ChannelType,
    val language: Language?,
    val order: Long,
    val orderType: ChannelOrderType
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    val episodesCount get(): Int = statistics.episodes

    val hasLanguage get(): Boolean = language != null

    fun addEpisodes(count: Int): Channel {
        val newCount = episodesCount + count
        return update(episodesCount = newCount)
    }

    fun removeEpisodes(count: Int): Channel {
        val updatedCount = episodesCount - count
        if (updatedCount < 0) {
            logger.warn("RemoveEpisode - Episode '$id' has negative episodes count '$updatedCount'. Default to 0.")
        }
        val newCount = max(updatedCount, 0)
        return update(episodesCount = newCount)
    }

    fun setLanguage(language: Language): Channel {
        return update(language = language)
    }

    fun setLastModificationDate(date: OffsetDateTime): Channel {
        return update(lastModificationDate = date)
    }

    private fun update(
        episodesCount: Int? = null,
        language: Language? = null,
        lastModificationDate: OffsetDateTime? = null,
        score: Int? = null
    ): Channel {
        val statistics = ChannelStatistics(
            subscribers = this.statistics.subscribers,
            episodes = episodesCount ?: this.statistics.episodes,
            score = score ?: this.statistics.score,
            quality = this.statistics.quality
        )

        return Channel(
            id = id,
            name = name,
            description = description,
            website = website,
            coverUrl = coverUrl,
            subscribed = subscribed,
            ownerId = ownerId,
            statistics = statistics,
            creationDate = creationDate,
            lastModificationDate = lastModificationDate ?: this.lastModificationDate,
            type = type,
            language = language ?: this.language,
            order = order,
            orderType = orderType
        )
    }

    fun updateScore(score: Int): Channel {
        if (score < 0) {
            logger.warn("UpdateScore - Channel '$id' received a negative score '$score'. Default to 0.")
        }
        val newScore = max(score, 0)
        return update(score = newScore)
    }

    fun isOwnedBy(playerId: Long) = ownerId == playerId
}
