package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.exception.*
import com.etermax.preguntados.episodes.core.domain.moderation.ModerationService
import com.etermax.preguntados.episodes.core.domain.moderation.UrlValidatorService
import com.etermax.preguntados.external.services.core.domain.api.profile.Language

class ChannelValidatorService(
    private val moderationService: ModerationService,
    private val urlValidatorService: UrlValidatorService
) {
    suspend fun validateUpdate(
        data: ActionData,
        channel: Channel
    ) {
        if (data.playerId != channel.ownerId) throw InvalidUpdateNotOwnChannelException(
            playerId = data.playerId,
            channelId = channel.id,
            ownerId = channel.ownerId
        )

        if (data.website != channel.website) {
            validateWebsite(data.website)
        }
        if (data.coverUrl != channel.coverUrl) {
            validateCover(data.coverUrl)
        }

        moderateNameAndDescriptionWithLanguage(
            data = data,
            channel = channel,
            language = channel.language ?: data.moderationLanguage
        )
    }

    suspend fun validateCreate(data: ActionData) {
        validateWebsite(data.website)
        validateCover(data.coverUrl)
        validateName(data.name, data.moderationLanguage, data.playerId)
        validateDescription(data.description, data.moderationLanguage, data.playerId)
    }

    private suspend fun validateName(name: String, language: Language, playerId: Long) {
        if (name.isEmpty() || name.isBlank()) {
            throw ChannelNameEmptyException(name)
        }

        if (name.length > 30) {
            throw ChannelNameLongerTooLongException(name, maxCharacters = 30)
        }

        val isNameAllowed = moderationService.isTextAllowed(playerId, language, name)
        if (!isNameAllowed) {
            throw ChannelNameNotAllowedException(name)
        }
    }

    private suspend fun validateDescription(description: String?, language: Language, playerId: Long) {
        if (description.isNullOrEmpty()) return

        if (description.length > 80) {
            throw ChannelDescriptionLongerTooLongException(description, maxCharacters = 80)
        }

        val isDescriptionAllowed = moderationService.isTextAllowed(playerId, language, description)
        if (!isDescriptionAllowed) {
            throw ChannelDescriptionNotAllowedException(description)
        }
    }

    private fun validateWebsite(url: String?) {
        if (url == null) return
        if (!urlValidatorService.isValid(url)) {
            throw ChannelInvalidWebsiteException(url)
        }
    }

    private fun validateCover(url: String) {
        if (!urlValidatorService.isValid(url)) {
            throw ChannelInvalidCoverUrlException(url)
        }
    }

    private suspend fun moderateNameAndDescriptionWithLanguage(
        data: ActionData,
        channel: Channel,
        language: Language
    ) {
        if (data.name != channel.name) {
            validateName(data.name, language, data.playerId)
        }
        if (data.description != channel.description) {
            validateDescription(data.description, language, data.playerId)
        }
    }
}
