package com.etermax.preguntados.episodes.core.domain.quality

import kotlinx.serialization.Polymorphic

@Polymorphic
sealed class Quality(val id: String, val quality: Int, type: String) {
    init {
        require(id.isNotBlank()) { "$type ID cannot be blank" }
        require(quality >= 0) { "Quality must be a non-negative integer" }
    }
}

class ChannelQuality(id: String, quality: Int) : Quality(id, quality, type = "Channel")

class EpisodeQuality(id: String, quality: Int) : Quality(id, quality, type = "Episode")
