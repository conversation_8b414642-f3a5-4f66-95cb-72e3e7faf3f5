package com.etermax.preguntados.episodes.core.infrastructure.search.repository

import com.etermax.preguntados.episodes.core.domain.search.UserBasedRepository
import com.etermax.preguntados.episodes.core.infrastructure.http.resilientPost
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import com.etermax.preguntados.external.services.core.infrastructure.http.safeReceive
import io.ktor.client.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonArray
import org.slf4j.LoggerFactory

class HttpUserBasedRepository(
    private val client: HttpClient,
    private val resilienceBundle: EndpointResilienceBundle
) : UserBasedRepository {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun find(episodesId: List<String>, offset: Int, perPage: Int, language: Language?): List<String> {
        return try {
            val body = createBody(episodesId, offset, perPage, language)
            client.resilientPost(
                urlString = URL,
                resilienceBundle = resilienceBundle,
                requestBuilder = {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    setBody(body)
                    expectSuccess = true
                }
            ) {
                return@resilientPost extractEpisodes(it)
            }
        } catch (e: Exception) {
            logger.error("Cannot get user based episodes", e)
            return emptyList()
        }
    }

    private fun createBody(episodesId: List<String>, offset: Int, perPage: Int, language: Language?) = UserBasedRequest(
        episodeIds = episodesId,
        recosWeight = RECOS_WEIGHT,
        likesWeight = LIKES_WEIGHT,
        playsWeight = PLAYS_WEIGHT,
        language = language?.name,
        offset = offset,
        perPage = perPage
    )

    private suspend fun extractEpisodes(response: HttpResponse): List<String> {
        if (response.status != HttpStatusCode.OK) {
            logger.info("Could not find user based episodes")
            return emptyList()
        }

        val items = response.safeReceive<List<JsonArray>>()
        return items.mapNotNull {
            it.firstOrNull()?.toString()?.removeSurrounding("\"")
        }
    }

    private companion object {
        const val URL = "/ds-api-episode-recommendations/api/episode-recommendations"
        const val RECOS_WEIGHT = 0.7
        const val LIKES_WEIGHT = 0.25
        const val PLAYS_WEIGHT = 0.05
    }
}

@Serializable
data class UserBasedRequest(
    @SerialName("episode_ids") val episodeIds: List<String>,
    @SerialName("recos_weight") val recosWeight: Double,
    @SerialName("likes_weight") val likesWeight: Double,
    @SerialName("plays_weight") val playsWeight: Double,
    @SerialName("language") val language: String?,
    @SerialName("offset") val offset: Int,
    @SerialName("per_page") val perPage: Int
)
