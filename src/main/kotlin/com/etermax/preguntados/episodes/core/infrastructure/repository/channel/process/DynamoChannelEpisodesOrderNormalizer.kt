package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.process

import com.etermax.preguntados.episodes.core.domain.channel.episode.ChannelEpisodeOrder
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.DynamoChannelEpisodesOrderRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class DynamoChannelEpisodesOrderNormalizer(
    private val isNormalizerEnabled: Boolean,
    private val channelEpisodesOrderRepository: DynamoChannelEpisodesOrderRepository
) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend fun normalize(items: List<ChannelEpisodeItem>) {
        if (!isNormalizerEnabled || items.isEmpty()) return

        // The channelId is not projected in the index used for this query
        val channelId = items.first().channelIdFromPartitionKey()

        val itemsToNormalize = items.mapNotNull { item ->
            if (item.isNormalized()) return@mapNotNull null

            ChannelEpisodeOrder(item.channelIdFromPartitionKey(), item.episodeId, item.episodeOrder.toLong())
        }

        if (itemsToNormalize.isEmpty()) {
            return
        }

        val episodesIds = itemsToNormalize.map { it.episodeId }
        runCatching {
            logger.warn("Normalizing episodes='$episodesIds'. ChannelId='$channelId'")
            channelEpisodesOrderRepository.put(itemsToNormalize)
        }.onFailure {
            logger.error("Fail to normalize episodes='$episodesIds'. ChannelId='$channelId'", it)
        }
    }
}
