package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.devices.domain.DeviceType
import com.etermax.preguntados.analytics.actions.TrackEpisodeAnalytics
import com.etermax.preguntados.analytics.service.PlayedEvent
import com.etermax.preguntados.episodes.core.domain.episode.*
import com.etermax.preguntados.episodes.core.domain.episode.notification.EpisodeNotificationService
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentService
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingRepository
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.notification.InviterNotificationData
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import kotlinx.coroutines.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class PlayEpisode(
    private val episodeRepository: EpisodeRepository,
    private val progressContentService: ProgressContentService,
    private val episodeNotificationService: EpisodeNotificationService,
    private val profileService: ProfileService,
    private val rankingRepository: RankingRepository,
    private val trackMetric: TrackEpisodeAnalytics,
    private val episodePlayersByOwnerRepository: EpisodePlayersByOwnerRepository,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) {
    val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): RemainingContent {
        return with(actionData) {
            logger.info("Player $playerId played episode $episodeId")
            episodeRepository.findById(episodeId)?.let { episode ->
                val progressContent = progressContentService.findProgress(episode.id, playerId)
                initializePlayerInRanking(playerId, episodeId, progressContent)
                notifyPlayed(episode, progressContent)
                notifyInviter(episode)
                savePlayerRelationshipWithOwner(episode.ownerId, actionData.playerId)

                val event = PlayedEvent(playerId, episodeId, episode.ownerId, from.country, from.device, from.isTablet)
                trackMetric(event)

                toRemainingContent(episode, progressContent)
            } ?: throw EpisodeNotFoundException(episodeId)
        }
    }

    private suspend fun savePlayerRelationshipWithOwner(ownerId: Long, playerId: Long) {
        if (ownerId == playerId) return

        withContext(dispatcher + SupervisorJob()) {
            launch {
                logger.info("PlayEpisode::Saving player {} relationship with owner {}", playerId, ownerId)
                episodePlayersByOwnerRepository.addPlayer(ownerId, playerId)
            }
        }
    }

    private suspend fun ActionData.toRemainingContent(
        episode: Episode,
        progressContent: ProgressContent?
    ): RemainingContent {
        val remainingContent = calculateRemainingContent(episode, progressContent, playerId)
        val ownerProfile = profileService.find(episode.ownerId)
        return RemainingContent(remainingContent, EpisodeSummary.from(episode, ownerProfile))
    }

    private suspend fun ActionData.notifyPlayed(episode: Episode, progressContent: ProgressContent?) {
        if (progressContent != null) {
            logger.info("[NOTIFICATIONS] Skip send notification to player ${episode.ownerId} because already played episode ${episode.id}")
            return
        }

        logger.info("[NOTIFICATIONS] Notifying episode played to ${episode.ownerId} from $playerId")
        episodeNotificationService.notifyPlayed(
            senderId = playerId,
            receiverId = episode.ownerId,
            episodeId = episode.id
        )
    }

    private suspend fun ActionData.notifyInviter(episode: Episode) {
        if (inviterNotificationData == null) return

        logger.info("[NOTIFICATIONS] Notifying to inviter ${inviterNotificationData.inviterId}")
        val playerProfile = inviterNotificationData.playerId?.let { profileService.find(it) }
        episodeNotificationService.notifyInviter(
            inviterId = inviterNotificationData.inviterId,
            playerName = inviterNotificationData.playerName,
            episodeId = episode.id,
            playerProfile = playerProfile
        )
    }

    private suspend fun calculateRemainingContent(
        episode: Episode,
        progressContent: ProgressContent?,
        playerId: Long
    ): List<String> {
        return if (progressContent == null || progressContent.hasFinishedEpisode) {
            episode.contents
        } else {
            val lastContentIndex = episode.contents.indexOf(progressContent.lastContentId)
            val remainingContent = episode.contents.drop(lastContentIndex + 1)
            remainingContent.ifEmpty { markAsFinished(episode, playerId) }
        }
    }

    private suspend fun markAsFinished(episode: Episode, playerId: Long): List<String> {
        progressContentService.registerProgress(
            episode.id,
            playerId,
            episode.contents.last(),
            episode.language.name,
            true
        )
        return episode.contents
    }

    private suspend fun initializePlayerInRanking(
        playerId: Long,
        episodeId: String,
        progressContent: ProgressContent?
    ) {
        if (progressContent != null) return
        rankingRepository.incrementScore(playerId, Domain(episodeId), INITIAL_SCORE)
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String,
        val from: From,
        val inviterNotificationData: InviterNotificationData? = null
    )

    private companion object {
        const val INITIAL_SCORE = 0
    }
}

data class From(
    private val requestCountry: String?,
    private val requestDevice: DeviceType?,
    private val requestIsTablet: Boolean?
) {
    val device: DeviceType = requestDevice ?: DeviceType.UNAVAILABLE
    val isTablet: Boolean = requestIsTablet ?: false
    val country: Country = requestCountry?.let { toCountry(it) } ?: Country.GX

    private fun toCountry(string: String): Country? = try {
        Country.valueOf(string.uppercase())
    } catch (_: IllegalArgumentException) {
        null
    }
}
