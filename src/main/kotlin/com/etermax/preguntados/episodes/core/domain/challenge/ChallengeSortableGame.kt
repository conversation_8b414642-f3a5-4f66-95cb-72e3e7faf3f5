package com.etermax.preguntados.episodes.core.domain.challenge

import com.etermax.preguntados.sortable.games.core.domain.pagination.EpochMills
import com.etermax.preguntados.sortable.games.core.domain.pagination.PlayerId
import com.etermax.preguntados.sortable.games.core.domain.pagination.SortableGame
import java.time.OffsetDateTime

class ChallengeSortableGame(private val details: ChallengeDetails) : SortableGame {
    override val id = details.challenge.id

    override suspend fun hasPlayerWon(playerId: PlayerId): Boolean {
        return isFinished() && (details.getPlayerPosition(playerId) ?: NO_POSITION) <= 3
    }

    override fun isFinished(): <PERSON><PERSON>an {
        val now = OffsetDateTime.now()
        return now.isAfter(details.challenge.endDate)
    }

    override fun isPlayerAwaitingToJoin(playerId: Long): Boolean {
        return details.getPlayerStatus(playerId)!! == ChallengePlayer.Status.PENDING
    }

    override fun isPlayerTurn(playerId: PlayerId): Boolean {
        return true // Player can always play.
    }

    override fun playersIsAvailableFor(): List<PlayerId> {
        return details.players.map { it.playerId }.toList()
    }

    override suspend fun sortingTime(playerId: Long): EpochMills {
        return details.challenge.endDate.toInstant().toEpochMilli()
    }

    fun isInProgress(): Boolean {
        val now = OffsetDateTime.now()
        return now.isAfter(details.challenge.startDate) && now.isBefore(details.challenge.endDate)
    }

    fun hasPlayerFinished(playerId: Long): Boolean {
        return details.getPlayerStatus(playerId)!! == ChallengePlayer.Status.FINISHED
    }

    fun isPlayerTop3(playerId: Long): Boolean {
        return (details.getPlayerPosition(playerId) ?: NO_POSITION) <= 3
    }

    private companion object {
        const val NO_POSITION = 1000
    }
}
