package com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables

import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class UpdateEpisodeQualityItem(
    @get:DynamoDbAttribute(EpisodeItemAttributes.EPISODE_ID) var episodeId: String = "",
    @get:DynamoDbAttribute(EpisodeItemAttributes.QUALITY) var quality: Int? = null
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = EpisodeItem.buildPartitionKey(episodeId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = EpisodeItem.EPISODE_SK
}
