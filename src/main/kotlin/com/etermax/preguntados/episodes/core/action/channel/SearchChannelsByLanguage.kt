package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.channel.ChannelReduced
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelByLanguageRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelByLanguageFilters
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter
import com.etermax.preguntados.external.services.core.domain.api.profile.Language

class SearchChannelsByLanguage(
    private val repository: ChannelByLanguageRepository,
    private val paginationSize: Int
) {
    suspend operator fun invoke(data: ActionData): PaginatedItems<ChannelReduced> {
        return findChannels(data)
    }

    private suspend fun findChannels(data: ActionData): PaginatedItems<ChannelReduced> = with(data) {
        val filters = ChannelByLanguageFilters(ownerId, language, includeWithNoLanguage)
        val pagination = PaginationFilter(paginationSize, lastEvaluatedKey)
        return repository.search(filters, pagination)
    }

    data class ActionData(
        val ownerId: Long,
        val language: Language,
        val lastEvaluatedKey: String?,
        val includeWithNoLanguage: Boolean = true
    )
}
