package com.etermax.preguntados.episodes.core.domain.search

import com.etermax.preguntados.episodes.core.domain.account.PlayerAccount
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary

data class OverviewInfo(
    val playerAccounts: List<PlayerAccount>,
    val episodes: List<EpisodeSummary>,
    val channels: List<ChannelSummary>
)
