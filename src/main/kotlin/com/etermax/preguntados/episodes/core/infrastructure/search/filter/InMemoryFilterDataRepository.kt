package com.etermax.preguntados.episodes.core.infrastructure.search.filter

import com.etermax.preguntados.episodes.core.domain.search.filter.FilterDataRepository

class InMemoryFilterDataRepository : FilterDataRepository {
    private val storage: MutableMap<String, ByteArray> = mutableMapOf()

    override suspend fun save(scope: String, data: ByteArray) {
        storage[scope] = data
    }

    override suspend fun getByScope(scope: String): ByteArray {
        return storage[scope] ?: ByteArray(0)
    }

    /**
     * Clears all stored data. Useful for test setup/teardown.
     */
    fun clear() {
        storage.clear()
    }
}
