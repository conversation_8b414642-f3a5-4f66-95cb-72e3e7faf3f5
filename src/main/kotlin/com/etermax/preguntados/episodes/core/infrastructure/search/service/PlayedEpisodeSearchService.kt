package com.etermax.preguntados.episodes.core.infrastructure.search.service

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.search.PlayedEpisodeSearchRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.OpenSearchPlayedRepository

class PlayedEpisodeSearchService(
    private val playedRepository: OpenSearchPlayedRepository,
    private val repository: PlayedEpisodeSearchRepository,
    private val episodesRepository: EpisodeRepository,
    private val clock: Clock
) {

    suspend fun search(playerId: Long, language: String?, restartCache: Boolean): List<String> {
        if (restartCache) {
            return fetchAndUpdateCache(playerId, language)
        }
        return repository.find(playerId, language) ?: fetchAndUpdateCache(playerId, language)
    }

    private suspend fun fetchAndUpdateCache(playerId: Long, language: String?): List<String> {
        val ids = playedRepository.lastPlayedEpisodesIds(playerId, OFFSET, LIMIT, language)
        val episodesId = episodesRepository
            .findByIds(ids.reversed().distinct())
            .filter { (it.startDate?.toMillis() ?: 0) <= clock.now().minusHours(6).toMillis() }
            .map { it.id }
        repository.save(playerId, language, episodesId)
        return episodesId
    }

    private companion object {
        const val OFFSET = 0
        const val LIMIT = 10
    }
}
