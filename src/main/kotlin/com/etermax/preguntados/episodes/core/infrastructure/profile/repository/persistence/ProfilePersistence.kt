package com.etermax.preguntados.episodes.core.infrastructure.profile.repository.persistence

import com.etermax.preguntados.episodes.core.domain.profile.Profile
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId

@Serializable
data class ProfilePersistence(
    @SerialName("i") val playerId: Long,
    @SerialName("n") val name: String,
    @SerialName("c") val country: String,
    @SerialName("p") val photoUrl: String? = null,
    @SerialName("s") val socialProfile: SocialProfilePersistence? = null,
    @SerialName("j") val joinDateInMillis: Long,
    @SerialName("r") val restriction: String? = null
) {

    fun to() = Profile(
        playerId = playerId,
        name = name,
        country = country,
        photoUrl = photoUrl,
        socialProfile = socialProfile?.to(),
        joinDate = toOffsetDateTime(),
        restriction = restriction
    )

    private fun toOffsetDateTime(): OffsetDateTime {
        return OffsetDateTime.ofInstant(Instant.ofEpochMilli(joinDateInMillis), ZoneId.systemDefault())
    }

    companion object {
        fun from(profile: Profile) = with(profile) {
            ProfilePersistence(
                playerId = playerId,
                name = name,
                country = country,
                photoUrl = photoUrl,
                socialProfile = socialProfile?.let { SocialProfilePersistence.from(it) },
                joinDateInMillis = joinDate.toInstant().toEpochMilli(),
                restriction = restriction
            )
        }
    }
}
