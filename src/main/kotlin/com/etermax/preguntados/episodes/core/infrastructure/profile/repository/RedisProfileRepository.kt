package com.etermax.preguntados.episodes.core.infrastructure.profile.repository

import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.repository.ProfileRepository
import com.etermax.preguntados.episodes.core.infrastructure.profile.repository.persistence.ProfilePersistence
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.time.Duration

class RedisProfileRepository(
    private val redis: RedisAsyncCommands<String, String>,
    private val ttlDuration: Duration,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : ProfileRepository {

    private val jsonMapper = Json { ignoreUnknownKeys = true }

    override suspend fun find(playerId: Long): Profile? {
        return withContext(dispatcher) {
            val key = createKey(playerId)
            redis.get(key).await()?.let { decode(it) }
        }
    }

    override suspend fun find(playerIds: List<Long>): Map<Long, Profile> {
        return withContext(dispatcher) {
            val keys = playerIds.map { createKey(it) }
            if (keys.isEmpty()) {
                return@withContext mapOf()
            }
            redis.mget(*keys.toTypedArray()).await().filter { it.hasValue() }
                .associate { decode(it.value).let { profile -> Pair(profile.playerId, profile) } }
        }
    }

    override suspend fun save(profile: Profile) {
        withContext(dispatcher) {
            val key = createKey(profile.playerId)
            redis.setex(key, ttlDuration.seconds, encode(profile)).await()
        }
    }

    private fun decode(profile: String) = jsonMapper.decodeFromString<ProfilePersistence>(profile).to()

    private fun encode(profile: Profile) = jsonMapper.encodeToString(ProfilePersistence.from(profile))

    private fun createKey(playerId: Long) = "$KEY:$playerId"

    private companion object {
        const val KEY = "pr:e:p"
    }
}
