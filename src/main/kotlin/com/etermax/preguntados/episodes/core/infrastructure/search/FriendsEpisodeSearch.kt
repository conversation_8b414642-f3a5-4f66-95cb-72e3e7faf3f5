package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.search.data.PlayerEpisodeData
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.future.await
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch.core.SearchResponse

class FriendsEpisodeSearch(
    private val client: OpenSearchAsyncClient,
    private val indexName: String
) {

    suspend fun search(friends: List<Long>, episodeId: String): List<Long> {
        return friends.chunked(CHUNKED_SIZE).map { chunk ->
            find(chunk, episodeId)
        }.awaitAll().flatten()
    }

    private suspend fun find(friends: List<Long>, episodeId: String): Deferred<List<Long>> = coroutineScope {
        async {
            val request = OpenSearchRequestFactory(indexName, 0, friends.size)
                .filterByEpisode(EpisodeItem.buildPartitionKey(episodeId))
                .filterByPlayer(friends)
                .build()

            val response = client.search(request, PlayerEpisodeData::class.java).await()
            if (response.hits().hits().isEmpty()) {
                emptyList()
            } else {
                response.toPlayerIds()
            }
        }
    }

    private fun OpenSearchRequestFactory.filterByPlayer(playersId: List<Long>) = this.also {
        mustTerms(PLAYER_ID_FIELD, playersId)
    }

    private fun OpenSearchRequestFactory.filterByEpisode(episodeId: String) = this.also {
        filterTerm(EPISODE_ID_FIELD, episodeId)
    }

    private fun SearchResponse<PlayerEpisodeData>.toPlayerIds(): List<Long> {
        return hits().hits().mapNotNull {
            val data = it.source() as PlayerEpisodeData
            data.playerId?.toLong()
        }
    }

    private companion object {
        const val CHUNKED_SIZE = 1000
        const val PLAYER_ID_FIELD = "player_id"
        const val EPISODE_ID_FIELD = "episode_id"
    }
}
