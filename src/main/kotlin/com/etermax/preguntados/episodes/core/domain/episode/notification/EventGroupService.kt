package com.etermax.preguntados.episodes.core.domain.episode.notification

import com.etermax.preguntados.episodes.core.domain.time.Clock
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import kotlin.math.max

class EventGroupService(
    private val eventGroupsRepository: EventGroupsRepository,
    private val clock: Clock,
    private val eventGroupsConfiguration: EventGroupsConfiguration
) {

    val logger: Logger = LoggerFactory.getLogger(this::class.java)
    suspend fun onEvent(episodeId: String, type: EventType, playerId: Long): Int {
        logger.info("[NOTIFICATIONS] Checking event group for player $playerId and type $type")
        val configuration = eventGroupsConfiguration.get(type) ?: return 1
        logger.info("[NOTIFICATIONS] Configuration for player $playerId and type $type is $configuration")
        if (!configuration.isEnabled) return 1

        val eventGroup = eventGroupsRepository.increment(episodeId, type, playerId)
        logger.info("[NOTIFICATIONS] Event group for player $playerId and type $type is $eventGroup")
        if (eventGroup.groupSize == null) {
            return 1
        }

        val groupEndDate = eventGroup.lastResetDate?.plus(configuration.groupDuration)
        if (groupEndDate != null && groupEndDate <= clock.now()) {
            return eventGroup.count
        }

        return if (eventGroup.reachedSize()) return eventGroup.count else 0
    }

    suspend fun reset(episodeId: String, eventType: EventType, playerId: Long) {
        logger.info("[NOTIFICATIONS] Reseting group for episode $episodeId and type $eventType")
        val configuration = eventGroupsConfiguration.get(eventType) ?: return
        if (!configuration.isEnabled) return
        val eventGroup = eventGroupsRepository.find(episodeId, eventType) ?: return
        logger.info("[NOTIFICATIONS] Event group for episode $episodeId and type $eventType is $eventGroup")
        if (eventGroup.count < configuration.minCountToGroup && eventGroup.groupSize == null) return

        val groupEndDate = eventGroup.lastResetDate?.plus(configuration.groupDuration) ?: clock.now()
        if (!eventGroup.reachedSize() && groupEndDate > clock.now()) {
            return
        }

        val groupSize = if (eventGroup.groupSize == null) {
            configuration.groupSizes.first()
        } else {
            val reachedGroupSize =
                configuration.groupSizes.firstOrNull { eventGroup.count < it } ?: configuration.groupSizes.last()
            max(reachedGroupSize, eventGroup.groupSize)
        }

        val resetGroup = EventGroup(episodeId, eventType, playerId, 0, groupSize, clock.now())
        logger.info("[NOTIFICATIONS] Reseted group for episode $episodeId and type $eventType to $")
        eventGroupsRepository.save(episodeId, eventType, resetGroup)
    }
}
