package com.etermax.preguntados.episodes.core.domain.search

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language

data class SearchParameters(
    val playerId: Long? = null,
    val language: Language? = null,
    val country: Country? = null,
    val episodeType: EpisodeType? = null,
    val status: EpisodeStatus? = null,
    val name: String? = null,
    val episodeId: String? = null,
    val channelId: String? = null,
    val sort: SortEpisode? = null,
    val isRestricted: Boolean = false,
    val offset: Int,
    val limit: Int
)
