package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelSearchFilters
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.future.await
import kotlinx.coroutines.reactive.asFlow
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Expression
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

class DynamoDBChannelRepository(
    dynamoDbClient: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<ChannelItem>
) : ChannelRepository, DynamoDBRepository<ChannelItem>(dynamoDbClient, table) {
    override suspend fun add(channel: Channel) {
        val item = ChannelItem.from(channel)
        saveItem(item, ChannelItem::class.java)
    }

    override suspend fun put(updatedChannel: Channel) {
        val updateRequest = UpdateItemEnhancedRequest
            .builder(ChannelItem::class.java)
            .item(ChannelItem.from(updatedChannel))
            .build()

        table.updateItem(updateRequest).await()
    }

    override suspend fun delete(channel: Channel) {
        val partitionKey = Key.builder().partitionValue(ChannelItem.buildPartitionKey(channel.id)).build()
        val conditional = QueryConditional.keyEqualTo(partitionKey)
        val itemsToDelete = table.query(conditional).items().asFlow().toList()

        if (itemsToDelete.isEmpty()) {
            return
        }

        itemsToDelete.chunked(25).forEach { chunksChannel ->
            val channelsBatch = chunksChannel.map {
                makeDeleteBatch(it, ChannelItem::class.java)
            }
            deleteItemsBulkWithRetry(channelsBatch, ChannelItem::class.java)
        }
    }

    override suspend fun findById(channelId: String): Channel? {
        val partitionKey = ChannelItem.buildPartitionKey(channelId)
        val item = findItem(
            Key.builder()
                .partitionValue(partitionKey)
                .sortValue(ChannelItem.CHANNEL_SK)
                .build()
        )
        return item?.toDomain()
    }

    override suspend fun findById(channelsId: List<String>): List<Channel> {
        if (channelsId.isEmpty()) return emptyList()

        val batches = channelsId.map {
            val key = Key.builder()
                .partitionValue(it)
                .sortValue(ChannelItem.CHANNEL_SK)
                .build()
            findItemBatch(key, ChannelItem::class.java)
        }

        val items = findItemsBulk(batches)

        return items.mapNotNull { it?.toDomain() }
    }

    override suspend fun search(filters: ChannelSearchFilters, pagination: PaginationFilter): PaginatedItems<Channel> {
        // TODO: When include private channels, exclude them from query when searching channels from another player

        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(filters.ownerId)
        }

        val requestBuilder = QueryEnhancedRequest
            .builder()
            .queryConditional(queryConditional)
            .limit(pagination.itemsSize)
            .scanIndexForward(false)

        pagination.lastEvaluatedKey?.takeIf { it.isNotBlank() }?.let { lastKey ->
            val skConditional = QueryConditional.sortLessThan {
                it.partitionValue(filters.ownerId)
                    .sortValue(lastKey.toLong())
            }
            requestBuilder.queryConditional(skConditional)
        }

        if (filters.onlyWithEpisodes) {
            requestBuilder.onlyWithEpisodes()
        }

        val request = requestBuilder.build()

        val allItems = mutableListOf<Channel>()
        var lastEvaluatedKey: String? = null
        var currentRequest = request

        while (allItems.size < pagination.itemsSize) {
            val page = table
                .index(ChannelIndexes.BY_OWNER_AND_LAST_MODIFICATION)
                .query(currentRequest)
                .asFlow()
                .toList()
                .firstOrNull()

            if (page == null) {
                lastEvaluatedKey = null
                break
            }

            val items = page.items()
                .asFlow()
                .mapNotNull { it.toDomain() }
                .toList()

            allItems.addAll(items)

            lastEvaluatedKey = page.lastEvaluatedKey()?.get(ChannelItemAttributes.LAST_MODIFICATION_DATE)?.n()

            if (lastEvaluatedKey == null) {
                break
            }

            currentRequest = QueryEnhancedRequest
                .builder()
                .queryConditional(
                    QueryConditional.sortLessThan {
                        it.partitionValue(filters.ownerId)
                            .sortValue(lastEvaluatedKey.toLong())
                    }
                )
                .limit(pagination.itemsSize - allItems.size)
                .scanIndexForward(false)
                .apply {
                    if (filters.onlyWithEpisodes) {
                        onlyWithEpisodes()
                    }
                }
                .build()
        }

        return PaginatedItems(lastEvaluatedKey, allItems)
    }

    override suspend fun hasChannels(filters: ChannelSearchFilters): Boolean {
        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(filters.ownerId)
        }

        val requestBuilder = QueryEnhancedRequest
            .builder()
            .queryConditional(queryConditional)

        if (filters.onlyWithEpisodes) {
            requestBuilder.onlyWithEpisodes()
        } else {
            requestBuilder.limit(1)
        }

        val request = requestBuilder.scanIndexForward(false).build()

        return table
            .index(ChannelIndexes.BY_OWNER_AND_LAST_MODIFICATION)
            .query(request)
            .asFlow()
            .firstOrNull()
            ?.items()
            ?.isNotEmpty() == true
    }

    private fun QueryEnhancedRequest.Builder.onlyWithEpisodes() {
        val filterExpression = Expression.builder()
            .expression("episodes_count > :zero")
            .expressionValues(
                mapOf(
                    ":zero" to AttributeValue.builder().n("0").build()
                )
            )
            .build()

        this.filterExpression(filterExpression)
    }
}
