package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelScoreRepository
import com.etermax.preguntados.episodes.core.domain.episode.update.QualityRepository
import com.etermax.preguntados.episodes.core.domain.quality.Quality
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelQualityItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelScoreItem
import kotlinx.coroutines.future.await
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Expression
import software.amazon.awssdk.enhanced.dynamodb.model.IgnoreNullsMode
import software.amazon.awssdk.enhanced.dynamodb.model.TransactUpdateItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest
import software.amazon.awssdk.services.dynamodb.model.TransactionCanceledException

class DynamoDbChannelScoreRepository(
    private val scoreTable: DynamoDbAsyncTable<UpdateChannelScoreItem>,
    private val table: DynamoDbAsyncTable<UpdateChannelQualityItem>,
    private val client: DynamoDbEnhancedAsyncClient
) : ChannelScoreRepository, QualityRepository {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun updateScore(channelId: String, score: Int) {
        val updateItem = UpdateChannelScoreItem(
            channelId = channelId,
            score = score
        )

        val request = UpdateItemEnhancedRequest.builder(UpdateChannelScoreItem::class.java)
            .item(updateItem)
            .build()

        scoreTable.updateItem(request).await()
    }

    override suspend fun updateQuality(qualities: List<Quality>) {
        qualities.chunked(25).map { chunk ->
            try {
                val request = buildTransactionRequest(chunk)
                client.transactWriteItems(request.build()).await()
            } catch (e: TransactionCanceledException) {
                handleTransactionCancellation(e, chunk)
            } catch (e: Exception) {
                logger.warn("Unexpected error updating channels quality: ${e.message}", e)
            }
        }
    }

    private suspend fun handleTransactionCancellation(
        exception: TransactionCanceledException,
        originalChunk: List<Quality>
    ) {
        val reasons = exception.cancellationReasons()
        val failedIndexes = reasons.mapIndexedNotNull { index, reason ->
            if (reason.code() == CONDITIONAL_CHECK_FAILED) index else null
        }

        if (failedIndexes.isEmpty()) {
            logger.error("Unexpected error updating quality but no for $CONDITIONAL_CHECK_FAILED reasons.", exception)
            return
        }

        val failedChannels = failedIndexes.map { originalChunk[it].id }
        logger.warn("Channels skip update quality due to $CONDITIONAL_CHECK_FAILED: $failedChannels")

        val retryItems = originalChunk.filterIndexed { index, _ -> index !in failedIndexes }
        if (retryItems.isNotEmpty()) {
            logger.info("Retrying transaction with remaining ${retryItems.size} items...")
            try {
                val retryRequest = buildTransactionRequest(retryItems)
                client.transactWriteItems(retryRequest.build()).await()
            } catch (e: Exception) {
                logger.error("Retry failed: ${e.message}", e)
            }
        }
    }

    private fun buildTransactionRequest(list: List<Quality>): TransactWriteItemsEnhancedRequest.Builder {
        val request = TransactWriteItemsEnhancedRequest.builder()

        list.forEach {
            val item = UpdateChannelQualityItem(it.id, it.quality)
            val transactItem = TransactUpdateItemEnhancedRequest
                .builder(UpdateChannelQualityItem::class.java)
                .item(item)
                .ignoreNullsMode(IgnoreNullsMode.DEFAULT)
                .conditionExpression(CONDITION)
                .build()

            request.addUpdateItem(table, transactItem)
        }
        return request
    }

    private companion object {
        const val CONDITIONAL_CHECK_FAILED = "ConditionalCheckFailed"
        val CONDITION: Expression? = Expression.builder()
            .expression("attribute_exists(${ChannelItemAttributes.CHANNEL_ID})")
            .build()
    }
}
