package com.etermax.preguntados.episodes.core.domain.challenge

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import java.time.OffsetDateTime

class ChallengeDetails(
    val playerId: Long,
    val challenge: Challenge,
    val episode: Episode,
    val players: List<ChallengePlayer>,
    val ranking: DeliveryRanking
) {
    fun isFinished() = challenge.endDate.isBefore(OffsetDateTime.now())

    fun getPlayerStatus(playerId: Long) = players.find { it.playerId == playerId }?.status

    fun getStatusForPlayer(playerId: Long): ChallengePlayer.Status {
        return players.first { it.playerId == playerId }.status
    }

    fun getPlayerPosition() = ranking.myRankingEntry?.position
}
