package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeDetails
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.sortable.games.core.domain.pagination.*
import kotlinx.coroutines.coroutineScope

class FindSortedChallenges(
    val sortedGames: SortedGames,
    val challengeService: ChallengeService,
    val summaryService: SummaryService
) {
    suspend operator fun invoke(actionData: ActionData) = coroutineScope {
        with(actionData) {
            val challenges = challengeService.getChallengeDetailsByUser(playerId)
            val challengesById = challenges.associateBy { c -> c.challenge.id }
            updateSortedGames(challenges)

            val (games, pendingGames) = when (section) {
                ActionData.Section.ALL -> {
                    getGames() to getPendingGames()
                }

                ActionData.Section.MATCHES -> {
                    getGames() to emptyList()
                }

                ActionData.Section.PENDING_MATCHES -> {
                    emptyList<SortedGame>() to getPendingGames()
                }
            }

            PlayerSortedChallengeSummary(
                challenges = games.map {
                    SortedChallengeSummary.from(challengesById[it.id]!!, it.sortValue)
                },
                pendingChallenges = pendingGames.map {
                    SortedChallengeSummary.from(challengesById[it.id]!!, it.sortValue)
                }
            )
        }
    }

    suspend fun updateSortedGames(challenges: List<ChallengeDetails>) {
        challenges.forEach { it ->
            sortedGames.add(it.toSortableGame())
        }
    }

    private fun ChallengeDetails.toSortableGame(): SortableGame {
        return ChallengeSortableGame(this)
    }

    private suspend fun ActionData.getGames(): List<SortedGame> {
        return gamesPagination?.let {
            sortedGames.getGamesFor(playerId, it.amount, it.skip)
        } ?: emptyList()
    }

    private suspend fun ActionData.getPendingGames(): List<SortedGame> {
        return pendingGamesPagination?.let {
            sortedGames.getPendingGames(playerId, it.amount, it.skip)
        } ?: emptyList()
    }

    data class PlayerSortedChallengeSummary(
        val challenges: List<SortedChallengeSummary>,
        val pendingChallenges: List<SortedChallengeSummary>
    )

    data class SortedChallengeSummary(
        val id: String,
        val startDate: Long,
        val endDate: Long,
        val ownerId: Long,
        val position: Int,
        val episodeName: String,
        val imageUrl: String,
        val status: ChallengePlayer.Status,
        val sortValue: Long
    ) {
        companion object {
            fun from(challenge: ChallengeDetails, sortValue: Long): SortedChallengeSummary = with(challenge) {
                SortedChallengeSummary(
                    id = challenge.challenge.id,
                    startDate = challenge.challenge.startDate.toMillis(),
                    endDate = challenge.challenge.endDate.toMillis(),
                    ownerId = challenge.challenge.ownerId,
                    position = challenge.getPlayerPosition() ?: 0,
                    episodeName = challenge.challenge.reference.name,
                    imageUrl = challenge.challenge.reference.cover,
                    status = challenge.getStatusForPlayer(playerId),
                    sortValue = sortValue
                )
            }
        }
    }

    data class ActionData(
        val playerId: Long,
        val section: Section,
        val gamesPagination: Pagination? = null,
        val pendingGamesPagination: Pagination? = null
    ) {
        enum class Section {
            ALL,
            MATCHES,
            PENDING_MATCHES,
        }

        data class Pagination(
            val amount: Int,
            val skip: Int
        )
    }

    class ChallengeSortableGame(private val details: ChallengeDetails) : SortableGame {
        override val id = details.challenge.id

        override suspend fun hasPlayerWon(playerId: PlayerId): Boolean {
            return isFinished() && details.getPlayerPosition() == 1
        }

        override fun isFinished(): Boolean {
            return details.isFinished()
        }

        override fun isPlayerAwaitingToJoin(playerId: Long): Boolean {
            return details.getPlayerStatus(playerId)!! == ChallengePlayer.Status.PENDING
        }

        override fun isPlayerTurn(playerId: PlayerId): Boolean {
            return details.getPlayerStatus(playerId) == ChallengePlayer.Status.PLAYING
        }

        override fun playersIsAvailableFor(): List<PlayerId> {
            return details.players.map { it.playerId }.toList()
        }

        override suspend fun sortingTime(playerId: Long): EpochMills {
            return details.challenge.endDate.toInstant().toEpochMilli()
        }
    }
}
