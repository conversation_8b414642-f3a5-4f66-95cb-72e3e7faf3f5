package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.Challenge.Status
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeDetails
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.sortable.games.core.domain.pagination.EpochMills
import com.etermax.preguntados.sortable.games.core.domain.pagination.PlayerId
import com.etermax.preguntados.sortable.games.core.domain.pagination.SortableGame
import com.etermax.preguntados.sortable.games.core.domain.pagination.SortedGames
import kotlinx.coroutines.coroutineScope

class FindSortedChallenges(
    val sortedGames: SortedGames,
    val challengeService: ChallengeService,
    val summaryService: SummaryService
) {
    suspend operator fun invoke(actionData: ActionData) = coroutineScope {
        with(actionData) {
            val challenges = challengeService.getChallengeDetailsByUser(playerId)
            val challengesById = challenges.associateBy { c -> c.challenge.id }
            updateSortedGames(challenges)

            val (games, pendingGames) = when (section) {
                ActionData.Section.ALL -> {
                    getGames(challengesById) to getPendingGames(challengesById)
                }

                ActionData.Section.MATCHES -> {
                    getGames(challengesById) to emptyList()
                }

                ActionData.Section.PENDING_MATCHES -> {
                    getPendingGames(challengesById) to emptyList()
                }
            }

            PlayerSortedChallengeSummary(
                challenges = games.map { SortedChallengeSummary.from(it) },
                pendingChallenges = pendingGames.map { SortedChallengeSummary.from(it) }
            )
        }
    }

    suspend fun updateSortedGames(challenges: List<ChallengeDetails>) {
        challenges.forEach { it ->
            sortedGames.add(it.toSortableGame())
        }
    }

    private fun ChallengeDetails.toSortableGame(): SortableGame {
        return ChallengeSortableGame(this)
    }

    private suspend fun ActionData.getGames(challengesById: Map<String, ChallengeDetails>): List<ChallengeDetails> {
        return gamesPagination?.let {
            sortedGames.getGamesFor(playerId, it.amount, it.skip).mapNotNull { game -> challengesById[game.id] }
        } ?: emptyList()
    }

    private suspend fun ActionData.getPendingGames(challengesById: Map<String, ChallengeDetails>): List<ChallengeDetails> {
        return pendingGamesPagination?.let {
            sortedGames.getPendingGames(playerId, it.amount, it.skip).mapNotNull { game -> challengesById[game.id] }
        } ?: emptyList()
    }

    data class PlayerSortedChallengeSummary(
        val challenges: List<SortedChallengeSummary>,
        val pendingChallenges: List<SortedChallengeSummary>
    )

    data class SortedChallengeSummary(
        val id: String,
        val startDate: Long,
        val endDate: Long,
        val ownerId: Long,
        val position: Int,
        val episodeName: String,
        val imageUrl: String,
        val status: Status
    ) {
        companion object {
            fun from(challenge: ChallengeDetails): SortedChallengeSummary = with(challenge) {
                SortedChallengeSummary(
                    id = challenge.challenge.id,
                    startDate = challenge.challenge.startDate.toMillis(),
                    endDate = challenge.challenge.endDate.toMillis(),
                    ownerId = challenge.challenge.ownerId,
                    position = challenge.getPlayerPosition() ?: 0,
                    episodeName = challenge.challenge.reference.name,
                    imageUrl = challenge.challenge.reference.cover,
                    status = challenge.getStatusForPlayer(playerId)
                )
            }
        }
    }

    data class ActionData(
        val playerId: Long,
        val section: Section,
        val gamesPagination: Pagination? = null,
        val pendingGamesPagination: Pagination? = null
    ) {
        enum class Section {
            ALL,
            MATCHES,
            PENDING_MATCHES,
        }

        data class Pagination(
            val amount: Int,
            val skip: Int
        )
    }

    class ChallengeSortableGame(private val details: ChallengeDetails) : SortableGame {
        override val id = details.challenge.id

        override suspend fun hasPlayerWon(playerId: PlayerId): Boolean {
            return isFinished() && details.getPlayerPosition() == 1
        }

        override fun isFinished(): Boolean {
            return details.isFinished()
        }

        override fun isPlayerAwaitingToJoin(playerId: Long): Boolean {
            return details.getPlayerStatus(playerId)!! == ChallengePlayer.Status.PENDING
        }

        override fun isPlayerTurn(playerId: PlayerId): Boolean {
            return details.getPlayerStatus(playerId) == ChallengePlayer.Status.PLAYING
        }

        override fun playersIsAvailableFor(): List<PlayerId> {
            return details.players.map { it.playerId }.toList()
        }

        override suspend fun sortingTime(playerId: Long): EpochMills {
            return details.challenge.endDate.toInstant().toEpochMilli()
        }
    }
}
