package com.etermax.preguntados.episodes.core.domain.quality.service

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.quality.QualityService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class AsyncDefaultQualityService(
    private val qualityService: QualityService,
    private val coroutineScope: CoroutineScope
) : QualityService {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun calculateChannelScore(episodeBeforeUpdate: Episode, episodeAfterUpdate: Episode) {
        coroutineScope.launch {
            try {
                qualityService.calculateChannelScore(
                    episodeBeforeUpdate = episodeBeforeUpdate,
                    episodeAfterUpdate = episodeAfterUpdate
                )
            } catch (e: Exception) {
                logger.error("[QUALITY] Error processing channel score for episode ${episodeAfterUpdate.id}", e)
            }
        }
    }

    override suspend fun calculateChannelScore(channelId: String, episodes: List<Episode>) {
        coroutineScope.launch {
            try {
                qualityService.calculateChannelScore(channelId = channelId, episodes = episodes)
            } catch (e: Exception) {
                logger.error(
                    "[QUALITY] Error processing channel score for channel $channelId, episode size ${episodes.size}",
                    e
                )
            }
        }
    }
}
