package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.recommendation.FriendsEpisode
import com.etermax.preguntados.episodes.core.domain.episode.recommendation.FriendsRecommendationService
import com.etermax.preguntados.episodes.core.domain.episode.recommendation.RecommendedEpisode
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class RecommendEpisodes(
    private val episodeRepository: EpisodeRepository,
    private val profileService: ProfileService,
    private val friendsRecommendationService: FriendsRecommendationService
) {

    suspend operator fun invoke(actionData: ActionData): List<RecommendedEpisode> = coroutineScope {
        val recommendations = friendsRecommendationService.recommend(actionData.playerId)
        if (recommendations.isEmpty()) return@coroutineScope emptyList()

        val episodesDeferred = async {
            val episodeIds = recommendations.map { it.episodeId }
            episodeRepository.findByIds(episodeIds)
        }

        val profilesDeferred = async {
            val playerIds = recommendations.map { it.friends }.flatten().distinct()
            profileService.findMany(playerIds)
        }

        buildRecommendedEpisodes(
            recommendations = recommendations,
            episodes = episodesDeferred.await().associateBy { it.id },
            profiles = profilesDeferred.await().associateBy { it.playerId }
        )
            .sortedByDescending { it.players.size }
            .take(LIMIT)
    }

    private fun buildRecommendedEpisodes(
        recommendations: List<FriendsEpisode>,
        episodes: Map<String, Episode>,
        profiles: Map<Long, Profile>
    ): List<RecommendedEpisode> {
        return recommendations.mapNotNull { recommendation ->
            val episode = episodes[recommendation.episodeId]?.let {
                EpisodeSummary.from(it, profiles[it.ownerId])
            } ?: return@mapNotNull null

            val players = recommendation.friends.mapNotNull { profiles[it] }.takeIf {
                it.isNotEmpty()
            } ?: return@mapNotNull null

            RecommendedEpisode(episode, players)
        }
    }

    private companion object {
        const val LIMIT = 10
    }

    data class ActionData(
        val playerId: Long
    )
}
