package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables

object ChannelItemAttributes {
    const val PK = "PK"
    const val OWNER_ID = "owner_id"
    const val CHANNEL_ID = "channel_id"
    const val LAST_MODIFICATION_DATE = "ch_modification_date"
    const val CHANNEL_ORDER = "ch_order"
    const val EPISODES_COUNT = "episodes_count"
    const val CREATION_DATE = "creation_date"
    const val TYPE = "type"
    const val LANGUAGE = "language"
    const val NAME = "name"
    const val SUBSCRIBERS_COUNT = "subscribers_count"
    const val SUBSCRIBED = "subscribed"
    const val COVER_URL = "cover_url"
    const val WEBSITE = "website"
    const val DESCRIPTION = "description"
    const val SCORE = "score"
    const val QUALITY = "quality"
    const val ORDER_TYPE = "order_type"
}

object EpisodeChannelItemAttributes {
    const val EPISODE_DATE_ADDED = "ep_date_added"
}
