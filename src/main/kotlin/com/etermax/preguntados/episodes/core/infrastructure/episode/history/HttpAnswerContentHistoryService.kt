package com.etermax.preguntados.episodes.core.infrastructure.episode.history

import com.etermax.preguntados.episodes.core.domain.episode.history.AnswerContentHistoryService
import com.etermax.preguntados.episodes.core.infrastructure.episode.history.request.AnswerContentRequest
import com.etermax.preguntados.episodes.core.infrastructure.http.resilientPost
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import io.ktor.client.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import org.slf4j.LoggerFactory

class HttpAnswerContentHistoryService(
    private val client: HttpClient,
    private val resilienceBundle: EndpointResilienceBundle,
    private val adminPassword: String
) : AnswerContentHistoryService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun addAnsweredContentToHistory(playerId: Long, contentId: String, gameMode: String) {
        try {
            logger.debug("[AddAnsweredContentToHistory] Preparing HTTP request to register answer content history for player $playerId and content $contentId")
            val body = AnswerContentRequest(contentId, gameMode)
            val urlString = URL.format(playerId)
            logger.debug("[AddAnsweredContentToHistory] POST -> {}, BODY -> {}", urlString, body)

            client.resilientPost(
                urlString = urlString,
                resilienceBundle = resilienceBundle,
                requestBuilder = {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    header("god-password", adminPassword)
                    setBody(body)
                    expectSuccess = true
                }
            ) { response ->
                if (response.status.isSuccess()) {
                    logger.debug("[AddAnsweredContentToHistory] HTTP request completed successfully for player $playerId and content $contentId")
                    logger.info("[AddAnsweredContentToHistory] Answer content history registered for player $playerId and content $contentId")
                } else {
                    val responseBody = response.bodyAsText()
                    logger.error("[AddAnsweredContentToHistory] Request failed with status ${response.status} for player $playerId and content $contentId. Response body: $responseBody")
                    throw RuntimeException("Request failed with status ${response.status}")
                }
            }
        } catch (e: Exception) {
            logger.error("[AddAnsweredContentToHistory] Cannot register answer content history for player $playerId and content $contentId. Error: ${e.message}", e)
            throw e
        }
    }

    private companion object {
        const val URL = "/api/users/%d/trivia-playlist/content/answer"
    }
}
