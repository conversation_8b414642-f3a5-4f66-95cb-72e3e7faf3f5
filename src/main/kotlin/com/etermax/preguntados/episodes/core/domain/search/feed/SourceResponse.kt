package com.etermax.preguntados.episodes.core.domain.search.feed

class SourceResponse<T>(val items: List<T>, val fetchCursor: FetchCursor? = null) {
    fun isEmpty(): Boolean {
        return items.isEmpty()
    }

    fun size(): Int {
        return items.size
    }

    companion object {
        inline fun <reified T> emptyResponse(fetchCursor: FetchCursor? = null): SourceResponse<T> {
            return SourceResponse(emptyList(), fetchCursor)
        }
    }
}
