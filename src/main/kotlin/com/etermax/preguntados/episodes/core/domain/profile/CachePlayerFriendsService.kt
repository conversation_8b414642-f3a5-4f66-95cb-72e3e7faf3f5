package com.etermax.preguntados.episodes.core.domain.profile

import com.etermax.preguntados.episodes.core.domain.profile.repository.PlayerFriendsRepository

class CachePlayerFriendsService(
    private val playerFriendsService: <PERSON>FriendsService,
    private val cacheRepository: PlayerFriendsRepository
) : PlayerFriendsService {

    override suspend fun findFollowedIds(playerId: Long): List<Long> =
        findCachedUserProfiles(playerId) ?: findAndCacheFollowers(playerId)

    private suspend fun findCachedUserProfiles(playerId: Long): List<Long>? {
        return cacheRepository.find(playerId)
    }

    private suspend fun findAndCacheFollowers(playerId: Long): List<Long> {
        return playerFriendsService.findFollowedIds(playerId).also {
            cacheRepository.save(playerId, it)
        }
    }
}
