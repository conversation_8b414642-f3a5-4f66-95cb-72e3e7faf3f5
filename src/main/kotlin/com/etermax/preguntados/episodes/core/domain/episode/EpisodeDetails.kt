package com.etermax.preguntados.episodes.core.domain.episode

import com.etermax.preguntados.episodes.core.domain.episode.progress.DeliveryProgress
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking

data class EpisodeDetails(
    val ranking: DeliveryRanking,
    val rankingWithFriends: DeliveryRanking,
    val rate: Rate.Type?,
    val hasPlayed: <PERSON><PERSON>an,
    val deliveryProgress: DeliveryProgress
)
