package com.etermax.preguntados.episodes.core.domain.profile

import com.etermax.preguntados.episodes.core.domain.profile.social.SocialNetwork
import com.etermax.preguntados.episodes.core.domain.profile.social.SocialProfile
import java.time.OffsetDateTime

data class Profile(
    val playerId: Long,
    val name: String,
    val country: String,
    val photoUrl: String?,
    val socialProfile: SocialProfile?,
    val joinDate: OffsetDateTime,
    val restriction: String?
) {

    companion object {
        private const val DEFAULT_COUNTRY_OT = "OT"

        fun build(
            playerId: Long,
            name: String,
            country: String?,
            pictureUrl: String? = null,
            facebookData: FacebookData,
            joinDate: OffsetDateTime,
            restriction: String?
        ): Profile {
            return Profile(
                playerId = playerId,
                name = name,
                country = country ?: DEFAULT_COUNTRY_OT,
                photoUrl = pictureUrl?.ifEmpty { null },
                socialProfile = buildSocialProfileFrom(facebookData),
                joinDate = joinDate,
                restriction = restriction
            )
        }

        private fun buildSocialProfileFrom(facebookData: FacebookData): SocialProfile? {
            with(facebookData) {
                val facebookId = showFacebookPicture.ifTrue(this.facebookId) ?: return null
                val facebookName = showFacebookName.ifTrue(this.facebookName)

                return SocialProfile(
                    SocialNetwork.FACEBOOK,
                    facebookId,
                    facebookName
                )
            }
        }

        private fun <T> Boolean.ifTrue(value: T): T? {
            return if (this) value else null
        }
    }

    data class FacebookData(
        private val _facebookId: String,
        val facebookName: String? = null,
        val showFacebookPicture: Boolean,
        val showFacebookName: Boolean
    ) {
        val facebookId: String?
            get() = _facebookId.ifEmpty { null }
    }
}
