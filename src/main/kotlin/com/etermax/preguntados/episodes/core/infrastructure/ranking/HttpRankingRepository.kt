package com.etermax.preguntados.episodes.core.infrastructure.ranking

import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankedPlayer
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Ranking
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingRepository
import com.etermax.preguntados.episodes.core.infrastructure.http.resilientGet
import com.etermax.preguntados.episodes.core.infrastructure.http.resilientPost
import com.etermax.preguntados.episodes.core.infrastructure.ranking.representation.*
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import org.slf4j.LoggerFactory

class HttpRankingRepository(
    private val client: HttpClient,
    private val resilienceBundle: EndpointResilienceBundle,
    private val platformGameId: String
) : RankingRepository {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun find(playerId: Long, domain: Domain, playersCount: Int): Ranking? {
        return try {
            val url = "${URL.toUrlWithScope(playerId, domain)}&from=1&amount=$playersCount"
            client.resilientGet(
                urlString = url,
                resilienceBundle = resilienceBundle,
                requestBuilder = {
                    contentType(ContentType.Application.Json)
                    expectSuccess = true
                }
            ) {
                return@resilientGet makeRanking(it, url)
            }
        } catch (e: Exception) {
            logger.error("Cannot get ranking for user: $playerId domain: $domain ", e)
            return null
        }
    }

    override suspend fun findForPlayers(playerId: Long, domain: Domain, playersId: List<Long>): List<RankedPlayer> {
        return try {
            val body = PlayersRequest(playersId.map { PlayerRequest(it) })
            val url = URL_PLAYERS.toUrlWithScope(playerId, domain)
            client.resilientPost(
                urlString = url,
                resilienceBundle = resilienceBundle,
                requestBuilder = {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    setBody(body)
                    expectSuccess = true
                }
            ) {
                return@resilientPost makePlayersRanking(it, url)
            }
        } catch (e: Exception) {
            logger.error("Cannot get ranking for players: $playersId with domain: $domain ", e)
            return emptyList()
        }
    }

    override suspend fun incrementScore(playerId: Long, domain: Domain, score: Int) {
        val body = IncrementScoreRequest(domain.toScope(), score)
        client.resilientPost(
            urlString = URL_SCORE.format(platformGameId, playerId),
            resilienceBundle = resilienceBundle,
            requestBuilder = {
                contentType(ContentType.Application.Json)
                accept(ContentType.Application.Json)
                setBody(body)
                expectSuccess = true
            }
        ) {
            logger.info(it.toString())
        }
    }

    private suspend fun makeRanking(response: HttpResponse, url: String): Ranking? {
        if (response.status != HttpStatusCode.OK) {
            logger.info("Service returned an empty response for url: $url")
            return null
        }

        val rankingResponse = response.body<RankingResponse>()
        return rankingResponse.to()
    }

    private suspend fun makePlayersRanking(response: HttpResponse, url: String): List<RankedPlayer> {
        if (response.status != HttpStatusCode.OK) {
            logger.info("Service returned an empty response for url: $url")
            return emptyList()
        }

        val rankingResponse = response.body<List<UserRank>>()
        return rankingResponse.map { it.to() }
    }

    private fun String.toUrlWithScope(playerId: Long, domain: Domain): String {
        val url = format(platformGameId, playerId)
        return "$url?scope=${domain.toScope()}"
    }

    private fun Domain.toScope() = SCOPE.format(value)

    private companion object {
        const val URL = "/games/%s/users/%s/global-ranking"
        const val URL_SCORE = "$URL/addScore"
        const val URL_PLAYERS = "$URL/players"
        const val SCOPE = "tc:ep:%s"
    }
}
