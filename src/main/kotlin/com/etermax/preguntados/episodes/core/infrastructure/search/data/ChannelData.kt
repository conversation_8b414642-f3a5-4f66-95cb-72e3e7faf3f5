package com.etermax.preguntados.episodes.core.infrastructure.search.data

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.ChannelStatistics
import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.utils.LanguageUtils
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class ChannelData(
    @JsonProperty(ChannelItemAttributes.CHANNEL_ID) val channelId: String = "",
    @JsonProperty(ChannelItemAttributes.NAME) val name: String = "",
    @JsonProperty(ChannelItemAttributes.DESCRIPTION) val description: String? = null,
    @JsonProperty(ChannelItemAttributes.WEBSITE) val website: String? = null,
    @JsonProperty(ChannelItemAttributes.COVER_URL) val coverUrl: String? = null,
    @JsonProperty(ChannelItemAttributes.SUBSCRIBED) val subscribed: Boolean? = null,
    @JsonProperty(ChannelItemAttributes.OWNER_ID) val ownerId: Long? = null,
    @JsonProperty(ChannelItemAttributes.SUBSCRIBERS_COUNT) val subscribersCount: Int? = null,
    @JsonProperty(ChannelItemAttributes.EPISODES_COUNT) val episodesCount: Int? = null,
    @JsonProperty(ChannelItemAttributes.CREATION_DATE) val creationDate: Long? = null,
    @JsonProperty(ChannelItemAttributes.LAST_MODIFICATION_DATE) val lastModificationDate: Long? = null,
    @JsonProperty(ChannelItemAttributes.TYPE) val type: String? = null,
    @JsonProperty(ChannelItemAttributes.LANGUAGE) val language: String? = null,
    @JsonProperty(ChannelItemAttributes.CHANNEL_ORDER) val order: String? = null,
    @JsonProperty(ChannelItemAttributes.SCORE) val score: Int? = null,
    @JsonProperty(ChannelItemAttributes.QUALITY) val quality: Int? = null,
    @JsonProperty(ChannelItemAttributes.ORDER_TYPE) val orderType: String? = null
) {
    fun toDomain(): Channel {
        return Channel(
            id = channelId,
            name = name,
            description = description,
            website = website,
            coverUrl = coverUrl!!,
            subscribed = subscribed!!,
            ownerId = ownerId!!,
            creationDate = OffsetDateTime.ofInstant(Instant.ofEpochMilli(creationDate!!), ZoneOffset.UTC),
            lastModificationDate = OffsetDateTime.ofInstant(
                Instant.ofEpochMilli(lastModificationDate!!),
                ZoneOffset.UTC
            ),
            statistics = ChannelStatistics(subscribersCount!!, episodesCount!!, score ?: 0, quality ?: 0),
            type = type!!.toChannelType(),
            language = LanguageUtils.from(language),
            order = order!!.toLong(),
            orderType = orderType?.toChannelOrderType() ?: ChannelOrderType.DATE_ADDED
        )
    }

    private fun String.toChannelType(): ChannelType {
        return when (this.uppercase()) {
            "PUBLIC" -> ChannelType.PUBLIC
            "PRIVATE" -> ChannelType.PUBLIC
            else -> throw RuntimeException("ChannelType '$this' not supported.")
        }
    }

    private fun String.toChannelOrderType(): ChannelOrderType {
        return when (this.uppercase()) {
            "DATE_ADDED" -> ChannelOrderType.DATE_ADDED
            "CUSTOM_ORDER" -> ChannelOrderType.CUSTOM_ORDER
            else -> throw RuntimeException("ChannelOrderType '$this' not supported.")
        }
    }
}
