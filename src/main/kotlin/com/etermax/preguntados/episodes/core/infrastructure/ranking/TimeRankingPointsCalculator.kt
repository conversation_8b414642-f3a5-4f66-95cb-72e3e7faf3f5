package com.etermax.preguntados.episodes.core.infrastructure.ranking

import com.etermax.preguntados.episodes.core.domain.episode.ranking.CalculatorInfo
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingPointsCalculator

class TimeRankingPointsCalculator : RankingPointsCalculator {

    override fun calculate(info: CalculatorInfo) = info.totalTime?.let {
        val result = (it - info.elapsedTime) * TOTAL_POINTS / it
        result.coerceAtLeast(NO_POINTS)
    } ?: NO_POINTS

    private companion object {
        const val TOTAL_POINTS = 50
        const val NO_POINTS = 0
    }
}
