package com.etermax.preguntados.episodes.core.domain.episode.finish

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking

data class FinishEpisodeDetails(
    val episodeSummary: EpisodeSummary,
    val rate: Rate.Type?,
    val ranking: DeliveryRanking?,
    val rankingWithFriends: DeliveryRanking?
)
