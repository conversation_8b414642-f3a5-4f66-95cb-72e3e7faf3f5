package com.etermax.preguntados.episodes.core.infrastructure.repository

import com.etermax.preguntados.episodes.core.domain.exception.WriteItemsBulkException
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.future.await
import kotlinx.coroutines.reactive.asFlow
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Expression
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.internal.client.DefaultDynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.model.*
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import java.util.*

open class DynamoDBRepository<T>(
    protected val client: DynamoDbEnhancedAsyncClient,
    protected val table: DynamoDbAsyncTable<T>
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    protected fun rawClient(): DynamoDbAsyncClient =
        (client as DefaultDynamoDbEnhancedAsyncClient).dynamoDbAsyncClient()

    protected fun findItemBatch(item: Key, itemClass: Class<T>): ReadBatch = ReadBatch.builder(itemClass)
        .mappedTableResource(table)
        .addGetItem(item)
        .build()

    suspend fun findItemsBulk(batches: List<ReadBatch>): List<T?> {
        val request = BatchGetItemEnhancedRequest.builder()
            .readBatches(batches)
            .build()

        return client.batchGetItem(request)
            .resultsForTable(table)
            .asFlow()
            .toList()
    }

    protected suspend fun deleteItem(key: Key) {
        val request = DeleteItemEnhancedRequest.builder().key(key).build()
        table.deleteItem(request).await()
    }

    protected suspend fun saveItem(item: T, itemClass: Class<T>) {
        val request = PutItemEnhancedRequest.builder(itemClass)
            .item(item)
            .build()
        table.putItem(request).await()
    }

    protected suspend fun findItem(key: Key): T? {
        val request = GetItemEnhancedRequest.builder()
            .key(key)
            .build()
        return table.getItem(request).await()
    }

    protected suspend fun findItems(
        partitionKey: String,
        limit: Int? = null,
        lastPkAndSk: Key? = null,
        skCondition: Expression? = null
    ): List<T> {
        val onlyPrimaryKeyWithoutSortedKey = Key.builder()
            .partitionValue(partitionKey)
            .build()

        val queryRequestBuilder = QueryEnhancedRequest.builder()
            .queryConditional(
                QueryConditional.keyEqualTo(onlyPrimaryKeyWithoutSortedKey)
            )

        limit?.let {
            queryRequestBuilder.limit(it)
        }

        lastPkAndSk?.let {
            queryRequestBuilder.exclusiveStartKey(it.primaryKeyMap(table.tableSchema()))
        }

        skCondition?.let { queryRequestBuilder.filterExpression(it) }

        val queryRequest = queryRequestBuilder.build()

        val items = mutableListOf<T>()
        table.query(queryRequest)
            .asFlow()
            .collect { page ->
                items.addAll(page.items())
            }

        return items
    }

    protected fun makeDeleteBatch(item: T, itemClass: Class<T>): WriteBatch = WriteBatch.builder(itemClass)
        .mappedTableResource(table)
        .addDeleteItem(item)
        .build()

    // TODO improve it with addItemsBulkWithRetry
    suspend fun deleteItemsBulkWithRetry(
        itemsBatch: List<WriteBatch>,
        itemClass: Class<T>,
        retries: Int = MAX_RETRIES
    ) {
        val batch = BatchWriteItemEnhancedRequest.builder()
            .writeBatches(itemsBatch)
            .build()
        val result = client.batchWriteItem(batch).await()

        val unprocessed = result.unprocessedPutItemsForTable(table)
        if (stopProcess(unprocessed, retries)) return

        val writeBatches = unprocessed.map { makeDeleteBatch(it, itemClass) }
        deleteItemsBulkWithRetry(writeBatches, itemClass, retries - 1)
    }

    protected fun makeWriteBatch(item: T, itemClass: Class<T>): WriteBatch = WriteBatch.builder(itemClass)
        .mappedTableResource(table)
        .addPutItem(item)
        .build()

    suspend fun addItemsBulkWithRetry(itemsBatch: List<WriteBatch>, itemClass: Class<T>, retries: Int = MAX_RETRIES) {
        val batch = BatchWriteItemEnhancedRequest.builder()
            .writeBatches(itemsBatch)
            .build()
        val result = client.batchWriteItem(batch).await()

        val unprocessed = result.unprocessedPutItemsForTable(table)
        if (stopProcess(unprocessed, retries)) return

        val writeBatches = unprocessed.map { makeWriteBatch(it, itemClass) }
        addItemsBulkWithRetry(writeBatches, itemClass, retries - 1)
    }

    private fun stopProcess(unprocessed: List<T>, retries: Int): Boolean {
        if (retries <= 0) logger.error("Failed to add items bulk", WriteItemsBulkException(table.tableName()))
        return (unprocessed.isEmpty() || retries <= 0)
    }

    companion object {
        const val MAX_RETRIES = 3

        private val mapper = Json { ignoreUnknownKeys = true }

        fun encodeLastEvaluatedKey(key: Map<String, AttributeValue>): String {
            val asMap: Map<String, Map<String, String>> = key.mapValues { (_, value) ->
                when {
                    value.s() != null -> mapOf("S" to value.s())
                    value.n() != null -> mapOf("N" to value.n())
                    else -> throw IllegalArgumentException("Unsupported AttributeValue type")
                }
            }
            val json = mapper.encodeToString(asMap)
            return Base64.getUrlEncoder().encodeToString(json.toByteArray())
        }

        fun decodeLastEvaluatedKey(token: String): Map<String, AttributeValue> {
            val json = String(Base64.getUrlDecoder().decode(token))
            val raw: Map<String, Map<String, String>> = mapper.decodeFromString(json)

            return raw.mapValues { (_, v) ->
                when {
                    v.containsKey("S") -> AttributeValue.builder().s(v["S"]).build()
                    v.containsKey("N") -> AttributeValue.builder().n(v["N"]).build()
                    else -> throw IllegalArgumentException("Unsupported AttributeValue format")
                }
            }
        }
    }
}

typealias TransactionBuilder = TransactWriteItemsEnhancedRequest.Builder
