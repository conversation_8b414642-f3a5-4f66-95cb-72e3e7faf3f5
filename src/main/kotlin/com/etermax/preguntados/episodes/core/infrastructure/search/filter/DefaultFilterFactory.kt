package com.etermax.preguntados.episodes.core.infrastructure.search.filter

import com.etermax.preguntados.episodes.core.domain.search.filter.Filter
import com.etermax.preguntados.episodes.core.domain.search.filter.FilterDataRepository
import com.etermax.preguntados.episodes.core.domain.search.filter.FilterFactory
import com.etermax.preguntados.episodes.core.domain.search.filter.FilterType
import org.opensearch.client.opensearch.OpenSearchAsyncClient

class DefaultFilterFactory(
    private val repository: FilterDataRepository,
    private val osClient: OpenSearchAsyncClient,
    private val episodesIndexName: String
) : FilterFactory {
    override suspend fun create(userId: Long, type: FilterType, reset: <PERSON>olean): Filter {
        return when (type) {
            FilterType.DUPLICATE -> createDuplicateFilter(userId, reset)
            FilterType.PLAYED_AND_DUPLICATE -> createPlayedAndDuplicateFilter(userId, reset)
        }
    }

    private suspend fun createDuplicateFilter(userId: Long, reset: <PERSON><PERSON><PERSON>): Filter {
        // TODO no leer desde el repo si hay que hacer reset
        val scope = userId.toString()
        val filter = DuplicateFilter(scope, repository)

        if (reset) {
            filter.reset()
        }
        return filter
    }

    private suspend fun createPlayedAndDuplicateFilter(userId: Long, reset: Boolean): Filter {
        val filter = PlayedDuplicatedFilter(userId, repository, osClient, episodesIndexName)

        if (reset) {
            filter.reset()
        }
        return filter
    }
}
