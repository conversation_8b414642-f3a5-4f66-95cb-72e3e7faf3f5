package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.search.BaseSearch.Companion.START_DATE_FIELD
import com.etermax.preguntados.episodes.core.infrastructure.search.OpenSearchRequestFactory
import com.etermax.preguntados.episodes.core.infrastructure.search.v2.OpenSearchEpisodeItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.future.await
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder
import org.opensearch.client.opensearch.core.SearchResponse
import org.slf4j.LoggerFactory

class RecentEpisodeSource(
    private val client: OpenSearchAsyncClient,
    private val episodesIndexName: String
) : Source<String> {

    private val logger = LoggerFactory.getLogger(RecentEpisodeSource::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<String> {
        with(request) {
            if (range.limit <= 0) {
                logger.debug("[FEED] Fetch called with non-positive limit: {}", range.limit)
                return emptyResponse(fetchCursor)
            }

            val offsetCursor = fetchCursor as? OffsetFetchCursor
            if (offsetCursor != null && offsetCursor.exhausted) {
                logger.debug("[FEED] Fetch called with exhausted cursor, returning empty response")
                return emptyResponse(fetchCursor)
            }

            val effectiveRange = applyFetchCursor(range, offsetCursor)
            logger.debug(
                "[FEED] Effective range applied: offset={}, limit={}",
                effectiveRange.offset,
                effectiveRange.limit
            )

            val osRequest = OpenSearchRequestFactory(episodesIndexName, effectiveRange.offset, effectiveRange.limit)
                .filterByLanguage(language)
                .filterByType(EpisodeType.PUBLIC)
                .filterByStatus(EpisodeStatus.PUBLISHED)
                .queryByCountry(country)
                .includeEpisodeIdOnly()
                .sortByDate()
                .build()

            val osResponse = try {
                client.search(osRequest, OpenSearchEpisodeItem::class.java).await()
            } catch (ex: Exception) {
                logger.error("[FEED] Error executing search request", ex)
                return emptyResponse()
            }

            val items = osResponse.ids()
            logger.debug(
                "[FEED] OpenSearch returned {} episodes for offset {} with limit {}",
                items.size,
                effectiveRange.offset,
                effectiveRange.limit
            )

            val exhausted = items.size < effectiveRange.limit
            return SourceResponse(items, OffsetFetchCursor(effectiveRange.offset + items.size, exhausted))
        }
    }

    private fun applyFetchCursor(range: OffsetLimit, offsetCursor: OffsetFetchCursor?): OffsetLimit {
        return if (offsetCursor != null) {
            OffsetLimit(offsetCursor.offset, range.limit).also {
                logger.debug("[FEED] Applied fetch cursor offset: {}", it.offset)
            }
        } else {
            range.also {
                logger.debug("[FEED] No fetch cursor provided, using original range offset: {}", it.offset)
            }
        }
    }

    private fun OpenSearchRequestFactory.sortByDate() = this.also {
        sortBy(START_DATE_FIELD, SortOrder.Desc)
    }

    private fun OpenSearchRequestFactory.includeEpisodeIdOnly() = this.also {
        includeSourceFields(EpisodeItemAttributes.EPISODE_ID)
    }

    private fun OpenSearchRequestFactory.filterByStatus(status: EpisodeStatus?) = this.also {
        status?.let { filterTerm(EpisodeItemAttributes.STATUS, it.name.lowercase()) }
    }

    private fun OpenSearchRequestFactory.filterByType(type: EpisodeType?) = this.also {
        type?.let { filterTerm(EpisodeItemAttributes.TYPE, it.name) }
    }

    private fun OpenSearchRequestFactory.filterByLanguage(language: Language?) = this.also {
        language?.let { filterTerm(EpisodeItemAttributes.LANGUAGE, it.name) }
    }

    private fun OpenSearchRequestFactory.queryByCountry(country: Country?) = this.also {
        country?.let { shouldTerm(EpisodeItemAttributes.COUNTRY, it.name) }
    }

    private fun SearchResponse<OpenSearchEpisodeItem>.ids(): List<String> {
        return hits().hits().mapNotNull { it.source()?.episodeId }
    }
}
