package com.etermax.preguntados.episodes.core.infrastructure.http.error

import io.ktor.client.statement.*
import io.ktor.http.*

class GatewayUnexpectedException(
    message: String,
    cause: Throwable?,
    val isServerError: Boolean = false
) : RuntimeException(message, cause) {

    constructor(
        url: String,
        status: HttpStatusCode,
        body: String
    ) : this("Unexpected error on $url - $status. Message: $body", null, status.value >= 500)

    companion object {
        private const val PASSWORD_PARAMETER = "password"
        suspend fun buildFrom(response: HttpResponse): GatewayUnexpectedException {
            return GatewayUnexpectedException(excludePassword(response.request.url), response.status, response.bodyAsText())
        }

        private fun excludePassword(url: Url): String {
            val urlBuilder = URLBuilder(url)
            urlBuilder.parameters.remove(PASSWORD_PARAMETER)
            return urlBuilder.toString()
        }
    }
}

class GatewayProtocolException(url: Url, status: HttpStatusCode, body: String, cause: Throwable? = null) :
    RuntimeException("Protocol exception on $url - $status. Message: $body", cause) {
    constructor(
        url: Url,
        status: HttpStatusCode,
        cause: Throwable
    ) : this(url, status, "N/A", cause)
}
