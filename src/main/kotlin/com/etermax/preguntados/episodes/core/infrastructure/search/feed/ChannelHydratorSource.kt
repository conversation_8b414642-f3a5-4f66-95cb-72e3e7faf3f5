package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.search.feed.Source
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceRequest
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem.Companion.CHANNEL_PREFIX
import org.slf4j.LoggerFactory
import kotlin.time.ExperimentalTime
import kotlin.time.measureTimedValue

@OptIn(ExperimentalTime::class)
class ChannelHydratorSource(
    private val source: Source<String>,
    private val repository: ChannelRepository
) : Source<Channel> {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val prefix = CHANNEL_PREFIX

    override suspend fun fetch(request: SourceRequest): SourceResponse<Channel> {
        logger.debug("[FEED] Fetching channel IDs with range: offset={}, limit={}", request.range.offset, request.range.limit)

        val rawResponse = source.fetch(request)
        val ids = rawResponse.items.map { prefix + it }
        logger.debug("[FEED] Raw channel IDs: {}. Hydrated channel IDs: {}", rawResponse.items, ids)

        val (channels, findByTime) = measureTimedValue {
            repository.findById(ids)
        }
        logger.debug("[LATENCY] channels findByTime={}ms", findByTime)
        logger.info("Hydrated {} channels from {} IDs", channels.size, ids.size)

        return SourceResponse(channels, rawResponse.fetchCursor)
    }
}
