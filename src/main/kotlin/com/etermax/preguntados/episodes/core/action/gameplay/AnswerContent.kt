package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.history.AnswerContentHistoryService
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentService
import com.etermax.preguntados.episodes.core.domain.episode.ranking.CalculatorInfo
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingPointsCalculator
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class AnswerContent(
    private val rankingRepository: RankingRepository,
    private val calculator: RankingPointsCalculator,
    private val progressContentService: ProgressContentService,
    private val episodeRepository: EpisodeRepository,
    private val answerContentHistoryService: AnswerContentHistoryService
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)
    private val scope = CoroutineScope(Dispatchers.IO)

    suspend operator fun invoke(actionData: ActionData) {
        with(actionData) {
            val answerLog = if (isCorrect) "correctly" else "incorrectly"
            logger.info("Player $playerId answered $answerLog $contentId from episode $episodeId")

            if (isFinished()) {
                return
            }

            updateFinishedStatus(actionData.playerId, actionData.episodeId, actionData.contentId)
            incrementScore(actionData)
            addAnsweredContentToHistory()
        }
    }

    private suspend fun incrementScore(actionData: ActionData) {
        if (!actionData.isCorrect) return
        rankingRepository.incrementScore(
            playerId = actionData.playerId,
            domain = Domain(actionData.episodeId),
            score = actionData.getScore()
        )
    }

    private suspend fun updateFinishedStatus(
        playerId: Long,
        episodeId: String,
        contentId: String
    ) {
        val episode = episodeRepository.findById(episodeId)
        val isLastContent = episode?.contents?.last() == contentId
        if (episode != null && isLastContent) {
            progressContentService.registerProgress(
                episodeId,
                playerId,
                contentId,
                episode.language.name,
                true
            )
        }
    }

    private suspend fun ActionData.isFinished() = isAlreadyFinished(playerId, episodeId)

    private suspend fun isAlreadyFinished(playerId: Long, episodeId: String) =
        progressContentService.findProgress(episodeId, playerId)?.hasFinishedEpisode ?: false

    private fun ActionData.getScore(): Int {
        if (!isCorrect) return ZERO_SCORE
        return POINTS_PER_CORRECT_ANSWER + getExtraPoints(elapsedTime, totalTime)
    }

    private fun getExtraPoints(elapsedTime: Int, totalTime: Int?): Int {
        val info = CalculatorInfo(elapsedTime, totalTime)
        return calculator.calculate(info).coerceAtLeast(ZERO_SCORE)
    }

    private fun ActionData.addAnsweredContentToHistory() = scope.launch {
        try {
            logger.debug("[AddAnsweredContentToHistory] Starting fire and forget answer content history for player $playerId and content $contentId")
            answerContentHistoryService.addAnsweredContentToHistory(playerId, contentId, "episode")
            logger.debug("[AddAnsweredContentToHistory] Successfully sent answer content history for player $playerId and content $contentId")
        } catch (e: Exception) {
            logger.error(
                "Error in fire and forget answer content history for player $playerId and content $contentId",
                e
            )
        }
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String,
        val contentId: String,
        val isCorrect: Boolean,
        val elapsedTime: Int,
        val totalTime: Int?
    )

    private companion object {
        const val POINTS_PER_CORRECT_ANSWER = 50
        const val ZERO_SCORE = 0
    }
}
