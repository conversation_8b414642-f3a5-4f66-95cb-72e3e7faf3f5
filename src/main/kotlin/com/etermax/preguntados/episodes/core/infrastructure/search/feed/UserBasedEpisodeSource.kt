package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.search.UserBasedRepository
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import com.etermax.preguntados.episodes.core.infrastructure.search.service.PlayedEpisodeSearchService
import org.slf4j.LoggerFactory

class UserBasedEpisodeSource(
    private val userBasedRepository: UserBasedRepository,
    private val playedEpisodeSearchService: PlayedEpisodeSearchService,
    private val episodesRepository: EpisodeRepository
) : Source<String> {

    private val logger = LoggerFactory.getLogger(UserBasedEpisodeSource::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<String> {
        if (request.range.limit <= 0) {
            logger.debug("[FEED] Fetch called with non-positive limit: {}", request.range.limit)
            return emptyResponse(request.fetchCursor)
        }

        val offsetCursor = request.fetchCursor as? OffsetFetchCursor
        if (offsetCursor != null && offsetCursor.exhausted) {
            logger.debug("[FEED] Fetch called with exhausted cursor, returning empty response")
            return emptyResponse(offsetCursor)
        }

        val referenceEpisodes = playedEpisodeSearchService.search(
            playerId = request.userId,
            language = request.language?.name,
            restartCache = request.fetchCursor == null
        )
        if (referenceEpisodes.isEmpty()) {
            logger.debug("[FEED] Fetch with no reference episodes, returning empty response")
            return emptyResponse(OffsetFetchCursor(offsetCursor?.offset ?: request.range.offset, true))
        }

        val effectiveRange = applyFetchCursor(request.range, offsetCursor)
        logger.debug(
            "[FEED] Effective range calculated: offset={}, limit={}",
            effectiveRange.offset,
            effectiveRange.limit
        )

        val items =
            buildSearchRequest(request, referenceEpisodes, effectiveRange.offset, effectiveRange.limit)

        logger.debug("[FEED] Search returned {} episode IDs", items.size)
        logger.info("[FEED] User-based returned {} episodes IDs for player {}", items.size, request.userId)

        val nextOffset = effectiveRange.offset + items.size

        val exhausted = items.size < effectiveRange.limit

        val existentEpisodes = episodesRepository.findByIds(items)
        if (items.size != existentEpisodes.size) {
            logger.warn("Non-existent user-based episodes: ${items - existentEpisodes.map { it.id }}")
        }
        return SourceResponse(existentEpisodes.map { it.id }, OffsetFetchCursor(nextOffset, exhausted))
    }

    private fun applyFetchCursor(range: OffsetLimit, offsetCursor: OffsetFetchCursor?): OffsetLimit {
        return if (offsetCursor != null) {
            OffsetLimit(offsetCursor.offset, range.limit).also {
                logger.debug("[FEED] Applied fetch cursor offset: {}", it.offset)
            }
        } else {
            range.also {
                logger.debug("[FEED] No fetch cursor applied, using original range offset: {}", it.offset)
            }
        }
    }

    private suspend fun buildSearchRequest(
        request: SourceRequest,
        referenceEpisodes: List<String>,
        offset: Int,
        limit: Int
    ): List<String> {
        return userBasedRepository.find(
            episodesId = referenceEpisodes,
            offset = offset,
            perPage = limit,
            language = request.language
        )
    }
}
