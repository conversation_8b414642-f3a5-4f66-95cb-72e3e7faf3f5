package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class UpdateChannelEpisodesCountItem(
    @get:DynamoDbAttribute(ChannelItemAttributes.CHANNEL_ID) var channelId: String = "",
    @get:DynamoDbAttribute(ChannelItemAttributes.EPISODES_COUNT) var episodesCount: Int? = null,
    @get:DynamoDbAttribute(ChannelItemAttributes.LAST_MODIFICATION_DATE) var lastModificationDate: Long? = null
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = ChannelItem.buildPartitionKey(channelId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = ChannelItem.CHANNEL_SK

    companion object {
        fun buildPartitionKey(channelId: String) = ChannelItem.buildPartitionKey(channelId)

        fun from(channel: Channel) = with(channel) {
            UpdateChannelEpisodesCountItem(
                id,
                episodesCount = this.episodesCount,
                lastModificationDate = lastModificationDate.toMillis()
            )
        }
    }
}
