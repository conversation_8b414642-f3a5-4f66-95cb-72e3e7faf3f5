package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters

class ByChannelSearch(
    private val episodeRepository: EpisodeRepository,
    private val channelEpisodesRepository: ChannelEpisodesRepository
) : Search {
    override suspend fun match(parameters: SearchParameters) =
        parameters.sort == SortEpisode.EPISODES_FOR_CHANNEL

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        with(parameters) {
            safeLet<Long, String, List<String>>(playerId, channelId) { _, channelId ->
                // FIXME Support query pagination and native sorting
                val ids = channelEpisodesRepository.findAllEpisodeIdsFrom(channelId, ChannelOrderType.DATE_ADDED)
                val episodes = episodeRepository.findByIds(ids)
                val episodeById = episodes.associateBy { it.id }
                return ids.mapNotNull { episodeById[it] }
            }
        }
        return emptyList()
    }

    private inline fun <A, B, R> safeLet(a: A?, b: B?, block: (A, B) -> R): R? {
        return if (a != null && b != null) block(a, b) else null
    }
}
