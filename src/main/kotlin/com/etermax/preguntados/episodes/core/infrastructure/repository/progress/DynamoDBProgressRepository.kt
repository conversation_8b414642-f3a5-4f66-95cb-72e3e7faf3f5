package com.etermax.preguntados.episodes.core.infrastructure.repository.progress

import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.tables.ProgressItem
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.reactive.asFlow
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest

class DynamoDBProgressRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<ProgressItem>
) : ProgressContentRepository, DynamoDBRepository<ProgressItem>(client, table) {

    override suspend fun findBy(episodeId: String, playerId: Long): ProgressContent? {
        val key = Key.builder()
            .partitionKey(episodeId)
            .sortKeyProgress(playerId)
            .build()
        val conditional = QueryConditional
            .keyEqualTo(key)
        val request = QueryEnhancedRequest.builder()
            .queryConditional(conditional)
            .build()
        return table
            .query(request)
            .items()
            .asFlow()
            .toList()
            .firstOrNull()
            ?.to()
    }

    override suspend fun save(progresses: List<ProgressContent>) {
        progresses.chunked(25).forEach { chunksProgresses ->
            val progressBatch = chunksProgresses.map {
                makeWriteBatch(ProgressItem.from(it), ProgressItem::class.java)
            }
            addItemsBulkWithRetry(progressBatch, ProgressItem::class.java)
        }
    }

    override suspend fun save(progressContent: ProgressContent) {
        save(listOf(progressContent))
    }

    private fun Key.Builder.partitionKey(episodeId: String): Key.Builder {
        partitionValue(ProgressItem.buildPartitionKey(episodeId))
        return this
    }

    private fun Key.Builder.sortKeyProgress(playerId: Long): Key.Builder {
        sortValue(ProgressItem.buildProgressSortedKey(playerId))
        return this
    }
}
