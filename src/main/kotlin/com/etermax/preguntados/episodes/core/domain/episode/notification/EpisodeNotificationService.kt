package com.etermax.preguntados.episodes.core.domain.episode.notification

import com.etermax.preguntados.episodes.core.domain.notification.*
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.external.services.core.domain.clerk.ClerkService
import kotlinx.coroutines.*
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class EpisodeNotificationService(
    private val profileService: ProfileService,
    private val eventGroupService: EventGroupService,
    private val clerkService: ClerkService,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO,
    private val notificationsConfiguration: () -> NotificationsConfiguration
) {

    val config by lazy {
        notificationsConfiguration()
    }

    val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend fun notifyPlayed(senderId: Long, receiverId: Long, episodeId: String) {
        launch {
            if (!canSend(senderId, receiverId, config.played)) {
                logger.info("[NOTIFICATIONS] Cannot send play notification to player $receiverId from $senderId. Config: $config")
                return@launch
            }

            val count = eventGroupService.onEvent(episodeId, EventType.PLAY, receiverId)
            if (count > 0) {
                val profile = profileService.find(senderId)
                val canvasId =
                    if (count == 1 || config.played.pluralCanvasId == null) config.played.singleCanvasId else config.played.pluralCanvasId!!
                sendNotification(canvasId, receiverId, episodeId, count, profile)

                eventGroupService.reset(episodeId, EventType.PLAY, receiverId)
            }
        }
    }

    suspend fun notifyInviter(
        inviterId: Long,
        playerName: String,
        episodeId: String,
        playerProfile: Profile?
    ) {
        launch {
            clerkService.sendNotification(
                listOf(inviterId),
                mapOf(
                    "canvas_id" to config.inviterCanvasId,
                    "player_name" to playerName,
                    "episode_id" to episodeId,
                    "profiles" to if (playerProfile == null) "[]" else "[${encodeToString(playerProfile)}]",
                    "preview_type" to "EPISODE",
                    "preview_id" to episodeId
                )
            )
        }
    }

    suspend fun notifyPlayersAtEpisodeCreation(
        players: List<Long>,
        episodeOwnerProfile: Profile,
        episodeId: String
    ) {
        if (!players.any()) return

        launch {
            clerkService.sendNotification(
                players,
                mapOf(
                    "canvas_id" to config.creatorToPlayersCanvasId,
                    "sender_name" to episodeOwnerProfile.name,
                    "episode_id" to episodeId,
                    "profiles" to "[${encodeToString(episodeOwnerProfile)}]",
                    "preview_type" to "EPISODE",
                    "preview_id" to episodeId
                )
            )
        }
    }

    suspend fun notifyLiked(senderId: Long, receiverId: Long, episodeId: String) {
        launch {
            if (!canSend(senderId, receiverId, config.liked)) {
                logger.info("[NOTIFICATIONS] Cannot send like notification to player $receiverId from $senderId. Config: $config")
                return@launch
            }

            val count = eventGroupService.onEvent(episodeId, EventType.LIKE, receiverId)
            logger.info("[NOTIFICATIONS] Sending notification $count to player $receiverId")
            if (count > 0) {
                val profile = profileService.find(senderId)
                val canvasId =
                    if (count == 1 || config.liked.pluralCanvasId == null) config.liked.singleCanvasId else config.liked.pluralCanvasId!!
                sendNotification(canvasId, receiverId, episodeId, count, profile)
                eventGroupService.reset(episodeId, EventType.LIKE, receiverId)
            }
        }
    }

    private suspend fun sendNotification(
        canvasId: String,
        receiverId: Long,
        episodeId: String,
        count: Int,
        profile: Profile
    ) {
        if (canvasId.isBlank()) return

        clerkService.sendNotification(
            listOf(receiverId),
            mapOf(
                "canvas_id" to canvasId,
                "episode_id" to episodeId,
                "count" to count.toString(),
                "sender_id" to profile.name,
                "sender_name" to profile.name,
                "profiles" to "[${encodeToString(profile)}]",
                "preview_type" to "EPISODE",
                "preview_id" to episodeId
            )
        )
    }

    private fun canSend(senderId: Long, receiverId: Long, config: NotificationConfiguration): Boolean {
        if (receiverId == senderId) return false
        return config.isEnabled
    }

    private suspend fun launch(action: suspend () -> Unit) {
        withContext(dispatcher + SupervisorJob()) {
            launch {
                action()
            }
        }
    }

    private fun encodeToString(profile: Profile) =
        buildJsonObject {
            put("id", profile.playerId.toString())
            put("handle", profile.name)
            profile.photoUrl?.let { put("avatar_url", it) }
        }.toString()
}
