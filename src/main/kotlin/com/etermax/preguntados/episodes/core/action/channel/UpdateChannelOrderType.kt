package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUpdateOrderTypeRepository
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.PlayerNotOwnChannelException
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class UpdateChannelOrderType(
    private val repository: ChannelRepository,
    private val orderTypeRepository: ChannelUpdateOrderTypeRepository
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(data: ActionData) = with(data) {
        val channel = findChannel(channelId)

        if (!channel.isOwnedBy(playerId)) {
            throw PlayerNotOwnChannelException(playerId, channelId)
        }

        if (channel.orderType == orderType) {
            logger.warn("Channel='$channelId' already has OrderType='$orderType'. Not updating.")
            return
        }

        orderTypeRepository.put(channelId, orderType)
    }

    private suspend fun findChannel(channelId: String): Channel {
        return repository.findById(channelId) ?: throw ChannelNotFoundException(channelId)
    }

    data class ActionData(val playerId: Long, val channelId: String, val orderType: ChannelOrderType)
}
