package com.etermax.preguntados.episodes.core.infrastructure.ranking.representation

import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankedPlayer
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Ranking
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingEntry
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RankingResponse(
    @SerialName("userRanking") val userRanking: List<UserRank>,
    @SerialName("userRank") val userRank: UserRank
) {
    fun to() = Ranking(
        players = userRanking.map {
            RankedPlayer(it.userData.userId.toLong(), RankingEntry(it.position, it.score.toLong()))
        },
        player = RankedPlayer(
            userRank.userData.userId.toLong(),
            RankingEntry(userRank.position, userRank.score.toLong())
        )
    )
}
