package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.episode.Rate.Type.DISLIKE
import com.etermax.preguntados.episodes.core.domain.episode.Rate.Type.LIKE
import com.etermax.preguntados.episodes.core.domain.episode.notification.EpisodeNotificationService
import com.etermax.preguntados.episodes.core.domain.episode.rate.RateRepository
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.quality.QualityService
import org.slf4j.LoggerFactory

class RateEpisode(
    private val rateRepository: RateRepository,
    private val episodeRepository: EpisodeRepository,
    private val episodeNotificationService: EpisodeNotificationService,
    private val profileService: ProfileService,
    private val isUpdateRateCountersEnabled: Boolean,
    private val qualityService: QualityService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): EpisodeSummary = with(actionData) {
        logger.info("Player $playerId rated $rateType episode $episodeId")
        val previousRate = rateRepository.findBy(playerId, episodeId)
        val episode = episodeRepository.findById(episodeId)
            ?: throw IllegalArgumentException("Episode not found: $episodeId")
        val previousEpisodeCopied = copyEpisode(episode)
        val profile = profileService.find(episode.ownerId)

        if (rateType == previousRate) return EpisodeSummary.from(episode, profile)

        val ratedEpisode = rateEpisode(
            episode = episode,
            playerId = playerId,
            previousRate = previousRate,
            newRate = rateType
        )

        processChannelScoreAsync(previousEpisodeCopied, ratedEpisode)

        return EpisodeSummary.from(ratedEpisode, profile)
    }

    private fun copyEpisode(episode: Episode) = episode.copy(
        rate = episode.rate.copy(
            _likes = episode.rate.likes,
            _dislikes = episode.rate.dislikes
        )
    )

    private suspend fun rateEpisode(
        episode: Episode,
        playerId: Long,
        previousRate: Rate.Type?,
        newRate: Rate.Type?
    ): Episode {
        updatePlayerRate(playerId, episode.id, newRate)
        return updateEpisodeRateCounters(episode, previousRate, newRate).also {
            notifyLike(playerId, it, newRate)
        }
    }

    private suspend fun updatePlayerRate(
        playerId: Long,
        episodeId: String,
        newRate: Rate.Type?
    ) {
        when (newRate) {
            null -> rateRepository.delete(playerId, episodeId)
            else -> rateRepository.save(playerId, episodeId, newRate == LIKE)
        }
    }

    private suspend fun updateEpisodeRateCounters(
        episode: Episode,
        previousRate: Rate.Type?,
        newRate: Rate.Type?
    ): Episode {
        if (!isUpdateRateCountersEnabled) return episode
        if (previousRate == newRate) return episode

        var like = 0
        var dislike = 0

        when (previousRate) {
            LIKE -> {
                episode.decrementLikes()
                like -= 1
            }
            DISLIKE -> {
                episode.decrementUnlikes()
                dislike -= 1
            }
            null -> {
                like = 0
                dislike = 0
            }
        }

        when (newRate) {
            LIKE -> {
                episode.incrementLikes()
                like += 1
            }
            DISLIKE -> {
                episode.incrementUnlikes()
                dislike += 1
            }

            null -> { }
        }

        if (like != 0 || dislike != 0) {
            episodeRepository.updateRate(episodeId = episode.id, like = like, dislike = dislike)
        }
        return episode
    }

    private suspend fun notifyLike(playerId: Long, episode: Episode, newRate: Rate.Type?) {
        if (newRate == LIKE) {
            logger.info("[NOTIFICATIONS] Notifying like from player $playerId to episode ${episode.id}")
            episodeNotificationService.notifyLiked(
                senderId = playerId,
                receiverId = episode.ownerId,
                episodeId = episode.id
            )
        }
    }

    private suspend fun processChannelScoreAsync(
        previousEpisodeCopied: Episode,
        ratedEpisode: Episode
    ) {
        try {
            qualityService.calculateChannelScore(
                episodeBeforeUpdate = previousEpisodeCopied,
                episodeAfterUpdate = ratedEpisode
            )
        } catch (e: Exception) {
            logger.error("[QUALITY] Error processing channel score for episode ${previousEpisodeCopied.id}", e)
        }
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String,
        val rateType: Rate.Type?
    )
}
