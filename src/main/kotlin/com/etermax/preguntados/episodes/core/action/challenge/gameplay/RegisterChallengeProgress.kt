package com.etermax.preguntados.episodes.core.action.challenge.gameplay

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService.ChallengeContext
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import org.slf4j.LoggerFactory

class RegisterChallengeProgress(
    private val challengeService: ChallengeService,
    private val summaryService: SummaryService,
    private val episodesRepository: EpisodeRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): EpisodeSummary {
        logger.info("Player ${actionData.playerId} registered content ${actionData.contentId} for challenge ${actionData.challengeId}")
        val context = getChallengeContext(actionData)

        registerProgress(actionData, context)
        incrementViews(context)

        return summaryService.toEpisodeSummary(context.episode)!!
    }

    private suspend fun getChallengeContext(actionData: ActionData): ChallengeContext {
        return challengeService.getChallengeContext(actionData.challengeId, actionData.playerId)
    }

    private suspend fun registerProgress(actionData: ActionData, context: ChallengeContext) {
        challengeService.registerProgress(actionData.playerId, actionData.contentId, context)
    }

    private suspend fun incrementViews(context: ChallengeContext) {
        if (context.progress == null) {
            episodesRepository.updateView(context.episode.id)
        }
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String,
        val contentId: String
    )
}
