package com.etermax.preguntados.episodes.core.infrastructure.ranking.representation

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class PlayersRequest(
    @SerialName("players_id") val players: List<PlayerRequest>
)

@Serializable
class PlayerRequest(
    @SerialName("id") val players: <PERSON>,
    @SerialName("nickname") val nickname: String? = null
)
