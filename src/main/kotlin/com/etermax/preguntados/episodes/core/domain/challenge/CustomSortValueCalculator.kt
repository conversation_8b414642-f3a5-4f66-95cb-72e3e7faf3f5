package com.etermax.preguntados.episodes.core.domain.challenge

import com.etermax.preguntados.sortable.games.core.domain.pagination.SortableGame
import com.etermax.preguntados.sortable.games.core.domain.pagination.sortValue.SortValueCalculator
import kotlin.time.Duration.Companion.days

class CustomSortValueCalculator : SortValueCalculator {
    override suspend fun calculateFor(
        sortableGame: SortableGame,
        playerId: Long
    ): Long {
        val game = sortableGame as ChallengeSortableGame

        val isInProgress = game.isInProgress()
        val hasPlayerFinished = game.hasPlayerFinished(playerId)

        val isFinished = game.isFinished()
        val isPlayerTop3 = game.isPlayerTop3(playerId)

        val sortingTime = sortableGame.sortingTime(playerId)

        return when {
            isInProgress && !hasPlayerFinished -> sortingTime
            isFinished && isPlayerTop3 -> sortingTime + wonOffset
            isFinished && !isPlayerTop3 -> sortingTime + lostOffset
            else -> sortingTime + upcomingOffset
        }
    }

    private companion object {
        private const val WON_OFFSET_MULTIPLIER = 2
        private const val LOST_OFFSET_MULTIPLIER = 3
        private const val UPCOMING_OFFSET_MULTIPLIER = 4

        private val oneYear = 365.days.inWholeMilliseconds
        private val oneHundredYears = oneYear * 100

        private val wonOffset = oneHundredYears * WON_OFFSET_MULTIPLIER
        private val lostOffset = oneHundredYears * LOST_OFFSET_MULTIPLIER
        private val upcomingOffset = oneHundredYears * UPCOMING_OFFSET_MULTIPLIER
    }
}
