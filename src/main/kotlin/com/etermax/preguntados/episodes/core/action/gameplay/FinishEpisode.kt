package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.analytics.actions.TrackEpisodeAnalytics
import com.etermax.preguntados.analytics.service.FinishedEvent
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.finish.FinishEpisodeDetails
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingService
import com.etermax.preguntados.episodes.core.domain.episode.rate.RateRepository
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class FinishEpisode(
    private val episodeRepository: EpisodeRepository,
    private val profileService: ProfileService,
    private val rateRepository: RateRepository,
    private val rankingService: RankingService,
    private val trackMetric: TrackEpisodeAnalytics
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): FinishEpisodeDetails = coroutineScope {
        with(actionData) {
            logger.info("Player $playerId finished episode $episodeId")

            val episode = episodeRepository.findById(episodeId) ?: throw EpisodeNotFoundException(episodeId)

            val ranking = async { rankingService.findRanking(playerId, domain) }
            val rankingWithFriends = async { rankingService.findRankingWithFriends(playerId, domain) }
            val episodeSummary = async { getSummary(episode) }
            val playerRate = async { rateRepository.findBy(playerId, episodeId) }

            trackMetric(FinishedEvent(playerId, episodeId, episode.ownerId))

            FinishEpisodeDetails(
                episodeSummary = episodeSummary.await(),
                rate = playerRate.await(),
                ranking = ranking.await(),
                rankingWithFriends = rankingWithFriends.await()
            )
        }
    }

    private suspend fun getSummary(episode: Episode): EpisodeSummary {
        val profile = profileService.find(episode.ownerId)
        return EpisodeSummary.from(episode, profile)
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String
    ) {
        val domain = Domain(episodeId)
    }
}
