package com.etermax.preguntados.episodes.core.infrastructure.repository.episode

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedRequest
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedResponse
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeByOwnerItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.future.await
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.reactive.awaitFirstOrNull
import software.amazon.awssdk.enhanced.dynamodb.*
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.ScanEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.ReturnValue
import software.amazon.awssdk.services.dynamodb.model.UpdateItemRequest
import java.util.Locale

class DynamoDBEpisodeRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<EpisodeItem>,
    private val byOwnerIndex: DynamoDbAsyncIndex<EpisodeByOwnerItem>
) : EpisodeRepository, DynamoDBRepository<EpisodeItem>(client, table) {

    override suspend fun findAll(ownerId: Long?, language: Language?, name: String?, country: Country?): List<Episode> {
        val scanRequestBuilder = ScanEnhancedRequest.builder()

        val expressionNames = mutableMapOf("#pk" to "PK", "#sk" to "SK")
        val expressionValues = mutableMapOf(
            ":prefixValue" to AttributeValue.builder().s("E#").build(),
            ":skValue" to AttributeValue.builder().s(EpisodeItem.EPISODE_SK).build()
        )

        val conditions = mutableListOf("begins_with(#pk, :prefixValue)", "#sk = :skValue")

        ownerId?.let {
            expressionNames["#ownerId"] = "owner_id"
            expressionValues[":ownerIdValue"] = AttributeValue.builder().n(it.toString()).build()
            conditions.add("attribute_exists(#ownerId) AND #ownerId = :ownerIdValue")
        }

        language?.let {
            expressionNames["#language"] = "language"
            expressionValues[":languageValue"] = AttributeValue.builder().s(it.name).build()
            conditions.add("attribute_exists(#language) AND #language = :languageValue")
        }

        name?.let {
            expressionNames["#name"] = "name"
            expressionValues[":nameValue"] = AttributeValue.builder().s(it).build()
            conditions.add("attribute_exists(#name) AND contains(#name, :nameValue)")
        }

        country?.let {
            expressionNames["#country"] = "country"
            expressionValues[":countryValue"] = AttributeValue.builder().s(it.name).build()
            conditions.add("attribute_exists(#country) AND #country = :countryValue")
        }

        val filterExpression = Expression.builder()
            .expression(conditions.joinToString(" AND "))
            .expressionNames(expressionNames)
            .expressionValues(expressionValues)
            .build()

        val scanRequest = scanRequestBuilder.filterExpression(filterExpression).build()

        return table.scan(scanRequest)
            .items()
            .asFlow()
            .toList()
            .filter { it.sk == EpisodeItem.EPISODE_SK }
            .mapNotNull { it.to() }
    }

    override suspend fun findById(episodeId: String): Episode? {
        val key = Key.builder()
            .partitionKey(episodeId)
            .sortKey()
            .build()
        val conditional = QueryConditional
            .keyEqualTo(key)
        val request = QueryEnhancedRequest.builder()
            .queryConditional(conditional)
            .build()
        return table
            .query(request)
            .items()
            .asFlow()
            .toList()
            .firstOrNull()
            ?.to()
    }

    override suspend fun findByIds(episodeIds: List<String>): List<Episode> {
        if (episodeIds.isEmpty()) return emptyList()

        val batches = episodeIds.map {
            val key = Key.builder()
                .partitionKey(it)
                .sortKey()
                .build()
            findItemBatch(key, EpisodeItem::class.java)
        }

        return findItemsBulk(batches)
            .mapNotNull { it?.to() }
    }

    @OptIn(FlowPreview::class)
    override suspend fun findBy(
        ownerId: Long,
        language: Language?,
        type: EpisodeType?,
        status: EpisodeStatus?,
        channelId: String?
    ): List<String> {
        val expressionNames = mutableMapOf<String, String>()
        val expressionValues = mutableMapOf<String, AttributeValue>()
        val conditions = mutableListOf<String>()

        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(ownerId)
        }

        language?.let {
            expressionNames["#language"] = "language"
            expressionValues[":languageValue"] = AttributeValue.builder().s(it.name).build()
            conditions.add("#language = :languageValue")
        }

        type?.let {
            expressionNames["#type"] = "type"
            expressionValues[":typeValue"] = AttributeValue.builder().s(it.name).build()
            conditions.add("#type = :typeValue")
        }

        status?.let {
            expressionNames["#status"] = "status"
            expressionValues[":statusValue"] = AttributeValue.builder().s(it.name).build()
            conditions.add("#status = :statusValue")
        }

        expressionNames["#channelId"] = ChannelItemAttributes.CHANNEL_ID
        channelId?.let {
            expressionValues[":channelIdValue"] = AttributeValue.builder().s(channelId).build()
            conditions.add("#channelId = :channelIdValue")
        } ?: run {
            conditions.add("attribute_not_exists(#channelId)")
        }

        val expression = Expression.builder()
            .expression(conditions.joinToString(" AND "))
            .expressionNames(expressionNames)
            .expressionValues(expressionValues)
            .build()

        val request = QueryEnhancedRequest.builder()
            .queryConditional(queryConditional)
            .filterExpression(expression).build()

        return byOwnerIndex
            .query(request)
            .asFlow()
            .flatMapMerge { page -> page.items().asFlow() }
            .mapNotNull { it.episodeId }
            .toList()
    }

    override suspend fun findBy(
        paginatedRequest: PaginatedRequest,
        ownerId: Long,
        language: Language?,
        type: EpisodeType?,
        status: EpisodeStatus?,
        channelId: String?
    ): PaginatedResponse<String> {
        val expressionNames = mutableMapOf<String, String>()
        val expressionValues = mutableMapOf<String, AttributeValue>()
        val conditions = mutableListOf<String>()

        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(ownerId)
        }

        language?.let {
            expressionNames["#language"] = "language"
            expressionValues[":languageValue"] = AttributeValue.builder().s(it.name).build()
            conditions.add("#language = :languageValue")
        }

        type?.let {
            expressionNames["#type"] = "type"
            expressionValues[":typeValue"] = AttributeValue.builder().s(it.name).build()
            conditions.add("#type = :typeValue")
        }

        status?.let {
            expressionNames["#status"] = "status"
            expressionValues[":statusValue"] = AttributeValue.builder().s(it.name).build()
            conditions.add("#status = :statusValue")
        }

        expressionNames["#channelId"] = "channel_id"
        channelId?.let {
            expressionValues[":channelIdValue"] = AttributeValue.builder().s(channelId).build()
            conditions.add("#channelId = :channelIdValue")
        } ?: run {
            conditions.add("attribute_not_exists(#channelId)")
        }

        val expression = Expression.builder()
            .expression(conditions.joinToString(" AND "))
            .expressionNames(expressionNames)
            .expressionValues(expressionValues)
            .build()

        val items = mutableListOf<String>()
        var nextKey = paginatedRequest.nextToken?.let { decodeLastEvaluatedKey(it) }
        var done = false

        while (!done && items.size < paginatedRequest.size) {
            val query = QueryEnhancedRequest.builder()
                .limit(paginatedRequest.size) // limit applies before filtering
                .queryConditional(queryConditional)
                .filterExpression(expression)
                .apply {
                    nextKey?.let { exclusiveStartKey(it) }
                }
                .build()

            val page = byOwnerIndex.query(query).awaitFirstOrNull() ?: break

            val pageItems = page.items().mapNotNull { it.episodeId }

            items.addAll(pageItems)

            if (items.size >= paginatedRequest.size || page.lastEvaluatedKey() == null) {
                done = true
                nextKey = page.lastEvaluatedKey()
            } else {
                nextKey = page.lastEvaluatedKey()
            }
        }

        return PaginatedResponse(
            items = items,
            nextToken = if (items.size < paginatedRequest.size) null else nextKey?.let { encodeLastEvaluatedKey(it) }
        )
    }

    override suspend fun delete(episodeId: String) {
        val key = Key.builder()
            .partitionKey(episodeId)
            .build()
        val conditional = QueryConditional
            .keyEqualTo(key)
        val itemsToDelete = table
            .query(conditional)
            .items().asFlow().toList()

        if (itemsToDelete.isEmpty()) {
            return
        }

        itemsToDelete.chunked(25).forEach { chunksEpisodes ->
            val episodesBatch = chunksEpisodes.map {
                makeDeleteBatch(it, EpisodeItem::class.java)
            }
            deleteItemsBulkWithRetry(episodesBatch, EpisodeItem::class.java)
        }
    }

    override suspend fun save(episodes: List<Episode>) {
        episodes.chunked(25).forEach { chunksEpisodes ->
            val episodesBatch = chunksEpisodes.map {
                makeWriteBatch(EpisodeItem.from(it), EpisodeItem::class.java)
            }
            addItemsBulkWithRetry(episodesBatch, EpisodeItem::class.java)
        }
    }

    override suspend fun save(episode: Episode) {
        save(listOf(episode))
    }

    override suspend fun updateItem(episode: Episode) {
        val updateRequest = UpdateItemEnhancedRequest.builder(EpisodeItem::class.java)
            .item(EpisodeItem.from(episode))
            .build()
        table.updateItem(updateRequest)
    }

    override suspend fun hasEpisodes(ownerId: Long): Boolean {
        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(ownerId)
        }

        val request = QueryEnhancedRequest
            .builder()
            .queryConditional(queryConditional)
            .scanIndexForward(false)
            .limit(1)
            .build()

        return byOwnerIndex
            .query(request)
            .asFlow()
            .firstOrNull()
            ?.items()
            ?.isNotEmpty() == true
    }

    override suspend fun updateRate(episodeId: String, like: Int, dislike: Int) {
        val key = buildKey(episodeId)

        val expression = """
            SET
                #likes = if_not_exists(#likes, :zero) + :likeDelta,
                #dislikes = if_not_exists(#dislikes, :zero) + :dislikeDelta
        """.trimIndent()
        val updateResponse = rawClient().updateItem(
            UpdateItemRequest.builder()
                .tableName(table.tableName())
                .key(key)
                .updateExpression(expression)
                .expressionAttributeNames(
                    mapOf(
                        "#likes" to ATTR_LIKES,
                        "#dislikes" to ATTR_DISLIKES
                    )
                )
                .expressionAttributeValues(
                    mapOf(
                        ":likeDelta" to AttributeValue.fromN(like.toString()),
                        ":dislikeDelta" to AttributeValue.fromN(dislike.toString()),
                        ":zero" to AttributeValue.fromN("0")
                    )
                )
                .returnValues(ReturnValue.UPDATED_NEW)
                .build()
        ).await()

        val newLikes = updateResponse.attributes()[ATTR_LIKES]?.n()?.toLong() ?: 0L
        val newDislikes = updateResponse.attributes()[ATTR_DISLIKES]?.n()?.toLong() ?: 0L
        val newRate = Rate(_likes = newLikes, _dislikes = newDislikes).calculate()
        val newRateFormated = String.format(Locale.US, "%f", newRate)

        rawClient().updateItem(
            UpdateItemRequest.builder()
                .tableName(table.tableName())
                .key(key)
                .updateExpression("SET #rate = :rate")
                .expressionAttributeNames(mapOf("#rate" to ATTR_RATE))
                .expressionAttributeValues(mapOf(":rate" to AttributeValue.fromN(newRateFormated)))
                .build()
        ).await()
    }

    override suspend fun plusOneReport(episodeId: String) {
        val key = buildKey(episodeId)

        val request = UpdateItemRequest.builder()
            .tableName(table.tableName())
            .key(key)
            .updateExpression("ADD $ATTR_REPORTS :reportDelta")
            .expressionAttributeValues(mapOf(":reportDelta" to AttributeValue.fromN("1")))
            .returnValues(ReturnValue.UPDATED_NEW)
            .build()
        rawClient().updateItem(request).await()
    }

    override suspend fun updateView(episodeId: String, views: Int) {
        val expression = """
            SET
                #views = if_not_exists(#views, :zero) + :viewsDelta
        """.trimIndent()
        rawClient().updateItem(
            UpdateItemRequest.builder()
                .tableName(table.tableName())
                .key(buildKey(episodeId))
                .updateExpression(expression)
                .expressionAttributeNames(
                    mapOf(
                        "#views" to ATTR_VIEWS
                    )
                )
                .expressionAttributeValues(
                    mapOf(
                        ":viewsDelta" to AttributeValue.fromN(views.toString()),
                        ":zero" to AttributeValue.fromN("0")
                    )
                )
                .build()
        ).await()
    }

    private fun buildKey(episodeId: String) = mapOf(
        "PK" to AttributeValue.fromS(EpisodeItem.buildPartitionKey(episodeId)),
        "SK" to AttributeValue.fromS(EpisodeItem.EPISODE_SK)
    )

    private fun Key.Builder.partitionKey(episodeId: String): Key.Builder {
        // FIXME This can create problems, we should always search with the formal id
        val value = if (episodeId.startsWith(EpisodeItem.EPISODE_PREFIX)) {
            episodeId
        } else {
            EpisodeItem.buildPartitionKey(episodeId)
        }
        partitionValue(value)
        return this
    }

    private fun Key.Builder.sortKey(): Key.Builder {
        sortValue(EpisodeItem.EPISODE_SK)
        return this
    }

    private companion object {
        const val ATTR_LIKES = "likes"
        const val ATTR_DISLIKES = "dislikes"
        const val ATTR_REPORTS = "reports"
        const val ATTR_RATE = "rate"
        const val ATTR_VIEWS = "views"
    }
}
