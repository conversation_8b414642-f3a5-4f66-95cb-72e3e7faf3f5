package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import com.etermax.preguntados.episodes.core.infrastructure.search.service.RecentEpisodeSearchService
import org.slf4j.LoggerFactory
import kotlin.random.Random

class RandomRecentEpisodeSource(
    private val recentEpisodeSearchService: RecentEpisodeSearchService
) : Source<String> {

    private val logger = LoggerFactory.getLogger(RandomRecentEpisodeSource::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<String> {
        with(request) {
            if (range.limit <= 0) {
                logger.debug("[FEED] Fetch called with non-positive limit: {}", range.limit)
                return emptyResponse(fetchCursor)
            }

            val offsetCursor = fetchCursor as? OffsetFetchCursor
            if (offsetCursor != null && offsetCursor.exhausted) {
                logger.debug("[FEED] Fetch called with exhausted cursor, returning empty response")
                return emptyResponse(fetchCursor)
            }

            val effectiveRange = applyFetchCursor(range, offsetCursor)
            val fetchLimit = effectiveRange.limit * 2

            logger.debug(
                "[FEED] Effective range applied: offset={}, limit={}, fetchLimit={}",
                effectiveRange.offset,
                effectiveRange.limit,
                fetchLimit
            )

            val allItems = recentEpisodeSearchService.search(language, country, effectiveRange.offset, fetchLimit)
            logger.debug(
                "[FEED] OpenSearch returned {} episodes for offset {} with limit {}",
                allItems.size,
                effectiveRange.offset,
                effectiveRange.limit
            )
            logger.info("[FEED] Random recent returned {} episodes IDs for player {}", allItems.size, request.userId)

            val selectedItems = allItems.shuffled(Random).take(effectiveRange.limit)
            val exhausted = allItems.size < fetchLimit
            val newOffset = effectiveRange.offset + allItems.size

            return SourceResponse(
                selectedItems,
                OffsetFetchCursor(offset = newOffset, exhausted = exhausted)
            )
        }
    }

    private fun applyFetchCursor(range: OffsetLimit, offsetCursor: OffsetFetchCursor?): OffsetLimit {
        return if (offsetCursor != null) {
            OffsetLimit(offsetCursor.offset, range.limit).also {
                logger.debug("[FEED] Applied fetch cursor offset: {}", it.offset)
            }
        } else {
            range.also {
                logger.debug("[FEED] No fetch cursor provided, using original range offset: {}", it.offset)
            }
        }
    }
}
