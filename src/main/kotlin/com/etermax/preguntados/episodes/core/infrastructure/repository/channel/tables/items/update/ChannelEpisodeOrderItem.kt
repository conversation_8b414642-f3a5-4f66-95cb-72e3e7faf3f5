package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update

import com.etermax.preguntados.episodes.core.domain.channel.episode.ChannelEpisodeOrder
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItemUtils.toNormalizedLength
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class ChannelEpisodeOrderItem(
    @get:DynamoDbAttribute("episode_id") var episodeId: String = "",

    @get:DynamoDbAttribute(ChannelItemAttributes.CHANNEL_ID) var channelId: String = "",

    @get:DynamoDbAttribute("ep_order")
    var episodeOrder: String = ""
) {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = ChannelEpisodeItem.buildPartitionKey(channelId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = ChannelEpisodeItem.buildSortedKey(episodeId)

    companion object {
        fun from(domain: ChannelEpisodeOrder): ChannelEpisodeOrderItem {
            return ChannelEpisodeOrderItem(domain.episodeId, domain.channelId, domain.episodeOrder.toNormalizedLength())
        }
    }
}
