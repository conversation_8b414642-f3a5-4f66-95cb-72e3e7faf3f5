package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.PlayerRecentSearchRepository
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.domain.search.filter.Filter
import com.etermax.preguntados.episodes.core.domain.search.filter.FilterFactory
import com.etermax.preguntados.episodes.core.domain.search.filter.FilterType
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.OpenSearchPlayedRepository
import com.etermax.preguntados.episodes.core.infrastructure.search.data.EmbeddingEpisodeData
import com.etermax.preguntados.episodes.core.infrastructure.search.data.EpisodeData
import kotlinx.coroutines.future.await
import org.opensearch.client.json.JsonData
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch.core.MgetRequest
import org.opensearch.client.opensearch.core.MgetResponse
import org.opensearch.client.opensearch.core.MsearchRequest
import org.opensearch.client.opensearch.core.SearchRequest
import org.opensearch.client.opensearch.core.msearch.RequestItem
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.random.Random

/**
 * @deprecated Search based feed will be removed.
 * @see com.etermax.preguntados.episodes.core.domain.search.feed.FeedService
 */
@Deprecated("Search based feed will be removed")
class FeedSearch(
    tokenizer: Tokenizer,
    private val playedSearchRepository: OpenSearchPlayedRepository,
    private val recentSearch: RecentSearch,
    private val fallbackSearch: FeedFallbackSearch,
    private val filterFactory: FilterFactory,
    private val recentOffsetRepository: PlayerRecentSearchRepository,
    private val client: OpenSearchAsyncClient,
    private val indexName: String,
    private val lastPlayedThreshold: Int
) : Search, BaseSearch(tokenizer) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun match(parameters: SearchParameters): Boolean = with(parameters) {
        sort == null && name.isNullOrBlank()
    }

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        val playerId = parameters.playerId
        if (playerId == null) return emptyList()
        val episodeIds = playedSearchRepository.lastPlayedEpisodesIds(playerId, 0, lastPlayedThreshold)

        if (episodeIds.size < lastPlayedThreshold) {
            return fallbackSearch.search(parameters)
        }

        val embeddings = getEpisodesEmbedding(episodeIds)

        with(parameters) {
            val playerId = this.playerId!!
            val split = splitOffsetAndLimit(offset, limit, playerId)

            val similarOffsets = divideEvenly(split.similarOffset, embeddings.size)
            val similarLimits = divideEvenly(split.similarLimit, embeddings.size)

            val embeddingRequests = embeddings.mapIndexed { index, embedding ->
                val offset = similarOffsets[index]
                val limit = similarLimits[index]
                buildSingleRequest(offset, limit, parameters, embeddings, embedding).toMsearchRequest()
            }
            val recentRequest =
                getRecentEpisodesRequest(parameters, split.recentOffset, split.recentLimit).toMsearchRequest()

            val (similarEpisodes, recentEpisodes) = search(embeddingRequests, recentRequest)

            val dedupKey = "feed:$playerId:$language"
            val filter = filterFactory.create(playerId, FilterType.DUPLICATE, offset == 0)

            try {
                val similarEpisodes = similarEpisodes.filter { filter.apply(it.id) }

                val completedRecents = completeRecentsIfNecessary(
                    recentEpisodes = recentEpisodes,
                    targetSize = limit - similarEpisodes.size,
                    parameters = parameters,
                    recentOffset = split.recentOffset + split.recentLimit,
                    filter = filter
                )

                val page = similarEpisodes + completedRecents

                // Create a seed based on search parameters for predictable shuffling
                val random = random(parameters, page)
                val shuffled = page.shuffled(random)
                return shuffled
            } finally {
                filter.save()
            }
        }
    }

    private suspend fun completeRecentsIfNecessary(
        recentEpisodes: List<Episode>,
        targetSize: Int,
        parameters: SearchParameters,
        recentOffset: Int,
        filter: Filter
    ): List<Episode> {
        var recentOffset = recentOffset
        var recents = recentEpisodes
            .filter { filter.apply(it.id) }
            .distinctBy { it.ownerId }

        while (recents.size < targetSize) {
            val nextLimit = targetSize - recents.size
            val result = getRecentEpisodes(parameters, recentOffset, nextLimit)
            recentOffset += result.size

            if (result.isEmpty()) break

            val recentFiltered = result.filter { filter.apply(it.id) }

            recents = (recents + recentFiltered).distinctBy { it.ownerId }
        }

        recentOffsetRepository.save(parameters.playerId!!, recentOffset)

        return recents
    }

    private suspend fun search(
        embeddingRequests: List<RequestItem>,
        recentRequest: RequestItem
    ): Pair<List<Episode>, List<Episode>> {
        val requests = embeddingRequests + recentRequest
        val msearchRequest = MsearchRequest.Builder().searches(requests).build()

        val responses = client.msearch(msearchRequest, EpisodeData::class.java).await().toEpisodesLists()
        val similarEpisodes = responses.dropLast(1).flatten()
        val recentEpisodes = responses.last()

        logger.info("Feed search: {} similar, {} recents.", similarEpisodes.size, recentEpisodes.size)
        return Pair(similarEpisodes, recentEpisodes)
    }

    private fun random(
        parameters: SearchParameters,
        responses: List<Episode>
    ): Random {
        val seed = parameters.run {
            offset.hashCode() + limit.hashCode() + (language?.hashCode() ?: 0) + responses.sumOf { it.id.hashCode() }
        }
        return Random(seed.toLong())
    }

    private fun divideEvenly(total: Int, parts: Int): List<Int> {
        val base = total / parts
        val remainder = total % parts
        return List(parts) { i -> if (i < remainder) base + 1 else base }
    }

    private fun SearchRequest.toMsearchRequest(): RequestItem = RequestItem.Builder()
        .header { header -> header.index(this.index().first()) }
        .body { body ->
            body.query(this.query())
            body.source(this.source())
            body.from(this.from())
            body.size(this.size())
            this.sort()?.let { body.sort(it) }
            this.aggregations()?.let { body.aggregations(it) }
            body
        }
        .build()

    private fun buildSingleRequest(
        offset: Int,
        limit: Int,
        parameters: SearchParameters,
        embeddings: List<EmbeddingEpisodeData>,
        embedding: EmbeddingEpisodeData
    ): SearchRequest {
        val request = OpenSearchRequestFactory(indexName, offset, limit)
            .filterByLanguage(parameters.language?.name)
            .filterByType(EpisodeType.PUBLIC)
            .filterByStatus(EpisodeStatus.PUBLISHED)
            .atLeast5Views()
            .filterOlderThan(sevenDaysAgo())
            .filterNotByEpisodes(embeddings)
            .filterByEmbedding(embedding, parameters.limit)
            .build()
        return request
    }

    private suspend fun getEpisodesEmbedding(episodeIds: List<String>): List<EmbeddingEpisodeData> {
        val request = MgetRequest.Builder()
            .ids(episodeIds)
            .index(indexName)
            .sourceIncludes(EPISODE_ID_FIELD, EMBEDDING_FIELD)
            .build()

        val response: MgetResponse<EmbeddingEpisodeData> =
            client.mget(request, EmbeddingEpisodeData::class.java).await()

        return response.docs().mapNotNull { it.result()?.source() }
    }

    private fun getRecentEpisodesRequest(parameters: SearchParameters, offset: Int, limit: Int): SearchRequest {
        val recentParams = parameters.copy(
            sort = SortEpisode.RECENT,
            offset = offset,
            limit = limit
        )
        return recentSearch.buildRequest(recentParams)
    }

    private suspend fun getRecentEpisodes(parameters: SearchParameters, offset: Int, limit: Int): List<Episode> {
        val recentParams = parameters.copy(
            sort = SortEpisode.RECENT,
            offset = offset,
            limit = limit
        )
        return recentSearch.search(recentParams)
    }

    suspend fun splitOffsetAndLimit(receivedOffset: Int, limit: Int, playerId: Long): SearchSplit {
        val similarLimit = (limit * 0.75).toInt()
        val recentLimit = limit - similarLimit

        val isJustRefreshed = receivedOffset == 0
        val similarOffset = if (isJustRefreshed) 0 else (receivedOffset * 0.75).toInt()
        val recentOffset = if (isJustRefreshed) 0 else (recentOffsetRepository.find(playerId) ?: 0)

        return SearchSplit(
            similarOffset = similarOffset,
            similarLimit = similarLimit,
            recentOffset = recentOffset,
            recentLimit = recentLimit
        )
    }

    private fun OpenSearchRequestFactory.filterOlderThan(days: Long) = this.also {
        filterRange(START_DATE_FIELD) {
            it.lt(JsonData.of(days))
        }
    }

    private fun OpenSearchRequestFactory.atLeast5Views(): OpenSearchRequestFactory =
        filterGreaterThanOrEqual(VIEWS_FIELD, 5)

    private fun sevenDaysAgo(): Long = Instant.now().minus(7, ChronoUnit.DAYS).toEpochMilli()
}

data class SearchSplit(
    val similarOffset: Int,
    val similarLimit: Int,
    val recentOffset: Int,
    val recentLimit: Int
)
