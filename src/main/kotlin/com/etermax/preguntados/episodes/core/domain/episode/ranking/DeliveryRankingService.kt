package com.etermax.preguntados.episodes.core.domain.episode.ranking

import com.etermax.preguntados.episodes.core.domain.profile.ProfileService

class DeliveryRankingService(
    private val profileService: ProfileService
) {

    suspend fun complete(ranking: Ranking): DeliveryRanking {
        val playerIds = ranking.players.associateBy { it.playerId }
        val profiles = profileService.findMany(playerIds.keys.toList())
            .map { profile -> DeliveryRankedPlayer(profile, playerIds[profile.playerId]!!.rankingEntry) }
            .recalculatePositions(playerIds.keys.toList())
            .sortedByDescending { it.rankingEntry.points }

        val playerRankingEntry = profiles
            .firstOrNull { profile -> profile.profile.playerId == ranking.player.playerId }?.rankingEntry
            ?: ranking.player.rankingEntry
        return DeliveryRanking(profiles, playerRankingEntry)
    }

    private fun List<DeliveryRankedPlayer>.recalculatePositions(players: List<Long>): List<DeliveryRankedPlayer> {
        if (players.size == this.size) return this
        return this
            .sortedByDescending { it.rankingEntry.points }
            .mapIndexed { i, it ->
                it.copy(rankingEntry = it.rankingEntry.copy(position = i.plus(1)))
            }
    }
}
