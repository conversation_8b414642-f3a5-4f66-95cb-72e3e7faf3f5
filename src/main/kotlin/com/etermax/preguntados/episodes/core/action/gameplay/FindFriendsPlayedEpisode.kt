package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.domain.profile.PlayerFriendsService
import com.etermax.preguntados.episodes.core.infrastructure.search.FriendsEpisodeSearch

class FindFriendsPlayedEpisode(
    private val playerFriendsService: PlayerFriendsService,
    private val friendsEpisodeSearch: FriendsEpisodeSearch
) {

    suspend operator fun invoke(actionData: ActionData): List<Long> {
        val friends = playerFriendsService.findFollowedIds(actionData.playerId)
        if (friends.isEmpty()) return emptyList()
        return friendsEpisodeSearch.search(friends, actionData.episodeId)
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String
    )
}
