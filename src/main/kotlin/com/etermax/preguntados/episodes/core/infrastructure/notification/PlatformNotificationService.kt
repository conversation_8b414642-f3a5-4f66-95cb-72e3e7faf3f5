package com.etermax.preguntados.episodes.core.infrastructure.notification

import com.etermax.preguntados.episodes.core.domain.notification.Notification
import com.etermax.preguntados.episodes.core.domain.notification.NotificationService
import com.etermax.preguntados.episodes.core.domain.notification.NotificationWithHoursLeft
import com.etermax.preguntados.episodes.core.domain.notification.SimpleNotification
import com.etermax.preguntados.episodes.core.infrastructure.notification.representation.NotificationRequest
import io.ktor.client.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.http.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class PlatformNotificationService(private val client: HttpClient, private val platformGameId: String) :
    NotificationService {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)
    override suspend fun notify(notification: Notification) {
        logger.debug(
            "Sending notification {} to user {} for game {} [senderId: {}]",
            notification.type,
            notification.receiverId,
            notification.gameId,
            notification.senderProfile.id
        )
        sendTo(notification.receiverId, createBody(notification))
    }

    override suspend fun notify(notification: NotificationWithHoursLeft) {
        logger.debug(
            "Sending notification {} with {} hours left to user {} for game {} [senderId: {}]",
            notification.type,
            notification.hoursLeft,
            notification.receiverId,
            notification.gameId,
            notification.senderProfile.id
        )
        sendTo(notification.receiverId, createBody(notification))
    }

    override suspend fun notify(notification: SimpleNotification) {
        val receiverId = notification.receiverId
        val body = createBody(notification)
        logger.debug(
            "Sending notification to user {} with dataAsString {} from [SenderId: {}]",
            receiverId,
            body.notification.dataAsString,
            notification.senderProfile.id
        )
        sendTo(receiverId, body)
    }

    private fun createBody(notification: SimpleNotification): NotificationRequest {
        return NotificationRequest.from(notification)
    }

    private suspend fun sendTo(receiverId: Long, body: NotificationRequest) {
        val url = URL.format(platformGameId, receiverId)
        client.post(url) {
            contentType(ContentType.Application.Json)
            accept(ContentType.Application.Json)
            setBody(body)
            expectSuccess = true
        }
    }

    private fun createBody(notification: Notification): NotificationRequest {
        return NotificationRequest.from(notification)
    }

    private fun createBody(notification: NotificationWithHoursLeft): NotificationRequest {
        return NotificationRequest.from(notification)
    }

    private companion object {
        const val URL = "games/%s/users/%s/push-notifications"
    }
}
