package com.etermax.preguntados.episodes.core.domain.time

import java.time.OffsetDateTime

fun OffsetDateTime.toMillis(loseMillis: Boolean = true): Long {
    return if (loseMillis) this.toEpochSecond() * 1000
    else this.toInstant().toEpochMilli()
}

fun Int.minutesToSeconds() = this * SECONDS_IN_MINUTES
fun Long.minutesToSeconds() = this * SECONDS_IN_MINUTES

fun OffsetDateTime.isBeforeOrEqualsTo(time: OffsetDateTime): Boolean {
    return this.isBefore(time) || this.isEqual(time)
}

private const val SECONDS_IN_MINUTES = 60L
