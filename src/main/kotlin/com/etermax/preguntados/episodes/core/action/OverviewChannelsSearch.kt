package com.etermax.preguntados.episodes.core.action

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.infrastructure.search.SearchChannelsByName
import com.etermax.preguntados.episodes.utils.LanguageUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class OverviewChannelsSearch(
    private val summaryService: SummaryService,
    private val channelsSearch: SearchChannelsByName
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): List<ChannelSummary> {
        with(actionData) {
            logger.info("Player $playerId find overview channels for [$name] and [$language]")
            return actionData.findChannels()
        }
    }

    private suspend fun ActionData.findChannels(): List<ChannelSummary> {
        val lang = LanguageUtils.from(language)
        val channels = channelsSearch.search(name, lang, offset, limit)
        return summaryService.toChannelsSummary(channels)
    }

    data class ActionData(
        val playerId: Long,
        val name: String,
        val language: String?,
        val offset: Int,
        val limit: Int
    )
}
