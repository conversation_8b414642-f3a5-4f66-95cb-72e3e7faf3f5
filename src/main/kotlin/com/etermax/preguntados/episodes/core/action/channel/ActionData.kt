package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.external.services.core.domain.api.profile.Language

data class ActionData(
    val playerId: Long,
    val coverUrl: String,
    val moderationLanguage: Language,
    val channelId: String? = null,
    val type: ChannelType?,
    private val _name: String,
    private val _description: String?,
    private val _website: String?
) {
    val name = _name.trim()

    val description = _description?.trim()

    val website = _website?.trim()
}
