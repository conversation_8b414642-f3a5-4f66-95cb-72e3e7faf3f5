package com.etermax.preguntados.episodes.core.infrastructure.notification.representation

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SimpleNotificationMessage(
    @SerialName("type") val type: String,
    @SerialName("game_id") val gameId: String,
    @SerialName("user_id") val userId: String,
    @SerialName("text") val text: String
)
