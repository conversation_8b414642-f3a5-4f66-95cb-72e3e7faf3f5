package com.etermax.preguntados.episodes.core.infrastructure.repository.rate.tables

import com.etermax.preguntados.episodes.core.domain.episode.Rate
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class RateItem(
    @get:DynamoDbAttribute("episode_id") var episodeId: String = "",
    @get:DynamoDbAttribute("owner_id") var ownerId: Long = 0,
    @get:DynamoDbAttribute("liked") var liked: Boolean? = null
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = buildPartitionKey(episodeId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = buildSortedKey(ownerId)

    fun to() = when (liked) {
        true -> Rate.Type.LIKE
        false -> Rate.Type.DISLIKE
        else -> null
    }

    companion object {
        private const val EPISODE_PREFIX = "E"
        private const val PLAYER_PREFIX = "P"
        private const val RATE_SK = "RATE"

        fun buildPartitionKey(episodeId: String) = "$EPISODE_PREFIX#$episodeId"

        fun buildSortedKey(playerId: Long) = "$RATE_SK#$PLAYER_PREFIX#$playerId"

        fun from(playerId: Long, episodeId: String, liked: Boolean) = RateItem(
            episodeId = episodeId,
            ownerId = playerId,
            liked = liked
        )
    }
}
