package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.search.data.EpisodeData
import kotlinx.coroutines.future.await
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder
import org.opensearch.client.opensearch.core.SearchRequest

class RecentSearch(
    private val client: OpenSearchAsyncClient,
    private val indexName: String,
    tokenizer: Tokenizer
) : Search, BaseSearch(tokenizer) {

    override suspend fun match(parameters: SearchParameters) = parameters.sort == SortEpisode.RECENT

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        with(parameters) {
            val request = buildRequest(parameters)
            val response = client.search(request, EpisodeData::class.java).await()
            return response.toEpisodes()
        }
    }

    fun buildRequest(parameters: SearchParameters): SearchRequest = with(parameters) {
        OpenSearchRequestFactory(indexName, offset, limit)
            .queryByName(name)
            .queryByCountry(country?.name)
            .filterByLanguage(language?.name)
            .filterByType(EpisodeType.PUBLIC)
            .filterByStatus(EpisodeStatus.PUBLISHED)
            .sortByDate()
            .excludeEmbedding()
            .build()
    }

    private fun OpenSearchRequestFactory.sortByDate() = this.also {
        sortBy(START_DATE_FIELD, SortOrder.Desc)
    }
}
