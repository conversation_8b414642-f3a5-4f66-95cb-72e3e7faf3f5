package com.etermax.preguntados.episodes.core.domain.episode.notification

import java.time.Duration

data class EventGroupsConfiguration(val groups: Map<EventType, EventGroupConfiguration>) {
    fun get(type: EventType): EventGroupConfiguration? {
        return groups[type]
    }
}

data class EventGroupConfiguration(
    val isEnabled: <PERSON><PERSON>an,
    val minCountToGroup: Int,
    val groupSizes: List<Int>,
    val groupDuration: Duration
)

enum class EventType {
    LIKE,
    PLAY;
}
