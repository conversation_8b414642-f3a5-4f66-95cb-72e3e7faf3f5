package com.etermax.preguntados.episodes.core.action

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedRequest
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedResponse
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedResponseSummary
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedService
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import kotlin.time.ExperimentalTime
import kotlin.time.measureTimedValue

@OptIn(ExperimentalTime::class)
class GetFeed(val service: FeedService, val summaryService: SummaryService) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(request: ActionData): FeedResponseSummary {
        try {
            val response = service.fetch(request.toFeedRequest())

            val (summary, summaryTime) = measureTimedValue {
                from(response)
            }
            logger.info("[LATENCY] composeSummary=${summaryTime}ms")

            return summary
        } catch (e: Throwable) {
            logger.error("Unexpected error occurred while retrieving feed ", e)
            throw e
        }
    }

    suspend fun from(response: FeedResponse) = with(response) {
        FeedResponseSummary(
            paginationToken = paginationToken,
            items = response.items.map { item ->
                when (item) {
                    is Episode -> summaryService.toEpisodeSummary(item)!!
                    is Channel -> summaryService.toChannelSummary(item)!!
                    else -> throw IllegalArgumentException("Unknown item type")
                }
            }
        )
    }

    private fun ActionData.toFeedRequest(): FeedRequest =
        FeedRequest(
            userId = userId,
            language = language,
            limit = limit,
            layout = layout,
            country = country,
            paginationToken = paginationToken,
            debug = debug
        )

    data class ActionData(
        val userId: Long?,
        val language: Language?,
        val country: Country?,
        val limit: Int,
        val layout: String?,
        val paginationToken: String?,
        val debug: Boolean
    )
}
