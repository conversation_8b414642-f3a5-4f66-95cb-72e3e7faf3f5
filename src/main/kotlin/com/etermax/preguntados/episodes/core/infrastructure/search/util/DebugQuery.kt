package com.etermax.preguntados.episodes.core.infrastructure.search.util

import jakarta.json.Json
import jakarta.json.stream.JsonGenerator
import org.opensearch.client.json.JsonpMapper
import org.opensearch.client.json.JsonpSerializable
import java.io.StringWriter

fun JsonpSerializable.debugQuery(mapper: JsonpMapper): String {
    return StringWriter().use { sw ->
        val generator: JsonGenerator = Json.createGenerator(sw)
        this.serialize(generator, mapper)
        generator.close()
        sw.toString()
    }
}
