package com.etermax.preguntados.episodes.core.infrastructure.search.feed.service

import com.etermax.preguntados.episodes.core.domain.search.feed.FeedRequest
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedResponse
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedService

class FeedServiceProxy(private val defaultFeedService: DefaultFeedService, private val anonymousFeedService: AnonymousFeedService) : FeedService {
    override suspend fun fetch(feedRequest: FeedRequest): FeedResponse {
        return if (feedRequest.userId != null) {
            defaultFeedService.fetch(feedRequest)
        } else {
            anonymousFeedService.fetch(feedRequest)
        }
    }
}
