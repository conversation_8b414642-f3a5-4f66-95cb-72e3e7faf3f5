package com.etermax.preguntados.episodes.core.domain.quality

import com.etermax.preguntados.episodes.core.domain.episode.Episode

interface QualityService {

    /**
     * Calculates the channel score based on the episodes' like rates and views.
     *
     * The new score is calculated only if:
     * 1. The episode has channelId set, and
     * 2. The episode like rate is above the minimum threshold (minimumRates), or
     * 3. The episode views are above the minimum threshold (minimumViews) and the episode like rate has been changed, or
     * 4. The episode has fallen below the rate or view thresholds
     *
     * otherwise, the score is not updated.
     *
     */
    suspend fun calculateChannelScore(episodeBeforeUpdate: Episode, episodeAfterUpdate: Episode)

    /**
     * Calculates the channel score based on new episodes added to channel.
     *
     * The new score is calculated only if:
     *  at least one episode
     *      the like rate is above the minimum threshold (minimumRates), or
     *      the views are above the minimum threshold (minimumViews)
     */
    suspend fun calculateChannelScore(channelId: String, episodes: List<Episode>)
}
