package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.search.BaseSearch.Companion.RATE_FIELD
import com.etermax.preguntados.episodes.core.infrastructure.search.OpenSearchRequestFactory
import com.etermax.preguntados.episodes.core.infrastructure.search.v2.OpenSearchEpisodeItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.future.await
import org.opensearch.client.json.JsonData
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder
import org.opensearch.client.opensearch.core.SearchResponse
import org.slf4j.LoggerFactory

class HighQualityRecentEpisodeSource(
    private val client: OpenSearchAsyncClient,
    private val episodesIndexName: String,
    private val requiredLikesForTopRated: Int,
    private val requiredViewsForTopRated: Int
) : Source<String> {
    private val logger = LoggerFactory.getLogger(RecentEpisodeSource::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<String> {
        with(request) {
            if (range.limit <= 0) {
                logger.debug("[FEED] Fetch called with non-positive limit: {}", range.limit)
                return emptyResponse(fetchCursor)
            }

            val offsetCursor = fetchCursor as? OffsetFetchCursor
            if (offsetCursor != null && offsetCursor.exhausted) {
                logger.debug("[FEED] Fetch called with exhausted cursor, returning empty response")
                return emptyResponse(fetchCursor)
            }

            val effectiveRange = applyFetchCursor(range, offsetCursor)
            logger.debug(
                "[FEED] Effective range applied: offset={}, limit={}",
                effectiveRange.offset,
                effectiveRange.limit
            )

            val osRequest = OpenSearchRequestFactory(episodesIndexName, effectiveRange.offset, effectiveRange.limit)
                .filterByLanguage(language)
                .filterByType(EpisodeType.PUBLIC)
                .filterByStatus(EpisodeStatus.PUBLISHED)
                .queryByCountry(country)
                .includeEpisodeIdOnly()
                .filterByLikesAndViews()
                .sortByRate()
                .build()

            val osResponse = try {
                client.search(osRequest, OpenSearchEpisodeItem::class.java).await()
            } catch (ex: Exception) {
                logger.error("[FEED] Error executing search request", ex)
                return emptyResponse()
            }

            val items = osResponse.extractEpisodeIds()
            logger.debug("[FEED] Search returned {} items", items.size)

            val exhausted = items.size < effectiveRange.limit
            return SourceResponse(items, OffsetFetchCursor(effectiveRange.offset + items.size, exhausted))
        }
    }

    private fun applyFetchCursor(range: OffsetLimit, offsetCursor: OffsetFetchCursor?): OffsetLimit {
        return if (offsetCursor != null) {
            OffsetLimit(offsetCursor.offset, range.limit).also {
                logger.debug("[FEED] Applied fetch cursor offset: {}", it.offset)
            }
        } else {
            range.also {
                logger.debug("[FEED] No fetch cursor provided, using original range offset: {}", it.offset)
            }
        }
    }

    private fun OpenSearchRequestFactory.sortByRate() = this.also {
        sortBy(RATE_FIELD, SortOrder.Desc)
    }

    private fun OpenSearchRequestFactory.includeEpisodeIdOnly() = this.also {
        includeSourceFields(EpisodeItemAttributes.EPISODE_ID)
    }

    private fun OpenSearchRequestFactory.filterByStatus(status: EpisodeStatus?) = this.also {
        status?.let { filterTerm(EpisodeItemAttributes.STATUS, it.name.lowercase()) }
    }

    private fun OpenSearchRequestFactory.filterByType(type: EpisodeType?) = this.also {
        type?.let { filterTerm(EpisodeItemAttributes.TYPE, it.name) }
    }

    private fun OpenSearchRequestFactory.filterByLanguage(language: Language?) = this.also {
        language?.let { filterTerm(EpisodeItemAttributes.LANGUAGE, it.name) }
    }

    private fun OpenSearchRequestFactory.queryByCountry(country: Country?) = this.also {
        country?.let { shouldTerm(EpisodeItemAttributes.COUNTRY, it.name) }
    }

    private fun SearchResponse<OpenSearchEpisodeItem>.extractEpisodeIds(): List<String> {
        return hits().hits().mapNotNull { it.source()?.episodeId }
    }

    private fun OpenSearchRequestFactory.filterByLikesAndViews() = this.also {
        val script = listOf(
            "doc['${EpisodeItemAttributes.LIKES}'].value + doc['${EpisodeItemAttributes.DISLIKES}'].value >= params.target",
            "(doc['${EpisodeItemAttributes.VIEWS}'].size() > 0 ? doc['${EpisodeItemAttributes.VIEWS}'].value : 0) >= params.minViews"
        ).joinToString(" && ")
        filterScript(
            script = script,
            params = mapOf(
                "target" to JsonData.of(requiredLikesForTopRated),
                "minViews" to JsonData.of(requiredViewsForTopRated)
            )
        )
    }
}
