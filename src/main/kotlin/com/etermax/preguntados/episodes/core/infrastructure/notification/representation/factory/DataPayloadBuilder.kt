package com.etermax.preguntados.episodes.core.infrastructure.notification.representation.factory

import com.etermax.preguntados.episodes.core.domain.notification.NotificationType
import java.io.UnsupportedEncodingException
import java.net.URLEncoder

class DataPayloadBuilder(private val encoding: String = "UTF-8") {
    private val payload: MutableMap<String, String> = mutableMapOf()
    private val flutterNewClassicFlag = "data.FNC"

    init {
        payload[flutterNewClassicFlag] = true.toString()
    }

    fun withType(type: NotificationType): DataPayloadBuilder {
        return apply { payload["data.TYPE"] = type.asString() }
    }

    fun withGameId(gameId: Long): DataPayloadBuilder {
        return apply { payload["data.GID"] = gameId.toString() }
    }

    fun withOpponentName(opponentName: String): DataPayloadBuilder {
        return apply { payload["data.OPP"] = encodeValue(opponentName) }
    }

    fun withSenderId(senderId: Long): DataPayloadBuilder {
        return apply { payload["data.U"] = senderId.toString() }
    }

    fun withShowSenderFacebookPicture(showSenderFacebookPicture: Boolean): DataPayloadBuilder {
        return apply { payload["data.SFP"] = showSenderFacebookPicture.toString() }
    }

    fun withSenderFacebookId(senderFacebookId: String): DataPayloadBuilder {
        return apply { if (senderFacebookId.isNotBlank()) payload["data.FID"] = senderFacebookId }
    }

    fun withHoursToExpire(hours: Int): DataPayloadBuilder {
        return apply { payload["data.HTST"] = hours.toString() }
    }

    fun build(): String {
        return payload.entries.joinToString("&")
    }

    private fun encodeValue(value: String): String {
        return try {
            URLEncoder.encode(value, encoding)
        } catch (e: UnsupportedEncodingException) {
            throw IllegalArgumentException("Error encoding arguments for notification", e)
        }
    }
}
