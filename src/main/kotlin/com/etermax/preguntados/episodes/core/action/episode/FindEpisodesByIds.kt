package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary

class FindEpisodesByIds(
    private val episodesRepository: EpisodeRepository,
    private val summaryService: SummaryService
) {

    suspend operator fun invoke(actionData: ActionData): List<EpisodeSummary> {
        val episodes = episodesRepository.findByIds(actionData.episodesId.distinct())
        val t = episodes.groupBy { it }
        println(t.map { it.value.size > 1 })
        return episodes.mapNotNull {
            summaryService.toEpisodeSummary(it)
        }
    }

    data class ActionData(
        val episodesId: List<String>
    )
}
