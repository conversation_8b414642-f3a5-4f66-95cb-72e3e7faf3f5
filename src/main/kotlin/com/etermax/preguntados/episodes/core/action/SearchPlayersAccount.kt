package com.etermax.preguntados.episodes.core.action

import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountParams
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountService

class SearchPlayersAccount(
    private val playerAccountService: PlayerAccountService
) {

    suspend operator fun invoke(actionData: ActionData) =
        playerAccountService.search(actionData.toPlayerAccountParams())

    data class ActionData(
        val playerId: Long,
        val query: String,
        val amount: Int,
        val skip: Int,
        val skipRestricted: Boolean?,
        val fields: String?,
        val token: String?
    ) {

        fun toPlayerAccountParams() = PlayerAccountParams(
            playerId = playerId,
            query = query,
            amount = amount,
            skip = skip,
            skipRestricted = skipRestricted,
            fields = fields,
            token = token
        )
    }
}
