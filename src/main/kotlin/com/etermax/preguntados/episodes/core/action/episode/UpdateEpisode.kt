package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.domain.episode.*
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus.PUBLISHED
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionSingleExecutor
import com.etermax.preguntados.episodes.core.domain.exception.*
import com.etermax.preguntados.episodes.core.domain.moderation.ModerationService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.time.Clock

class UpdateEpisode(
    private val episodeRepository: EpisodeRepository,
    private val profileService: ProfileService,
    private val moderationService: ModerationService,
    private val clock: Clock,
    private val postActionExecutor: UpdateChannelEpisodePostActionSingleExecutor,
    private val episodeCreationNotifier: EpisodeCreationNotifier
) {

    suspend operator fun invoke(actionData: ActionData): EpisodeSummary {
        return with(actionData) {
            val updatedEpisode = episodeRepository.findById(episodeId)?.let { currentEpisode ->
                if (hasNoChangesComparedTo(currentEpisode)) return@let currentEpisode

                validate(currentEpisode)

                currentEpisode.copy(
                    name = name ?: currentEpisode.name,
                    cover = cover ?: currentEpisode.cover,
                    banner = banner ?: currentEpisode.banner,
                    contents = contents ?: currentEpisode.contents,
                    status = status ?: currentEpisode.status,
                    type = type ?: currentEpisode.type,
                    startDate = getStartDate(currentEpisode) ?: currentEpisode.startDate,
                    channelId = this.getChannelIdToUpdate(currentEpisode) // TODO: Validate channel exists
                ).let { updatedEpisode ->
                    episodeRepository.updateItem(updatedEpisode)
                    executePostActions(currentEpisode, actionData)
                    notifyIfEpisodeIsBeingPublished(currentEpisode, actionData)
                    updatedEpisode
                }
            } ?: throw EpisodeNotFoundException(episodeId)

            val profile = profileService.find(playerId)
            EpisodeSummary.from(updatedEpisode, profile)
        }
    }

    private suspend fun notifyIfEpisodeIsBeingPublished(currentEpisode: Episode, actionData: ActionData) {
        if (episodeIsBeingPublished(actionData, currentEpisode)) {
            episodeCreationNotifier.notifyEpisodeCreationToPlayers(currentEpisode)
        }
    }

    private fun episodeIsBeingPublished(actionData: ActionData, currentEpisode: Episode) =
        actionData.status != null && actionData.status == PUBLISHED && currentEpisode.status != PUBLISHED

    private fun ActionData.getStartDate(currentEpisode: Episode) =
        clock.now().takeIf { currentEpisode.status != PUBLISHED && status == PUBLISHED }

    private fun ActionData.getChannelIdToUpdate(currentEpisode: Episode) =
        if (channelId?.isBlank() == true) null
        else channelId ?: currentEpisode.channelId

    private fun ActionData.hasNoChangesComparedTo(episode: Episode) =
        name.isNullOrSameAs(episode.name) &&
            cover.isNullOrSameAs(episode.cover) &&
            banner.isNullOrSameAs(episode.banner) &&
            contents.isNullOrSameAs(episode.contents) &&
            status.isNullOrSameAs(episode.status) &&
            channelId.isNullOrSameAs(episode.channelId)

    private fun <T> T?.isNullOrSameAs(oldValue: T) = this == null || this == oldValue

    private suspend fun ActionData.validate(episode: Episode) {
        if (playerId != episode.ownerId) {
            throw InvalidUpdateNotOwnEpisodeException(playerId, episode.ownerId, episode.id)
        }

        if (name != null) {
            if (name.trim().isEmpty()) {
                throw EpisodeNameEmptyException(name)
            }

            if (!moderationService.isTextAllowed(playerId, episode.language, name)) {
                throw EpisodeNameNotAllowedException(name)
            }
        }

        if (contents?.isEmpty() == true) {
            throw EpisodeEmptyContentException(episodeId)
        }
    }

    private suspend fun executePostActions(oldEpisode: Episode, data: ActionData) {
        val updateData = UpdateChannelEpisodePostActionData(status = data.status, channelId = data.channelId)
        postActionExecutor.execute(oldEpisode, updateData)
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String,
        val name: String?,
        val cover: String?,
        val banner: String?,
        val contents: List<String>?,
        val status: EpisodeStatus?,
        val type: EpisodeType?,
        private val _channelId: String?
    ) {
        val channelId = _channelId?.trim()
    }
}
