package com.etermax.preguntados.episodes.core.domain.episode

import com.etermax.preguntados.episodes.core.domain.episode.notification.EpisodeNotificationService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import kotlinx.coroutines.*

class EpisodeCreationNotifier(
    private val episodeNotificationService: EpisodeNotificationService,
    private val episodePlayersByOwnerRepository: EpisodePlayersByOwnerRepository,
    private val profileService: ProfileService,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) {
    suspend fun notifyEpisodeCreationToPlayers(episode: Episode) {
        withContext(dispatcher + SupervisorJob()) {
            launch {
                val players = episodePlayersByOwnerRepository.get(episode.ownerId)
                if (!players.any()) return@launch

                val ownerProfile = profileService.find(episode.ownerId)
                episodeNotificationService.notifyPlayersAtEpisodeCreation(players, ownerProfile, episode.id)
            }
        }
    }
}
