package com.etermax.preguntados.episodes.core.domain.channel.episode

import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostAction
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import org.slf4j.LoggerFactory

class UpdateChannelEpisodeProcessDraftPostAction(
    private val repository: ChannelUnpublishedEpisodesRepository,
    private val onError: (() -> Unit)? = null
) : UpdateChannelEpisodePostAction {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun applies(oldEpisode: Episode, updateData: UpdateChannelEpisodePostActionData): Boolean {
        if (oldEpisode.status != EpisodeStatus.PENDING) return false
        if (updateData.status != EpisodeStatus.DRAFT) return false
        if (!oldEpisode.hasChannel) return false

        return true
    }

    override suspend fun execute(
        oldEpisode: Episode,
        updateData: UpdateChannelEpisodePostActionData,
        alreadyCheckIfApplies: Boolean
    ) {
        runCatching {
            if (!alreadyCheckIfApplies && !applies(oldEpisode, updateData)) return

            val item = ChannelUnpublishedEpisode(oldEpisode.channelId!!, oldEpisode.id)
            repository.delete(item)
        }.onFailure {
            logger.error("Fail to process DRAFT episode '${oldEpisode.id}'", it)
            onError?.invoke()
        }
    }
}
