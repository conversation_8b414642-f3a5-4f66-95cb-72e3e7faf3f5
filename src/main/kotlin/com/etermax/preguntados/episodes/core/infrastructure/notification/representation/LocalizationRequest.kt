package com.etermax.preguntados.episodes.core.infrastructure.notification.representation

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LocalizationRequest(
    @SerialName("title") val title: String? = null,
    @SerialName("titleArgs") val titleArgs: List<String>? = null,
    @SerialName("body") val body: String? = null,
    @SerialName("bodyArgs") val bodyArgs: List<String>? = null
)
