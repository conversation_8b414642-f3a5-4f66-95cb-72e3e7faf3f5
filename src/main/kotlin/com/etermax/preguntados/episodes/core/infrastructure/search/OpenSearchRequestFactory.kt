package com.etermax.preguntados.episodes.core.infrastructure.search

import org.opensearch.client.json.JsonData
import org.opensearch.client.opensearch._types.FieldValue
import org.opensearch.client.opensearch._types.Script
import org.opensearch.client.opensearch._types.SortOrder
import org.opensearch.client.opensearch._types.Time
import org.opensearch.client.opensearch._types.aggregations.Aggregation
import org.opensearch.client.opensearch._types.query_dsl.BoolQuery
import org.opensearch.client.opensearch._types.query_dsl.KnnQuery
import org.opensearch.client.opensearch._types.query_dsl.Query
import org.opensearch.client.opensearch._types.query_dsl.RangeQuery
import org.opensearch.client.opensearch._types.query_dsl.TermQuery
import org.opensearch.client.opensearch.core.SearchRequest

class OpenSearchRequestFactory(
    index: String,
    offset: Int,
    limit: Int
) {

    private val request = SearchRequest.Builder()
        .index(index)
        .from(offset)
        .size(limit)

    private val mustQueries = mutableListOf<Query>()
    private val mustNotQueries = mutableListOf<Query>()
    private val filterQueries = mutableListOf<Query>()
    private val shouldQueries = mutableListOf<Query>()

    private var getJustIds = false
    private val sourceExcludes = mutableListOf<String>()
    private val sourceIncludes = mutableListOf<String>()
    private var script: Script? = null

    fun build(): SearchRequest {
        val query = request.query { queryBuilder ->
            if (script != null) {
                queryBuilder.scriptScore { scriptScoreQuery ->
                    scriptScoreQuery
                        .query { q -> q.bool { it.buildFilters() } }
                        .script(script)
                }
            } else {
                queryBuilder.bool { it.buildFilters() }
            }
        }

        query.source { sourceBuilder ->
            if (getJustIds) {
                sourceBuilder.fetch(false)
            } else {
                sourceBuilder.filter {
                    it.excludes(sourceExcludes)
                    it.includes(sourceIncludes)
                }
            }
        }

        return request.build()
    }

    private fun BoolQuery.Builder.buildFilters() = this
        .filter(filterQueries)
        .must(mustQueries)
        .should(shouldQueries)
        .mustNot(mustNotQueries)

    fun addScript(source: String): OpenSearchRequestFactory {
        script = Script.Builder().inline { inline ->
            inline.source(source)
        }.build()
        return this
    }

    fun mustPrefixesFuzziness(field: String, values: List<String>): OpenSearchRequestFactory {
        val combinedQuery = values.joinToString(" AND ") { value ->
            "$field:($value* OR $value~1)"
        }

        mustQueries += Query.Builder().queryString { queryString ->
            queryString.query(combinedQuery)
        }.build()

        return this
    }

    fun mustNotIds(values: List<String>): OpenSearchRequestFactory {
        mustNotQueries += Query.Builder().ids {
            it.values(values)
        }.build()
        return this
    }

    fun mustNotExist(field: String): OpenSearchRequestFactory {
        mustNotQueries += Query.Builder().exists { exists ->
            exists.field(field)
        }.build()
        return this
    }

    fun mustTerms(fieldName: String, values: List<Long>): OpenSearchRequestFactory {
        mustQueries += Query.Builder().terms { termsQuery ->
            termsQuery.field(fieldName).terms { terms ->
                terms.value(values.map { FieldValue.of(it) })
            }
        }.build()
        return this
    }

    fun shouldKnn(field: String, value: FloatArray?, k: Int?): OpenSearchRequestFactory {
        val knn = KnnQuery.Builder()
            .field(field)
            .vector(value)
            .k(k)

        shouldQueries += knn.build().toQuery()
        return this
    }

    fun shouldMatch(field: String, value: String): OpenSearchRequestFactory {
        shouldQueries += Query.Builder().match { matchQuery ->
            matchQuery.field(field).query { it.stringValue(value) }.boost(3.0f)
        }.build()
        return this
    }

    fun shouldTerm(fieldName: String, value: String, boost: Float = 1.0f): OpenSearchRequestFactory {
        shouldQueries += Query.Builder().term { termQuery ->
            termQuery
                .field(fieldName)
                .boost(boost)
                .value { it.stringValue(value) }
        }.build()
        return this
    }

    fun filterTerm(fieldName: String, value: Long): OpenSearchRequestFactory {
        val termQuery = TermQuery.Builder().field(fieldName).caseInsensitive(true).value { valueFilter ->
            valueFilter.longValue(value)
        }

        filterQueries += Query.Builder().term(termQuery.build()).build()
        return this
    }

    fun filterTerm(fieldName: String, value: String): OpenSearchRequestFactory {
        val termQuery = TermQuery.Builder().field(fieldName).caseInsensitive(true).value { valueFilter ->
            valueFilter.stringValue(value)
        }

        filterQueries += Query.Builder().term(termQuery.build()).build()
        return this
    }

    fun filterScript(script: String, params: Map<String, JsonData>): OpenSearchRequestFactory {
        filterQueries += Query.Builder().script { queryBuilder ->
            queryBuilder.script { inlineScript ->
                inlineScript.inline { source ->
                    source.source(script).lang(PAINLESS).params(params)
                }
            }
        }.build()
        return this
    }

    fun filterRange(
        fieldName: String,
        predicate: (RangeQuery.Builder) -> RangeQuery.Builder
    ): OpenSearchRequestFactory {
        val rangeQuery = RangeQuery.Builder().field(fieldName)
        val rangeQueryBuilder = predicate(rangeQuery).build()
        filterQueries += Query.Builder().range(rangeQueryBuilder).build()
        return this
    }

    fun filterGreaterThanOrEqual(fieldName: String, value: Number): OpenSearchRequestFactory {
        return filterRange(fieldName) { rangeQuery ->
            rangeQuery.gte(JsonData.of(value))
        }
    }

    fun sortBy(fieldName: String, order: SortOrder = SortOrder.Asc): OpenSearchRequestFactory {
        request.sort {
            it.field { f -> f.field(fieldName).order(order).missing(FieldValue.of(FIRST)) }
        }

        return this
    }

    fun useScroll(time: String): OpenSearchRequestFactory {
        request.scroll(Time.Builder().time(time).build())
        return this
    }

    fun addAggregation(key: String, aggregation: () -> Aggregation): OpenSearchRequestFactory {
        request.aggregations(key, aggregation())
        return this
    }

    fun excludeSourceFields(vararg fields: String): OpenSearchRequestFactory {
        this.sourceExcludes.addAll(fields)
        return this
    }

    fun includeSourceFields(vararg fields: String): OpenSearchRequestFactory {
        this.sourceIncludes.addAll(fields)
        return this
    }

    fun excludeSources(): OpenSearchRequestFactory {
        this.getJustIds = true
        return this
    }

    companion object {
        private const val PAINLESS = "painless"
        private const val FIRST = "_first"
    }
}
