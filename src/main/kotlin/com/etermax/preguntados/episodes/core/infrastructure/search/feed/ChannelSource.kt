package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.search.OpenSearchRequestFactory
import com.etermax.preguntados.episodes.core.infrastructure.search.v2.OpenSearchChannelItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.future.await
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder
import org.opensearch.client.opensearch.core.SearchRequest
import org.opensearch.client.opensearch.core.SearchResponse
import org.slf4j.LoggerFactory

abstract class ChannelSource(
    private val client: OpenSearchAsyncClient
) : Source<String> {

    abstract suspend fun generateRequest(request: SourceRequest, offsetLimit: OffsetLimit): SearchRequest

    private val logger = LoggerFactory.getLogger(ChannelSource::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<String> {
        with(request) {
            if (range.limit <= 0) {
                logger.debug("[FEED] Fetch called with non-positive limit: {}", range.limit)
                return emptyResponse(fetchCursor)
            }

            val offsetCursor = fetchCursor as? OffsetFetchCursor
            if (offsetCursor != null && offsetCursor.exhausted) {
                logger.debug("[FEED] Fetch called with exhausted cursor, returning empty response")
                return emptyResponse(fetchCursor)
            }

            val effectiveRange = applyFetchCursor(range, offsetCursor)
            logger.debug(
                "[FEED] Effective fetch range - offset: {}, limit: {}",
                effectiveRange.offset,
                effectiveRange.limit
            )

            val osRequest = generateRequest(request, effectiveRange)

            logger.debug(
                "[FEED] Constructed OpenSearch request for channels with filters - language: {}, type: {}, minContent: {}",
                language?.name,
                ChannelType.PUBLIC,
                2
            )

            val osResponse = try {
                client.search(osRequest, OpenSearchChannelItem::class.java).await()
            } catch (ex: Exception) {
                logger.error("[FEED] Error executing OpenSearch query", ex)
                return emptyResponse()
            }

            val items = osResponse.ids()
            logger.info(
                "[FEED] OpenSearch returned {} channels for offset {} with limit {}",
                items.size,
                effectiveRange.offset,
                effectiveRange.limit
            )

            val exhausted = items.size < effectiveRange.limit
            return SourceResponse(items, OffsetFetchCursor(effectiveRange.offset + items.size, exhausted))
        }
    }

    private fun applyFetchCursor(range: OffsetLimit, offsetCursor: OffsetFetchCursor?): OffsetLimit {
        return if (offsetCursor != null) {
            logger.debug("[FEED] Applying fetch cursor with offset: {}", offsetCursor.offset)
            OffsetLimit(offsetCursor.offset, range.limit)
        } else {
            logger.debug("[FEED] No fetch cursor provided; using original offset: {}", range.offset)
            range
        }
    }

    protected fun OpenSearchRequestFactory.filterByLanguage(language: Language?) = this.also {
        language?.let { filterTerm(ChannelItemAttributes.LANGUAGE, it.name) }
    }

    protected fun OpenSearchRequestFactory.filterByType(type: ChannelType?) = this.also {
        type?.let { filterTerm(ChannelItemAttributes.TYPE, it.name) }
    }

    protected fun OpenSearchRequestFactory.filterByMinContent(count: Int) = this.also {
        filterGreaterThanOrEqual(ChannelItemAttributes.EPISODES_COUNT, count)
    }

    protected fun OpenSearchRequestFactory.includeChannelIdOnly() = this.also {
        includeSourceFields(ChannelItemAttributes.CHANNEL_ID)
    }

    protected fun OpenSearchRequestFactory.sortByDate() = this.also {
        sortBy(ChannelItemAttributes.LAST_MODIFICATION_DATE, SortOrder.Desc)
    }

    protected fun OpenSearchRequestFactory.sortByScore() = this.also {
        sortBy(ChannelItemAttributes.SCORE, SortOrder.Desc)
    }

    protected fun OpenSearchRequestFactory.sortByQuality() = this.also {
        sortBy(ChannelItemAttributes.QUALITY, SortOrder.Desc)
    }

    private fun SearchResponse<OpenSearchChannelItem>.ids(): List<String> {
        return hits().hits().mapNotNull { it.source()?.channelId }
    }
}
