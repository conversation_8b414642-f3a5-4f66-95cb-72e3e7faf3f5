package com.etermax.preguntados.episodes.core.infrastructure.search.filter

import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class DynamoDBFilterDataItem(
    @get:DynamoDbAttribute("scope") var scope: String = "",
    @get:DynamoDbAttribute("data") var data: SdkBytes = SdkBytes.fromByteArray(ByteArray(0))
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = buildPartitionKey(scope)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = buildSortedKey()

    companion object {
        private const val FILTER_DATA_PREFIX = "F#"
        private const val FILTER_DATA_SK = "FILTER_DATA"

        fun buildPartitionKey(scope: String) = "$FILTER_DATA_PREFIX$scope"

        fun buildSortedKey() = FILTER_DATA_SK
    }
}
