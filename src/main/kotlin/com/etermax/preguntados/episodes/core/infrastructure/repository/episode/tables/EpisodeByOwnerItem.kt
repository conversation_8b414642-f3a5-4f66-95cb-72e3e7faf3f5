package com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables

import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey

@DynamoDbBean
class EpisodeByOwnerItem(
    @get:DynamoDbSecondaryPartitionKey(indexNames = [EpisodesIndexes.BY_OWNER])
    @get:DynamoDbAttribute(EpisodeItemAttributes.OWNER_ID)
    var ownerId: Long = 0,
    @get:DynamoDbAttribute(EpisodeItemAttributes.PK) var episodeId: String = ""
)
