package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items

import com.etermax.preguntados.episodes.core.domain.channel.ChannelReduced
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey

@DynamoDbBean
class ByOwnerChannelReducedItem(
    @get:DynamoDbSecondaryPartitionKey(indexNames = [ChannelIndexes.BY_OWNER_AND_LAST_MODIFICATION])
    @get:DynamoDbAttribute(ChannelItemAttributes.OWNER_ID)
    var ownerId: Long = 0,

    @get:DynamoDbSecondarySortKey(indexNames = [ChannelIndexes.BY_OWNER_AND_LAST_MODIFICATION])
    @get:DynamoDbAttribute(ChannelItemAttributes.LAST_MODIFICATION_DATE)
    var lastModificationDate: Long = 0,

    @get:DynamoDbAttribute(ChannelItemAttributes.COVER_URL)
    var coverUrl: String = "",

    @get:DynamoDbAttribute(ChannelItemAttributes.NAME)
    var name: String = "",

    @get:DynamoDbAttribute(ChannelItemAttributes.CHANNEL_ID)
    var channelId: String = ""
) {
    fun toDomain(): ChannelReduced {
        return ChannelReduced(
            id = channelId,
            coverUrl = coverUrl,
            name = name
        )
    }
}
