package com.etermax.preguntados.episodes.core.infrastructure.search.v2

import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class OpenSearchChannelItem {
    @JsonProperty(ChannelItemAttributes.CHANNEL_ID)
    val channelId: String? = null
}
