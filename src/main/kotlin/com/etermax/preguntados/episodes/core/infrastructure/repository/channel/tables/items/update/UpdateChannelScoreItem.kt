package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update

import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class UpdateChannelScoreItem(
    @get:DynamoDbAttribute(ChannelItemAttributes.CHANNEL_ID) var channelId: String = "",
    @get:DynamoDbAttribute(ChannelItemAttributes.SCORE) var score: Int? = null
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = ChannelItem.buildPartitionKey(channelId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = ChannelItem.CHANNEL_SK

    companion object {
        fun buildPartitionKey(channelId: String) = ChannelItem.buildPartitionKey(channelId)
    }
}
