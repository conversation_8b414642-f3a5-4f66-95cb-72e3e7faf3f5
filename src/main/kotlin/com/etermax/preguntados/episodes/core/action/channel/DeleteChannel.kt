package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelDeleteRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.PlayerNotOwnChannelException

class DeleteChannel(
    private val channelRepository: ChannelRepository,
    private val deleteRepository: ChannelDeleteRepository,
    private val unpublishedEpisodesRepository: ChannelUnpublishedEpisodesRepository
) {
    suspend operator fun invoke(userId: Long, channelId: String) {
        val channel = findChannel(userId, channelId)
        deleteRepository.delete(channel.id)
        unpublishedEpisodesRepository.deleteAll(channelId)
    }

    private suspend fun findChannel(userId: Long, channelId: String): Channel {
        val channel = channelRepository.findById(channelId) ?: throw ChannelNotFoundException(channelId)

        if (channel.ownerId != userId) {
            throw PlayerNotOwnChannelException(userId, channelId)
        }

        return channel
    }
}
