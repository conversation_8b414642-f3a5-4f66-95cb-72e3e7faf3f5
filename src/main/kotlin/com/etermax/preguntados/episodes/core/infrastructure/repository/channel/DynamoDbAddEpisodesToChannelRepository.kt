package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.AddEpisodesToChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.TransactionBuilder
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.AssignChannelIdToEpisodeItem
import kotlinx.coroutines.future.await
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.model.TransactPutItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactUpdateItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest

class DynamoDbAddEpisodesToChannelRepository(
    private val client: DynamoDbEnhancedAsyncClient,
    private val episodeTable: DynamoDbAsyncTable<AssignChannelIdToEpisodeItem>,
    private val channelTable: DynamoDbAsyncTable<ChannelItem>,
    private val channelEpisodesTable: DynamoDbAsyncTable<ChannelEpisodeItem>
) : AddEpisodesToChannelRepository {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun add(channel: Channel, channelEpisodes: Set<ChannelEpisode>) {
        val channelId = channel.id
        var alreadyAddedChannel = false
        val chunkedChannelEpisodes = channelEpisodes.chunked(MAX_ITEMS_PER_TRANSACTION / 2)

        chunkedChannelEpisodes.forEachIndexed { index, chunk ->
            logger.info("ChannelId='$channelId' - Adding chunk='$index' with size='${chunk.size}'")
            val transactionBuilder = TransactWriteItemsEnhancedRequest.builder()

            if (!alreadyAddedChannel) {
                transactionBuilder.updateChannel(channel)
                alreadyAddedChannel = true
            }

            chunk.forEach { aChannelEpisode ->
                transactionBuilder.assignChannelIdToEpisode(aChannelEpisode)
                transactionBuilder.addChannelEpisode(aChannelEpisode)
            }

            client.transactWriteItems(transactionBuilder.build()).await()

            logger.info("ChannelId='$channelId' - Finish adding chunk='$index' with size='${chunk.size}'")
        }
    }

    private fun TransactionBuilder.addChannelEpisode(
        aChannelEpisode: ChannelEpisode
    ) {
        val channelEpisodeItem = ChannelEpisodeItem.from(aChannelEpisode)
        this.addPutItem(
            channelEpisodesTable,
            TransactPutItemEnhancedRequest.builder(ChannelEpisodeItem::class.java).item(channelEpisodeItem)
                .build()
        )
    }

    private fun TransactionBuilder.assignChannelIdToEpisode(
        aChannelEpisode: ChannelEpisode
    ) {
        val assignChannelIdToEpisodeItem = AssignChannelIdToEpisodeItem.from(aChannelEpisode)
        this.addUpdateItem(
            episodeTable,
            TransactUpdateItemEnhancedRequest.builder(AssignChannelIdToEpisodeItem::class.java)
                .item(assignChannelIdToEpisodeItem).build()
        )
    }

    private fun TransactionBuilder.updateChannel(channel: Channel) {
        val item = ChannelItem.from(channel)

        this.addPutItem(
            channelTable,
            TransactPutItemEnhancedRequest.builder(ChannelItem::class.java).item(item).build()
        )
    }

    private companion object {
        const val MAX_ITEMS_PER_TRANSACTION = 24
    }
}
