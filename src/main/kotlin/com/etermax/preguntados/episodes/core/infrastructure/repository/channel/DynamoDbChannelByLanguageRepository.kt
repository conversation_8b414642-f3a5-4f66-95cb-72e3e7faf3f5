package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.ChannelReduced
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelByLanguageRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelByLanguageFilters
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ByOwnerChannelReducedItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.reactive.asFlow
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Expression
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

class DynamoDbChannelByLanguageRepository(
    dynamoDbClient: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<ChannelItem>,
    private val byOwnerAndLastModificationIndex: DynamoDbAsyncIndex<ByOwnerChannelReducedItem>
) : ChannelByLanguageRepository, DynamoDBRepository<ChannelItem>(dynamoDbClient, table) {

    override suspend fun search(
        filters: ChannelByLanguageFilters,
        pagination: PaginationFilter
    ): PaginatedItems<ChannelReduced> {
        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(filters.ownerId)
        }

        val requestBuilder = QueryEnhancedRequest
            .builder()
            .queryConditional(queryConditional)
            .limit(pagination.itemsSize)
            .filterExpression(filters.toExpression())
            .scanIndexForward(false)

        pagination.lastEvaluatedKey?.takeIf { it.isNotBlank() }?.let { lastKey ->
            val skConditional = QueryConditional.sortLessThan {
                it.partitionValue(filters.ownerId)
                    .sortValue(lastKey.toLong())
            }
            requestBuilder.queryConditional(skConditional)
        }

        val request = requestBuilder.build()

        val allItems = mutableListOf<ChannelReduced>()
        var lastEvaluatedKey: String? = null
        var currentRequest = request

        while (allItems.size < pagination.itemsSize) {
            val page = byOwnerAndLastModificationIndex
                .query(currentRequest)
                .asFlow()
                .firstOrNull()

            if (page == null) {
                lastEvaluatedKey = null
                break
            }

            val items = page.items().mapNotNull { it.toDomain() }
            allItems.addAll(items)

            lastEvaluatedKey = page.lastEvaluatedKey()?.get(ChannelItemAttributes.LAST_MODIFICATION_DATE)?.n()

            if (lastEvaluatedKey == null) {
                break
            }

            currentRequest = QueryEnhancedRequest
                .builder()
                .queryConditional(
                    QueryConditional.sortLessThan {
                        it.partitionValue(filters.ownerId)
                            .sortValue(lastEvaluatedKey.toLong())
                    }
                )
                .limit(pagination.itemsSize - allItems.size)
                .filterExpression(filters.toExpression())
                .scanIndexForward(false)
                .build()
        }

        return PaginatedItems(lastEvaluatedKey, allItems)
    }

    private fun ChannelByLanguageFilters.toExpression(): Expression {
        var expression = "#n_language = :v_language"
        val expressionNames = mapOf(
            "#n_language" to "language"
        )
        val expressionValues = mapOf(
            ":v_language" to AttributeValue.builder().s(language.name.uppercase()).build()
        )

        if (this.includeWithNoLanguage) {
            expression += " OR attribute_not_exists(#n_language)"
        }

        return Expression.builder()
            .expression(expression)
            .expressionNames(expressionNames)
            .expressionValues(expressionValues)
            .build()
    }
}
