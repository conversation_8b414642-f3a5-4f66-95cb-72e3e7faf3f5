package com.etermax.preguntados.episodes.core.domain.channel.service

import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class ChannelUnpublishedEpisodesService(private val repository: ChannelUnpublishedEpisodesRepository) {

    private val logger: Logger by lazy { LoggerFactory.getLogger(this::class.java) }

    // TODO: Add tests
    suspend fun count(channelId: String): Int {
        runCatching {
            return repository.count(channelId)
        }.getOrElse {
            logger.error("Fail to get unpublished episodes count for channel='$channelId'. Returning 0.", it)
            return 0
        }
    }
}
