package com.etermax.preguntados.episodes.core.domain

import com.etermax.preguntados.episodes.core.domain.challenge.*
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlin.reflect.KProperty1

class SummaryService(
    private val profileService: ProfileService,
    private val unpublishedEpisodesService: ChannelUnpublishedEpisodesService
) {

    suspend fun toEpisodeSummary(episodeSupplier: Episode?) =
        summarize(Episode::ownerId, EpisodeSummary::from, episodeSupplier)

    suspend fun toChallengeSummary(challengeSupplier: ChallengeDetails?): ChallengeSummary? {
        return challengeSupplier?.let { challenge ->
            ChallengeSummary.from(
                challenge.challenge,
                challenge.ranking,
                ChallengePlayersSummary(
                    challenge.players.map {
                        ChallengePlayerSummary.from(
                            it,
                            profileService.find(it.playerId)
                        )
                    }
                ),
                toEpisodeSummary(challenge.episode)!!
            )
        }
    }

    suspend fun toChannelSummary(channelSupplier: Channel?) =
        summarizeChannel(Channel::id, Channel::ownerId, ChannelSummary::from, channelSupplier)

    suspend fun toEpisodesSummary(episodesSupplier: List<Episode>?) =
        summarizeList(Episode::ownerId, EpisodeSummary::from, episodesSupplier)

    suspend fun toChannelsSummary(channelsSupplier: List<Channel>?) =
        summarizeChannelList(Channel::id, Channel::ownerId, ChannelSummary::from, channelsSupplier)

    private suspend fun <R, T> summarize(
        playerIdExtractor: KProperty1<R, Long>,
        summaryFactory: (R, Profile?) -> T,
        supplier: R?
    ): T? {
        val item = supplier ?: return null
        val profile = profileService.find(playerIdExtractor.get(item))
        return summaryFactory(item, profile)
    }

    private suspend fun <R, T> summarizeList(
        playerIdExtractor: KProperty1<R, Long>,
        summaryFactory: (R, Profile?) -> T,
        supplier: List<R>?
    ): List<T> {
        val items = supplier ?: return emptyList()

        val playersId = items.map { playerIdExtractor.get(it) }.distinct()
        val profiles = profileService.findMany(playersId).associateBy { it.playerId }
        return items.map { summaryFactory(it, profiles[playerIdExtractor.get(it)]) }
    }

    private suspend fun <R, T> summarizeChannel(
        channelIdIdExtractor: KProperty1<R, String>,
        playerIdExtractor: KProperty1<R, Long>,
        summaryFactory: (R, Profile?, Int) -> T,
        supplier: R?
    ): T? = coroutineScope {
        val item = supplier ?: return@coroutineScope null
        val channelId = channelIdIdExtractor.get(item)
        val profileTask = async { profileService.find(playerIdExtractor.get(item)) }
        val unpublishedEpisodes = async { unpublishedEpisodesService.count(channelId) }
        summaryFactory(item, profileTask.await(), unpublishedEpisodes.await())
    }

    private suspend fun <R, T> summarizeChannelList(
        channelIdIdExtractor: KProperty1<R, String>,
        playerIdExtractor: KProperty1<R, Long>,
        summaryFactory: (R, Profile?, Int) -> T,
        supplier: List<R>?
    ): List<T> = coroutineScope {
        val items = supplier ?: return@coroutineScope emptyList()

        val playersId = items.map { playerIdExtractor.get(it) }.distinct()
        val profiles = profileService.findMany(playersId).associateBy { it.playerId }
        val tasks = items.map {
            async {
                val channelId = channelIdIdExtractor.get(it)
                val unpublishedEpisodes = unpublishedEpisodesService.count(channelId)
                summaryFactory(it, profiles[playerIdExtractor.get(it)], unpublishedEpisodes)
            }
        }
        tasks.awaitAll()
    }

    suspend fun toChallengePlayersSummary(players: List<ChallengePlayer>): ChallengePlayersSummary {
        return ChallengePlayersSummary(
            players.map {
                ChallengePlayerSummary(
                    profileService.find(it.playerId),
                    it.status
                )
            }
        )
    }
}
