package com.etermax.preguntados.episodes.core.infrastructure.notification.representation

import com.etermax.preguntados.episodes.core.domain.notification.Notification
import com.etermax.preguntados.episodes.core.infrastructure.notification.representation.factory.NotificationRequestFactory
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class NotificationPayloadRequest(
    @SerialName("title") val title: String? = null,
    @SerialName("body") val body: String? = null,
    @SerialName("data") val data: Map<String, String>? = null,
    @SerialName("dataAsString") val dataAsString: String? = null,
    @SerialName("badge") val badge: Int? = null,
    @SerialName("localization") val localization: LocalizationRequest
) {
    companion object {
        fun from(notification: Notification) = with(notification) {
            NotificationRequestFactory.create(this)
        }
    }
}
