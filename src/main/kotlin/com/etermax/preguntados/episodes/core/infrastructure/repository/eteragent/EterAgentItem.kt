package com.etermax.preguntados.episodes.core.infrastructure.repository.eteragent

import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey

@DynamoDbBean
class EterAgentItem(
    @get:DynamoDbAttribute("player_id") var playerId: String = "",
    @get:DynamoDbAttribute("eteragent") var eteragent: String = ""
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = playerId
}
