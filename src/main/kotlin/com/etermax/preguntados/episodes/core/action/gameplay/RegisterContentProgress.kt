package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.quality.QualityService
import org.slf4j.LoggerFactory

class RegisterContentProgress(
    private val progressContentService: ProgressContentService,
    private val episodeRepository: EpisodeRepository,
    private val profileService: ProfileService,
    private val qualityService: QualityService,
    private val isIncrementViewsEnabled: () -> <PERSON><PERSON><PERSON>
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): EpisodeSummary {
        with(actionData) {
            logger.info("Player $playerId registered content $contentId from episode $episodeId")

            val episode = episodeRepository.findById(episodeId) ?: throw IllegalArgumentException("Episode not found: $episodeId")
            val progress = progressContentService.findProgress(episodeId, playerId)
            registerContent(episode, progress)
            return incrementViews(episode, progress).let {
                processChannelScoreAsync(episodeBeforeUpdate = episode, episodeAfterUpdate = it)
                EpisodeSummary.from(it, profileService.find(it.ownerId))
            }
        }
    }

    private suspend fun processChannelScoreAsync(
        episodeBeforeUpdate: Episode,
        episodeAfterUpdate: Episode
    ) {
        try {
            qualityService.calculateChannelScore(
                episodeBeforeUpdate = episodeBeforeUpdate,
                episodeAfterUpdate = episodeAfterUpdate
            )
        } catch (e: Exception) {
            logger.error("[QUALITY] Error processing channel score for episode ${episodeAfterUpdate.id}", e)
        }
    }

    private suspend fun incrementViews(
        episode: Episode,
        progress: ProgressContent?
    ): Episode {
        if (isIncrementViewsEnabled() && progress == null) {
            episodeRepository.updateView(episode.id)
            return episode.incrementViews()
        }

        return episode
    }

    private suspend fun ActionData.registerContent(
        episode: Episode,
        progress: ProgressContent?
    ) {
        progressContentService.registerProgress(
            episodeId,
            playerId,
            contentId,
            episode.language.name,
            progress?.hasFinishedEpisode ?: false
        )
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String,
        val contentId: String
    )
}
