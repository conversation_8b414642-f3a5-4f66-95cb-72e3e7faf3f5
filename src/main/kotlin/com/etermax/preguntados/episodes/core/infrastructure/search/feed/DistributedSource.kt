package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import org.slf4j.LoggerFactory

class DistributedSource(
    private val sources: List<Source<String>>,
    private val distributionStrategy: DistributionStrategy = EvenlyDistributionStrategy()
) : Source<String> {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<String> {
        logger.debug("[FEED] Starting fetch with limit={}, offset={}", request.range.limit, request.range.offset)

        if (request.range.limit <= 0 || sources.isEmpty()) {
            logger.debug("[FEED] No sources or non-positive limit. Returning empty response.")
            return emptyResponse()
        }

        if (request.fetchCursor?.exhausted == true) {
            logger.debug("[FEED] <PERSON><PERSON> called with exhausted cursor, returning empty response")
            return emptyResponse(request.fetchCursor)
        }

        val itemsPerSource = distributionStrategy.distribute(request.range.limit, sources.size)
        val offsetsPerSource = distributionStrategy.distribute(request.range.offset, sources.size)

        logger.debug("[FEED] Items per source: {}. Offsets per source: {}", itemsPerSource, offsetsPerSource)

        val initialResults = fetchFromSources(sources, request, itemsPerSource, offsetsPerSource)
        val initialItems = initialResults.flatMap { it.items }
        val initialCursors = initialResults.map { it.fetchCursor }

        logger.debug("[FEED] Fetched {} items in initial fetch", initialItems.size)

        val remainingItems = request.range.limit - initialItems.size
        if (remainingItems <= 0) {
            logger.debug("[FEED] No backfill needed")
            return SourceResponse(initialItems, buildCursor(initialCursors))
        }

        val activeSources = identifyActiveSources(initialResults)
        if (activeSources.isEmpty()) {
            logger.debug("[FEED] All sources exhausted. Returning {} items.", initialItems.size)
            return SourceResponse(initialItems, buildCursor(initialCursors))
        }

        logger.debug("[FEED] Backfilling {} items from {} active sources", remainingItems, activeSources.size)

        val backfillResults = backfillFromSources(
            initialCursors,
            activeSources,
            sources,
            request,
            itemsPerSource,
            offsetsPerSource,
            remainingItems
        )

        val backfillItems = backfillResults.flatMap { it.items }
        val backfillCursors = backfillResults.map { it.fetchCursor }

        val finalItems = initialItems + backfillItems
        val finalCursors = mergeCursors(initialCursors, activeSources, backfillCursors)

        logger.debug("[FEED] Final item count: {}", finalItems.size)

        return SourceResponse(finalItems, buildCursor(finalCursors))
    }

    private fun buildCursor(cursors: List<FetchCursor?>): FetchCursor? =
        DistributedFetchCursor(cursors, cursors.all { it?.exhausted == true })

    private fun identifyActiveSources(
        results: List<SourceResponse<String>>
    ): List<Int> =
        results.mapIndexedNotNull { index, response ->
            if (response.fetchCursor?.exhausted == false) index else null
        }

    private fun mergeCursors(
        originalCursors: List<FetchCursor?>,
        updatedIndices: List<Int>,
        newCursors: List<FetchCursor?>
    ): List<FetchCursor?> {
        val merged = originalCursors.toMutableList()
        updatedIndices.forEachIndexed { i, index ->
            merged[index] = newCursors.getOrNull(i)
        }
        return merged
    }

    private suspend fun fetchFromSources(
        sources: List<Source<String>>,
        request: SourceRequest,
        itemsPerSource: List<Int>,
        offsetsPerSource: List<Int>
    ): List<SourceResponse<String>> = coroutineScope {
        val cursors = request.fetchCursor as? DistributedFetchCursor
        sources.mapIndexed { index, source ->
            async {
                val offset = offsetsPerSource.getOrNull(index) ?: 0
                val limit = itemsPerSource.getOrNull(index) ?: 0
                val cursor = cursors?.delegates?.getOrNull(index)

                val sourceRequest = request.copy(
                    range = OffsetLimit(offset, limit),
                    fetchCursor = cursor
                )

                logger.debug(
                    "[FEED] Fetching from source {} with offset={}, limit={}, cursor={}",
                    index,
                    offset,
                    limit,
                    cursor
                )

                source.fetch(sourceRequest)
            }
        }.awaitAll()
    }

    private suspend fun backfillFromSources(
        initialCursors: List<FetchCursor?>,
        activeSourceIndices: List<Int>,
        sources: List<Source<String>>,
        originalRequest: SourceRequest,
        originalLimits: List<Int>,
        originalOffsets: List<Int>,
        totalRemaining: Int
    ): List<SourceResponse<String>> = coroutineScope {
        val backfillStrategy = EvenlyDistributionStrategy()
        val backfillLimits = backfillStrategy.distribute(totalRemaining, activeSourceIndices.size)

        activeSourceIndices.mapIndexed { i, sourceIndex ->
            async {
                val offset =
                    (originalOffsets.getOrNull(sourceIndex) ?: 0) + (originalLimits.getOrNull(sourceIndex) ?: 0)
                val limit = backfillLimits.getOrNull(i) ?: 0
                val cursor = initialCursors.getOrNull(sourceIndex)

                val backfillRequest = originalRequest.copy(
                    range = OffsetLimit(offset, limit),
                    fetchCursor = cursor
                )

                logger.debug("[FEED] Backfilling from source {} with offset={}, limit={}", sourceIndex, offset, limit)

                sources[sourceIndex].fetch(backfillRequest)
            }
        }.awaitAll()
    }
}
