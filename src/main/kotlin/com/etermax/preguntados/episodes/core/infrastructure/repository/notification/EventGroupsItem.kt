package com.etermax.preguntados.episodes.core.infrastructure.repository.notification

import com.etermax.preguntados.episodes.core.domain.episode.notification.EventGroup
import com.etermax.preguntados.episodes.core.domain.episode.notification.EventType
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId

@DynamoDbBean
class EventGroupsItem(
    @get:DynamoDbAttribute("episode_id") var episodeId: String = "",
    @get:DynamoDbAttribute("player_id") var playerId: Long = 0,
    @get:DynamoDbAttribute("event_type") var eventType: String = "",
    @get:DynamoDbAttribute("count") var count: Int = 0,
    @get:DynamoDbAttribute("group_size") var groupSize: Int? = null,
    @get:DynamoDbAttribute("last_reset_date_epoch_seconds") var lastResetDateEpochSeconds: Long? = null
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = episodeId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = eventType

    fun to(): EventGroup {
        return EventGroup(
            episodeId = pk,
            eventType = EventType.valueOf(sk),
            playerId = playerId,
            count = count,
            groupSize = groupSize,
            lastResetDate = lastResetDateEpochSeconds?.let { toOffsetDateTime(it) }
        )
    }

    private fun toOffsetDateTime(value: Long): OffsetDateTime {
        return OffsetDateTime.ofInstant(Instant.ofEpochSecond(value), ZoneId.systemDefault())
    }
}
