package com.etermax.preguntados.episodes.core.domain.challenge

import com.etermax.preguntados.episodes.core.domain.profile.Profile

data class ChallengePlayerSummary(
    val profile: Profile,
    val status: ChallengePlayer.Status
) {
    companion object {
        fun from(player: ChallengePlayer, profile: Profile) = with(player) {
            ChallengePlayerSummary(
                profile = profile,
                status = status
            )
        }
    }
}
