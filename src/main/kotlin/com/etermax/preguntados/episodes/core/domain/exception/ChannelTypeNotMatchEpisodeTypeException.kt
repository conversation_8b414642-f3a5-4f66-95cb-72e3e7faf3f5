package com.etermax.preguntados.episodes.core.domain.exception

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.exception.base.BusinessException

class ChannelTypeNotMatchEpisodeTypeException(channel: Channel, episode: Episode) :
    BusinessException(
        "Channel='${channel.id}' has type='${channel.type}'. " +
            "Episode='${episode.id}' has type='${episode.type}'"
    )
