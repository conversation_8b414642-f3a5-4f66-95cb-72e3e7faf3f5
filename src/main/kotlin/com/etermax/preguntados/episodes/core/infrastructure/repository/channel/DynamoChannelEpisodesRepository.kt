package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelEpisode
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.process.DynamoChannelEpisodesOrderNormalizer
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelEpisodesCountItem
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.future.await
import kotlinx.coroutines.launch
import kotlinx.coroutines.reactive.asFlow
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.*

class DynamoChannelEpisodesRepository(
    dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient,
    private val channelTable: DynamoDbAsyncTable<UpdateChannelEpisodesCountItem>,
    private val channelEpisodesTable: DynamoDbAsyncTable<ChannelEpisodeItem>,
    private val channelEpisodesOrderNormalizer: DynamoChannelEpisodesOrderNormalizer
) : ChannelEpisodesRepository, DynamoDBRepository<ChannelEpisodeItem>(dynamoDbEnhancedClient, channelEpisodesTable) {

    override suspend fun add(channelEpisode: ChannelEpisode) {
        val item = ChannelEpisodeItem.from(channelEpisode)
        saveItem(item, ChannelEpisodeItem::class.java)
    }

    @OptIn(FlowPreview::class)
    override suspend fun findAllFrom(channelId: String): PaginatedItems<ChannelEpisode> {
        val partitionKey = ChannelEpisodeItem.buildPartitionKey(channelId)
        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(partitionKey)
        }

        val request = QueryEnhancedRequest.builder()
            .queryConditional(queryConditional)
            .limit(LIMIT_PER_PAGE)
            .build()

        val channelEpisodesItems = table.query(request)
            .asFlow()
            .flatMapMerge { page -> page.items().asFlow() }
            .toList()

        normalize(channelEpisodesItems)

        val channelEpisodes = channelEpisodesItems.mapNotNull { it.toDomain() }
            .toSet()

        if (channelEpisodes.count() < LIMIT_PER_PAGE) {
            return PaginatedItems(null, channelEpisodes)
        }
        return PaginatedItems(channelEpisodes.last().episodeId, channelEpisodes)
    }

    @OptIn(FlowPreview::class)
    override suspend fun findAllEpisodeIdsFrom(channelId: String, order: ChannelOrderType): List<String> {
        val partitionKey = ChannelEpisodeItem.buildPartitionKey(channelId)
        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(partitionKey)
        }

        val request = QueryEnhancedRequest.builder()
            .queryConditional(queryConditional)
            .limit(LIMIT_PER_PAGE)
            .scanIndexForward(false)
            .build()

        val channelEpisodesItems = table
            .index(getIndexFor(order))
            .query(request)
            .asFlow()
            .flatMapMerge { page -> page.items().asFlow() }
            .toList()

        normalize(channelEpisodesItems)

        return channelEpisodesItems.map { it.episodeId }
    }

    override suspend fun findChannelEpisodesLimited(
        channelId: String,
        episodesLimit: Int,
        order: ChannelOrderType
    ): Set<ChannelEpisode> {
        // TODO: Tests
        return findChannelEpisodesFor(channelId, episodesLimit, order).toSet()
    }

    override suspend fun findChannelEpisodesLimited(
        channels: Collection<Channel>,
        episodesPerChannel: Int
    ): Set<ChannelEpisode> = coroutineScope {
        val tasks = channels.map { channel ->
            async { findChannelEpisodesFor(channel.id, episodesPerChannel, channel.orderType) }
        }
        val results = awaitAll(*tasks.toTypedArray())
        results.flatten().toSet()
    }

    @OptIn(FlowPreview::class)
    private suspend fun findChannelEpisodesFor(
        channelId: String,
        episodesPerChannelLimit: Int,
        order: ChannelOrderType
    ): List<ChannelEpisode> {
        val partitionKey = ChannelEpisodeItem.buildPartitionKey(channelId)
        val queryConditional = QueryConditional.keyEqualTo {
            it.partitionValue(partitionKey)
        }

        val request = QueryEnhancedRequest.builder()
            .queryConditional(queryConditional)
            .limit(episodesPerChannelLimit)
            .scanIndexForward(false)
            .build()

        val results = table
            .index(getIndexFor(order))
            .query(request)
            .asFlow()
            .flatMapConcat { page ->
                page.items().asFlow()
            }
            .mapNotNull { it.toDomain() }
            .take(episodesPerChannelLimit)
            .toList()
        return results
    }

    override suspend fun delete(channelId: String, episodeId: String, channel: Channel) {
        val transactionBuilder = TransactWriteItemsEnhancedRequest.builder()

        val item = UpdateChannelEpisodesCountItem.from(channel)
        transactionBuilder.addUpdateItem(
            channelTable,
            TransactUpdateItemEnhancedRequest.builder(UpdateChannelEpisodesCountItem::class.java).item(item).build()
        )

        val key = Key
            .builder()
            .partitionValue(ChannelEpisodeItem.buildPartitionKey(channelId))
            .sortValue(ChannelEpisodeItem.buildSortedKey(episodeId))
            .build()

        transactionBuilder.addDeleteItem(
            channelEpisodesTable,
            TransactDeleteItemEnhancedRequest.builder().key(key).build()
        )

        client.transactWriteItems(transactionBuilder.build()).await()
    }

    private fun getIndexFor(order: ChannelOrderType): String {
        return when (order) {
            ChannelOrderType.DATE_ADDED -> ChannelIndexes.BY_ID_AND_EPISODES_ADDED_DATE
            ChannelOrderType.CUSTOM_ORDER -> ChannelIndexes.BY_ID_AND_EPISODES_CUSTOM_ORDER
        }
    }

    private suspend fun normalize(channelEpisodesItems: List<ChannelEpisodeItem>) {
        coroutineScope {
            launch {
                channelEpisodesOrderNormalizer.normalize(channelEpisodesItems)
            }
        }
    }

    private companion object {
        const val LIMIT_PER_PAGE = 10
    }
}
