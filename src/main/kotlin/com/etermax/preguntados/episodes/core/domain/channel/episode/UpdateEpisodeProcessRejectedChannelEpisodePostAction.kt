package com.etermax.preguntados.episodes.core.domain.channel.episode

import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostAction
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import org.slf4j.LoggerFactory

class UpdateEpisodeProcessRejectedChannelEpisodePostAction(
    private val unpublishedRepository: ChannelUnpublishedEpisodesRepository,
    private val channelEpisodesService: ChannelEpisodesService
) : UpdateChannelEpisodePostAction {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun applies(oldEpisode: Episode, updateData: UpdateChannelEpisodePostActionData): Boolean {
        if (updateData.channelId == null) return false
        if (updateData.status == null) return false
        if (updateData.status != EpisodeStatus.REJECTED) return false

        return true
    }

    override suspend fun execute(
        oldEpisode: Episode,
        updateData: UpdateChannelEpisodePostActionData,
        alreadyCheckIfApplies: Boolean
    ) {
        val channelId = updateData.channelId ?: return

        runCatching {
            if (alreadyCheckIfApplies || applies(oldEpisode, updateData)) {
                val item = ChannelUnpublishedEpisode(channelId, oldEpisode.id)
                unpublishedRepository.delete(item)
                channelEpisodesService.removeChannelFromEpisode(oldEpisode.id)
            }
        }.onFailure {
            logger.error(
                "Fail to process rejected episode '${oldEpisode.id}' with channel='$channelId'",
                it
            )
        }
    }
}
