package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.ChannelStatistics
import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.utils.LanguageUtils
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@DynamoDbBean
class ChannelItem(
    @get:DynamoDbAttribute(ChannelItemAttributes.CHANNEL_ID)
    var channelId: String = "",

    @get:DynamoDbAttribute(ChannelItemAttributes.NAME)
    var name: String = "",

    @get:DynamoDbAttribute(ChannelItemAttributes.DESCRIPTION)
    var description: String? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.WEBSITE)
    var website: String? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.COVER_URL)
    var coverUrl: String? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.SUBSCRIBED)
    var subscribed: Boolean? = null,

    @get:DynamoDbSecondaryPartitionKey(indexNames = [ChannelIndexes.BY_OWNER_AND_LAST_MODIFICATION])
    @get:DynamoDbAttribute(ChannelItemAttributes.OWNER_ID)
    var ownerId: Long? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.SUBSCRIBERS_COUNT)
    var subscribersCount: Int? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.EPISODES_COUNT)
    var episodesCount: Int? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.CREATION_DATE)
    var creationDate: Long? = null,

    @get:DynamoDbSecondarySortKey(indexNames = [ChannelIndexes.BY_OWNER_AND_LAST_MODIFICATION])
    @get:DynamoDbAttribute(ChannelItemAttributes.LAST_MODIFICATION_DATE)
    var lastModificationDate: Long? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.TYPE)
    var type: String? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.LANGUAGE)
    var language: String? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.CHANNEL_ORDER)
    var order: String? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.SCORE)
    var score: Int? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.QUALITY)
    var quality: Int? = null,

    @get:DynamoDbAttribute(ChannelItemAttributes.ORDER_TYPE)
    var orderType: String? = null
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = buildPartitionKey(channelId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = CHANNEL_SK

    fun toDomain(): Channel? {
        try {
            return Channel(
                id = channelId,
                name = name,
                description = description,
                website = website,
                coverUrl = coverUrl!!,
                subscribed = subscribed!!,
                ownerId = ownerId!!,
                creationDate = OffsetDateTime.ofInstant(Instant.ofEpochMilli(creationDate!!), ZoneOffset.UTC),
                lastModificationDate = OffsetDateTime.ofInstant(
                    Instant.ofEpochMilli(lastModificationDate!!),
                    ZoneOffset.UTC
                ),
                statistics = ChannelStatistics(subscribersCount!!, episodesCount!!, score ?: 0, quality ?: 0),
                type = type!!.toChannelType(),
                language = LanguageUtils.from(language),
                order = order!!.toLong(),
                orderType = orderType?.toChannelOrderType() ?: ChannelOrderType.DATE_ADDED
            )
        } catch (e: Exception) {
            logger.error("Error mapping channelItem [$channelId] to domain", e)
            return null
        }
    }

    companion object {
        const val CHANNEL_PREFIX = "C#"
        const val CHANNEL_SK = "CHANNEL"

        fun buildPartitionKey(channelId: String) = "$CHANNEL_PREFIX$channelId"

        fun from(channel: Channel) = with(channel) {
            ChannelItem(
                channelId = id,
                name = name,
                description = description,
                website = website,
                coverUrl = coverUrl,
                subscribed = subscribed,
                ownerId = ownerId,
                creationDate = creationDate.toMillis(),
                lastModificationDate = lastModificationDate.toMillis(),
                subscribersCount = statistics.subscribers,
                episodesCount = statistics.episodes,
                type = type.asString(),
                language = language?.name?.uppercase(),
                order = order.toString(),
                score = statistics.score,
                quality = statistics.quality,
                orderType = orderType.asString()
            )
        }

        fun ChannelOrderType.asString(): String {
            return when (this) {
                ChannelOrderType.DATE_ADDED -> "DATE_ADDED"
                ChannelOrderType.CUSTOM_ORDER -> "CUSTOM_ORDER"
            }
        }

        private fun String.toChannelType(): ChannelType {
            return when (this.uppercase()) {
                "PUBLIC" -> ChannelType.PUBLIC
                "PRIVATE" -> ChannelType.PRIVATE
                else -> throw RuntimeException("ChannelType '$this' not supported.")
            }
        }

        private fun ChannelType.asString(): String {
            return when (this) {
                ChannelType.PUBLIC -> "PUBLIC"
                ChannelType.PRIVATE -> "PRIVATE"
            }
        }

        private fun String.toChannelOrderType(): ChannelOrderType {
            return when (this.uppercase()) {
                "DATE_ADDED" -> ChannelOrderType.DATE_ADDED
                "CUSTOM_ORDER" -> ChannelOrderType.CUSTOM_ORDER
                else -> throw RuntimeException("ChannelOrderType '$this' not supported.")
            }
        }
    }
}
