package com.etermax.preguntados.episodes.core.infrastructure.search.feed.layout

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.slf4j.LoggerFactory

/**
 * Composes a feed by interleaving items from primary and secondary sources
 * in an 4:1 pattern (1P, 1S, 3P and 1S, 4P).
 * Each secondary item counts as 2 units of size.
 */
class FourByOneFeedSource<E, C>(
    private val primarySource: Source<E>,
    private val secondarySource: Source<C>
) : Source<Any> {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun fetch(request: SourceRequest): SourceResponse<Any> {
        val totalSize = request.range.limit
        if (totalSize <= 0) {
            logger.debug("[FEED] Empty response requested (limit = $totalSize).")
            return emptyResponse(request.fetchCursor)
        }

        if (request.fetchCursor?.exhausted == true) {
            logger.debug("[FEED] Fetch called with exhausted cursor, returning empty response")
            return emptyResponse(request.fetchCursor)
        }

        val cursors = request.fetchCursor as? FourByOneFetchCursor
        logger.debug("[FEED] Received fetch request with cursor: {}", cursors)

        if (cursors?.primary?.exhausted == true) {
            logger.debug("[FEED] Fetch called with exhausted primary cursor, returning empty response")
            return emptyResponse(request.fetchCursor)
        }

        val (primaryLimit, secondaryLimit) = calculateItemLimits(totalSize)
        logger.debug("[FEED] Composing feed of size $totalSize → primary: $primaryLimit, secondary: $secondaryLimit")

        val (primaryResponse, secondaryResponse) = fetchFromSources(request, primaryLimit, secondaryLimit, cursors)

        if (primaryResponse.items.isEmpty()) {
            logger.debug("[FEED] No primary items found. Returning empty response.")
            return emptyResponse(buildCursor(primaryResponse.fetchCursor, cursors?.secondary))
        }
        logger.debug(
            "[FEED] Fetched {} primary items, cursor: {}",
            primaryResponse.items.size,
            primaryResponse.fetchCursor
        )
        logger.debug(
            "[FEED] Fetched {} secondary items, cursor: {}",
            secondaryResponse.items.size,
            secondaryResponse.fetchCursor
        )

        val primaryItems = primaryResponse.items
        val secondaryItems = secondaryResponse.items

        val primaryCursor = primaryResponse.fetchCursor
        val secondaryCursor = secondaryResponse.fetchCursor

        val extraPrimaryNeeded = calculateExtraPrimaryItems(secondaryItems.size, secondaryLimit)
        logger.debug("[FEED] Extra primary items needed due to secondary shortfall: {}", extraPrimaryNeeded)

        val (finalPrimaryItems, finalPrimaryCursor) = if (extraPrimaryNeeded > 0) {
            val extraResponse = primarySource.fetch(
                request.copy(
                    range = OffsetLimit(primaryItems.size, extraPrimaryNeeded),
                    fetchCursor = primaryCursor
                )
            )
            logger.debug("[FEED] Fetched ${extraResponse.items.size} additional primary items")
            Pair(primaryItems + extraResponse.items, extraResponse.fetchCursor)
        } else {
            Pair(primaryItems, primaryCursor)
        }

        val composedItems = composeFeed(finalPrimaryItems, secondaryItems)
        logger.debug("[FEED] Composed final feed with {} items", composedItems.size)

        val finalCursor = buildCursor(finalPrimaryCursor, secondaryCursor)
        logger.debug("[FEED] Generated final fetch cursor: {}", finalCursor)

        return SourceResponse(composedItems, finalCursor)
    }

    private suspend fun fetchFromSources(
        request: SourceRequest,
        primaryLimit: Int,
        secondaryLimit: Int,
        cursors: FourByOneFetchCursor?
    ): Pair<SourceResponse<E>, SourceResponse<C>> = coroutineScope {
        val primaryRequest = async {
            primarySource.fetch(
                request.copy(range = OffsetLimit(0, primaryLimit), fetchCursor = cursors?.primary)
            )
        }
        val secondaryRequest = async {
            secondarySource.fetch(
                request.copy(range = OffsetLimit(0, secondaryLimit), fetchCursor = cursors?.secondary)
            )
        }

        val primary = primaryRequest.await()
        val secondary = secondaryRequest.await()

        Pair(primary, secondary)
    }

    private fun calculateItemLimits(size: Int): Pair<Int, Int> {
        val completePatterns = size / 6
        val remainingUnits = size % 6

        val secondaryCount = completePatterns + if (remainingUnits >= 2) 1 else 0
        val primaryCount = size - (secondaryCount * 2)

        logger.debug("[FEED] Calculated item limits → primary: {}, secondary: {}", primaryCount, secondaryCount)
        return primaryCount to secondaryCount
    }

    private fun calculateExtraPrimaryItems(actualSecondary: Int, expectedSecondary: Int): Int {
        val missing = expectedSecondary - actualSecondary
        val needed = missing * 2
        logger.debug("[FEED] Secondary items shortfall: {}, need {} extra primary items", missing, needed)
        return needed
    }

    private fun composeFeed(primaryItems: List<E>, secondaryItems: List<C>): List<Any> {
        val result = mutableListOf<Any>()
        var pIndex = 0
        var sIndex = 0
        var alternate = true

        logger.debug(
            "[FEED] Starting feed composition with {} primary and {} secondary items",
            primaryItems.size,
            secondaryItems.size
        )

        while (pIndex < primaryItems.size || sIndex < secondaryItems.size) {
            if (alternate) {
                logger.debug("[FEED] Applying pattern A (1P + 1S + 3P)")
                if (pIndex < primaryItems.size) result += primaryItems[pIndex++]!!
                if (sIndex < secondaryItems.size) result += secondaryItems[sIndex++]!!
                repeat(3) {
                    if (pIndex < primaryItems.size) result += primaryItems[pIndex++]!!
                }
            } else {
                logger.debug("[FEED] Applying pattern B (1S + 4P)")
                if (sIndex < secondaryItems.size) result += secondaryItems[sIndex++]!!
                repeat(4) {
                    if (pIndex < primaryItems.size) result += primaryItems[pIndex++]!!
                }
            }
            alternate = !alternate
        }

        logger.debug("[FEED] Finished feed composition, total items: {}", result.size)
        return result
    }

    private fun buildCursor(primary: FetchCursor?, secondary: FetchCursor?): FetchCursor? {
        return FourByOneFetchCursor(primary, secondary, primary?.exhausted ?: false)
    }
}
