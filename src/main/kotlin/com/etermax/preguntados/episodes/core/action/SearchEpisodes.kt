package com.etermax.preguntados.episodes.core.action

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.episode.*
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.DeliveryService
import com.etermax.preguntados.episodes.core.domain.search.EpisodeSearchResult
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language

class SearchEpisodes(
    private val summaryService: SummaryService,
    private val deliveryService: DeliveryService,
    private val newEpisodesRepository: NewEpisodeRepository
) {

    suspend operator fun invoke(actionData: ActionData): List<EpisodeSummary> {
        val episodes = actionData.search()
            .takeIf { it.episodes.isNotEmpty() }
            ?.let { refreshEpisodes(actionData.sort, actionData.playerId, it.episodes) }
        return summaryService.toEpisodesSummary(episodes)
    }

    private suspend fun refreshEpisodes(sort: SortEpisode?, playerId: Long?, episodes: List<Episode>): List<Episode> {
        return if (playerId != null) {
            swapUpdatedEpisodes(sort, playerId, episodes)
        } else {
            episodes
        }
    }

    private suspend fun swapUpdatedEpisodes(
        sort: SortEpisode?,
        playerId: Long,
        episodes: List<Episode>
    ): List<Episode> {
        if (sort != SortEpisode.MINE) {
            return episodes
        }

        val updatedEpisodes = newEpisodesRepository
            .find(playerId)
            .map { it.episode }
            .filterNot { it in episodes }
        val swappedEpisodes = episodes + updatedEpisodes
        return swappedEpisodes.moveDraftsToFront()
    }

    private suspend fun ActionData.search(): EpisodeSearchResult {
        return EpisodeSearchResult(deliveryService.search(toSearchParameters()))
    }

    private fun List<Episode>.moveDraftsToFront(): List<Episode> {
        val (drafts, others) = partition { it.status == EpisodeStatus.DRAFT }
        return drafts + others
    }

    data class ActionData(
        val playerId: Long?,
        val language: Language? = null,
        val name: String? = null,
        val sort: SortEpisode? = null,
        val country: Country? = null,
        val episodeId: String? = null,
        val channelId: String? = null,
        val isRestricted: Boolean,
        val offset: Int,
        val limit: Int
    ) {

        fun toSearchParameters() = SearchParameters(
            playerId = playerId,
            language = language,
            country = country ?: Country.US,
            episodeType = EpisodeType.PUBLIC,
            status = EpisodeStatus.PUBLISHED,
            name = name,
            episodeId = episodeId,
            channelId = channelId,
            sort = sort,
            isRestricted = isRestricted,
            offset = offset,
            limit = limit
        )
    }
}
