package com.etermax.preguntados.episodes.core.domain.search.feed

import kotlinx.serialization.Polymorphic
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@Polymorphic
sealed class FetchCursor {
    abstract val exhausted: Boolean
}

@Serializable
@SerialName("offset")
data class OffsetFetchCursor(val offset: Int, override val exhausted: Boolean = false) : FetchCursor()

@Serializable
@SerialName("filtered")
data class FilteredFetchCursor(val delegate: FetchCursor?, override val exhausted: Boolean = false) : FetchCursor()

@Serializable
@SerialName("mixed")
data class MixedSimilarityFetchCursor(val delegates: List<FetchCursor?>?, override val exhausted: Boolean = false) : FetchCursor()

@Serializable
@SerialName("distributed")
data class DistributedFetchCursor(val delegates: List<FetchCursor?>?, override val exhausted: Boolean = false) : FetchCursor()

@Serializable
@SerialName("fourByOne")
data class FourByOneFetchCursor(val primary: FetchCursor?, val secondary: FetchCursor?, override val exhausted: Boolean = false) : FetchCursor()

@Serializable
@SerialName("eightByTwoFetchCursor")
data class EightByTwoFetchCursor(val primary: FetchCursor?, val secondary: FetchCursor?, override val exhausted: Boolean = false) : FetchCursor()
