package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.time.Clock

class UpdateChannel(
    private val repository: ChannelRepository,
    private val channelValidatorService: ChannelValidatorService,
    private val summaryService: SummaryService,
    private val clock: Clock
) {
    suspend operator fun invoke(data: ActionData): ChannelSummary {
        if (data.channelId == null) throw ChannelNotFoundException("Has not channelId in action data")
        val channel = repository.findById(data.channelId) ?: throw ChannelNotFoundException(data.channelId)

        channelValidatorService.validateUpdate(
            data = data,
            channel = channel
        )

        val updatedChannel = channel.copy(
            name = data.name,
            coverUrl = data.coverUrl,
            description = data.description,
            website = data.website,
            lastModificationDate = clock.now()
        )

        repository.put(updatedChannel)
        val channelSummary = summaryService.toChannelSummary(updatedChannel)
        return channelSummary!!
    }
}
