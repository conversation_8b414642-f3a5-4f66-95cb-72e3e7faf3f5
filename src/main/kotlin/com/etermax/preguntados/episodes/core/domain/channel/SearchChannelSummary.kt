package com.etermax.preguntados.episodes.core.domain.channel

import org.slf4j.LoggerFactory

data class SearchChannelSummary(
    val channelSummary: ChannelSummary,
    val episodeCovers: List<String>
) {
    companion object {

        private val logger by lazy { LoggerFactory.getLogger(this::class.java) }

        fun from(channelSummary: ChannelSummary, episodeCovers: List<String>): SearchChannelSummary {
            return SearchChannelSummary(
                channelSummary = channelSummary,
                episodeCovers = episodeCovers
            ).also {
                val episodesCount = channelSummary.statistics.episodes
                val coversCount = episodeCovers.count()

                if (episodesCount < coversCount) {
                    logger.warn("Channel '${channelSummary.id}' has less episodes '$episodesCount' than coversUrls '$coversCount'")
                }
            }
        }
    }
}
