package com.etermax.preguntados.episodes.core.infrastructure.repository.progress.tables

import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class ProgressItem(
    @get:DynamoDbAttribute("episode_id") var episodeId: String = "",
    @get:DynamoDbAttribute("owner_id") var ownerId: Long = 0,
    @get:DynamoDbAttribute("last_content_id") var lastContentId: String? = null,
    @get:DynamoDbAttribute("language") var language: String? = null,
    @get:DynamoDbAttribute("has_finished_episode") var hasFinishedEpisode: Boolean? = null
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = buildPartitionKey(episodeId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = buildProgressSortedKey(ownerId)

    fun to() = ProgressContent(
        episodeId = episodeId,
        playerId = ownerId,
        lastContentId = lastContentId,
        language = language,
        hasFinishedEpisode = hasFinishedEpisode ?: false
    )

    companion object {
        private const val EPISODE_PREFIX = "E"
        private const val PLAYER_PREFIX = "P"
        private const val PROGRESS_SK = "PROGRESS"

        fun buildPartitionKey(episodeId: String) = "$EPISODE_PREFIX#$episodeId"

        fun buildProgressSortedKey(playerId: Long) = "$PROGRESS_SK#$PLAYER_PREFIX#$playerId"

        fun from(progressContent: ProgressContent) = with(progressContent) {
            ProgressItem(
                episodeId = episodeId,
                ownerId = playerId,
                lastContentId = lastContentId,
                language = progressContent.language,
                hasFinishedEpisode = hasFinishedEpisode
            )
        }
    }
}
