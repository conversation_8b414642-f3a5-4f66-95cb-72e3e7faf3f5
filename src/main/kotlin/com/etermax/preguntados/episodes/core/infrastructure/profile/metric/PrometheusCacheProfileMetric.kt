package com.etermax.preguntados.episodes.core.infrastructure.profile.metric

import com.etermax.preguntados.episodes.core.domain.metric.CounterMetric
import io.prometheus.client.Counter

class PrometheusCacheProfileMetric : CounterMetric {

    private val counter: Counter = Counter.build()
        .name(METRIC_NAME)
        .help(HELP)
        .labelNames(RESULT_LABEL)
        .register()

    override fun count(vararg labels: String) {
        counter.labels(*labels).inc()
    }

    companion object {
        private const val METRIC_NAME = "cache_profile_requests_ratio_total"
        private const val HELP = "represent how many times use cache over request api"
        private const val RESULT_LABEL = "result"

        const val HIT = "hit"
        const val MISS = "miss"
    }
}
