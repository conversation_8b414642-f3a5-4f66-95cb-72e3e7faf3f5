package com.etermax.preguntados.episodes.core.domain.channel.repository

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelEpisode
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems

interface ChannelEpisodesRepository {
    suspend fun add(channelEpisode: ChannelEpisode)
    suspend fun findAllFrom(channelId: String): PaginatedItems<ChannelEpisode>
    suspend fun findAllEpisodeIdsFrom(channelId: String, order: ChannelOrderType = ChannelOrderType.DATE_ADDED): List<String>
    suspend fun findChannelEpisodesLimited(
        channelId: String,
        episodesLimit: Int,
        order: ChannelOrderType
    ): Set<ChannelEpisode>
    suspend fun findChannelEpisodesLimited(channels: Collection<Channel>, episodesPerChannel: Int): Set<ChannelEpisode>
    suspend fun delete(channelId: String, episodeId: String, channel: Channel)
}
