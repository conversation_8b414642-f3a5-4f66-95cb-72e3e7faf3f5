package com.etermax.preguntados.episodes.core.infrastructure.repository.progress

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.PlayedEpisodeItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.search.OpenSearchRequestFactory
import com.etermax.preguntados.episodes.core.infrastructure.search.data.PlayerEpisodeData
import kotlinx.coroutines.future.await
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.SortOrder
import org.opensearch.client.opensearch.core.SearchResponse

class OpenSearchPlayedRepository(
    private val client: OpenSearchAsyncClient,
    private val indexName: String
) {

    suspend fun lastPlayedEpisodesIds(
        playerId: Long,
        offset: Int,
        limit: Int,
        language: String? = null
    ): List<String> {
        val request = OpenSearchRequestFactory(indexName, offset, limit)
            .filterTerm(PlayedEpisodeItemAttributes.USER_ID, playerId)
            .filterByLanguage(language)
            .sortBy(PlayedEpisodeItemAttributes.TIMESTAMP, SortOrder.Desc)
            .includeSourceFields(PlayedEpisodeItemAttributes.EPISODE_ID)
            .build()
        val response = client.search(request, PlayerEpisodeData::class.java).await()
        return response.toEpisodeIds()
    }

    // TODO: Unify. Separated for testing.
    suspend fun lastPlayedEpisodesIdsV2(
        playerId: Long,
        offset: Int,
        limit: Int,
        language: String? = null,
        onlyPublic: Boolean = false
    ): List<String> {
        val request = OpenSearchRequestFactory(indexName, offset, limit)
            .filterTerm(PlayedEpisodeItemAttributes.USER_ID, playerId)
            .filterByLanguage(language)
            .filterByPublicIf(onlyPublic)
            .sortBy(PlayedEpisodeItemAttributes.TIMESTAMP, SortOrder.Desc)
            .includeSourceFields(PlayedEpisodeItemAttributes.EPISODE_ID)
            .build()
        val response = client.search(request, PlayerEpisodeData::class.java).await()
        return response.toEpisodeIds()
    }

    private fun OpenSearchRequestFactory.filterByLanguage(language: String?) = this.also {
        language?.let { filterTerm(PlayedEpisodeItemAttributes.LANGUAGE, it) }
    }

    private fun OpenSearchRequestFactory.filterByPublicIf(onlyPublic: Boolean) = this.also {
        if (onlyPublic) {
            filterTerm(EpisodeItemAttributes.TYPE, EpisodeType.PUBLIC.name)
        }
    }

    private fun SearchResponse<PlayerEpisodeData>.toEpisodeIds(): List<String> {
        return hits().hits().mapNotNull {
            val data = it.source() as PlayerEpisodeData
            data.episodeId
        }
    }
}
