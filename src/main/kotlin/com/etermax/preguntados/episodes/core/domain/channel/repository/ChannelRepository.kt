package com.etermax.preguntados.episodes.core.domain.channel.repository

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelSearchFilters
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter

interface ChannelRepository {
    suspend fun add(channel: Channel)
    suspend fun put(updatedChannel: Channel)
    suspend fun delete(channel: Channel)
    suspend fun findById(channelId: String): Channel?
    suspend fun findById(channelsId: List<String>): List<Channel>
    suspend fun search(filters: ChannelSearchFilters, pagination: PaginationFilter): PaginatedItems<Channel>
    suspend fun hasChannels(filters: ChannelSearchFilters): Boolean
}
