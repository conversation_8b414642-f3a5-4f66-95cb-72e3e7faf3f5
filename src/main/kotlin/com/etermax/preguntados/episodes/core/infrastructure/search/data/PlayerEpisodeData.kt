package com.etermax.preguntados.episodes.core.infrastructure.search.data

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class PlayerEpisodeData(
    @JsonProperty("player_id") val playerId: String? = null,
    @JsonProperty("episode_id") val episodeId: String? = null,
    @JsonProperty("episode_doc_id") val episodeDocId: String? = null,
    @JsonProperty("timestamp") val timestamp: Long? = null,
    @JsonProperty("language") val language: String? = null
)
