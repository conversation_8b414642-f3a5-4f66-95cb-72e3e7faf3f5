package com.etermax.preguntados.episodes.core.domain.challenge

import java.time.OffsetDateTime

data class Challenge(
    val id: String,
    val createDate: OffsetDateTime,
    val startDate: OffsetDateTime,
    val endDate: OffsetDateTime,
    val expireDate: OffsetDateTime,
    val ownerId: Long,
    val reference: EpisodeReference
) {
    enum class Status {
        PENDING,
        IN_PROGRESS,
        ENDED
    }
}
