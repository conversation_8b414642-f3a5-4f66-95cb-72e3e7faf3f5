package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.episodes.core.domain.search.feed.OffsetLimit
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceRequest
import com.etermax.preguntados.episodes.core.infrastructure.search.OpenSearchRequestFactory
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch.core.SearchRequest

class QualityChannelSource(
    client: OpenSearchAsyncClient,
    private val channelIndexName: String
) : ChannelSource(client) {

    override suspend fun generateRequest(request: SourceRequest, offsetLimit: OffsetLimit): SearchRequest {
        return OpenSearchRequestFactory(channelIndexName, offsetLimit.offset, offsetLimit.limit)
            .filterByLanguage(request.language)
            .filterByType(ChannelType.PUBLIC)
            .filterByMinContent(2)
            .includeChannelIdOnly()
            .sortByQuality()
            .build()
    }
}
