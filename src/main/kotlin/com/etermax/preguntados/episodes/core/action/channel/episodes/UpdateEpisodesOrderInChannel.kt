package com.etermax.preguntados.episodes.core.action.channel.episodes

import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.episode.ChannelEpisodeOrder
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesOrderRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.ChannelEpisodesNewOrderCannotBeEmptyException
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.PlayerNotOwnChannelException
import com.etermax.preguntados.episodes.core.domain.order.OrderItemCalculator
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class UpdateEpisodesOrderInChannel(
    private val episodesRepository: EpisodeRepository,
    private val channelEpisodesRepository: ChannelEpisodesRepository,
    private val channelEpisodesOrderRepository: ChannelEpisodesOrderRepository,
    private val channelRepository: ChannelRepository,
    private val orderItemCalculator: OrderItemCalculator
) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(data: ActionData) = with(data) {
        validateData()

        val updatedEpisodes = getUpdatedEpisodesOrders()
        channelEpisodesOrderRepository.put(updatedEpisodes)
    }

    private suspend fun ActionData.validateData() {
        if (newOrdersDefinition.isEmpty()) {
            throw ChannelEpisodesNewOrderCannotBeEmptyException(channelId)
        }

        val channel = channelRepository.findById(channelId) ?: throw ChannelNotFoundException(channelId)
        if (!channel.isOwnedBy(playerId)) {
            throw PlayerNotOwnChannelException(playerId, channelId)
        }

        discardInvalidEpisodes()
    }

    private suspend fun ActionData.discardInvalidEpisodes() {
        val episodesIds = newOrdersDefinition.values.toList()
        val episodes = episodesRepository.findByIds(episodesIds)
        val validEpisodesIds = episodes.filter { it.channelId == channelId && it.ownerId == playerId }.map { it.id }

        if (validEpisodesIds.isEmpty()) {
            logger.warn("All episodes='[${episodes.map { it.id }}]' do not belong to channel='$channelId'")
            throw ChannelEpisodesNewOrderCannotBeEmptyException(channelId)
        }

        if (validEpisodesIds.count() < episodesIds.count()) {
            val invalidEpisodesIds = episodesIds.filterNot { validEpisodesIds.contains(it) }
            logger.warn("Discarding episodes='[$invalidEpisodesIds]' do not belong to channel='$channelId'")

            val validNewOrdersDefinition = newOrdersDefinition.mapNotNull { entry ->
                if (validEpisodesIds.contains(entry.value)) {
                    Pair(entry.key, entry.value)
                } else null
            }.toMap()

            this.setNewOrdersDefinition(validNewOrdersDefinition)
        }
    }

    private suspend fun ActionData.getUpdatedEpisodesOrders(): MutableList<ChannelEpisodeOrder> {
        val sortedEpisodes = getSortedEpisodesByNewOrders()

        val newOrdersDefinitionKeys = newOrdersDefinition.keys.toList()
        val updatedEpisodesOrders = mutableListOf<ChannelEpisodeOrder>()

        val index = Index()
        while (index.value < newOrdersDefinitionKeys.size) {
            val currentOrder = newOrdersDefinitionKeys[index.value]

            val adjacentOrders = getAdjacentOrders(newOrdersDefinitionKeys, index, currentOrder)

            if (adjacentOrders.isEmpty()) {
                handleSingleNewOrder(index, sortedEpisodes, updatedEpisodesOrders)
            } else {
                handleAdjacentNewOrders(sortedEpisodes, adjacentOrders, currentOrder, updatedEpisodesOrders)
            }

            index.increase()
        }

        return updatedEpisodesOrders
    }

    private suspend fun ActionData.getSortedEpisodesByNewOrders(): MutableList<ChannelEpisodeOrder> {
        val maxOrder = newOrdersDefinition.keys.maxOrNull() ?: 0
        val episodesLimit = maxOrder + 2 // +2 to ensure we have episodes before and after

        val episodes = channelEpisodesRepository.findChannelEpisodesLimited(
            channelId,
            episodesLimit,
            ChannelOrderType.CUSTOM_ORDER
        )

        val sortedEpisodes = episodes
            .map { ChannelEpisodeOrder.from(it) }
            .sortedByDescending { it.episodeOrder }
            .toMutableList()

        // Remove updated episodes from original order
        sortedEpisodes.removeAll { newOrdersDefinition.values.contains(it.episodeId) }

        // Place each item in its new order with a random initial value
        newOrdersDefinition.forEach {
            sortedEpisodes.add(
                it.key,
                ChannelEpisodeOrder(
                    channelId = channelId,
                    episodeId = it.value,
                    _episodeOrder = ANY_INITIAL_ORDER
                )
            )
        }

        return sortedEpisodes
    }

    private fun getAdjacentOrders(
        newOrdersDefinitionKeys: List<IndexBaseZero>,
        index: Index,
        currentOrder: IndexBaseZero
    ): MutableSet<Int> {
        // If items changed to orders 0,1 and 2
        // 0 will be set as 'orderItemCalculator.calculate()'
        // 1,2 will be processed between 'orderItemCalculator.calculate()' and value from order 3
        var checkAdjacentOrders = currentOrder != 0

        val adjacentOrders = mutableSetOf<Int>()
        var offset = 1
        while (checkAdjacentOrders) {
            if (newOrdersDefinitionKeys.size <= index.value + 1) {
                break
            }

            val nextOrder = newOrdersDefinitionKeys[index.value + 1]
            val nextNewOrderIsAdjacent = nextOrder == currentOrder + offset
            if (nextNewOrderIsAdjacent) {
                val currentOrderAlreadyAdded = adjacentOrders.contains(currentOrder)
                if (!currentOrderAlreadyAdded) {
                    adjacentOrders.add(currentOrder)
                }
                adjacentOrders.add(nextOrder)
                index.increase()
                offset++
            } else {
                checkAdjacentOrders = false
            }
        }
        return adjacentOrders
    }

    private fun ActionData.handleSingleNewOrder(
        index: Index,
        sortedEpisodes: MutableList<ChannelEpisodeOrder>,
        updatedEpisodes: MutableList<ChannelEpisodeOrder>
    ) {
        val episodeOrderKey = newOrdersDefinition.keys.toList()[index.value]

        val episode = sortedEpisodes[episodeOrderKey]
        val newOrder = calculateOrder(sortedEpisodes, episodeOrderKey)
        episode.setOrder(newOrder)
        updatedEpisodes.add(episode)
    }

    private fun ActionData.handleAdjacentNewOrders(
        sortedEpisodes: MutableList<ChannelEpisodeOrder>,
        adjacentOrders: MutableSet<Int>,
        targetOrder: IndexBaseZero,
        updatedEpisodes: MutableList<ChannelEpisodeOrder>
    ) {
        val reducedSortedEpisodes = sortedEpisodes.toMutableList()

        // There are N element between next and before orders
        // Only left one item to calculate new order
        // Otherwise it will consider the ANY_INITIAL_ORDER from a new order
        adjacentOrders.forEachIndexed { idx, order ->
            val leaveFirstElementBetween = idx != 0
            if (leaveFirstElementBetween) {
                val adjacentEpisodeId = newOrdersDefinition[order]!!
                reducedSortedEpisodes.removeIf { episode -> episode.episodeId == adjacentEpisodeId }
            }
        }

        // If there are 2 items between 2000 and 1000 orders
        // the chunk will be (2000 - 1000) / (2 + 1) = 333
        val helper = calculateOrderHelper(targetOrder, reducedSortedEpisodes)
        val diff = helper.nextOrder - helper.previousOrder
        val chunk = diff / (adjacentOrders.size + 1)

        // and the final orders will be [2000, 1666, 1333, 1000]
        adjacentOrders.forEachIndexed { idx, i ->
            val updatedEpisode = ChannelEpisodeOrder(
                channelId = channelId,
                episodeId = newOrdersDefinition[i]!!,
                _episodeOrder = helper.nextOrder - (chunk * (idx + 1))
            )
            updatedEpisodes.add(updatedEpisode)
        }
    }

    private fun calculateOrder(episodes: List<ChannelEpisodeOrder>, targetOrder: Int): Long {
        // For order 0, use the orderItemCalculator to get the most recent order
        if (targetOrder == 0) {
            return orderItemCalculator.calculate()
        }
        val orderHelper = calculateOrderHelper(targetOrder, episodes)
        return (orderHelper.nextOrder + orderHelper.previousOrder) / 2
    }

    private fun calculateOrderHelper(targetOrder: Int, episodes: List<ChannelEpisodeOrder>): OrderHelper {
        // For other orders, calculate the average between the episode at next order and the before one
        val nextOrder = if (targetOrder < episodes.size) {
            episodes[targetOrder - 1].episodeOrder
        } else {
            // If target order is beyond the list, use the last episode's order
            episodes.lastOrNull()?.episodeOrder?.let { it - 1000 } ?: 0L
        }

        val previousOrder = if (targetOrder + 1 < episodes.size) {
            episodes[targetOrder + 1].episodeOrder
        } else {
            0L
        }

        return OrderHelper(nextOrder, previousOrder)
    }

    data class ActionData(
        val playerId: Long,
        val channelId: String,
        private var _newOrdersDefinition: Map<IndexBaseZero, String>
    ) {
        val newOrdersDefinition: Map<IndexBaseZero, String> get() = _newOrdersDefinition

        fun setNewOrdersDefinition(newDefinitions: Map<IndexBaseZero, String>) {
            _newOrdersDefinition = newDefinitions
        }
    }

    private data class OrderHelper(val nextOrder: Long, val previousOrder: Long)

    private class Index {
        private var _value = 0

        val value get() = _value

        fun increase() {
            _value++
        }
    }

    private companion object {
        const val ANY_INITIAL_ORDER = 12346L
    }
}

private typealias IndexBaseZero = Int
