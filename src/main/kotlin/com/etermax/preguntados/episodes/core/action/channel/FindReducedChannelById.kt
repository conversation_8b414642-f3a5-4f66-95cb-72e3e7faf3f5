package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.channel.ChannelReduced
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository

class FindReducedChannelById(private val repository: ChannelRepository) {
    suspend operator fun invoke(channelId: String): ChannelReduced? {
        val channel = repository.findById(channelId) ?: return null
        return ChannelReduced.from(channel)
    }
}
