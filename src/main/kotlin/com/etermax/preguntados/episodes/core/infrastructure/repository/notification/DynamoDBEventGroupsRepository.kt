package com.etermax.preguntados.episodes.core.infrastructure.repository.notification

import com.etermax.preguntados.episodes.core.domain.episode.notification.EventGroup
import com.etermax.preguntados.episodes.core.domain.episode.notification.EventGroupsRepository
import com.etermax.preguntados.episodes.core.domain.episode.notification.EventType
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import kotlinx.coroutines.future.await
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.ReturnValue
import software.amazon.awssdk.services.dynamodb.model.UpdateItemRequest

@DynamoDbBean
class DynamoDBEventGroupsRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<EventGroupsItem>
) : EventGroupsRepository, DynamoDBRepository<EventGroupsItem>(client, table) {

    override suspend fun find(episodeId: String, type: EventType): EventGroup? {
        return findItem(
            Key.builder().partitionValue(episodeId).sortValue(type.name).build()
        )?.to()
    }

    override suspend fun increment(episodeId: String, type: EventType, playerId: Long): EventGroup {
        val updateExpression = "SET #attr = if_not_exists(#attr, :start) + :increment, #playerId = :playerId"

        // Define the expression attribute names and values
        val expressionAttributeNames = mapOf("#attr" to "count", "#playerId" to "player_id")
        val expressionAttributeValues = mapOf(
            ":start" to AttributeValue.builder().n("0").build(),
            ":increment" to AttributeValue.builder().n("1").build(),
            ":playerId" to AttributeValue.builder().n(playerId.toString()).build()
        )

        // Build the update request
        val updateItemRequest = UpdateItemRequest.builder()
            .tableName(table.tableName())
            .key(
                mapOf(
                    "PK" to AttributeValue.builder().s(episodeId).build(),
                    "SK" to AttributeValue.builder().s(type.name).build()
                )
            )
            .updateExpression(updateExpression)
            .expressionAttributeNames(expressionAttributeNames)
            .expressionAttributeValues(expressionAttributeValues)
            .returnValues(ReturnValue.ALL_NEW)
            .build()

        // Execute the update
        val item = rawClient().updateItem(updateItemRequest).await()
        return table.tableSchema().mapToItem(item.attributes(), true).to()
    }

    override suspend fun save(episodeId: String, type: EventType, eventGroup: EventGroup) {
        val item = EventGroupsItem(
            episodeId = episodeId,
            playerId = eventGroup.playerId,
            eventType = type.name,
            count = eventGroup.count,
            groupSize = eventGroup.groupSize,
            lastResetDateEpochSeconds = eventGroup.lastResetDate?.toEpochSecond()
        )

        saveItem(item, EventGroupsItem::class.java)
    }
}
