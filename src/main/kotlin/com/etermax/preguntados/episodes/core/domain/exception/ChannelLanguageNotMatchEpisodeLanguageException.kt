package com.etermax.preguntados.episodes.core.domain.exception

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.exception.base.BusinessException

class ChannelLanguageNotMatchEpisodeLanguageException(channel: Channel, episode: Episode) :
    BusinessException(
        "Channel='${channel.id}' has language='${channel.language}'. " +
            "Episode='${episode.id}' has language='${episode.language}'"
    )
