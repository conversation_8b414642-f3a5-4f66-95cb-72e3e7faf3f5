package com.etermax.preguntados.episodes.core.domain.search.feed

import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language

data class OffsetLimit(val offset: Int, val limit: Int)

open class SourceRequest(
    val range: OffsetLimit,
    val userId: Long,
    val language: Language? = null,
    val country: Country? = null,
    val fetchCursor: FetchCursor? = null
) {
    open fun copy(range: OffsetLimit, fetchCursor: FetchCursor? = null) = SourceRequest(range, userId, language, country, fetchCursor)
}
