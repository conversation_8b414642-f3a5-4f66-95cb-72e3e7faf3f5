package com.etermax.preguntados.episodes.core.infrastructure.profile

import com.etermax.preguntados.episodes.core.domain.profile.PlayerFriendsService
import com.etermax.preguntados.episodes.core.infrastructure.http.resilientGet
import com.etermax.preguntados.episodes.core.infrastructure.profile.representation.PlayerIdsResponse
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import org.slf4j.LoggerFactory

class HttpPlayerFriendsService(
    private val client: HttpClient,
    private val resilienceBundle: EndpointResilienceBundle,
    private val p2AdminPassword: String
) : PlayerFriendsService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun findFollowedIds(playerId: Long): List<Long> {
        return try {
            client.resilientGet(
                urlString = URL.format(playerId),
                resilienceBundle = resilienceBundle,
                requestBuilder = {
                    contentType(ContentType.Application.Json)
                    header(ADMIN_PASS_HEADER, p2AdminPassword)
                    expectSuccess = true
                }
            ) {
                return@resilientGet makeResponse(it)
            }
        } catch (e: Exception) {
            logger.error("Cannot get followed for player $playerId", e)
            return emptyList()
        }
    }

    private suspend fun makeResponse(response: HttpResponse): List<Long> {
        if (response.status != HttpStatusCode.OK) {
            logger.info("Invalid status ${response.status} for content: $URL")
            return emptyList()
        }
        return response.body<PlayerIdsResponse>().userIds
    }

    private companion object {
        const val URL = "/api/users/%s/favorites-ids"
        const val ADMIN_PASS_HEADER = "god-password"
    }
}
