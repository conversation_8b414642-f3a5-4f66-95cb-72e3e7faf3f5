package com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update

import com.etermax.preguntados.episodes.core.domain.channel.ChannelEpisode
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class AssignChannelIdToEpisodeItem(
    @get:DynamoDbAttribute("episode_id") var episodeId: String = "",
    @get:DynamoDbAttribute(ChannelItemAttributes.CHANNEL_ID) var channelId: String? = null
) {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute("PK")
    var pk: String = buildPartitionKey(episodeId)

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute("SK")
    var sk: String = EPISODE_SK

    companion object {
        private const val EPISODE_PREFIX = "E#"
        const val EPISODE_SK = "EPISODE"

        fun buildPartitionKey(episodeId: String) = "$EPISODE_PREFIX$episodeId"

        fun from(channelEpisode: ChannelEpisode) = with(channelEpisode) {
            AssignChannelIdToEpisodeItem(
                episodeId = episodeId,
                channelId = channelId
            )
        }
    }
}
