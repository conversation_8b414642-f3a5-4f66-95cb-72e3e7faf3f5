package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.search.data.EmbeddingEpisodeData
import com.etermax.preguntados.episodes.core.infrastructure.search.data.EpisodeData
import org.opensearch.client.opensearch.core.MsearchResponse
import org.opensearch.client.opensearch.core.SearchResponse

open class BaseSearch(private val tokenizer: Tokenizer) {

    fun SearchResponse<EpisodeData>.toEpisodes(): List<Episode> {
        return hits().hits().mapNotNull {
            val data = it.source() as EpisodeData
            data.to()
        }
    }

    fun MsearchResponse<EpisodeData>.toEpisodesLists(): List<List<Episode>> {
        return this.responses()
            .map { it.result() }
            .map { it.toEpisodes() }
    }

    fun OpenSearchRequestFactory.queryByCountry(country: String?) = this.also {
        country?.let {
            shouldMatch(COUNTRY_FIELD, it)
        }
    }

    fun OpenSearchRequestFactory.filterByLanguage(language: String?) = this.also {
        language?.let {
            filterTerm(LANGUAGE_FIELD, it)
        }
    }

    fun OpenSearchRequestFactory.queryByName(name: String?) = this.also {
        name?.let {
            val prefixTerms = tokenizer.tokenizeForPrefixes(it)
            mustPrefixesFuzziness(NAME_FIELD, prefixTerms)
        }
    }

    fun OpenSearchRequestFactory.filterByType(type: EpisodeType?) = this.also {
        type?.let {
            filterTerm(TYPE_FIELD, it.name)
        }
    }

    fun OpenSearchRequestFactory.filterByOwner(ownerId: Long?) = this.also {
        ownerId?.let {
            filterTerm(OWNER_ID_FIELD, ownerId)
        }
    }

    fun OpenSearchRequestFactory.excludeEmbedding() = this.also {
        excludeSourceFields(EMBEDDING_FIELD)
    }

    fun OpenSearchRequestFactory.filterByStatus(status: EpisodeStatus?) = this.also {
        status?.let {
            filterTerm(STATUS_FIELD, it.name.lowercase())
        }
    }

    fun OpenSearchRequestFactory.filterNotByEpisodes(embeddings: List<EmbeddingEpisodeData>) = this.also {
        mustNotIds(
            embeddings.mapNotNull { data ->
                data.episodeId?.let { EpisodeItem.buildPartitionKey(it) }
            }
        )
    }

    fun OpenSearchRequestFactory.filterNotByIds(ids: List<String>) = this.also {
        mustNotIds(ids)
    }

    fun OpenSearchRequestFactory.filterByEmbeddings(embeddings: List<EmbeddingEpisodeData>, limit: Int) = this.also {
        val k = limit * embeddings.size
        embeddings.forEach { data ->
            shouldKnn(EMBEDDING_FIELD, data.embedding, k)
        }
    }

    fun OpenSearchRequestFactory.filterByEmbedding(embedding: EmbeddingEpisodeData, limit: Int) = this.also {
        shouldKnn(EMBEDDING_FIELD, embedding.embedding, limit)
    }

    internal companion object {
        const val EPISODE_ID_FIELD: String = "episode_id"
        const val TYPE_FIELD: String = "type"
        const val NAME_FIELD: String = "name"
        const val EMBEDDING_FIELD = "embedding"
        const val COUNTRY_FIELD: String = "country"
        const val LANGUAGE_FIELD: String = "language"
        const val STATUS_FIELD: String = "status"
        const val OWNER_ID_FIELD = "owner_id"
        const val CHANNEL_ID_FIELD = "channel_id"
        const val RATE_FIELD = "rate"
        const val QUALITY_FIELD = "quality"
        const val VIEWS_FIELD = "views"
        const val START_DATE_FIELD = "start_date"
    }
}
