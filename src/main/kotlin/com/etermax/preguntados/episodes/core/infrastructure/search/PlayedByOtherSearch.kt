package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.OpenSearchPlayedRepository

class PlayedByOtherSearch(
    private val playedRepository: OpenSearchPlayedRepository,
    private val episodeRepository: EpisodeRepository
) : Search {

    override suspend fun match(parameters: SearchParameters): Boolean {
        return parameters.sort == SortEpisode.EPISODES_PLAYED_FOR_OTHER_PROFILE
    }

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        val playerId = parameters.playerId ?: return emptyList()

        val offset = parameters.offset
        val limit = parameters.limit

        val ids = playedRepository.lastPlayedEpisodesIdsV2(playerId, offset, limit, onlyPublic = true)

        val episodesById = episodeRepository.findByIds(ids).associateBy { it.id }

        return ids.mapNotNull { episodesById[it.removePrefix("E#")] }
    }
}
