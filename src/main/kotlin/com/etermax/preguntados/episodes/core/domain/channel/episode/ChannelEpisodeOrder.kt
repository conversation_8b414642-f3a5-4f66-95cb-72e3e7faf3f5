package com.etermax.preguntados.episodes.core.domain.channel.episode

import com.etermax.preguntados.episodes.core.domain.channel.ChannelEpisode

data class ChannelEpisodeOrder(
    val channelId: String,
    val episodeId: String,
    private var _episodeOrder: Long
) {

    val episodeOrder: Long get() = _episodeOrder

    fun setOrder(order: Long) {
        _episodeOrder = order
    }

    companion object {
        fun from(episode: ChannelEpisode): ChannelEpisodeOrder {
            return with(episode) {
                ChannelEpisodeOrder(channelId, episodeId, episodeOrder)
            }
        }
    }
}
