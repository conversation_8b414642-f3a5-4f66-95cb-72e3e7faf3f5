package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.SearchChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelSearchFilters
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter
import org.slf4j.LoggerFactory

class SearchChannels(
    private val channelRepository: ChannelRepository,
    private val channelEpisodesRepository: ChannelEpisodesRepository,
    private val episodesRepository: EpisodeRepository,
    private val summaryService: SummaryService,
    private val paginationSize: Int
) {

    private val logger by lazy { LoggerFactory.getLogger(this::class.java) }

    suspend operator fun invoke(data: ActionData): PaginatedItems<SearchChannelSummary> {
        val channels = searchChannels(data)
        val episodesCoverByChannel = findCoversFrom(channels)
        return channels.toSearchSummaries(episodesCoverByChannel)
    }

    private suspend fun searchChannels(data: ActionData): PaginatedItems<Channel> {
        val filters = ChannelSearchFilters(data.profileOwnerId, data.onlyWithEpisodes)
        val pagination = PaginationFilter(paginationSize, data.lastEvaluatedKey)
        return channelRepository.search(filters, pagination)
    }

    private suspend fun findCoversFrom(channels: PaginatedItems<Channel>): Map<String, List<String>> {
        val orderedEpisodes = findEpisodesFrom(channels)
        return orderedEpisodes
            .groupBy { it.channelId!! }
            .mapValues { (_, episodes) -> episodes.map { it.cover } }
    }

    private suspend fun findEpisodesFrom(channels: PaginatedItems<Channel>): List<Episode> {
        val channelEpisodes = channelEpisodesRepository.findChannelEpisodesLimited(
            channels.items,
            episodesPerChannel = 3
        )
        val episodeIds = channelEpisodes.map { it.episodeId }
        val episodes = episodesRepository.findByIds(episodeIds)
        val episodeById = episodes.associateBy { it.id }
        return episodeIds.mapNotNull { id ->
            val episode = episodeById[id] ?: return@mapNotNull null
            if (episode.hasChannel) episode
            else {
                val channelId = channelEpisodes.find { it.episodeId == id }?.channelId
                logger.warn("Episode '$id' has no channelId but is linked to channel '$channelId'. Discarding from results.")
                null
            }
        }
    }

    private suspend fun PaginatedItems<Channel>.toSearchSummaries(episodesCoverByChannel: Map<String, List<String>>): PaginatedItems<SearchChannelSummary> {
        if (!this.hasItems()) return PaginatedItems(lastEvaluatedKey, listOf())

        val channels = this.items.toList()
        val summaries = summaryService.toChannelsSummary(channels)
        val searchSummaries = summaries.map {
            SearchChannelSummary.from(it, episodesCoverByChannel.getOrElse(it.id) { emptyList() })
        }
        return PaginatedItems(lastEvaluatedKey, searchSummaries)
    }

    data class ActionData(
        val playerId: Long,
        private val _profileOwnerId: Long?,
        val lastEvaluatedKey: String?
    ) {
        val profileOwnerId = _profileOwnerId ?: playerId

        val onlyWithEpisodes = playerId != profileOwnerId
    }
}
