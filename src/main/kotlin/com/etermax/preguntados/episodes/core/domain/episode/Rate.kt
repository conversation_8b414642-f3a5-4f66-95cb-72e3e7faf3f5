package com.etermax.preguntados.episodes.core.domain.episode

data class Rate(
    private var _likes: Long = 0,
    private var _dislikes: Long = 0
) {

    val likes get() = _likes
    val dislikes get() = _dislikes

    fun like() = _likes++
    fun dislike() = _dislikes++
    fun decrementLikes() = if (_likes > 0) _likes-- else 0
    fun decrementUnlikes() = if (_dislikes > 0) _dislikes-- else 0

    fun calculate(): Double {
        val totalVotes = likes + dislikes
        return if (totalVotes > 0) (likes / totalVotes.toDouble()) * 100 else 0.0
    }

    companion object {
        fun empty() = Rate()
    }

    enum class Type {
        LIKE, DISLIKE
    }
}
