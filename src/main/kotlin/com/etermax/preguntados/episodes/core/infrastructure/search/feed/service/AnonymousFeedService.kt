package com.etermax.preguntados.episodes.core.infrastructure.search.feed.service

import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.*
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.layout.EightByTwoFeedSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.layout.FourByOneFeedSource
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.slf4j.LoggerFactory

class AnonymousFeedService(
    val osClient: OpenSearchAsyncClient,
    val episodesIndexName: String,
    val channelIndexName: String,
    val episodesRepository: EpisodeRepository,
    val channelRepository: ChannelRepository
) : FeedService {
    private val codec = CursorCodec()

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun fetch(feedRequest: FeedRequest): FeedResponse {
        val fetchCursor = codec.decode(feedRequest.paginationToken, feedRequest.debug)

        val isFirstPage = feedRequest.paginationToken.isNullOrEmpty()
        val layout = feedRequest.layout

        val sourceRequest = SourceRequest(
            range = OffsetLimit(0, feedRequest.limit),
            userId = ANONYMOUS_USER_ID,
            language = feedRequest.language,
            country = feedRequest.country,
            fetchCursor = fetchCursor
        )

        logger.debug(
            "[FEED] Creating filter for fetchCursor? {}, token: {}, cursor: {}",
            isFirstPage,
            feedRequest.paginationToken,
            fetchCursor
        )

        val source = buildFTUEFeed(layout)

        val sourceResponse = source.fetch(sourceRequest)

        return if (sourceResponse.fetchCursor != null && sourceResponse.fetchCursor.exhausted) {
            logger.info("Feed exhausted, returning empty pagination token")
            FeedResponse(sourceResponse.items, null)
        } else {
            FeedResponse(sourceResponse.items, codec.encode(sourceResponse.fetchCursor, feedRequest.debug))
        }
    }

    private fun buildFTUEFeed(layout: String?): Source<Any> {
        val episodes = HighQualityEpisodeSource(osClient, episodesIndexName)
        val channels = QualityChannelSource(osClient, channelIndexName)

        return buildLayoutFeed(
            layout,
            primarySource = EpisodeHydratorSource(episodes, episodesRepository),
            secondarySource = ChannelHydratorSource(channels, channelRepository)
        )
    }

    private fun <E, C> buildLayoutFeed(
        layout: String?,
        primarySource: Source<E>,
        secondarySource: Source<C>
    ): Source<Any> {
        return when {
            layout == null -> FourByOneFeedSource(primarySource, secondarySource)
            layout.startsWith("C3") -> FourByOneFeedSource(primarySource, secondarySource)
            layout.startsWith("C6") -> EightByTwoFeedSource(primarySource, secondarySource)
            else -> FourByOneFeedSource(primarySource, secondarySource)
        }
    }

    private companion object {
        const val ANONYMOUS_USER_ID: Long = -1
    }
}
