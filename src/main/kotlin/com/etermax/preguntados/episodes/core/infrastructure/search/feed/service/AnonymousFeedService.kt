package com.etermax.preguntados.episodes.core.infrastructure.search.feed.service

import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedRequest
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedResponse
import com.etermax.preguntados.episodes.core.domain.search.feed.FeedService
import com.etermax.preguntados.episodes.core.domain.search.feed.OffsetLimit
import com.etermax.preguntados.episodes.core.domain.search.feed.Source
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceRequest
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.ChannelHydratorSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.CursorCodec
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.EpisodeHydratorSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.HighQualityEpisodeSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.HighQualityRecentEpisodeSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.QualityChannelSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.ScoreChannelSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.layout.EightByTwoFeedSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.layout.FourByOneFeedSource
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.slf4j.LoggerFactory

class AnonymousFeedService(
    val osClient: OpenSearchAsyncClient,
    val episodesIndexName: String,
    val channelIndexName: String,
    val episodesRepository: EpisodeRepository,
    val channelRepository: ChannelRepository,
    val configuration: DefaultFeedServiceConfiguration
) : FeedService {
    private val codec = CursorCodec()

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun fetch(feedRequest: FeedRequest): FeedResponse {
        val fetchCursor = codec.decode(feedRequest.paginationToken, feedRequest.debug)

        val isFirstPage = feedRequest.paginationToken.isNullOrEmpty()
        val layout = feedRequest.layout

        val sourceRequest = SourceRequest(
            range = OffsetLimit(0, feedRequest.limit),
            userId = ANONYMOUS_USER_ID,
            language = feedRequest.language,
            country = feedRequest.country,
            fetchCursor = fetchCursor
        )

        logger.debug(
            "[FEED] Creating filter for fetchCursor? {}, token: {}, cursor: {}",
            isFirstPage,
            feedRequest.paginationToken,
            fetchCursor
        )

        val source = buildFTUEFeed(layout)

        val sourceResponse = source.fetch(sourceRequest)

        return if (sourceResponse.fetchCursor != null && sourceResponse.fetchCursor.exhausted) {
            logger.info("Feed exhausted, returning empty pagination token")
            FeedResponse(sourceResponse.items, null)
        } else {
            FeedResponse(sourceResponse.items, codec.encode(sourceResponse.fetchCursor, feedRequest.debug))
        }
    }

    private fun getHighQualitySource(): Source<String> {
        return if (configuration.isSortedByQuality) {
            HighQualityEpisodeSource(osClient, episodesIndexName)
        } else {
            HighQualityRecentEpisodeSource(
                client = osClient,
                episodesIndexName = episodesIndexName,
                requiredLikesForTopRated = configuration.requiredLikesForHighQuality,
                requiredViewsForTopRated = configuration.requiredViewsForHighQuality
            )
        }
    }

    private fun buildFTUEFeed(layout: String?): Source<Any> {
        val episodes = getHighQualitySource()

        val channels = if (configuration.isSortedByQuality) {
            QualityChannelSource(osClient, channelIndexName)
        } else {
            ScoreChannelSource(osClient, channelIndexName)
        }

        return buildLayoutFeed(
            layout,
            primarySource = EpisodeHydratorSource(episodes, episodesRepository),
            secondarySource = ChannelHydratorSource(channels, channelRepository)
        )
    }

    private fun <E, C> buildLayoutFeed(
        layout: String?,
        primarySource: Source<E>,
        secondarySource: Source<C>
    ): Source<Any> {
        return when {
            layout == null -> FourByOneFeedSource(primarySource, secondarySource)
            layout.startsWith("C3") -> FourByOneFeedSource(primarySource, secondarySource)
            layout.startsWith("C6") -> EightByTwoFeedSource(primarySource, secondarySource)
            else -> FourByOneFeedSource(primarySource, secondarySource)
        }
    }

    private companion object {
        const val ANONYMOUS_USER_ID: Long = -1
    }
}
