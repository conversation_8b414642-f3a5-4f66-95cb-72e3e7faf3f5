package com.etermax.preguntados.episodes.core.infrastructure.repository.rate

import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.episode.rate.RateRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.rate.tables.RateItem
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.reactive.asFlow
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest

class DynamoDBRateRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<RateItem>
) : RateRepository, DynamoDBRepository<RateItem>(client, table) {

    override suspend fun save(playerId: Long, episodeId: String, liked: Boolean) {
        val writeBatch = makeWriteBatch(RateItem.from(playerId, episodeId, liked), RateItem::class.java)
        addItemsBulkWithRetry(listOf(writeBatch), RateItem::class.java)
    }

    override suspend fun findBy(playerId: Long, episodeId: String): Rate.Type? {
        val key = Key.builder()
            .partitionKey(episodeId)
            .sortKeyProgress(playerId)
            .build()
        val conditional = QueryConditional
            .keyEqualTo(key)
        val request = QueryEnhancedRequest.builder()
            .queryConditional(conditional)
            .build()
        return table
            .query(request)
            .items()
            .asFlow()
            .toList()
            .firstOrNull()
            ?.to()
    }

    override suspend fun delete(playerId: Long, episodeId: String) {
        val key = Key.builder()
            .partitionKey(episodeId)
            .sortKeyProgress(playerId)
            .build()
        deleteItem(key)
    }

    private fun Key.Builder.partitionKey(episodeId: String): Key.Builder {
        partitionValue(RateItem.buildPartitionKey(episodeId))
        return this
    }

    private fun Key.Builder.sortKeyProgress(playerId: Long): Key.Builder {
        sortValue(RateItem.buildSortedKey(playerId))
        return this
    }
}
