package com.etermax.preguntados.episodes.core.action.challenge.gameplay

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService.ChallengeContext
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.finish.FinishEpisodeDetails
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingService
import kotlinx.coroutines.coroutineScope
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class FinishChallenge(
    private val challengeService: ChallengeService,
    private val summaryService: SummaryService,
    private val rankingService: RankingService
) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): FinishEpisodeDetails = coroutineScope {
        logger.info("Player ${actionData.playerId} finished challenge ${actionData.challengeId}")

        val context = getChallengeContext(actionData)

        finishChallenge(actionData.challengeId, actionData.playerId, context)

        val episodeSummary = getEpisodeSummary(context)
        val ranking = getChallengeRanking(actionData, context)

        FinishEpisodeDetails(
            episodeSummary = episodeSummary,
            ranking = ranking,
            rate = null,
            rankingWithFriends = null
        )

        // TODO summary del challenge
        // TODO sugerencia de nuevos challenge
    }

    private suspend fun getChallengeContext(actionData: ActionData): ChallengeContext {
        return challengeService.getChallengeContext(actionData.challengeId, actionData.playerId)
    }

    private suspend fun finishChallenge(challengeId: String, playerId: Long, context: ChallengeContext) {
        challengeService.finishChallenge(challengeId, playerId, context)
    }

    private suspend fun getChallengeRanking(actionData: ActionData, context: ChallengeContext): DeliveryRanking {
        return rankingService.findRanking(actionData.playerId, Domain(context.scope))
    }

    private suspend fun getEpisodeSummary(context: ChallengeContext): EpisodeSummary {
        return summaryService.toEpisodeSummary(context.episode)!!
    }

    data class ActionData(
        val playerId: Long,
        val challengeId: String
    )
}
