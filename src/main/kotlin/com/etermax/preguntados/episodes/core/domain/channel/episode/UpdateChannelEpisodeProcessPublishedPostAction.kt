package com.etermax.preguntados.episodes.core.domain.channel.episode

import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostAction
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import com.etermax.preguntados.episodes.core.domain.exception.ChannelLanguageNotMatchEpisodeLanguageException
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import org.slf4j.LoggerFactory

class UpdateChannelEpisodeProcessPublishedPostAction(
    private val channelRepository: ChannelRepository,
    private val channelEpisodesService: ChannelEpisodesService,
    private val unpublishedEpisodesRepository: ChannelUnpublishedEpisodesRepository
) : UpdateChannelEpisodePostAction {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun applies(
        oldEpisode: Episode,
        updateData: UpdateChannelEpisodePostActionData
    ): Boolean {
        if (updateData.status == null) {
            logger.info("Not applying for episode='${oldEpisode.id}'. New status is null.")
            return false
        }

        if (oldEpisode.status == updateData.status) {
            logger.info("Not applying for episode='${oldEpisode.id}'. New status is '${updateData.status}' is same as actual status")
            return false
        }

        if (updateData.status != EpisodeStatus.PUBLISHED) {
            logger.info("Not applying for episode='${oldEpisode.id}'. New status is '${updateData.status}'. Only apply for PUBLISHED")
            return false
        }

        if (!oldEpisode.hasChannel && updateData.channelId == null) {
            return false
        }

        return true
    }

    override suspend fun execute(
        oldEpisode: Episode,
        updateData: UpdateChannelEpisodePostActionData,
        alreadyCheckIfApplies: Boolean
    ) {
        runCatching {
            if (!alreadyCheckIfApplies && !applies(oldEpisode, updateData)) return

            removeUnpublished(updateData, oldEpisode)

            val channelId = updateData.channelId ?: oldEpisode.channelId ?: return
            val channel = channelRepository.findById(channelId)

            if (channel == null) {
                removeChannelFromEpisode(oldEpisode.id)

                if (channelId.isNotEmpty()) {
                    val error = ChannelNotFoundException(channelId)
                    logger.warn("Removing episode='${oldEpisode.id}' from channel='$channelId'.", error)
                }

                return
            }

            if (channel.language != null && channel.language != oldEpisode.language) {
                val error = ChannelLanguageNotMatchEpisodeLanguageException(channel, oldEpisode)
                logger.warn("Removing episode='${oldEpisode.id}' from channel='$channelId'.", error)
                removeChannelFromEpisode(oldEpisode.id)
                return
            }

            channelEpisodesService.addEpisodeToChannel(channel, oldEpisode)
        }.onFailure {
            logger.error("Fail to add episode='${oldEpisode.id}' to channel='${updateData.channelId}'", it)
        }
    }

    private suspend fun removeChannelFromEpisode(episodeId: String) {
        channelEpisodesService.removeChannelFromEpisode(episodeId)
    }

    private suspend fun removeUnpublished(updateData: UpdateChannelEpisodePostActionData, oldEpisode: Episode) {
        if (!updateData.channelId.isNullOrEmpty()) {
            removeUnpublished(updateData.channelId, oldEpisode)
        }

        if (oldEpisode.channelId != null) {
            removeUnpublished(oldEpisode.channelId, oldEpisode)
        }
    }

    private suspend fun removeUnpublished(channelId: String, oldEpisode: Episode) {
        val item = ChannelUnpublishedEpisode(channelId, oldEpisode.id)
        unpublishedEpisodesRepository.delete(item)
    }
}
