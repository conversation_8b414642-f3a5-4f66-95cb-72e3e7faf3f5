package com.etermax.preguntados.episodes.core.infrastructure.search.service

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.search.RecentEpisodeRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.search.OpenSearchRequestFactory
import com.etermax.preguntados.episodes.core.infrastructure.search.v2.OpenSearchEpisodeItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.launch
import org.opensearch.client.json.JsonData
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.Time
import org.opensearch.client.opensearch.core.ScrollRequest
import org.opensearch.client.opensearch.core.SearchResponse
import org.slf4j.LoggerFactory
import java.time.temporal.ChronoUnit
import kotlin.time.ExperimentalTime
import kotlin.time.measureTime

@OptIn(ExperimentalTime::class)
class RecentEpisodeSearchService(
    private val client: OpenSearchAsyncClient,
    private val episodesIndexName: String,
    private val recentEpisodeRepository: RecentEpisodeRepository,
    private val clock: Clock
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    suspend fun search(language: Language?, country: Country?, offset: Int, limit: Int): List<String> {
        return recentEpisodeRepository.find(language?.name, offset, limit)
            ?: fetchAndUpdateCache(language, country)
    }

    private suspend fun fetchAndUpdateCache(language: Language?, country: Country?): List<String> {
        logger.info("Cache recent episodes for language {}", language)
        return buildResponse(language, country)?.let { response ->
            val episodesId = response.ids().shuffled()
            if (episodesId.isNotEmpty()) {
                recentEpisodeRepository.save(language?.name, episodesId)
                buildCache(language, response)
            }
            logger.info("Retrieve {} elements before cache", episodesId.size)
            episodesId
        } ?: emptyList()
    }

    private suspend fun buildResponse(language: Language?, country: Country?): SearchResponse<OpenSearchEpisodeItem>? {
        val osRequest = OpenSearchRequestFactory(episodesIndexName, 0, 50)
            .filterByLanguage(language)
            .filterByType(EpisodeType.PUBLIC)
            .filterByStatus(EpisodeStatus.PUBLISHED)
            .filterLast(sevenDaysAgo())
            .queryByCountry(country)
            .includeEpisodeIdOnly()
            .useScroll("5s")
            .build()
        try {
            return client.search(osRequest, OpenSearchEpisodeItem::class.java).await()
        } catch (ex: Exception) {
            logger.error("Could not cache recent episodes for language: $language and country: $country", ex)
            return null
        }
    }

    private fun buildCache(language: Language?, response: SearchResponse<OpenSearchEpisodeItem>) {
        CoroutineScope(Dispatchers.IO).launch {
            val cacheTime = measureTime {
                if (recentEpisodeRepository.isProcessing(language?.name)) {
                    logger.info("Cache is processing for language {}", language)
                    return@launch
                }

                val episodesId = mutableListOf<String>()
                var scrollId = response.scrollId()
                var currentResponse = response
                while (true) {
                    val hits = currentResponse.ids()
                    if (hits.isEmpty() || scrollId == null) break

                    episodesId += hits

                    val scrollRequest = ScrollRequest.Builder()
                        .scrollId(scrollId)
                        .scroll(Time.Builder().time("5s").build())
                        .build()
                    currentResponse = client.scroll(scrollRequest, OpenSearchEpisodeItem::class.java).await()
                    scrollId = currentResponse.scrollId()
                }

                if (scrollId != null) {
                    client.clearScroll { it.scrollId(scrollId) }
                }
                recentEpisodeRepository.save(language?.name, episodesId.shuffled())
                logger.info("Cached {} elements for language {}", episodesId.size, language)
            }
            logger.info("Cache time: $cacheTime ms")
        }
    }

    private fun sevenDaysAgo(): Long = clock.now().minus(7, ChronoUnit.DAYS).toMillis()

    private fun OpenSearchRequestFactory.includeEpisodeIdOnly() = this.also {
        includeSourceFields(EpisodeItemAttributes.EPISODE_ID)
    }

    private fun OpenSearchRequestFactory.filterByStatus(status: EpisodeStatus?) = this.also {
        status?.let { filterTerm(EpisodeItemAttributes.STATUS, it.name.lowercase()) }
    }

    private fun OpenSearchRequestFactory.filterByType(type: EpisodeType?) = this.also {
        type?.let { filterTerm(EpisodeItemAttributes.TYPE, it.name) }
    }

    private fun OpenSearchRequestFactory.filterByLanguage(language: Language?) = this.also {
        language?.let { filterTerm(EpisodeItemAttributes.LANGUAGE, it.name) }
    }

    private fun OpenSearchRequestFactory.queryByCountry(country: Country?) = this.also {
        country?.let { shouldTerm(EpisodeItemAttributes.COUNTRY, it.name) }
    }

    private fun OpenSearchRequestFactory.filterLast(cutoffMillis: Long) = this.also {
        filterRange(EpisodeItemAttributes.START_DATE) { it.gte(JsonData.of(cutoffMillis)) }
    }

    private fun SearchResponse<OpenSearchEpisodeItem>.ids(): List<String> {
        return hits().hits().mapNotNull { it.source()?.episodeId }
    }
}
