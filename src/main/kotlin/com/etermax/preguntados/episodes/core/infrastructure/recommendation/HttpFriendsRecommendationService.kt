package com.etermax.preguntados.episodes.core.infrastructure.recommendation

import com.etermax.preguntados.episodes.core.domain.episode.recommendation.FriendsEpisode
import com.etermax.preguntados.episodes.core.domain.episode.recommendation.FriendsRecommendationService
import com.etermax.preguntados.episodes.core.infrastructure.http.resilientGet
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import com.etermax.preguntados.external.services.core.infrastructure.http.safeReceive
import io.ktor.client.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.slf4j.LoggerFactory

class HttpFriendsRecommendationService(
    private val client: HttpClient,
    private val resilienceBundle: EndpointResilienceBundle
) : FriendsRecommendationService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun recommend(playerId: Long): List<FriendsEpisode> {
        return try {
            client.resilientGet(
                urlString = URL.format(playerId),
                resilienceBundle = resilienceBundle,
                requestBuilder = {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    expectSuccess = true
                }
            ) {
                return@resilientGet makeResponse(it)
            }
        } catch (e: Exception) {
            logger.error("Cannot get friends recommendations for player $playerId", e)
            return emptyList()
        }
    }

    private suspend fun makeResponse(response: HttpResponse): List<FriendsEpisode> {
        if (response.status != HttpStatusCode.OK) {
            logger.warn("Friends recommendations returned invalid status ${response.status}")
            return emptyList()
        }

        return response.safeReceive<FriendsRecommendationsResponse>().to()
    }

    private companion object {
        const val URL = "ds-api-episodes-friends-recommendation/api/episodes-friends-recommendation/%s"
    }
}

@Serializable
data class FriendsRecommendationsResponse(
    @SerialName("recommendation") val recommendations: List<FriendsRecommendationResponse>
) {
    fun to() = recommendations.map { it.to() }
}

@Serializable
data class FriendsRecommendationResponse(
    @SerialName("episode_id") val episodeId: String,
    @SerialName("user_ids") val friends: List<Long>
) {
    fun to() = FriendsEpisode(episodeId, friends)
}
