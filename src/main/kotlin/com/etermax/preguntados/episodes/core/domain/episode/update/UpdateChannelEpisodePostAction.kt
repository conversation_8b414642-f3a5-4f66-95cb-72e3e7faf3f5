package com.etermax.preguntados.episodes.core.domain.episode.update

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus

interface UpdateChannelEpisodePostAction {
    fun applies(oldEpisode: Episode, updateData: UpdateChannelEpisodePostActionData): Boolean

    suspend fun execute(
        oldEpisode: Episode,
        updateData: UpdateChannelEpisodePostActionData,
        alreadyCheckIfApplies: Boolean = false
    )
}

data class UpdateChannelEpisodePostActionData(val status: EpisodeStatus?, val channelId: String? = null)
