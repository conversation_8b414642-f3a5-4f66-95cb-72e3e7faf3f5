package com.etermax.preguntados.episodes.core.domain.quality.service

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelScoreRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.quality.QualityService
import com.etermax.preguntados.episodes.core.domain.quality.likerate.EpisodeLikeRateAvgCalculator
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class DefaultQualityService(
    private val channelEpisodesRepository: ChannelEpisodesRepository,
    private val episodeRepository: EpisodeRepository,
    private val channelRepository: ChannelRepository,
    private val episodeLikeRateAvgCalculator: EpisodeLikeRateAvgCalculator,
    private val channelScoreRepository: ChannelScoreRepository,
    private val minimumRates: Int,
    private val minimumViews: Int
) : QualityService {

    val logger: Logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun calculateChannelScore(episodeBeforeUpdate: Episode, episodeAfterUpdate: Episode) {
        runCatching {
            if (hasTheSameRateAndNotReachViewsThresholds(episodeBeforeUpdate, episodeAfterUpdate)) {
                logger.info("[QUALITY] Episode ${episodeAfterUpdate.id} has the same like rates & views are not reached. Skipping score update for channel id [${episodeAfterUpdate.channelId}].")
                return@runCatching
            }
            if (hasFallenBelowRateOrViewThresholds(episodeBeforeUpdate, episodeAfterUpdate)) {
                episodeAfterUpdate.channelId?.apply {
                    logInitialEpisodeStates(
                        episodeBeforeUpdate = episodeBeforeUpdate,
                        episodeAfterUpdate = episodeAfterUpdate
                    )
                    processScore(channelId = this, hasJustFallenBelowThresholds = true)
                } ?: run {
                    logger.info("[QUALITY] Episode ${episodeAfterUpdate.id} does not belong to any channel. Skipping score update for channel id [${episodeAfterUpdate.channelId}].")
                }
            } else {
                episodeAfterUpdate.channelId?.apply {
                    logInitialEpisodeStates(
                        episodeBeforeUpdate = episodeBeforeUpdate,
                        episodeAfterUpdate = episodeAfterUpdate
                    )
                    processScore(channelId = this)
                } ?: run {
                    logger.info("[QUALITY] Episode ${episodeAfterUpdate.id} does not belong to any channel. Skipping score update for channel id [${episodeAfterUpdate.channelId}].")
                }
            }
        }.onFailure {
            logger.error(
                "[QUALITY] Channel ${episodeAfterUpdate.channelId} score updated with error for episode ${episodeAfterUpdate.id}: ${it.message}",
                it
            )
        }
    }

    override suspend fun calculateChannelScore(channelId: String, episodes: List<Episode>) {
        runCatching {
            if (episodes.isEmpty()) {
                logger.info("[QUALITY] No episodes provided for score calculation. Skipping score update for channel id [$channelId].")
                return@runCatching
            }
            if (thereIsAnyEpisodeReachRatesOrViewThresholds(episodes)) {
                logger.info("[QUALITY] Processing channel score for channel $channelId with ${episodes.size} episodes.")
                processScore(channelId = channelId)
            } else {
                logger.info("[QUALITY] No episodes meet the rate or view thresholds for channel $channelId. Skipping score update.")
            }
        }.onFailure {
            logger.error(
                "[QUALITY] Error processing channel score for channel $channelId, episode size ${episodes.size}: ${it.message}",
                it
            )
        }
    }

    /**
     * ---------------
     * Private methods:
     * ---------------
     */

    private suspend fun processScore(channelId: String, hasJustFallenBelowThresholds: Boolean = false) {
        logger.info("[QUALITY] Processing score for channel $channelId with hasJustFallenBelowThresholds = $hasJustFallenBelowThresholds")

        val episodeIds = channelEpisodesRepository.findAllEpisodeIdsFrom(channelId)
        val episodes = episodeRepository.findByIds(episodeIds)
        val validEpisodes = filterEpisodesByRateOrViewsThreshold(episodes)

        if (validEpisodes.isEmpty()) {
            // If no valid episodes and has just fallen bellow thresholds, set score to 0 (clear score to channel)
            if (hasJustFallenBelowThresholds) {
                logger.info("[QUALITY] No valid episodes found for channel $channelId and just has fallen bellow thresholds. Setting score to 0.")
                val newScore = 0
                val channelUpdated = updateChannelScore(channelId, newScore)
                logger.info("[QUALITY] Channel $channelId score updated to [ ${channelUpdated.statistics.score} ] based on no valid episodes.")
                return
            } else {
                // If no valid episodes and not has just fallen bellow thresholds, skip score update
                logger.info("[QUALITY] No valid episodes found for channel $channelId. Skipping score update.")
                return
            }
        }

        logger.info("[QUALITY] valid episodes found for channel $channelId -> ${validEpisodes.size}")
        val newScore = episodeLikeRateAvgCalculator.calculateAverage(channelId = channelId, validEpisodes)
        val channelUpdated = updateChannelScore(channelId, newScore)
        logger.info("[QUALITY] Channel $channelId score updated to [ ${channelUpdated.statistics.score} ] based on ${validEpisodes.size} valid episodes.")
    }

    private suspend fun updateChannelScore(
        channelId: String,
        newScore: Int
    ): Channel {
        val channel = channelRepository.findById(channelId = channelId)
            ?: throw IllegalArgumentException("[QUALITY] Channel with id $channelId not found")
        val channelUpdated = channel.updateScore(newScore)
        channelScoreRepository.updateScore(channelId = channelUpdated.id, score = channelUpdated.statistics.score)
        return channelUpdated
    }

    private fun filterEpisodesByRateOrViewsThreshold(episodes: List<Episode>) =
        episodes.filter { episode -> hasReachRateOrViewThresholds(episode) }

    private fun hasReachRateOrViewThresholds(episode: Episode): Boolean {
        val totalRates = episode.rate.likes + episode.rate.dislikes
        return totalRates >= minimumRates || episode.views >= minimumViews
    }

    private fun hasFallenBelowRateOrViewThresholds(
        episodeBeforeUpdate: Episode,
        episodeAfterUpdate: Episode
    ) = hasReachRateOrViewThresholds(episodeBeforeUpdate) && !hasReachRateOrViewThresholds(episodeAfterUpdate)

    private fun hasTheSameRateAndNotReachViewsThresholds(
        episodeBeforeUpdate: Episode,
        episodeAfterUpdate: Episode
    ): Boolean {
        return episodeBeforeUpdate.rate.likes == episodeAfterUpdate.rate.likes &&
            episodeBeforeUpdate.rate.dislikes == episodeAfterUpdate.rate.dislikes &&
            episodeAfterUpdate.views < minimumViews
    }

    private fun thereIsAnyEpisodeReachRatesOrViewThresholds(episodes: List<Episode>): Boolean {
        return episodes.any { episode -> hasReachRateOrViewThresholds(episode) }
    }

    private fun logInitialEpisodeStates(episodeBeforeUpdate: Episode, episodeAfterUpdate: Episode) {
        logger.info(
            "[QUALITY] Calculating channel score. Episode id ${episodeBeforeUpdate.id} Channel id ${episodeAfterUpdate.channelId} - before update: [ " +
                "rate likes ${episodeBeforeUpdate.rate.likes} - " +
                "rate dislikes ${episodeBeforeUpdate.rate.dislikes} - " +
                "views ${episodeBeforeUpdate.views} - " +
                "channel ${episodeBeforeUpdate.channelId} -" +
                " ]" +
                " & Episode after update:" +
                "[ " +
                "rate likes ${episodeAfterUpdate.rate.likes} - " +
                "rate dislikes ${episodeAfterUpdate.rate.dislikes} - " +
                "views ${episodeAfterUpdate.views} - " +
                "channel ${episodeAfterUpdate.channelId} -" +
                " ]"
        )
    }
}
