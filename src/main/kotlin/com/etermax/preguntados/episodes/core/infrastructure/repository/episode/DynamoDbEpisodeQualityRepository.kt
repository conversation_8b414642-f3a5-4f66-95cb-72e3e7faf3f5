package com.etermax.preguntados.episodes.core.infrastructure.repository.episode

import com.etermax.preguntados.episodes.core.domain.episode.delete.BlackListRecommendationService
import com.etermax.preguntados.episodes.core.domain.episode.update.QualityRepository
import com.etermax.preguntados.episodes.core.domain.quality.Quality
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.UpdateEpisodeQualityItem
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Expression
import software.amazon.awssdk.enhanced.dynamodb.model.IgnoreNullsMode
import software.amazon.awssdk.enhanced.dynamodb.model.TransactUpdateItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest
import software.amazon.awssdk.services.dynamodb.model.TransactionCanceledException

class DynamoDbEpisodeQualityRepository(
    private val table: DynamoDbAsyncTable<UpdateEpisodeQualityItem>,
    private val client: DynamoDbEnhancedAsyncClient,
    private val blackListRecommendationService: BlackListRecommendationService
) : QualityRepository {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun updateQuality(qualities: List<Quality>) {
        qualities.chunked(25).forEach { chunk ->
            try {
                val request = buildTransactionRequest(chunk)
                client.transactWriteItems(request.build()).await()
            } catch (e: TransactionCanceledException) {
                handleTransactionCancellation(e, chunk)
            } catch (e: Exception) {
                logger.error("Unexpected error updating episodes quality: ${e.message}", e)
            }
        }
    }

    private suspend fun handleTransactionCancellation(
        exception: TransactionCanceledException,
        originalChunk: List<Quality>
    ) {
        val reasons = exception.cancellationReasons()
        val failedIndexes = reasons.mapIndexedNotNull { index, reason ->
            if (reason.code() == CONDITIONAL_CHECK_FAILED) index else null
        }

        if (failedIndexes.isEmpty()) {
            logger.error("Unexpected error updating quality but no for $CONDITIONAL_CHECK_FAILED reasons.", exception)
            return
        }

        handleFailedEpisodes(failedIndexes, originalChunk)

        val retryItems = originalChunk.filterIndexed { index, _ -> index !in failedIndexes }
        if (retryItems.isNotEmpty()) {
            logger.info("Retrying transaction with remaining ${retryItems.size} items...")
            try {
                val retryRequest = buildTransactionRequest(retryItems)
                client.transactWriteItems(retryRequest.build()).await()
            } catch (e: Exception) {
                logger.error("Retry failed: ${e.message}", e)
            }
        }
    }

    private fun handleFailedEpisodes(failedIndexes: List<Int>, originalChunk: List<Quality>) {
        val failedEpisodes = failedIndexes.map { originalChunk[it].id }
        logger.warn("Episodes skip update quality due to $CONDITIONAL_CHECK_FAILED: $failedEpisodes")
        CoroutineScope(Dispatchers.IO).launch {
            blackListRecommendationService.deleteEpisodes(failedEpisodes)
        }
    }

    private fun buildTransactionRequest(list: List<Quality>): TransactWriteItemsEnhancedRequest.Builder {
        val request = TransactWriteItemsEnhancedRequest.builder()

        list.forEach {
            val item = UpdateEpisodeQualityItem(it.id, it.quality)
            val transactItem = TransactUpdateItemEnhancedRequest
                .builder(UpdateEpisodeQualityItem::class.java)
                .item(item)
                .ignoreNullsMode(IgnoreNullsMode.DEFAULT)
                .conditionExpression(CONDITION)
                .build()

            request.addUpdateItem(table, transactItem)
        }

        return request
    }

    private companion object {
        const val CONDITIONAL_CHECK_FAILED = "ConditionalCheckFailed"
        val CONDITION: Expression? = Expression.builder()
            .expression("attribute_exists(${EpisodeItemAttributes.EPISODE_ID})")
            .build()
    }
}
