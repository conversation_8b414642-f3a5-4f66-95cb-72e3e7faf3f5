package com.etermax.preguntados.episodes.core.infrastructure.repository.episode

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.NewEpisode
import com.etermax.preguntados.episodes.core.domain.episode.NewEpisodeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.persistence.NewEpisodePersistence
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.time.Duration

class RedisNewEpisodeRepository(
    private val redis: RedisAsyncCommands<String, String>,
    private val ttl: Duration,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : NewEpisodeRepository {

    private val jsonMapper = Json { ignoreUnknownKeys = true }

    override suspend fun add(playerId: Long, episode: Episode) {
        return withContext(dispatcher) {
            redis.sadd(getKey(playerId), jsonMapper.encodeToString(NewEpisodePersistence.from(episode))).await()
            redis.expire(getKey(playerId), ttl)
        }
    }

    override suspend fun find(playerId: Long): List<NewEpisode> {
        return withContext(dispatcher) {
            redis.smembers(getKey(playerId)).await().mapNotNull {
                jsonMapper.decodeFromString<NewEpisodePersistence>(it).to()
            }
        }
    }

    private fun getKey(playerId: Long) = "pr:e:ue:$playerId"
}
