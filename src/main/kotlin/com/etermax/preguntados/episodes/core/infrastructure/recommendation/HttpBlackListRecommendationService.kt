package com.etermax.preguntados.episodes.core.infrastructure.recommendation

import com.etermax.preguntados.episodes.core.domain.episode.delete.BlackListRecommendationService
import com.etermax.preguntados.episodes.core.infrastructure.http.resilientPost
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import io.ktor.client.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.slf4j.LoggerFactory

class HttpBlackListRecommendationService(
    private val client: HttpClient,
    private val resilienceBundle: EndpointResilienceBundle,
    private val useBlackList: Boolean
) : BlackListRecommendationService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun deleteEpisode(episodeId: String) {
        deleteEpisodes(listOf(episodeId))
    }

    override suspend fun deleteEpisodes(episodesId: List<String>) {
        if (!useBlackList) return

        try {
            val body = BlackListRequest(episodeIds = episodesId)
            client.resilientPost(
                urlString = URL,
                resilienceBundle = resilienceBundle,
                requestBuilder = {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    setBody(body)
                    expectSuccess = true
                }
            ) { response ->
                if (response.status.isSuccess()) {
                    logger.info("[Black list] Episodes: $episodesId")
                } else {
                    val responseBody = response.bodyAsText()
                    logger.error("[Black list] Request failed with status ${response.status}. Response body: $responseBody")
                }
            }
        } catch (e: Exception) {
            logger.error("Cannot add black list episodes: $episodesId", e)
        }
    }

    private companion object {
        const val URL = "/ds-api-episode-recommendations/api/blacklist"
    }
}

@Serializable
data class BlackListRequest(
    @SerialName("episode_ids") val episodeIds: List<String>
)
