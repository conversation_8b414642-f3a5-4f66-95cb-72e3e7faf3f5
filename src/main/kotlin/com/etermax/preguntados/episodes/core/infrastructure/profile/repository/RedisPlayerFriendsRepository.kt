package com.etermax.preguntados.episodes.core.infrastructure.profile.repository

import com.etermax.preguntados.episodes.core.domain.profile.repository.PlayerFriendsRepository
import com.etermax.preguntados.episodes.core.infrastructure.profile.repository.persistence.PlayerFriendsPersistence
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.time.Duration

class RedisPlayerFriendsRepository(
    private val redis: RedisAsyncCommands<String, String>,
    private val ttlDuration: Duration,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : PlayerFriendsRepository {

    private val jsonMapper = Json { ignoreUnknownKeys = true }

    override suspend fun find(playerId: Long): List<Long>? {
        return with<PERSON>ontext(dispatcher) {
            val key = createKey(playerId)
            redis.get(key).await()?.let { decode(it) }
        }
    }

    override suspend fun save(playerId: Long, friends: List<Long>) {
        withContext(dispatcher) {
            val key = createKey(playerId)
            redis.setex(key, ttlDuration.seconds, encode(friends)).await()
        }
    }

    private fun decode(friends: String) =
        jsonMapper.decodeFromString<PlayerFriendsPersistence>(friends).friends

    private fun encode(friends: List<Long>) =
        jsonMapper.encodeToString(PlayerFriendsPersistence(friends))

    private fun createKey(playerId: Long) = "$KEY:$playerId"

    private companion object {
        const val KEY = "pr:e:pf"
    }
}
