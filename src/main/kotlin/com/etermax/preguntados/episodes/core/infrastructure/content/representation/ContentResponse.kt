package com.etermax.preguntados.episodes.core.infrastructure.content.representation

import com.etermax.preguntados.episodes.core.domain.content.Content
import com.etermax.preguntados.episodes.core.domain.content.ContentStatus
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ContentResponse(
    @SerialName("content_id") val contentId: String,
    @SerialName("status") val status: String
) {

    fun to() = Content(
        id = contentId,
        status = ContentStatus.valueOf(status.uppercase())
    )
}
