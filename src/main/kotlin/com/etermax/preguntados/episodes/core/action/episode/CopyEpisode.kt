package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer

class CopyEpisode(
    private val episodeRepository: EpisodeRepository,
    private val uuidSequencer: UUIDSequencer,
    private val profileService: ProfileService,
    private val clock: Clock
) {

    suspend operator fun invoke(actionData: ActionData): EpisodeSummary {
        return with(actionData) {
            episodeRepository.findById(episodeId)?.let { episode ->
                val copiedEpisode = episode.copy(
                    id = uuidSequencer.next(),
                    type = EpisodeType.PRIVATE,
                    startDate = clock.now(),
                    ownerId = playerId,
                    rate = Rate.empty(),
                    reports = NO_REPORTS,
                    views = NO_VIEWS
                )

                episodeRepository.save(copiedEpisode)
                val profile = profileService.find(playerId)
                EpisodeSummary.from(copiedEpisode, profile)
            } ?: throw EpisodeNotFoundException(episodeId)
        }
    }

    data class ActionData(
        val playerId: Long,
        val episodeId: String
    )

    private companion object {
        const val NO_REPORTS = 0L
        const val NO_VIEWS = 0L
    }
}
