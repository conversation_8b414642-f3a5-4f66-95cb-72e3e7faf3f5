package com.etermax.preguntados.episodes.core.action

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccount
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountParams
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountService
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.search.OverviewInfo
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.search.SearchChannelsByName
import com.etermax.preguntados.episodes.utils.LanguageUtils
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class OverviewInfoSearch(
    private val playerAccountService: PlayerAccountService,
    private val allSearch: Search,
    private val summaryService: SummaryService,
    private val channelsSearch: SearchChannelsByName
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend operator fun invoke(actionData: ActionData): OverviewInfo = coroutineScope {
        with(actionData) {
            logger.info("Player $playerId find overview info for [$name] and [$language]")

            val playerAccounts = async { findPlayerAccounts(playerId, name) }
            val episodes = async { findEpisodesSummary(playerId, name, language) }
            val channels = async { findChannels(name, language) }

            OverviewInfo(
                playerAccounts = playerAccounts.await(),
                episodes = episodes.await(),
                channels = channels.await()
            )
        }
    }

    private suspend fun findPlayerAccounts(playerId: Long, name: String): List<PlayerAccount> {
        val accounts = playerAccountService.search(
            PlayerAccountParams(
                playerId = playerId,
                query = name,
                amount = ACCOUNTS_LIMIT,
                skip = SKIP,
                skipRestricted = SKIP_RESTRICTED,
                fields = IS_FOLLOWED
            )
        )
        return accounts.playerAccounts
    }

    private suspend fun findEpisodesSummary(playerId: Long, name: String, language: String?): List<EpisodeSummary> {
        val parameters = SearchParameters(
            playerId = playerId,
            name = name,
            language = LanguageUtils.from(language),
            offset = OFFSET,
            limit = EPISODES_LIMIT
        )
        val episodes = allSearch.search(parameters)
        return summaryService.toEpisodesSummary(episodes)
    }

    private suspend fun findChannels(name: String, language: String?): List<ChannelSummary> {
        val channels = channelsSearch.search(
            name = name,
            language = LanguageUtils.from(language),
            offset = OFFSET,
            limit = CHANNELS_LIMIT
        )
        return summaryService.toChannelsSummary(channels)
    }

    private companion object {
        const val ACCOUNTS_LIMIT = 10
        const val EPISODES_LIMIT = 6
        const val CHANNELS_LIMIT = 1
        const val SKIP = 0
        const val OFFSET = 0
        const val SKIP_RESTRICTED = false
        const val IS_FOLLOWED = "is_followed"
    }

    data class ActionData(
        val playerId: Long,
        val name: String,
        val language: String?
    )
}
