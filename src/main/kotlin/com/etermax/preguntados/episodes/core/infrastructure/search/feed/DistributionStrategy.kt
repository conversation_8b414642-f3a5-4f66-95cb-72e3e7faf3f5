package com.etermax.preguntados.episodes.core.infrastructure.search.feed

/**
 * Strategy for distributing values across multiple sources
 */
interface DistributionStrategy {
    /**
     * Distributes a total value across a specified number of parts
     * @param total The total value to distribute
     * @param parts The number of parts to distribute across
     * @return A list of distributed values, with size equal to parts
     */
    fun distribute(total: Int, parts: Int): List<Int>
}

/**
 * Distributes values evenly across all parts
 */
class EvenlyDistributionStrategy : DistributionStrategy {
    override fun distribute(total: Int, parts: Int): List<Int> {
        if (parts <= 0) return emptyList()
        val base = total / parts
        val remainder = total % parts
        return List(parts) { i ->
            if (i < remainder) base + 1 else base
        }
    }
}

/**
 * Distributes values according to specified weights
 */
class WeightedDistributionStrategy(private val weights: List<Int>) : DistributionStrategy {
    override fun distribute(total: Int, parts: Int): List<Int> {
        if (parts <= 0 || weights.isEmpty()) return emptyList()

        // Ensure we have weights for all parts
        val normalizedWeights = if (weights.size >= parts) {
            weights.take(parts)
        } else {
            weights + List(parts - weights.size) { weights.last() }
        }

        val totalWeight = normalizedWeights.sum()
        if (totalWeight <= 0) return List(parts) { 0 }

        // Calculate initial distribution based on weights
        val initialDistribution = normalizedWeights.map { weight ->
            (total.toDouble() * weight / totalWeight).toInt()
        }

        // Handle any remainder due to integer division
        val initialSum = initialDistribution.sum()
        val remainder = total - initialSum

        if (remainder <= 0) return initialDistribution

        // Distribute remainder based on fractional parts
        val result = initialDistribution.toMutableList()

        // Calculate fractional parts that were truncated during integer conversion
        val fractions = normalizedWeights.mapIndexed { index, weight ->
            val exactValue = total.toDouble() * weight / totalWeight
            val fractionalPart = exactValue - initialDistribution[index]
            index to fractionalPart
        }.sortedByDescending { it.second }

        // Distribute remainder to indices with largest fractional parts first
        var remainingToDistribute = remainder
        for ((index, _) in fractions) {
            if (remainingToDistribute <= 0) break
            result[index] += 1
            remainingToDistribute--
        }

        return result
    }
}
