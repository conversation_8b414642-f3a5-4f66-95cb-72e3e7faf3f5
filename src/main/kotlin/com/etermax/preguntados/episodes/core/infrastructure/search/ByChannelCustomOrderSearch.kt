package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class ByChannelCustomOrderSearch(
    private val episodeRepository: EpisodeRepository,
    private val channelEpisodesRepository: ChannelEpisodesRepository
) : Search {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun match(parameters: SearchParameters) =
        parameters.sort == SortEpisode.EPISODES_FOR_CHANNEL_CUSTOM_ORDER

    override suspend fun search(parameters: SearchParameters): List<Episode> {
        val channelId = parameters.channelId

        if (channelId == null) {
            logger.warn(
                "Must specify channelId for this search",
                RuntimeException("Missing channel id for ByChannelCustomOrderSearch")
            )
            return emptyList()
        }
        val ids = channelEpisodesRepository.findAllEpisodeIdsFrom(channelId, ChannelOrderType.CUSTOM_ORDER)
        val episodes = episodeRepository.findByIds(ids)
        val episodeById = episodes.associateBy { it.id }
        return ids.mapNotNull { episodeById[it] }
    }
}
