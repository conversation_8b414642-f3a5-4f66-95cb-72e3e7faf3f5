package com.etermax.preguntados.episodes.core.infrastructure.notification.representation.factory

import com.etermax.preguntados.episodes.core.domain.notification.Notification
import com.etermax.preguntados.episodes.core.domain.notification.NotificationWithHoursLeft
import com.etermax.preguntados.episodes.core.infrastructure.notification.representation.LocalizationRequest
import com.etermax.preguntados.episodes.core.infrastructure.notification.representation.NotificationPayloadRequest
import com.etermax.preguntados.episodes.core.infrastructure.notification.representation.NotificationRequest

object NotificationRequestFactory {

    @JvmStatic
    fun create(notification: Notification): NotificationRequest {
        val payload = notification.buildPayload()
        return NotificationRequest(payload)
    }

    @JvmStatic
    fun create(notification: NotificationWithHoursLeft): NotificationRequest {
        val payload = notification.buildPayload()
        return NotificationRequest(payload)
    }

    private fun Notification.buildPayload(): NotificationPayloadRequest {
        val payloadBuilder = DataPayloadBuilder()
            .withType(type)
            .withGameId(gameId)
            .withOpponentName(senderProfile.userName)
            .withSenderId(senderProfile.id)

        senderProfile.facebookId?.let {
            payloadBuilder.withSenderFacebookId(it)
            payloadBuilder.withShowSenderFacebookPicture(showSenderFacebookPicture = true)
        }

        return NotificationPayloadRequest(
            dataAsString = payloadBuilder.build(),
            localization = LocalizationRequest(
                body = type.asString(),
                bodyArgs = listOf(senderProfile.userName)
            ),
            badge = 1
        )
    }

    private fun NotificationWithHoursLeft.buildPayload(): NotificationPayloadRequest {
        val payloadBuilder = DataPayloadBuilder()
            .withType(type)
            .withGameId(gameId)
            .withOpponentName(senderProfile.userName)
            .withSenderId(senderProfile.id)
            .withHoursToExpire(hoursLeft)

        senderProfile.facebookId?.let {
            payloadBuilder.withSenderFacebookId(it)
            payloadBuilder.withShowSenderFacebookPicture(showSenderFacebookPicture = true)
        }

        return NotificationPayloadRequest(
            dataAsString = payloadBuilder.build(),
            localization = LocalizationRequest(
                body = type.asString(),
                bodyArgs = emptyList()
            ),
            badge = 1
        )
    }
}
