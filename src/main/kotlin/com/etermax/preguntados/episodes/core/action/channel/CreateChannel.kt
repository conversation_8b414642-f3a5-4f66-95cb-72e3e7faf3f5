package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.*
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.order.OrderItemCalculator
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer

class CreateChannel(
    private val uuidSequencer: UUIDSequencer,
    private val summaryService: SummaryService,
    private val channelRepository: ChannelRepository,
    private val orderItemCalculator: OrderItemCalculator,
    private val channelValidatorService: ChannelValidatorService,
    private val clock: Clock
) {
    suspend operator fun invoke(data: ActionData): ChannelSummary {
        channelValidatorService.validateCreate(data = data)

        val channel = data.buildChannel()
        val summary = summaryService.toChannelSummary(channel)!!
        channelRepository.add(channel)
        return summary
    }

    private suspend fun ActionData.buildChannel(): Channel {
        val channelId = uuidSequencer.next()
        val order = orderItemCalculator.calculate()

        val channel = Channel(
            id = channelId,
            name = name.trim(),
            description = description?.trim(),
            website = website,
            coverUrl = coverUrl,
            subscribed = true,
            ownerId = playerId,
            statistics = ChannelStatistics.empty(),
            creationDate = clock.now(),
            lastModificationDate = clock.now(),
            type = type ?: ChannelType.PUBLIC,
            language = null,
            order = order,
            orderType = ChannelOrderType.DATE_ADDED
        )
        return channel
    }
}
