package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUpdateOrderTypeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem.Companion.asString
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelOrderTypeItem
import kotlinx.coroutines.future.await
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest

class DynamoDbChannelUpdateOrderTypeRepository(
    private val channelTable: DynamoDbAsyncTable<UpdateChannelOrderTypeItem>
) : ChannelUpdateOrderTypeRepository {
    override suspend fun put(channelId: String, orderType: ChannelOrderType) {
        val item = UpdateChannelOrderTypeItem(channelId, orderType.asString())

        val request = UpdateItemEnhancedRequest.builder(UpdateChannelOrderTypeItem::class.java)
            .item(item)
            .build()

        channelTable.updateItem(request).await()
    }
}
