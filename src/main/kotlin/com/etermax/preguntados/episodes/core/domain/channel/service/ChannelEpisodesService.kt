package com.etermax.preguntados.episodes.core.domain.channel.service

import com.etermax.preguntados.episodes.core.domain.PositiveNumber
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelEpisode
import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.episodes.core.domain.channel.repository.AddEpisodesToChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.exception.ChannelLanguageNotMatchEpisodeLanguageException
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.ChannelTypeNotMatchEpisodeTypeException
import com.etermax.preguntados.episodes.core.domain.order.OrderItemCalculator
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.temporal.ChronoUnit

class ChannelEpisodesService(
    private val episodeRepository: EpisodeRepository,
    private val channelRepository: ChannelRepository,
    private val orderItemCalculator: OrderItemCalculator,
    private val addEpisodesToChannelRepository: AddEpisodesToChannelRepository,
    private val clock: Clock
) {

    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    suspend fun canAddToChannel(channelId: String, episode: Episode): Boolean {
        val channel = channelRepository.findById(channelId)

        if (channel == null) {
            val error = ChannelNotFoundException(channelId)
            logger.warn("Cannot add episode='${episode.id}' to channel='$channelId'", error)
            return false
        }

        if (channel.language != null && channel.language != episode.language) {
            val error = ChannelLanguageNotMatchEpisodeLanguageException(channel, episode)
            logger.warn("Cannot to add episode='${episode.id}' to channel='$channelId'", error)
            return false
        }

        if (!typesMatch(channel, episode)) {
            val error = ChannelTypeNotMatchEpisodeTypeException(channel, episode)
            logger.warn("Cannot to add episode='${episode.id}' to channel='$channelId'", error)
            return false
        }

        return true
    }

    suspend fun addEpisodeToChannel(channel: Channel, episode: Episode) {
        addEpisodesToChannel(channel, setOf(episode.id), episode.language)
    }

    suspend fun addEpisodesToChannel(
        channel: Channel,
        episodesIds: Set<String>,
        episodesLanguage: Language
    ): Channel {
        val updatedChannel = updateChannel(channel, episodesIds, episodesLanguage)
        associateChannelWithEpisodes(updatedChannel, episodesIds)
        return updatedChannel
    }

    private fun updateChannel(channel: Channel, episodesIds: Set<String>, episodesLanguage: Language): Channel {
        val modificationDate = clock.now()
        var updatedChannel = channel
            .setLastModificationDate(modificationDate)
            .addEpisodes(episodesIds.count())

        if (!channel.hasLanguage) {
            updatedChannel = updatedChannel.setLanguage(episodesLanguage)
        }
        return updatedChannel
    }

    suspend fun removeChannelFromEpisode(episodeId: String) {
        val episode = episodeRepository.findById(episodeId) ?: return

        if (episode.channelId != null) {
            val updatedEpisode = episode.removeChannel()
            episodeRepository.updateItem(updatedEpisode)
        }
    }

    private suspend fun associateChannelWithEpisodes(channel: Channel, episodesIds: Set<String>) {
        val now = clock.now()
        addEpisodesToChannelRepository.add(
            channel,
            episodesIds.mapIndexed { index, episodeId ->
                ChannelEpisode(
                    channelId = channel.id,
                    episodeId = episodeId,
                    dateAdded = now.plus(index * 50L, ChronoUnit.MILLIS),
                    episodeOrder = orderItemCalculator.calculateFor(PositiveNumber(index))
                )
            }.toSet()
        )
    }

    private fun typesMatch(channel: Channel, episode: Episode): Boolean {
        if (channel.type == ChannelType.PUBLIC && episode.type == EpisodeType.PUBLIC) return true
        if (channel.type == ChannelType.PRIVATE && episode.type == EpisodeType.PRIVATE) return true
        return false
    }
}
