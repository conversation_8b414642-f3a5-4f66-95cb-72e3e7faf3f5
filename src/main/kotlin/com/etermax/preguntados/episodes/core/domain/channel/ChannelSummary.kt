package com.etermax.preguntados.episodes.core.domain.channel

import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import java.time.OffsetDateTime

data class ChannelSummary(
    val id: String,
    val name: String,
    val description: String?,
    val website: String?,
    val coverUrl: String,
    val subscribed: Boolean,
    val ownerId: Long,
    val owner: Profile?,
    val statistics: ChannelSummaryStatistics,
    val creationDate: OffsetDateTime,
    val lastModificationDate: OffsetDateTime,
    val type: ChannelType,
    val language: Language?,
    val orderType: ChannelOrderType
) {
    companion object {
        fun from(channel: Channel, ownerProfile: Profile?, unpublishedEpisodes: Int) = with(channel) {
            ChannelSummary(
                id = id,
                name = name,
                description = description,
                website = website,
                coverUrl = coverUrl,
                subscribed = subscribed,
                ownerId = ownerId,
                owner = ownerProfile,
                statistics = ChannelSummaryStatistics.from(statistics, unpublishedEpisodes),
                creationDate = creationDate,
                lastModificationDate = lastModificationDate,
                type = type,
                language = language,
                orderType = orderType
            )
        }
    }
}

data class ChannelReduced(
    val id: String,
    val name: String,
    val coverUrl: String
) {
    companion object {
        fun from(channel: Channel) = with(channel) {
            ChannelReduced(
                id = id,
                name = name,
                coverUrl = coverUrl
            )
        }
    }
}
