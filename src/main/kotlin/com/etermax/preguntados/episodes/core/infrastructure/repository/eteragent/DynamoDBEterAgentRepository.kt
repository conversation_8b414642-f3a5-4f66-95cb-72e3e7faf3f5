package com.etermax.preguntados.episodes.core.infrastructure.repository.eteragent

import com.etermax.preguntados.episodes.core.domain.eteragent.EterAgentRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.DynamoDBRepository
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key

class DynamoDBEterAgentRepository(
    client: DynamoDbEnhancedAsyncClient,
    table: DynamoDbAsyncTable<EterAgentItem>
) : EterAgentRepository, DynamoDBRepository<EterAgentItem>(client, table) {
    override suspend fun save(userId: Long, eterAgent: String) {
        saveItem(
            EterAgentItem(
                playerId = userId.toString(),
                eteragent = eterAgent
            ),
            EterAgentItem::class.java
        )
    }

    override suspend fun find(userId: Long): String? {
        return findItem(
            Key.builder()
                .partitionValue(userId.toString())
                .build()
        )?.eteragent
    }
}
