package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary

class FindEpisode(
    private val episodeRepository: EpisodeRepository,
    private val summaryService: SummaryService
) {

    suspend operator fun invoke(actionData: ActionData): EpisodeSummary? {
        val episodes = episodeRepository.findById(actionData.episodeId)
        return summaryService.toEpisodeSummary(episodes)
    }

    data class ActionData(
        val episodeId: String
    )
}
