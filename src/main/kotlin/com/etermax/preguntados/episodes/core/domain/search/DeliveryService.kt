package com.etermax.preguntados.episodes.core.domain.search

import com.etermax.preguntados.episodes.core.domain.episode.Episode

class DeliveryService(
    private val searches: List<Search>
) {

    suspend fun search(parameters: SearchParameters): List<Episode> {
        return searches.asSequence()
            .firstOrNull { it.match(parameters) }
            ?.search(parameters)
            ?: emptyList()
    }
}
