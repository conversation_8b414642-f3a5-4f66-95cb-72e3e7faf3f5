package com.etermax.preguntados.episodes.core.infrastructure.search.repository

import com.etermax.preguntados.episodes.core.domain.search.PlayerRecentSearchRepository
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import java.time.Duration

class RedisPlayerRecentSearchRepository(
    private val redis: RedisAsyncCommands<String, String>,
    private val ttl: Duration,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : PlayerRecentSearchRepository {

    override suspend fun save(playerId: Long, limit: Int) {
        return withContext(dispatcher) {
            val key = createKey(playerId)
            redis.setex(key, ttl.seconds, limit.toString()).await()
        }
    }

    override suspend fun find(playerId: Long): Int? {
        return with<PERSON>ontex<PERSON>(dispatcher) {
            val key = createKey(playerId)
            redis.get(key).await()?.toInt()
        }
    }

    private fun createKey(playerId: Long) = "$KEY:$playerId"

    private companion object {
        const val KEY = "pr:e:rs"
    }
}
