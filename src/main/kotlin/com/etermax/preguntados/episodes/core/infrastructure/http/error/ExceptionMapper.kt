package com.etermax.preguntados.episodes.core.infrastructure.http.error

import com.etermax.preguntados.episodes.core.domain.exception.*
import com.etermax.preguntados.episodes.core.domain.exception.base.BadRequestException
import com.etermax.preguntados.episodes.core.domain.exception.base.BusinessException
import com.etermax.preguntados.episodes.core.domain.exception.base.ConflictException
import com.etermax.preguntados.episodes.core.domain.exception.base.EntityNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.base.ForbiddenException
import io.ktor.http.*
import kotlinx.serialization.SerializationException

class ExceptionMapper {

    fun toHttpCode(throwable: Throwable): HttpStatusCode {
        return when (throwable) {
            is BusinessException -> HttpStatusCode.BadRequest
            is EntityNotFoundException -> HttpStatusCode.NotFound
            is ForbiddenException -> HttpStatusCode.Forbidden
            is ConflictException -> HttpStatusCode.Conflict
            is BadRequestException -> HttpStatusCode.BadRequest
            else -> HttpStatusCode.InternalServerError
        }
    }

    fun toDomainCode(error: Throwable) = when (error) {
        is SerializationException -> "2000"
        is IllegalArgumentException -> "2001"
        is EpisodeNotFoundException -> "2002"
        is InvalidProfileException -> "2003"
        is EpisodeNameEmptyException -> "2004"
        is EpisodeNameNotAllowedException -> "2005"
        is InvalidUpdateNotOwnEpisodeException -> "2006"
        is EpisodeEmptyContentException -> "2007"
        is MandatoryAttributeValidateException -> "2008"
        is PlayerNotOwnEpisodeException -> "2009"
        is EpisodeNotInChannelException -> "2010"
        is ChannelNotFoundException -> "3000"
        is ChannelNameEmptyException -> "3001"
        is ChannelNameNotAllowedException -> "3002"
        is ChannelNameLongerTooLongException -> "3003"
        is ChannelInvalidCoverUrlException -> "3004"
        is ChannelInvalidWebsiteException -> "3005"
        is ChannelDescriptionNotAllowedException -> "3010"
        is ChannelDescriptionLongerTooLongException -> "3011"
        is ChannelCannotAddEmptyEpisodesException -> "3020"
        is InvalidUpdateNotOwnChannelException -> "3021"
        is PlayerNotOwnChannelException -> "3022"
        is ChannelLanguageNotMatchEpisodeLanguageException -> "3023"
        is ChannelTypeNotMatchEpisodeTypeException -> "3024"
        else -> "UNDEFINED"
    }
}
