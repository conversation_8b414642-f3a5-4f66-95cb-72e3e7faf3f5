package com.etermax.preguntados.episodes.core.domain.episode

import com.etermax.preguntados.episodes.core.domain.challenge.Challenge
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import java.time.OffsetDateTime

data class Episode(
    val id: String,
    val name: String,
    val language: Language,
    val country: Country,
    val type: EpisodeType,
    val startDate: OffsetDateTime?,
    val cover: String,
    val banner: String,
    val ownerId: Long,
    val contents: List<String>,
    val channelId: String?,
    val rate: Rate,
    val views: Long,
    val reports: Long,
    val status: EpisodeStatus,
    val quality: Int
) {
    val hasChannel = !channelId.isNullOrEmpty()

    val isDraft = status == EpisodeStatus.DRAFT

    val isPublished = status == EpisodeStatus.PUBLISHED

    fun incrementLikes() = rate.like()
    fun incrementUnlikes() = rate.dislike()
    fun decrementLikes() = rate.decrementLikes()
    fun decrementUnlikes() = rate.decrementUnlikes()
    fun incrementViews() = copy(views = views + 1)
    fun assignChannel(channelId: String) = copy(channelId = channelId)
    fun removeChannel() = copy(channelId = null)
    fun overrideWith(challenge: Challenge) = copy(
        name = challenge.reference.name,
        cover = challenge.reference.cover,
        contents = challenge.reference.contents
    )
}
