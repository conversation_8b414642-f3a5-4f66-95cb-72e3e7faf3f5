package com.etermax.preguntados.episodes.core.infrastructure.search.data

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class EmbeddingEpisodeData(
    @JsonProperty("episode_id") val episodeId: String? = null,
    @JsonProperty("embedding") val embedding: FloatArray? = null
)
