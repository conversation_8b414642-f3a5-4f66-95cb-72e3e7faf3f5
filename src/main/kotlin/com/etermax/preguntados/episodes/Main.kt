package com.etermax.preguntados.episodes

import com.etermax.preguntados.episodes.modules.Banner
import com.etermax.preguntados.episodes.modules.DeliveryProvider.apiServer
import com.etermax.preguntados.episodes.modules.GatewaysProvider
import com.etermax.preguntados.episodes.modules.JobsProvider.pendingEpisodesJob
import com.etermax.preguntados.episodes.modules.RepositoriesProvider
import kotlinx.coroutines.*
import org.apache.logging.log4j.LogManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory

fun main() {
    val logger: Logger = LoggerFactory.getLogger("Main")

    println(Banner.TEXT)

    logger.info("Starting main application ...")

    registerShutdownHook()

    pendingEpisodesJob.start()

    apiServer.start()
}

private fun registerShutdownHook() {
    Runtime.getRuntime().addShutdownHook(ShutdownHook)
}

object ShutdownHook : Thread() {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)
    override fun run() {
        logger.info("Termination signal was received")
        runBlocking {
            logger.info("Stopping consumers...")
            stopJobs()
            apiServer.stop()
            GatewaysProvider.closeClients()
            RepositoriesProvider.closeRepositories()
            LogManager.shutdown()
        }
    }

    private suspend fun stopJobs() {
        withContext(Dispatchers.IO) {
            listOf(
                launch { pendingEpisodesJob.stop() }
            ).joinAll()
        }
    }
}
