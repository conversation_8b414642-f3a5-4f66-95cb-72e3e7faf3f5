package com.etermax.preguntados.episodes.architecture

import com.etermax.preguntados.episodes.http.handler.Handler
import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.ArchRule
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes
import com.tngtech.archunit.library.dependencies.SlicesRuleDefinition.slices
import org.junit.jupiter.api.TestInstance

/**
 * ArchitectureTest performs tests against the project architectural rules to find violations
 *
 * * How to use in kotlin:
 *  * https://www.archunit.org/userguide/html/000_Index.html#_using_junit_support_with_kotlin
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AnalyzeClasses(packages = ["com.etermax.preguntados.episodes"], importOptions = [ImportOption.DoNotIncludeTests::class])
class ArchitectureTest {

    private lateinit var rule: ArchRule

    @ArchTest
    fun `Project has cycle dependencies`(importedClasses: JavaClasses) {
        rule = slices().matching("com.etermax.preguntados.episodes.(*)..").should().beFreeOfCycles()
        rule.check(importedClasses)
    }

    @ArchTest
    fun `Every ktor handler ends with Handler`(importedClasses: JavaClasses) {
        rule = classes().that().implement(Handler::class.java).should().haveSimpleNameEndingWith("Handler")
        rule.check(importedClasses)
    }

    @ArchTest
    fun `Domain should only use classes from the domain`(importedClasses: JavaClasses) {
        rule = classes().that().resideInAnyPackage("..domain..")
            .should().onlyDependOnClassesThat().resideInAnyPackage(
                "",
                "..domain..",
                "java..",
                "kotlin..",
                "kotlinx..",
                "org.slf4j..",
                "org.jetbrains.annotations"
            )

        rule.check(importedClasses)
    }
}
