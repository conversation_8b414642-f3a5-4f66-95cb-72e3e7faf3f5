package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.domain.channel.episode.ChannelEpisodeOrder
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.process.DynamoChannelEpisodesOrderNormalizer
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DynamoChannelEpisodesOrderNormalizerShould {
    private lateinit var normalizer: DynamoChannelEpisodesOrderNormalizer
    private lateinit var repository: DynamoChannelEpisodesOrderRepository

    private lateinit var items: List<ChannelEpisodeItem>

    @BeforeEach
    fun setUp() {
        repository = mockk(relaxUnitFun = true)
        normalizer = DynamoChannelEpisodesOrderNormalizer(isNormalizerEnabled = true, repository)

        items = emptyList()
    }

    @Test
    fun `not normalize when every item has episode order with 19 characters`() = runTest {
        givenAllItemsNormalized()
        whenNormalize()
        thenDoNotNormalize()
    }

    @Test
    fun `not normalize when toggle is disabled`() = runTest {
        normalizer = DynamoChannelEpisodesOrderNormalizer(isNormalizerEnabled = false, repository)
        givenAllItemsNotNormalized()
        whenNormalize()
        thenDoNotNormalize()
    }

    @Test
    fun `normalize all items when all items have episode order with less than 19 characters`() = runTest {
        givenAllItemsNotNormalized()
        whenNormalize()
        thenNormalizeAllItems()
    }

    @Test
    fun `normalize one item when only one item has episode order with less than 19 characters`() = runTest {
        givenOnlyOneItemNotNormalized()
        whenNormalize()
        thenNormalizeOneItem()
    }

    private fun givenAllItemsNormalized() {
        val episode1 = ChannelEpisodeItem(
            episodeId = "episode_1",
            channelId = CHANNEL_ID,
            dateAdded = DATE_ADDED,
            episodeOrder = "1234567890123456789"
        )

        val episode2 = ChannelEpisodeItem(
            episodeId = "episode_2",
            channelId = CHANNEL_ID,
            dateAdded = DATE_ADDED,
            episodeOrder = "9876543210987654321"
        )
        items = listOf(episode1, episode2)
    }

    private fun givenAllItemsNotNormalized() {
        val episode1 = ChannelEpisodeItem(
            episodeId = "episode_1",
            channelId = CHANNEL_ID,
            dateAdded = DATE_ADDED,
            episodeOrder = "1234567890123456"
        )

        val episode2 = ChannelEpisodeItem(
            episodeId = "episode_2",
            channelId = CHANNEL_ID,
            dateAdded = DATE_ADDED,
            episodeOrder = "9876543210"
        )
        items = listOf(episode1, episode2)
    }

    private fun givenOnlyOneItemNotNormalized() {
        val episode1 = ChannelEpisodeItem(
            episodeId = "episode_1",
            channelId = CHANNEL_ID,
            dateAdded = DATE_ADDED,
            episodeOrder = "1234567890123456789"
        )

        val episode2 = ChannelEpisodeItem(
            episodeId = "episode_2",
            channelId = CHANNEL_ID,
            dateAdded = DATE_ADDED,
            episodeOrder = "9876543210"
        )
        items = listOf(episode1, episode2)
    }

    private suspend fun whenNormalize() {
        normalizer.normalize(items)
    }

    private fun thenDoNotNormalize() {
        coVerify(exactly = 0) { repository.put(any()) }
    }

    private fun thenNormalizeAllItems() {
        val episode1 = ChannelEpisodeOrder(
            episodeId = "episode_1",
            channelId = CHANNEL_ID,
            _episodeOrder = 1234567890123456
        )

        val episode2 = ChannelEpisodeOrder(
            episodeId = "episode_2",
            channelId = CHANNEL_ID,
            _episodeOrder = 9876543210
        )
        val itemsNormalized = listOf(episode1, episode2)

        coVerify(exactly = 1) { repository.put(itemsNormalized) }
    }

    private fun thenNormalizeOneItem() {
        val episode2 = ChannelEpisodeOrder(
            episodeId = "episode_2",
            channelId = CHANNEL_ID,
            _episodeOrder = 9876543210
        )
        val itemsNormalized = listOf(episode2)

        coVerify(exactly = 1) { repository.put(itemsNormalized) }
    }

    private companion object {
        const val CHANNEL_ID = ChannelMother.ID
        const val DATE_ADDED = 1000L
    }
}
