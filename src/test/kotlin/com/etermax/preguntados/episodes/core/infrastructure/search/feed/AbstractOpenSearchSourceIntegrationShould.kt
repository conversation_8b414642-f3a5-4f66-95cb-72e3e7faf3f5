package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.utils.LocalOpenSearch
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import java.time.OffsetDateTime
import kotlin.test.assertEquals

abstract class AbstractOpenSearchSourceIntegrationShould(val indexDescriptor: String) {
    protected val indexName = "episodes-${System.currentTimeMillis()}"

    protected val osClient = LocalOpenSearch.buildOpenSearch(LocalOpenSearch.Config(indexName, indexDescriptor))

    protected fun thenSameElements(expected: List<String>, actualResults: List<String>) {
        assertEquals(expected.sorted(), actualResults.sorted())
    }

    protected fun givenAnEpisode(
        id: String = "id",
        name: String = "Episode",
        type: EpisodeType = EpisodeType.PUBLIC,
        status: EpisodeStatus = EpisodeStatus.PUBLISHED,
        country: Country = Country.US,
        language: Language = Language.EN,
        likes: Long = 100,
        dislikes: Long = 0,
        reports: Long = 2,
        views: Long = 100,
        quality: Int = 0,
        embedding: FloatArray = FloatArray(0),
        startDate: OffsetDateTime? = null,
        channelId: String? = null
    ): Episode {
        val e = EpisodeMother.buildEpisode(
            id = id,
            name = name,
            type = type,
            status = status,
            country = country,
            language = language,
            likes = likes,
            dislikes = dislikes,
            reports = reports,
            views = views,
            startDate = startDate,
            channelId = channelId,
            quality = quality
        )
        saveToOpenSearch(e, embedding)
        return e
    }

    private fun saveToOpenSearch(episode: Episode, embedding: FloatArray) {
        LocalOpenSearch.saveToOpenSearch(osClient, indexName, episode.toDocumentId(), episode.toDocument(embedding))
    }

    protected fun Episode.toDocumentId(): String = "E#$id"

    protected fun Episode.toDocument(embedding: FloatArray): Map<String, Any> {
        val document = mutableMapOf<String, Any>(
            "PK" to toDocumentId(),
            "episode_id" to id,
            "name" to name,
            "status" to status,
            "type" to type,
            "language" to language,
            "country" to country,
            "owner_id" to ownerId,
            "rate" to (rate.likes / (rate.likes + rate.dislikes).toDouble() * 100),
            "views" to views,
            "likes" to rate.likes,
            "dislikes" to rate.dislikes,
            "quality" to quality,
            "start_date" to (startDate?.toInstant()?.toEpochMilli() ?: 0)
        )

        if (embedding.isNotEmpty()) {
            document["embedding"] = embedding
        }

        return document
    }
}
