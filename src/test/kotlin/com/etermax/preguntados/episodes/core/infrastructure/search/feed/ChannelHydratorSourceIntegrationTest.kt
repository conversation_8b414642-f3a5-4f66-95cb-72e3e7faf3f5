package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.infrastructure.search.channels.AbstractChannelsSearchIntegrationShould
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime.now
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class ChannelHydratorSourceIntegrationTest : AbstractChannelsSearchIntegrationShould() {

    private lateinit var source: Source<Channel>

    @BeforeEach
    fun setUp() {
        source = ChannelHydratorSource(RecentChannelSource(osClient, indexNamePerTest), repository)
    }

    @Test
    fun `hydrate channels`() = runTest {
        val channel = givenAChannel(episodesCount = 3)

        val result = whenRetrieve()

        assertEquals(listOf(channel), result.items)
    }

    @Test
    fun `retrieve channels respecting fetch cursor`() = runTest {
        (1..20).forEach { i ->
            givenAChannel(id = i.toString(), episodesCount = 3, lastModificationDate = now().minusHours(i.toLong()))
        }

        val result = whenRetrieve(offset = 0, limit = 10)

        val secondResult = whenRetrieve(
            offset = 0,
            limit = 10,
            fetchCursor = result.fetchCursor
        )

        val thirdResult = whenRetrieve(
            offset = 0,
            limit = 10,
            fetchCursor = secondResult.fetchCursor
        )

        assertNotNull(result.fetchCursor)
        assertEquals((1..10).map { it.toString() }.toSet(), result.items.map { it.id }.toSet())

        assertNotNull(secondResult.fetchCursor)
        assertEquals((11..20).map { it.toString() }.toSet(), secondResult.items.map { it.id }.toSet())

        assertNotNull(thirdResult.fetchCursor)
        assertTrue((thirdResult.fetchCursor as OffsetFetchCursor).exhausted)
        assertEquals(emptySet(), thirdResult.items.map { it.id }.toSet())
    }

    private suspend fun whenRetrieve(
        offset: Int = 0,
        limit: Int = 10,
        language: Language? = null,
        fetchCursor: FetchCursor? = null
    ): SourceResponse<Channel> {
        return source.fetch(
            SourceRequest(
                OffsetLimit(
                    offset = offset,
                    limit = limit
                ),
                userId = PLAYER_ID,
                language = language,
                fetchCursor = fetchCursor
            )
        )
    }
}
