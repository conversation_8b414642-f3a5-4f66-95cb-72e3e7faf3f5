package com.etermax.preguntados.episodes.core

import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import java.time.OffsetDateTime

object EpisodeMother {

    const val OWNER_ID = 666L
    const val SEQUENCE_ID = "EPISODE-KEY"
    const val NAME = "NAME"
    const val START_DATE = "2025-02-17T18:03:00.000Z"
    const val COVER = "COVER"
    const val BANNER = "BANNER"
    const val CHANNEL_ID = ChannelMother.ID
    val LANGUAGE = Language.EN
    val COUNTRY = Country.US
    val EPISODE_TYPE = EpisodeType.PUBLIC
    val CONTENTS = listOf("10", "20", "30")
    val EPISODE_STATUS = EpisodeStatus.PUBLISHED
    private const val LIKES = 100L
    private const val DISLIKES = 30L
    private const val REPORTS = 5L
    private const val NO_QUALITY = 0
    private const val NO_VIEWS = 0L

    fun buildEpisode(
        id: String = SEQUENCE_ID,
        name: String = NAME,
        language: Language = LANGUAGE,
        country: Country = COUNTRY,
        type: EpisodeType = EPISODE_TYPE,
        startDate: OffsetDateTime? = OffsetDateTime.parse(START_DATE),
        cover: String = COVER,
        banner: String = BANNER,
        ownerId: Long = OWNER_ID,
        contents: List<String> = CONTENTS,
        channelId: String? = null,
        likes: Long = LIKES,
        dislikes: Long = DISLIKES,
        reports: Long = REPORTS,
        views: Long = NO_VIEWS,
        status: EpisodeStatus = EPISODE_STATUS,
        quality: Int = NO_QUALITY
    ) = Episode(
        id = id,
        name = name,
        language = language,
        country = country,
        type = type,
        startDate = startDate,
        cover = cover,
        banner = banner,
        ownerId = ownerId,
        contents = contents,
        channelId = channelId,
        rate = Rate(likes, dislikes),
        reports = reports,
        views = views,
        status = status,
        quality = quality
    )

    fun buildUnpublishedEpisode(
        channelId: String = ChannelMother.ID,
        episodeId: String = SEQUENCE_ID
    ): ChannelUnpublishedEpisode {
        return ChannelUnpublishedEpisode(channelId, episodeId)
    }
}
