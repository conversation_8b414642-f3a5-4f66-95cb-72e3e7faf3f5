package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.search.feed.OffsetLimit
import com.etermax.preguntados.episodes.core.domain.search.feed.Source
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceRequest
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.DynamoDBEpisodeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.AssertionsForInterfaceTypes.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema.fromBean

class EpisodeHydratorSourceIntegrationTest {
    private lateinit var source: Source<Episode>
    private lateinit var result: SourceResponse<Episode>
    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        dynamoDbTestServer.buildEnhancedAsyncClient(dynamoDbTestServer.buildAsyncClient())
    private val dbTable: DynamoDbAsyncTable<EpisodeItem> =
        dynamoDbEnhancedClient.table(TABLE_NAME, fromBean(EpisodeItem::class.java))
    private val repository: DynamoDBEpisodeRepository =
        DynamoDBEpisodeRepository(dynamoDbEnhancedClient, dbTable, mockk())

    @Test
    fun `hydrate episodes`() = runTest {
        givenAnEpisode()
        givenAnHydratorSource()

        whenRetrieve()

        assertThat(result.items).containsExactly(EPISODE)
    }

    private suspend fun givenAnEpisode() {
        repository.save(EPISODE)
    }

    private fun givenAnHydratorSource() {
        val fake = object : Source<String> {
            override suspend fun fetch(request: SourceRequest): SourceResponse<String> =
                SourceResponse(listOf(EPISODE.id))
        }
        source = EpisodeHydratorSource(fake, repository)
    }

    private suspend fun whenRetrieve() {
        result = source.fetch(
            SourceRequest(
                OffsetLimit(
                    offset = 0,
                    limit = 10
                ),
                userId = EPISODE.ownerId,
                language = EPISODE.language
            )
        )
    }

    private companion object {
        const val TABLE_NAME = "dev_episodes"
        val EPISODE = buildEpisode()

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(EpisodeItem::class.java to TABLE_NAME))
    }
}
