package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.doubles.ChannelUnpublishedEpisodesFactory
import com.etermax.preguntados.episodes.core.doubles.channel.repository.InMemoryChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FindChannelByIdShould {
    private lateinit var action: FindChannelById

    private lateinit var repository: ChannelRepository
    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService

    private var result: ChannelSummary? = null

    @BeforeEach
    fun setUp() {
        repository = InMemoryChannelRepository()
        profileService = mockk()
        unpublishedEpisodesService = ChannelUnpublishedEpisodesFactory.mock()
        val summaryService = SummaryService(profileService, unpublishedEpisodesService)
        action = FindChannelById(repository, summaryService)

        result = null

        coEvery { profileService.find(PLAYER_ID) } returns ProfileMother.aProfile(PLAYER_ID)
    }

    @Test
    fun `return null summary when channel not found`() = runTest {
        whenFind()
        thenSummaryIsNull()
    }

    @Test
    fun `not search profile when channel not found`() = runTest {
        whenFind()
        thenNotSearchProfile()
    }

    @Test
    fun `return summary`() = runTest {
        givenAChannel()
        whenFind()
        thenReturnsSummary()
    }

    private suspend fun givenAChannel() {
        repository.add(ChannelMother.aChannel())
    }

    private suspend fun whenFind() {
        result = action.invoke(ChannelMother.ID)
    }

    private fun thenReturnsSummary() {
        assertThat(result).isEqualTo(ChannelMother.aChannelSummary())
    }

    private fun thenSummaryIsNull() {
        assertThat(result).isNull()
    }

    private fun thenNotSearchProfile() {
        coVerify(exactly = 0) { profileService.find(any()) }
    }
}
