package com.etermax.preguntados.episodes.core.infrastructure.repository

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelEpisode
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.DynamoChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.DynamoDBChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.DynamoDbAddEpisodesToChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.AssignChannelIdToEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelEpisodesCountItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.DynamoDBEpisodeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.byOwnerIndex
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import java.time.OffsetDateTime

class DynamoChannelEpisodesRepositoryShould {

    private lateinit var episodeRepository: DynamoDBEpisodeRepository
    private lateinit var channelRepository: DynamoDBChannelRepository
    private lateinit var channelEpisodesRepository: DynamoChannelEpisodesRepository
    private lateinit var addEpisodesToChannelRepositories: DynamoDbAddEpisodesToChannelRepository
    private var limitedEpisodesResult: Set<ChannelEpisode> = setOf()
    private var episodeIdsResult: List<String> = listOf()

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        val channelTable = createChannelTable()
        val channelEpisodeTable = createChannelEpisodeTable()

        episodeRepository = DynamoDBEpisodeRepository(
            dynamoDbEnhancedClient,
            createEpisodesTable(),
            byOwnerIndex
        )

        channelRepository = DynamoDBChannelRepository(dynamoDbEnhancedClient, channelTable)

        channelEpisodesRepository = DynamoChannelEpisodesRepository(
            dynamoDbEnhancedClient,
            createUpdateChannelEpisodesCountTable(),
            channelEpisodeTable,
            channelEpisodesOrderNormalizer = mockk(relaxed = true)
        )

        addEpisodesToChannelRepositories = DynamoDbAddEpisodesToChannelRepository(
            dynamoDbEnhancedClient,
            createAssignChannelIdToEpisodesTable(),
            channelTable,
            channelEpisodeTable
        )
    }

    @Test
    fun `save an episode`() = runTest {
        givenEpisodes()

        whenSave(CHANNEL_ID, setOf(CHANNEL_EPISODE))

        thenEpisodeIsSaved(CHANNEL_ID, EPISODE_ID)

        val episode = episodeRepository.findById(CHANNEL_EPISODE.episodeId)
        assertThat(episode!!.channelId).isEqualTo(CHANNEL_EPISODE.channelId)
    }

    @Test
    fun `save multiples episodes`() = runTest {
        whenSave(
            CHANNEL_ID,
            setOf(CHANNEL_EPISODE, CHANNEL_EPISODE2, CHANNEL_EPISODE3)
        )

        thenEpisodeIsSaved(CHANNEL_ID, EPISODE_ID)
        thenEpisodeIsSaved(CHANNEL_ID, EPISODE_ID2)
    }

    @Test
    fun `save more than 100 episodes`() = runTest {
        whenSave(
            CHANNEL_ID,
            List(101) { ChannelMother.aChannelEpisode(channelId = CHANNEL_ID, episodeId = "episode_${it + 1}") }.toSet()
        )

        thenEpisodeIsSaved(CHANNEL_ID, "episode_1")
        thenEpisodeIsSaved(CHANNEL_ID, "episode_101")
    }

    @Test
    fun `find limited channel episodes by channel ids for one channel`() = runTest {
        givenChannelEpisodesSavedForChannels()

        whenFindLimitedChannelEpisodesFor(listOf(ChannelMother.aChannel(id = CHANNEL_ID)), episodesPerChannelLimit = 1)

        thenFindChannelEpisodes(setOf(CHANNEL_EPISODE))
    }

    @Test
    fun `find more limited episodes than exists for one channel`() = runTest {
        givenChannelEpisodesSavedForChannels()

        whenFindLimitedChannelEpisodesFor(listOf(ChannelMother.aChannel(id = CHANNEL_ID)), episodesPerChannelLimit = 10)

        thenFindChannelEpisodes(setOf(CHANNEL_EPISODE, CHANNEL_EPISODE2))
    }

    @Test
    fun `find limited channel episodes by channel ids for all channel`() = runTest {
        givenChannelEpisodesSavedForChannels()

        whenFindLimitedChannelEpisodesFor(listOf(ChannelMother.aChannel(id = CHANNEL_ID), ChannelMother.aChannel(id = CHANNEL_ID2)), episodesPerChannelLimit = 3)

        thenFindChannelEpisodes(setOf(CHANNEL_EPISODE, CHANNEL_EPISODE2, CHANNEL2_EPISODE))
    }

    @Test
    fun `find limited channel episodes by channel ids for all channel with limitless`() = runTest {
        givenChannelEpisodesSavedForChannels()

        whenFindLimitedChannelEpisodesFor(listOf(ChannelMother.aChannel(id = CHANNEL_ID), ChannelMother.aChannel(id = CHANNEL_ID2)), 1)

        thenFindChannelEpisodes(setOf(CHANNEL_EPISODE, CHANNEL2_EPISODE))
    }

    @Test
    fun `find all episode ids by channel ordered by date added`() = runTest {
        givenEpisodesSavedForChannels(CHANNEL_ID)

        whenFindAllEpisodeIdsFor(CHANNEL_ID, ChannelOrderType.DATE_ADDED)

        thenFindEpisodeIds(listOf(EPISODE_ID4, EPISODE_ID3, EPISODE_ID2, EPISODE_ID))
    }

    @Test
    fun `find all episode ids by channel ordered by custom order`() = runTest {
        givenEpisodesSavedForChannels(CHANNEL_ID)

        whenFindAllEpisodeIdsFor(CHANNEL_ID, ChannelOrderType.CUSTOM_ORDER)

        thenFindEpisodeIds(listOf(EPISODE_ID3, EPISODE_ID2, EPISODE_ID4, EPISODE_ID))
    }

    @Test
    fun `remove an episode from channel`() = runTest {
        givenEpisodesSavedForChannels(CHANNEL_ID)
        whenDelete()
        thenEpisodeIsDeleted()
    }

    private suspend fun givenEpisodes() {
        episodeRepository.save(EPISODE_1)
        episodeRepository.save(EPISODE_2)
        episodeRepository.save(EPISODE_3)
    }

    private suspend fun givenChannelEpisodesSavedForChannels() {
        episodeRepository.save(EPISODE_1)
        episodeRepository.save(EPISODE_2)
        episodeRepository.save(EPISODE_3)
        episodeRepository.save(EPISODE_4)

        val channel = ChannelMother.aChannel(CHANNEL_ID)
        addEpisodesToChannelRepositories.add(channel, setOf(CHANNEL_EPISODE, CHANNEL_EPISODE2))

        val channel2 = ChannelMother.aChannel(CHANNEL_ID2)
        addEpisodesToChannelRepositories.add(channel2, setOf(CHANNEL2_EPISODE))
    }

    private suspend fun givenEpisodesSavedForChannels(channelId: String) {
        episodeRepository.save(EPISODE_1)
        episodeRepository.save(EPISODE_2)
        episodeRepository.save(EPISODE_3)
        episodeRepository.save(EPISODE_4)

        val channelEpisodes = setOf(
            ChannelEpisode(channelId, EPISODE_ID, OffsetDateTime.parse(EPISODE_DATE_ADDED_EPISODE_1), EPISODE_ORDER_1),
            ChannelEpisode(channelId, EPISODE_ID2, OffsetDateTime.parse(EPISODE_DATE_ADDED_EPISODE_2), EPISODE_ORDER_3),
            ChannelEpisode(channelId, EPISODE_ID3, OffsetDateTime.parse(EPISODE_DATE_ADDED_EPISODE_3), EPISODE_ORDER_4),
            ChannelEpisode(channelId, EPISODE_ID4, OffsetDateTime.parse(EPISODE_DATE_ADDED_EPISODE_4), EPISODE_ORDER_2)
        )

        val channel = ChannelMother.aChannel(channelId, episodesCount = 4)
        channelRepository.add(channel)
        addEpisodesToChannelRepositories.add(channel, channelEpisodes)
    }

    private suspend fun whenFindAllEpisodeIdsFor(channelId: String, order: ChannelOrderType) {
        episodeIdsResult = channelEpisodesRepository.findAllEpisodeIdsFrom(channelId, order)
    }

    private suspend fun whenFindLimitedChannelEpisodesFor(channel: List<Channel>, episodesPerChannelLimit: Int) {
        limitedEpisodesResult =
            channelEpisodesRepository.findChannelEpisodesLimited(channel, episodesPerChannelLimit)
    }

    private suspend fun whenSave(
        channelId: String,
        channelEpisodes: Set<ChannelEpisode> = setOf()
    ) {
        val channel = ChannelMother.aChannel(channelId)
        addEpisodesToChannelRepositories.add(channel, channelEpisodes)
    }

    private suspend fun whenDelete() {
        channelEpisodesRepository.delete(
            CHANNEL_ID,
            EPISODE_ID,
            ChannelMother.aChannel(id = CHANNEL_ID, episodesCount = 3)
        )
    }

    private suspend fun thenEpisodeIsSaved(channelId: String, episodeId: String) {
        val savedEpisodes = channelEpisodesRepository.findAllFrom(channelId)
        assertThat(savedEpisodes.items.map { it.episodeId }).contains(episodeId)
    }

    private fun thenFindChannelEpisodes(channelEpisodes: Set<ChannelEpisode>) {
        assertEquals(channelEpisodes.size, limitedEpisodesResult.size)
        assertThat(limitedEpisodesResult).containsAll(channelEpisodes)
    }

    private suspend fun thenEpisodeIsDeleted() {
        val episodesIds = channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL_ID)
        assertThat(episodesIds).doesNotContain(EPISODE_ID)
        assertThat(episodesIds.count()).isEqualTo(3)

        val channel = channelRepository.findById(CHANNEL_ID)
        assertThat(channel!!.episodesCount).isEqualTo(3)
    }

    private fun thenFindEpisodeIds(episodeIdsExpected: List<String>) {
        assertEquals(episodeIdsExpected.size, episodeIdsResult.size)
        assertThat(episodeIdsResult).containsExactlyElementsOf(episodeIdsExpected)
    }

    private fun createEpisodesTable(): DynamoDbAsyncTable<EpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(EPISODES_TABLE_NAME, TableSchema.fromBean(EpisodeItem::class.java))
    }

    private fun createAssignChannelIdToEpisodesTable(): DynamoDbAsyncTable<AssignChannelIdToEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(EPISODES_TABLE_NAME, TableSchema.fromBean(AssignChannelIdToEpisodeItem::class.java))
    }

    private fun createUpdateChannelEpisodesCountTable(): DynamoDbAsyncTable<UpdateChannelEpisodesCountItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(UpdateChannelEpisodesCountItem::class.java))
    }

    private fun createChannelTable(): DynamoDbAsyncTable<ChannelItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(ChannelItem::class.java))
    }

    private fun createChannelEpisodeTable(): DynamoDbAsyncTable<ChannelEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_EPISODES_TABLE_NAME, TableSchema.fromBean(ChannelEpisodeItem::class.java))
    }

    private companion object {
        const val TABLE_PREFIX = "dev"
        const val EPISODES_TABLE_NAME = "${TABLE_PREFIX}_episodes"
        const val CHANNELS_TABLE_NAME = "${TABLE_PREFIX}_channels"
        const val CHANNELS_EPISODES_TABLE_NAME = "${TABLE_PREFIX}_channels_episodes"
        const val EPISODE_ID = "EPISODE-123"
        const val EPISODE_ID2 = "EPISODE-1234"
        const val EPISODE_ID3 = "EPISODE-1235"
        const val EPISODE_ID4 = "EPISODE-12356"
        const val CHANNEL_ID = "CHANNEL-123"
        const val CHANNEL_ID2 = "CHANNEL-456"
        const val EPISODE_DATE_ADDED = "2025-02-17T18:03:00.000Z"
        const val EPISODE_DATE_ADDED_EPISODE_1 = "2025-02-17T18:03:00.000Z"
        const val EPISODE_DATE_ADDED_EPISODE_2 = "2025-02-18T18:03:00.000Z"
        const val EPISODE_DATE_ADDED_EPISODE_3 = "2025-02-19T18:03:00.000Z"
        const val EPISODE_DATE_ADDED_EPISODE_4 = "2025-02-20T18:03:00.000Z"
        const val EPISODE_ORDER_1 = 1000L
        const val EPISODE_ORDER_2 = 2000L
        const val EPISODE_ORDER_3 = 3000L
        const val EPISODE_ORDER_4 = 4000L
        val EPISODE_1 = EpisodeMother.buildEpisode(id = EPISODE_ID)
        val EPISODE_2 = EpisodeMother.buildEpisode(id = EPISODE_ID2)
        val EPISODE_3 = EpisodeMother.buildEpisode(id = EPISODE_ID3)
        val EPISODE_4 = EpisodeMother.buildEpisode(id = EPISODE_ID4)

        val CHANNEL_EPISODE =
            ChannelEpisode(CHANNEL_ID, EPISODE_ID, OffsetDateTime.parse(EPISODE_DATE_ADDED), EPISODE_ORDER_1)
        val CHANNEL_EPISODE2 =
            ChannelEpisode(CHANNEL_ID, EPISODE_ID2, OffsetDateTime.parse(EPISODE_DATE_ADDED), EPISODE_ORDER_3)
        val CHANNEL_EPISODE3 =
            ChannelEpisode(CHANNEL_ID, EPISODE_ID3, OffsetDateTime.parse(EPISODE_DATE_ADDED), EPISODE_ORDER_4)
        val CHANNEL2_EPISODE =
            ChannelEpisode(CHANNEL_ID2, EPISODE_ID4, OffsetDateTime.parse(EPISODE_DATE_ADDED), EPISODE_ORDER_2)

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                EpisodeItem::class.java to EPISODES_TABLE_NAME,
                ChannelItem::class.java to CHANNELS_TABLE_NAME,
                ChannelEpisodeItem::class.java to CHANNELS_EPISODES_TABLE_NAME
            )
        )
    }
}
