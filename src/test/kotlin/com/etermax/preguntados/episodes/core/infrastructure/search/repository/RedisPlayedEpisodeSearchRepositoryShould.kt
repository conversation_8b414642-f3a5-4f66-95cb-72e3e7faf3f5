package com.etermax.preguntados.episodes.core.infrastructure.search.repository

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.future.await
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration

@ExtendWith(RedisTestServer::class)
class RedisPlayedEpisodeSearchRepositoryShould {

    private lateinit var redisAsync: RedisAsyncCommands<String, String>
    private lateinit var repository: RedisPlayedEpisodeSearchRepository
    private var result: List<String>? = null

    @BeforeEach
    fun setUp() {
        redisAsync = EmbeddedRedisUtils.buildClient()
        repository = RedisPlayedEpisodeSearchRepository(redisAsync, Duration.parse(TEN_SECONDS))
    }

    @Test
    fun `save episodes`() = runTest {
        whenSave()

        thenEpisodesAreSaved()
    }

    @Test
    fun `save episodes for a language`() = runTest {
        whenSave(language = Language.EN)

        thenEpisodesAreSaved(language = Language.EN)
    }

    @Test
    fun `return null when episodes were not saved`() = runTest {
        whenFind()

        assertThat(result).isNull()
    }

    @Test
    fun `find existing episodes`() = runTest {
        givenSavedEpisodes()

        whenFind()

        assertThat(result).containsExactly(*EPISODES.toTypedArray())
    }

    @Test
    fun `set ttl when saving episodes with no language`() = runTest {
        whenSave()

        thenTtlIsSet()
    }

    @Test
    fun `set ttl when saving episodes`() = runTest {
        whenSave(language = Language.ES)

        thenTtlIsSet(language = Language.ES)
    }

    private suspend fun givenSavedEpisodes() {
        repository.save(PLAYER_ID, Language.ES.name, EPISODES)
    }

    private suspend fun whenSave(language: Language? = null) {
        repository.save(PLAYER_ID, language?.name, EPISODES)
    }

    private suspend fun whenFind() {
        result = repository.find(PLAYER_ID, Language.ES.name)
    }

    private suspend fun thenEpisodesAreSaved(language: Language? = null) {
        val savedEpisodes = repository.find(PLAYER_ID, language?.name)
        assertThat(savedEpisodes).isEqualTo(EPISODES)
    }

    private suspend fun thenTtlIsSet(language: Language? = null) {
        val ttl = redisAsync.ttl("$KEY:$PLAYER_ID:${language ?: DEFAULT}").await()
        assertThat(ttl).isGreaterThanOrEqualTo(1)
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val KEY = "pr:e:py"
        const val DEFAULT = "ne"
        const val TEN_SECONDS = "PT10S"
        val EPISODES = (1..10).map { "E-$it" }
    }
}
