package com.etermax.preguntados.episodes.core.infrastructure.repository

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.enhanced.dynamodb.model.BatchWriteItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.BatchWriteResult
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.PutRequest
import software.amazon.awssdk.services.dynamodb.model.WriteRequest
import java.util.concurrent.CompletableFuture

class DynamoDBRepositoryShould {

    private lateinit var repository: DynamoDBRepository<EpisodeItem>
    private lateinit var dynamoDbStandardClient: DynamoDbAsyncClient
    private lateinit var dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient

    @BeforeEach
    fun setUp() {
        dynamoDbEnhancedClient = mockk(relaxed = true)
        dynamoDbStandardClient = mockk(relaxed = true)
        repository = DynamoDBRepository(dynamoDbEnhancedClient, createTable())
    }

    @Test
    fun `add items bulk without retry`() = runTest {
        givenProcessedRequests()

        whenAddItem()

        thenAddedItemsRetry(times = 0)
    }

    @Test
    fun `add items bulk with all retries`() = runTest {
        givenEveryTimeUnprocessedRequests()

        whenAddItem()

        thenAddedItemsRetry(times = MAX_RETRIES)
    }

    @Test
    fun `add items bulk with two retries`() = runTest {
        givenTwoTimesUnprocessedRequests()

        whenAddItem()

        thenAddedItemsRetry(times = 2)
    }

    private fun givenProcessedRequests() {
        coEvery {
            dynamoDbEnhancedClient.batchWriteItem(any<BatchWriteItemEnhancedRequest>())
        } returns createProcessedRequests()
    }

    private fun givenEveryTimeUnprocessedRequests() {
        coEvery {
            dynamoDbEnhancedClient.batchWriteItem(any<BatchWriteItemEnhancedRequest>())
        } returns createUnprocessedRequests()
    }

    private fun givenTwoTimesUnprocessedRequests() {
        coEvery {
            dynamoDbEnhancedClient.batchWriteItem(any<BatchWriteItemEnhancedRequest>())
        } returns createUnprocessedRequests() andThen createUnprocessedRequests() andThen createProcessedRequests()
    }

    private suspend fun whenAddItem() {
        repository.addItemsBulkWithRetry(emptyList(), EpisodeItem::class.java, MAX_RETRIES)
    }

    private fun thenAddedItemsRetry(times: Int) {
        coVerify(exactly = times + 1) { dynamoDbEnhancedClient.batchWriteItem(any<BatchWriteItemEnhancedRequest>()) }
    }

    private fun createProcessedRequests(): CompletableFuture<BatchWriteResult> {
        return CompletableFuture.supplyAsync {
            BatchWriteResult.builder().unprocessedRequests(emptyMap()).build()
        }
    }

    private fun createUnprocessedRequests(): CompletableFuture<BatchWriteResult> {
        return CompletableFuture.supplyAsync {
            val putRequest = PutRequest.builder().item(createEpisodeItem()).build()
            val writeRequest = WriteRequest.builder().putRequest(putRequest).build()
            val unprocessedRequests = mapOf(TABLE_NAME to listOf(writeRequest))
            BatchWriteResult.builder().unprocessedRequests(unprocessedRequests).build()
        }
    }

    private fun createEpisodeItem(): Map<String, AttributeValue> {
        return mapOf(
            "PK" to AttributeValue.builder().s(EpisodeItem.buildPartitionKey(EPISODE_ID)).build(),
            "SK" to AttributeValue.builder().s(EpisodeItem.EPISODE_SK).build(),
            "episode_id" to AttributeValue.builder().s(EPISODE_ID).build(),
            "owner_id" to AttributeValue.builder().n("666").build(),
            "country" to AttributeValue.builder().s("US").build(),
            "language" to AttributeValue.builder().s("EN").build(),
            "name" to AttributeValue.builder().s("name").build(),
            "description" to AttributeValue.builder().s("description").build(),
            "type" to AttributeValue.builder().s("type").build(),
            "start_date" to AttributeValue.builder().n("123").build(),
            "cover" to AttributeValue.builder().s("cover").build(),
            "banner" to AttributeValue.builder().s("banner").build()
        )
    }

    private fun createTable(): DynamoDbAsyncTable<EpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(TABLE_NAME, TableSchema.fromBean(EpisodeItem::class.java))
    }

    private companion object {
        const val TABLE_PREFIX = "dev"
        const val TABLE_NAME = "${TABLE_PREFIX}_episodes"
        const val MAX_RETRIES = 5
        const val EPISODE_ID = "ABC-123"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(EpisodeItem::class.java to TABLE_NAME))
    }
}
