package com.etermax.preguntados.episodes.core.action

import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccount
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountParams
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountService
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountsToken
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.search.OverviewInfo
import com.etermax.preguntados.episodes.core.domain.search.Search
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.search.SearchChannelsByName
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class OverviewInfoSearchShould {

    private lateinit var playerAccountService: PlayerAccountService
    private lateinit var allSearch: Search
    private lateinit var channelsByName: SearchChannelsByName
    private lateinit var summaryService: SummaryService
    private lateinit var overviewInfoSearch: OverviewInfoSearch

    private lateinit var result: OverviewInfo
    private lateinit var actionData: OverviewInfoSearch.ActionData

    @BeforeEach
    fun setUp() {
        playerAccountService = mockk()
        allSearch = mockk()
        summaryService = mockk()
        channelsByName = mockk()

        overviewInfoSearch = OverviewInfoSearch(
            playerAccountService = playerAccountService,
            allSearch = allSearch,
            summaryService = summaryService,
            channelsSearch = channelsByName
        )

        actionData = OverviewInfoSearch.ActionData(
            playerId = PLAYER_ID,
            name = QUERY,
            language = LANGUAGE
        )
    }

    @Test
    fun `return overview info with player accounts, episodes and channels`() = runTest {
        givenPlayerAccounts()
        givenEpisodes()
        givenChannels()

        whenSearchOverviewInfo()

        thenReturnsOverviewInfoWith(PLAYER_ACCOUNTS, EPISODE_SUMMARIES, CHANNELS_SUMMARIES)
    }

    @Test
    fun `return overview info with empty player accounts when service returns empty`() = runTest {
        givenEmptyPlayerAccounts()
        givenEpisodes()
        givenEmptyChannels()

        whenSearchOverviewInfo()

        thenReturnsOverviewInfoWith(emptyList(), EPISODE_SUMMARIES, emptyList())
    }

    @Test
    fun `return overview info with empty episodes when search returns empty`() = runTest {
        givenPlayerAccounts()
        givenEmptyEpisodes()
        givenEmptyChannels()

        whenSearchOverviewInfo()

        thenReturnsOverviewInfoWith(PLAYER_ACCOUNTS, emptyList(), emptyList())
    }

    @Test
    fun `return overview info with empty channels`() = runTest {
        givenPlayerAccounts()
        givenEpisodes()
        givenEmptyChannels()

        whenSearchOverviewInfo()

        thenChannelsAreEmpty()
    }

    private fun givenPlayerAccounts() {
        val params = PlayerAccountParams(
            playerId = PLAYER_ID,
            query = QUERY,
            amount = 10,
            skip = 0,
            skipRestricted = false,
            fields = "is_followed"
        )
        coEvery {
            playerAccountService.search(
                params
            )
        } returns PlayerAccountsToken(PLAYER_ACCOUNTS, "")
    }

    private fun givenEmptyPlayerAccounts() {
        coEvery {
            playerAccountService.search(any())
        } returns PlayerAccountsToken.empty()
    }

    private fun givenEpisodes() {
        val params = SearchParameters(
            playerId = PLAYER_ID,
            language = Language.valueOf(LANGUAGE.uppercase()),
            name = QUERY,
            offset = 0,
            limit = 6
        )
        coEvery {
            allSearch.search(params)
        } returns EPISODES

        coEvery {
            summaryService.toEpisodesSummary(EPISODES)
        } returns EPISODE_SUMMARIES
    }

    private fun givenEmptyEpisodes() {
        coEvery {
            allSearch.search(any())
        } returns emptyList()

        coEvery {
            summaryService.toEpisodesSummary(emptyList())
        } returns emptyList()
    }

    private fun givenChannels() {
        val language = Language.valueOf(LANGUAGE.uppercase())
        coEvery {
            channelsByName.search(QUERY, language, 0, 1)
        } returns CHANNELS
        coEvery {
            summaryService.toChannelsSummary(CHANNELS)
        } returns CHANNELS_SUMMARIES
    }

    private fun givenEmptyChannels() {
        coEvery {
            channelsByName.search(any(), any(), any(), any())
        } returns emptyList()

        coEvery {
            summaryService.toChannelsSummary(emptyList())
        } returns emptyList()
    }

    private suspend fun whenSearchOverviewInfo() {
        result = overviewInfoSearch(actionData)
    }

    private fun thenReturnsOverviewInfoWith(
        playerAccounts: List<PlayerAccount>,
        episodes: List<EpisodeSummary>,
        channels: List<ChannelSummary>
    ) {
        assertThat(result.playerAccounts).isEqualTo(playerAccounts)
        assertThat(result.episodes).isEqualTo(episodes)
        assertThat(result.channels).isEqualTo(channels)
    }

    private fun thenChannelsAreEmpty() {
        assertThat(result.channels).isEmpty()
    }

    private companion object {
        const val PLAYER_ID = 123L
        const val QUERY = "test"
        const val LANGUAGE = "en"

        val PLAYER_ACCOUNTS = listOf(
            PlayerAccount(id = 456L, name = "Player 1", username = "P1", photoUrl = "url1", isFollowed = true, facebookId = null),
            PlayerAccount(id = 789L, name = "Player 2", username = "P2", photoUrl = "url2", isFollowed = false, facebookId = null)
        )

        val EPISODES = listOf<Episode>(
            mockk(relaxed = true),
            mockk(relaxed = true)
        )

        val EPISODE_SUMMARIES = listOf<EpisodeSummary>(
            mockk(relaxed = true),
            mockk(relaxed = true)
        )

        val CHANNELS = listOf<Channel>(
            mockk(relaxed = true)
        )

        val CHANNELS_SUMMARIES = listOf<ChannelSummary>(
            mockk(relaxed = true)
        )
    }
}
