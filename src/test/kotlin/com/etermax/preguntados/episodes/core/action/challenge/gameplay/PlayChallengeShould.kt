package com.etermax.preguntados.episodes.core.action.challenge.gameplay

import com.etermax.preguntados.episodes.core.ChallengeMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.episode.RemainingContent
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.exception.ChallengeAlreadyFinishedException
import com.etermax.preguntados.episodes.core.domain.exception.ChallengeNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.time.DefaultClock
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@Suppress("SameParameterValue")
class PlayChallengeShould {

    private lateinit var challengeService: ChallengeService
    private lateinit var summaryService: SummaryService
    private lateinit var clock: Clock

    private lateinit var result: RemainingContent
    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        challengeService = mockk()
        summaryService = mockk()
        clock = DefaultClock()
    }

    @Test
    fun `throw error when challenge does not exist`() = runTest {
        // Given a not existing challenge
        givenANotExistingChallenge(CHALLENGE_ID)

        whenPlayChallenge()

        thenExceptionIsThrown(ChallengeNotFoundException::class.java)
    }

    @Test
    fun `throw error when episode does not exist`() = runTest {
        givenANotExistingEpisode(EPISODE_ID)

        whenPlayChallenge()

        thenExceptionIsThrown(EpisodeNotFoundException::class.java)
    }

    @Test
    fun `ensure player initialization`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = null
        )
        coEvery { challengeService.getChallengeContext(any(), any()) } returns context

        whenPlayChallenge()

        coVerify(exactly = 1) { challengeService.ensurePlayerInitialized(PLAYER_ID, eq(context)) }
    }

    @Test
    fun `ensure finish challenge when there is no remaining content`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, "C3", null, false)
        )
        coEvery { challengeService.getChallengeContext(any(), any()) } returns context
        coEvery { challengeService.ensurePlayerInitialized(any(), any()) } returns Unit
        coEvery { challengeService.calculateRemainingContent(any()) } returns emptyList()

        whenPlayChallenge()

        coVerify(exactly = 1) { challengeService.finishChallenge(CHALLENGE_ID, PLAYER_ID, eq(context)) }
    }

    @Test
    fun `throw error when challenge is already finished`() = runTest {
        // Given a ChallengeContext
        val startDate = clock.now().minusMinutes(10)
        val endDate = clock.now().minusMinutes(1)
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(
                id = CHALLENGE_ID,
                episodeId = EPISODE_ID,
                startDate = startDate,
                endDate = endDate
            ),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, "C3", null, true)
        )
        coEvery { challengeService.getChallengeContext(any(), any()) } returns context

        whenPlayChallenge()

        thenExceptionIsThrown(ChallengeAlreadyFinishedException::class.java)
    }

    private fun givenANotExistingChallenge(challengeId: String = CHALLENGE_ID) {
        coEvery { challengeService.getChallengeContext(any(), any()) } throws ChallengeNotFoundException(challengeId)
    }

    private fun givenANotExistingEpisode(episodeId: String) {
        coEvery { challengeService.getChallengeContext(any(), any()) } throws EpisodeNotFoundException(episodeId)
    }

    private suspend fun whenPlayChallenge(playerId: Long = PLAYER_ID) {
        error = kotlin.runCatching {
            val actionData = PlayChallenge.ActionData(playerId, CHALLENGE_ID)
            val play = PlayChallenge(challengeService, summaryService, clock)
            result = play(actionData)
        }.exceptionOrNull()
    }

    private fun thenExceptionIsThrown(type: Class<*>) {
        assertThat(error).isExactlyInstanceOf(type)
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val CHALLENGE_ID = "CHA-123"
        const val EPISODE_ID = "EPI-123"
    }
}
