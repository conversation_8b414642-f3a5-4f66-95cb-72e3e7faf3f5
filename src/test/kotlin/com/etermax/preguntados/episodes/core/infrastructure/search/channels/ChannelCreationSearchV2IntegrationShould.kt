package com.etermax.preguntados.episodes.core.infrastructure.search.channels

import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.search.ChannelCreationSearchV2
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.AbstractOpenSearchSourceIntegrationShould
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertTrue

class ChannelCreationSearchV2IntegrationShould :
    AbstractOpenSearchSourceIntegrationShould(indexDescriptor = "opensearch/mapping/episodes-index-mapping-v1.0.json") {

    private lateinit var search: ChannelCreationSearchV2
    private lateinit var episodeRepository: EpisodeRepository

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk()
        search = ChannelCreationSearchV2(episodeRepository, indexName, osClient)
    }

    @Test
    fun `retrieve episodes without channel id`() = runTest {
        val episode1 = givenAnEpisode(id = "1", country = Country.US, language = Language.EN)
        val episode2 = givenAnEpisode(id = "2", country = Country.AR, language = Language.EN)
        val episode3 = givenAnEpisode(id = "3", country = Country.US, language = Language.EN)
        val episodes = listOf(episode1, episode2, episode3)
        givenEpisodesFromDynamo(episodes)

        val result = whenRetrieve(language = Language.EN)

        assertTrue(result.containsAll(episodes))
    }

    @Test
    fun `retrieve only episodes without channel id`() = runTest {
        val episode1 = givenAnEpisode(id = "1", country = Country.US, language = Language.EN)
        val episode2 = givenAnEpisode(id = "2", country = Country.AR, language = Language.EN)
        val episode3 = givenAnEpisode(id = "3", country = Country.US, language = Language.EN, channelId = "channel_id")
        val episodes = listOf(episode1, episode2, episode3)
        givenEpisodesFromDynamo(episodes)

        val result = whenRetrieve(language = Language.EN)

        assertTrue(result.containsAll(listOf(episode1, episode2)))
        assertTrue(!result.contains(episode3))
    }

    private fun givenEpisodesFromDynamo(episodes: List<Episode>) {
        coEvery {
            episodeRepository.findByIds(any())
        } returns episodes
    }

    private suspend fun whenRetrieve(
        offset: Int = 0,
        limit: Int = 10,
        language: Language? = null
    ): List<Episode> {
        return search.search(
            SearchParameters(
                playerId = 1L,
                language = language,
                sort = SortEpisode.EPISODES_FOR_CHANNEL_CREATION_V2,
                offset = offset,
                limit = limit,
                episodeType = EpisodeType.PUBLIC
            )
        )
    }
}
