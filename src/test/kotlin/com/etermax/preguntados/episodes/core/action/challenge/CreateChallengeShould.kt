package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.action.challenge.CreateChallenge.ActionData
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayerRepository
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeRepository
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeSummary
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer
import com.etermax.preguntados.episodes.core.infrastructure.time.DefaultClock
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.assertj.core.api.AssertionsForInterfaceTypes.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.Duration

class CreateChallengeShould {

    private lateinit var action: CreateChallenge
    private lateinit var response: ChallengeSummary
    private var error: Throwable? = null

    private lateinit var challengesRepository: ChallengeRepository
    private lateinit var challengePlayersRepository: ChallengePlayerRepository
    private lateinit var episodesRepository: EpisodeRepository
    private lateinit var uuidSequencer: UUIDSequencer
    private lateinit var summaryService: SummaryService
    private lateinit var profileService: ProfileService
    private lateinit var clock: Clock
    private val ttl = Duration.ofSeconds(DURATION_IN_SECONDS)

    @BeforeEach
    fun setUp() {
        challengesRepository = mockk()
        challengePlayersRepository = mockk()
        episodesRepository = mockk()
        uuidSequencer = UUIDSequencer()

        profileService = mockk()
        coEvery { profileService.find(PLAYER_ID) } returns ProfileMother.aProfile(PLAYER_ID)

        summaryService = SummaryService(profileService, mockk())
        clock = DefaultClock()
    }

    @Test
    fun `fail trying to create challenge of a non existent episode`() = runTest {
        givenAMissingEpisode(MISSING_EPISODE_ID)

        whenCreate(PLAYER_ID, MISSING_EPISODE_ID, DURATION_IN_SECONDS)

        thenEpisodeNotFoundExceptionIsThrown()
    }

    @Test
    fun `persist challenge`() = runTest {
        givenAnEpisode(EPISODE_ID)
        coEvery { challengesRepository.save(any()) } returns Unit
        coEvery { challengePlayersRepository.save(any<ChallengePlayer>()) } returns Unit

        whenCreate(PLAYER_ID, EPISODE_ID, DURATION_IN_SECONDS)

        thenChallengeIsCreated()
    }

    fun givenAMissingEpisode(episodeId: String) {
        coEvery { episodesRepository.findById(episodeId) } returns null
    }

    fun givenAnEpisode(episodeId: String) {
        coEvery { episodesRepository.findById(episodeId) } returns EpisodeMother.buildEpisode(id = episodeId)
    }

    suspend fun whenCreate(playerId: Long, episodeId: String, durationInSeconds: Long) {
        val request = ActionData(playerId, episodeId, durationInSeconds)

        action = CreateChallenge(
            challengesRepository,
            challengePlayersRepository,
            episodesRepository,
            uuidSequencer,
            summaryService,
            clock,
            ttl
        )

        error = kotlin.runCatching {
            response = action(request)
        }.exceptionOrNull()
    }

    private fun thenEpisodeNotFoundExceptionIsThrown() {
        Assertions.assertThat(error).isExactlyInstanceOf(EpisodeNotFoundException::class.java)
    }

    fun thenChallengeIsCreated() {
        assertThat(error).isNull()
        assertThat(response).isNotNull
        assertThat(response.ownerId).isEqualTo(PLAYER_ID)
        assertThat(response.players.players).filteredOnAssertions { it.profile.playerId == PLAYER_ID }.hasSize(1)
        assertThat(response.ranking.players).isEmpty()

        coVerify(exactly = 1) { challengesRepository.save(any()) }
        coVerify(exactly = 1) { challengePlayersRepository.save(any<ChallengePlayer>()) }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "EPI-123"
        const val MISSING_EPISODE_ID = "MISSING-123"
        const val DURATION_IN_SECONDS = 3600L
    }
}
