package com.etermax.preguntados.episodes.core.action

import com.etermax.preguntados.episodes.core.domain.account.PlayerAccount
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountParams
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountService
import com.etermax.preguntados.episodes.core.domain.account.PlayerAccountsToken
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SearchPlayersAccountShould {

    private lateinit var playerAccountService: PlayerAccountService
    private lateinit var searchPlayersAccount: SearchPlayersAccount
    private lateinit var result: PlayerAccountsToken

    @BeforeEach
    fun setUp() {
        playerAccountService = mockk()
        searchPlayersAccount = SearchPlayersAccount(playerAccountService)
    }

    @Test
    fun `search players with provided parameters`() = runTest {
        givenPlayerAccountServiceReturnsAccounts()

        whenSearchPlayersAccount()

        thenPlayerAccountServiceIsCalledWithCorrectParams()
        thenReturnsPlayerAccounts()
    }

    @Test
    fun `search players with empty results`() = runTest {
        givenPlayerAccountServiceReturnsEmptyResults()

        whenSearchPlayersAccount()

        thenPlayerAccountServiceIsCalledWithCorrectParams()
        thenReturnsEmptyResults()
    }

    @Test
    fun `search players with token`() = runTest {
        givenPlayerAccountServiceReturnsAccountsWithToken()

        whenSearchPlayersAccountWithToken()

        thenPlayerAccountServiceIsCalledWithToken()
        thenReturnsPlayerAccountsWithToken()
    }

    @Test
    fun `search players with skip restricted flag`() = runTest {
        givenPlayerAccountServiceReturnsAccounts()

        whenSearchPlayersAccountWithSkipRestricted()

        thenPlayerAccountServiceIsCalledWithSkipRestricted()
        thenReturnsPlayerAccounts()
    }

    @Test
    fun `search players with custom fields`() = runTest {
        givenPlayerAccountServiceReturnsAccounts()

        whenSearchPlayersAccountWithCustomFields()

        thenPlayerAccountServiceIsCalledWithCustomFields()
        thenReturnsPlayerAccounts()
    }

    private fun givenPlayerAccountServiceReturnsAccounts() {
        coEvery {
            playerAccountService.search(any())
        } returns PlayerAccountsToken(PLAYER_ACCOUNTS, "")
    }

    private fun givenPlayerAccountServiceReturnsEmptyResults() {
        coEvery {
            playerAccountService.search(any())
        } returns PlayerAccountsToken.empty()
    }

    private fun givenPlayerAccountServiceReturnsAccountsWithToken() {
        coEvery {
            playerAccountService.search(any())
        } returns PlayerAccountsToken(PLAYER_ACCOUNTS, NEXT_TOKEN)
    }

    private suspend fun whenSearchPlayersAccount() {
        val actionData = SearchPlayersAccount.ActionData(
            playerId = PLAYER_ID,
            query = QUERY,
            amount = AMOUNT,
            skip = SKIP,
            skipRestricted = null,
            fields = null,
            token = null
        )
        result = searchPlayersAccount(actionData)
    }

    private suspend fun whenSearchPlayersAccountWithToken() {
        val actionData = SearchPlayersAccount.ActionData(
            playerId = PLAYER_ID,
            query = QUERY,
            amount = AMOUNT,
            skip = SKIP,
            skipRestricted = null,
            fields = null,
            token = TOKEN
        )
        result = searchPlayersAccount(actionData)
    }

    private suspend fun whenSearchPlayersAccountWithSkipRestricted() {
        val actionData = SearchPlayersAccount.ActionData(
            playerId = PLAYER_ID,
            query = QUERY,
            amount = AMOUNT,
            skip = SKIP,
            skipRestricted = true,
            fields = null,
            token = null
        )
        result = searchPlayersAccount(actionData)
    }

    private suspend fun whenSearchPlayersAccountWithCustomFields() {
        val actionData = SearchPlayersAccount.ActionData(
            playerId = PLAYER_ID,
            query = QUERY,
            amount = AMOUNT,
            skip = SKIP,
            skipRestricted = null,
            fields = CUSTOM_FIELDS,
            token = null
        )
        result = searchPlayersAccount(actionData)
    }

    private fun thenPlayerAccountServiceIsCalledWithCorrectParams() {
        val expectedParams = PlayerAccountParams(
            playerId = PLAYER_ID,
            query = QUERY,
            amount = AMOUNT,
            skip = SKIP,
            skipRestricted = null,
            fields = null,
            token = null
        )
        coVerify { playerAccountService.search(expectedParams) }
    }

    private fun thenPlayerAccountServiceIsCalledWithToken() {
        val expectedParams = PlayerAccountParams(
            playerId = PLAYER_ID,
            query = QUERY,
            amount = AMOUNT,
            skip = SKIP,
            skipRestricted = null,
            fields = null,
            token = TOKEN
        )
        coVerify { playerAccountService.search(expectedParams) }
    }

    private fun thenPlayerAccountServiceIsCalledWithSkipRestricted() {
        val expectedParams = PlayerAccountParams(
            playerId = PLAYER_ID,
            query = QUERY,
            amount = AMOUNT,
            skip = SKIP,
            skipRestricted = true,
            fields = null,
            token = null
        )
        coVerify { playerAccountService.search(expectedParams) }
    }

    private fun thenPlayerAccountServiceIsCalledWithCustomFields() {
        val expectedParams = PlayerAccountParams(
            playerId = PLAYER_ID,
            query = QUERY,
            amount = AMOUNT,
            skip = SKIP,
            skipRestricted = null,
            fields = CUSTOM_FIELDS,
            token = null
        )
        coVerify { playerAccountService.search(expectedParams) }
    }

    private fun thenReturnsPlayerAccounts() {
        assertThat(result.playerAccounts).isEqualTo(PLAYER_ACCOUNTS)
        assertThat(result.token).isEqualTo("")
    }

    private fun thenReturnsEmptyResults() {
        assertThat(result.playerAccounts).isEmpty()
        assertThat(result.token).isEqualTo("")
    }

    private fun thenReturnsPlayerAccountsWithToken() {
        assertThat(result.playerAccounts).isEqualTo(PLAYER_ACCOUNTS)
        assertThat(result.token).isEqualTo(NEXT_TOKEN)
    }

    private companion object {
        const val PLAYER_ID = 123L
        const val QUERY = "test"
        const val AMOUNT = 10
        const val SKIP = 0
        const val TOKEN = "next-page-token"
        const val NEXT_TOKEN = "next-token-from-service"
        const val CUSTOM_FIELDS = "name,photo_url"

        val PLAYER_ACCOUNTS = listOf(
            PlayerAccount(id = 456L, name = "Player 1", username = "P1", photoUrl = "url1", isFollowed = true, facebookId = null),
            PlayerAccount(id = 789L, name = "Player 2", username = "P2", photoUrl = "url2", isFollowed = false, facebookId = null)
        )
    }
}
