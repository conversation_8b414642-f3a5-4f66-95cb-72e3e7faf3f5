package com.etermax.preguntados.episodes.core.infrastructure.profile

import com.etermax.preguntados.episodes.core.domain.profile.Profile
import java.time.OffsetDateTime

object ProfileMother {
    const val PLAYER_ID = 100L
    private const val FACEBOOK_ID = "facebookId"
    private const val SHOW_FACEBOOK_PICTURE = true
    private const val SHOW_FACEBOOK_NAME = true
    private const val PHOTO_URL = "photoUrl"
    private const val PROFILE_NAME = "playerName"
    private const val COUNTRY = "AR"
    private const val FACEBOOK_NAME = "facebookName"

    private val NOW = OffsetDateTime.now()

    fun aProfile(
        playerId: Long = PLAYER_ID,
        username: String = PROFILE_NAME,
        country: String? = COUNTRY,
        facebookId: String = FACEBOOK_ID,
        facebookName: String? = FACEBOOK_NAME,
        showFacebookPicture: Boolean = SHOW_FACEBOOK_PICTURE,
        showFacebookName: Boolean = SHOW_FACEBOOK_NAME,
        photoUrl: String? = PHOTO_URL,
        joinDate: OffsetDateTime = NOW
    ) = Profile.build(
        playerId = playerId,
        name = username,
        country = country,
        pictureUrl = photoUrl,
        facebookData = Profile.FacebookData(facebookId, facebookName, showFacebookPicture, showFacebookName),
        joinDate = joinDate.minusNanos(joinDate.nano.toLong()),
        restriction = null
    )
}
