package com.etermax.preguntados.episodes.core.infrastructure.search.filter

import com.etermax.embedded.redis.RedisTestServer
import io.lettuce.core.ClientOptions
import io.lettuce.core.RedisClient
import io.lettuce.core.RedisURI
import io.mockk.spyk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration
import kotlin.test.assertContentEquals
import kotlin.test.assertTrue

@ExtendWith(RedisTestServer::class)
class RedisFilterDataRepositoryShould {

    private val uri = RedisURI.create("redis://localhost:${RedisTestServer.port}")
    private val redisAsync = spyk(
        RedisClient.create(uri).also {
            it.options = ClientOptions.builder().autoReconnect(false).build()
        }.connect().async()
    )
    private val repository = RedisFilterDataRepository(redisAsync, Duration.ofSeconds(10))

    @Test
    fun `save and retrieve filter data`() = runTest {
        val scope = "test-scope"
        val data = "stored-test-data".toByteArray()
        repository.save(scope, data)

        val retrievedData = repository.getByScope(scope)

        assertContentEquals(data, retrievedData)
    }

    @Test
    fun `retrieve non-existent data returns empty byte array`() = runTest {
        val retrievedData = repository.getByScope("non-existent-scope")

        assertTrue(retrievedData.isEmpty())
    }
}
