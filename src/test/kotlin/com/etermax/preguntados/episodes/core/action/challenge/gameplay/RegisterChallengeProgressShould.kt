package com.etermax.preguntados.episodes.core.action.challenge.gameplay

import com.etermax.preguntados.episodes.core.ChallengeMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.RegisterChallengeProgress.ActionData
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@Suppress("SameParameterValue")
class RegisterChallengeProgressShould {

    private lateinit var challengeService: ChallengeService
    private lateinit var summaryService: SummaryService
    private lateinit var episodesRepository: EpisodeRepository

    private lateinit var result: EpisodeSummary

    @BeforeEach
    fun setUp() {
        challengeService = mockk()
        summaryService = mockk(relaxed = true)
        episodesRepository = mockk(relaxed = true)
    }

    @Test
    fun `register progress content`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = null
        )
        coEvery { challengeService.getChallengeContext(any(), any()) } returns context
        coEvery { challengeService.registerProgress(PLAYER_ID, "C1", context) } returns Unit

        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID, "C1")
        whenRegisterProgressContent(actionData)

        coVerify(exactly = 1) {
            challengeService.registerProgress(PLAYER_ID, "C1", context)
        }
    }

    @Test
    fun `increment episode views when registering content for the first time`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = null
        )
        coEvery { challengeService.getChallengeContext(any(), any()) } returns context
        coEvery { challengeService.registerProgress(PLAYER_ID, "C1", context) } returns Unit

        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID, "C1")
        whenRegisterProgressContent(actionData)

        thenEpisodeViewsIncremented(EPISODE_ID)
    }

    @Test
    fun `not increment episode views when progress already exists`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, "C1", null, false)
        )
        coEvery { challengeService.getChallengeContext(any(), any()) } returns context
        coEvery { challengeService.registerProgress(PLAYER_ID, "C2", context) } returns Unit

        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID, "C2")
        whenRegisterProgressContent(actionData)

        thenEpisodeViewsNotIncremented(EPISODE_ID)
    }

    private suspend fun whenRegisterProgressContent(data: ActionData) {
        val registerContentProgress = RegisterChallengeProgress(challengeService, summaryService, episodesRepository)
        result = registerContentProgress(data)
    }

    private fun thenEpisodeViewsIncremented(episodeId: String) {
        coVerify(exactly = 1) { episodesRepository.updateView(episodeId) }
    }

    private fun thenEpisodeViewsNotIncremented(episodeId: String) {
        coVerify(exactly = 0) { episodesRepository.updateView(episodeId) }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "EPI-123"
        const val CHALLENGE_ID = "CHA-123"
    }
}
