package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.EpisodeMother.SEQUENCE_ID
import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class CopyEpisodeShould {

    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var uuidSequencer: UUIDSequencer
    private lateinit var profileService: ProfileService
    private lateinit var clock: Clock

    private lateinit var result: EpisodeSummary

    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk(relaxed = true)
        uuidSequencer = mockk()
        profileService = mockk(relaxed = true)
        clock = mockk(relaxed = true)
        givenASequencer()
        givenAClock()
    }

    @Test
    fun `throw exception when episode does not exist`() = runTest {
        givenNoEpisode()

        whenCopy()

        thenEpisodeNotFoundExceptionIsThrown()
    }

    @Test
    fun `copy episode`() = runTest {
        givenAnEpisode()

        whenCopy()

        thenEpisodeIsCopied()
    }

    @Test
    fun `retrieve copied episode summary`() = runTest {
        givenAProfile()
        givenAnEpisode()

        whenCopy()

        thenEpisodeSummaryIsRetrieved()
    }

    private fun givenASequencer() {
        coEvery { uuidSequencer.next() } returns SEQUENCE_ID
    }

    private fun givenAClock() {
        coEvery { clock.now() } returns NOW
    }

    private fun givenNoEpisode() {
        coEvery { episodeRepository.findById(any()) } returns null
    }

    private fun givenAnEpisode() {
        coEvery { episodeRepository.findById(EPISODE_ID) } returns EPISODE
    }

    private fun givenAProfile() {
        coEvery { profileService.find(PLAYER_ID) } returns PROFILE
    }

    private suspend fun whenCopy() {
        val actionData = CopyEpisode.ActionData(PLAYER_ID, EPISODE_ID)
        val copyEpisode = CopyEpisode(episodeRepository, uuidSequencer, profileService, clock)
        error = kotlin.runCatching {
            result = copyEpisode(actionData)
        }.exceptionOrNull()
    }

    private fun thenEpisodeNotFoundExceptionIsThrown() {
        assertThat(error).isExactlyInstanceOf(EpisodeNotFoundException::class.java)
    }

    private fun thenEpisodeIsCopied() {
        coVerify(exactly = 1) { episodeRepository.save(COPIED_EPISODE) }
    }

    private fun thenEpisodeSummaryIsRetrieved() {
        assertThat(result).isEqualTo(COPIED_EPISODE_SUMMARY)
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "ABC-123"
        val NOW: OffsetDateTime = OffsetDateTime.now()
        val PROFILE = ProfileMother.aProfile(playerId = PLAYER_ID)
        val EPISODE = buildEpisode(
            id = EPISODE_ID,
            ownerId = 999,
            type = EpisodeType.PUBLIC,
            startDate = NOW.minusYears(2),
            likes = 10,
            dislikes = 5,
            reports = 3,
            views = 20
        )
        val COPIED_EPISODE = buildEpisode(
            id = SEQUENCE_ID,
            ownerId = PLAYER_ID,
            type = EpisodeType.PRIVATE,
            startDate = NOW,
            likes = 0,
            dislikes = 0,
            reports = 0,
            views = 0
        )
        val COPIED_EPISODE_SUMMARY = EpisodeSummary.from(COPIED_EPISODE, PROFILE)
    }
}
