package com.etermax.preguntados.episodes.core.infrastructure.moderation

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class RegexUrlValidatorServiceShould {

    @Test
    fun `return true for correct URLs`() {
        val validUrls = listOf(
            "https://sub.example.com",
            "https://example.com",
            "https://example.com.ar",
            "http://sub.example.com",
            "http://example.com",
            "http://example.com.ar",
            "www.example.com",
            "example.com",
            "sub.example.com",
            "www.my-site.net"
        )

        validUrls.forEach { url ->
            assertThat(RegexUrlValidatorService().isValid(url)).isTrue()
        }
    }

    @Test
    fun `return false for incorrect URLs`() {
        val invalidUrls = listOf(
            "",
            "   ",
            "http://",
            "www.",
            "example",
            "www.example.c",
            "www.exam!ple.com",
            "https://.com",
            "ftp://example.com"
        )

        invalidUrls.forEach { url ->
            assertThat(RegexUrlValidatorService().isValid(url)).isFalse()
        }
    }
}
