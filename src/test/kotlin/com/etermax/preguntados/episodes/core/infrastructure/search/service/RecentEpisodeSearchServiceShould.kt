package com.etermax.preguntados.episodes.core.infrastructure.search.service

import com.etermax.preguntados.episodes.core.domain.search.RecentEpisodeRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.AbstractOpenSearchSourceIntegrationShould
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class RecentEpisodeSearchServiceShould :
    AbstractOpenSearchSourceIntegrationShould(indexDescriptor = "opensearch/mapping/episodes-index-mapping-v1.0.json") {

    private lateinit var repository: RecentEpisodeRepository
    private lateinit var clock: Clock
    private lateinit var service: RecentEpisodeSearchService
    private lateinit var result: List<String>

    @BeforeEach
    fun setUp() {
        repository = mockk(relaxed = true)
        clock = mockk()
        coEvery { clock.now() } returns NOW
        service = RecentEpisodeSearchService(osClient, indexName, repository, clock)
    }

    @Test
    fun `use episodes from cache`() = runTest {
        givenEpisodesCached()

        whenSearch()

        thenEpisodesAreRetrieved()
    }

    @Test
    fun `do not save when cache is used`() = runTest {
        givenEpisodesCached()

        whenSearch()

        thenEpisodesAreNotSaved()
    }

    @Test
    fun `search episodes from index`() = runTest {
        givenNoEpisodesCached()
        givenEpisodesFromIndex()

        whenSearch()

        thenEpisodesFromIndexAre("EP-1", "EP-2", "EP-3")
    }

    @Test
    fun `save episodes searched from index`() = runTest {
        givenNoEpisodesCached()
        givenEpisodesFromIndex()

        whenSearch()

        thenEpisodesAreSaved()
    }

    @Test
    fun `search episodes from index greater than 7 days`() = runTest {
        givenNoEpisodesCached()
        givenOlderEpisodesFromIndex()

        whenSearch()

        thenEpisodesFromIndexAre("EP-1", "EP-4")
    }

    private fun givenEpisodesCached() {
        coEvery { repository.find(LANGUAGE.name, OFFSET, LIMIT) } returns EPISODES
    }

    private fun givenNoEpisodesCached() {
        coEvery { repository.find(any(), any(), any()) } returns null
    }

    private fun givenEpisodesFromIndex() {
        givenAnEpisode(id = "EP-1", country = COUNTRY, language = LANGUAGE, startDate = NOW.minusDays(1))
        givenAnEpisode(id = "EP-2", country = COUNTRY, language = LANGUAGE, startDate = NOW.minusDays(2))
        givenAnEpisode(id = "EP-3", country = COUNTRY, language = LANGUAGE, startDate = NOW.minusDays(3))
    }

    private fun givenOlderEpisodesFromIndex() {
        givenAnEpisode(id = "EP-1", country = COUNTRY, language = LANGUAGE, startDate = NOW.minusDays(1))
        givenAnEpisode(id = "EP-2", country = COUNTRY, language = LANGUAGE, startDate = NOW.minusDays(20))
        givenAnEpisode(id = "EP-3", country = COUNTRY, language = LANGUAGE, startDate = NOW.minusDays(30))
        givenAnEpisode(id = "EP-4", country = COUNTRY, language = LANGUAGE, startDate = NOW.minusDays(4))
    }

    private suspend fun whenSearch() {
        result = service.search(LANGUAGE, COUNTRY, OFFSET, LIMIT)
    }

    private fun thenEpisodesAreRetrieved() {
        assertThat(result).isEqualTo(EPISODES)
    }

    private fun thenEpisodesAreNotSaved() {
        coVerify(exactly = 0) { repository.save(any(), any()) }
    }

    private fun thenEpisodesFromIndexAre(vararg episodesId: String) {
        assertThat(result).containsExactlyInAnyOrder(*episodesId)
    }

    private fun thenEpisodesAreSaved() {
        coVerify(exactly = 1) { repository.save(LANGUAGE.name, result) }
    }

    private companion object {
        const val OFFSET = 0
        const val LIMIT = 10
        val LANGUAGE = Language.ES
        val COUNTRY = Country.AR
        val EPISODES = (1..10).map { "E-$it" }
        val NOW: OffsetDateTime = OffsetDateTime.now()
    }
}
