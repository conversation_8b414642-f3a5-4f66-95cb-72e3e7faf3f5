package com.etermax.preguntados.episodes.core.infrastructure.search.repository

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.core.domain.search.RecentEpisodeRepository
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.lettuce.core.api.async.RedisAsyncCommands
import kotlinx.coroutines.future.await
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration

@ExtendWith(RedisTestServer::class)
class RedisRecentEpisodeRepositoryShould {

    private lateinit var redisAsync: RedisAsyncCommands<String, String>
    private lateinit var repository: RecentEpisodeRepository
    private var result: List<String>? = null
    private var isProcessing: Boolean? = null

    @BeforeEach
    fun setUp() {
        redisAsync = EmbeddedRedisUtils.buildClient()
        repository = RedisRecentEpisodeRepository(redisAsync, Duration.parse(TEN_SECONDS))
    }

    @Test
    fun `save episodes`() = runTest {
        whenSave(language = Language.ES)

        thenEpisodesAreSaved(language = Language.ES)
    }

    @Test
    fun `save episodes with another language`() = runTest {
        whenSave(language = Language.EN)

        thenEpisodesAreSaved(language = Language.EN)
    }

    @Test
    fun `save episodes with no language and country`() = runTest {
        whenSave()

        thenEpisodesAreSaved()
    }

    @Test
    fun `return null when episodes were not saved`() = runTest {
        whenFind(0, 10)

        assertThat(result).isNull()
    }

    @Test
    fun `find existing episodes`() = runTest {
        givenSavedEpisodes()

        whenFind(0, 3)

        assertThat(result).containsExactly("E-1", "E-2", "E-3")
    }

    @Test
    fun `find next episodes`() = runTest {
        givenSavedEpisodes()

        whenFind(3, 3)

        assertThat(result).containsExactly("E-4", "E-5", "E-6")
    }

    @Test
    fun `find less episodes than limit`() = runTest {
        givenSavedEpisodes()

        whenFind(6, 20)

        assertThat(result).containsExactly("E-7", "E-8", "E-9", "E-10")
    }

    @Test
    fun `find empty episodes`() = runTest {
        givenSavedEpisodes()

        whenFind(15, 20)

        assertThat(result).isEmpty()
    }

    @Test
    fun `set ttl when saving episodes with no language and country`() = runTest {
        whenSave()

        thenTtlIsSet()
    }

    @Test
    fun `set ttl when saving episodes`() = runTest {
        whenSave(language = Language.ES)

        thenTtlIsSet(language = Language.ES)
    }

    @Test
    fun `not marked as processed`() = runTest {
        whenIsProcessing(Language.ES)

        assertThat(isProcessing).isFalse
    }

    @Test
    fun `marked as processed`() = runTest {
        givenMarkedAsProcessed()

        whenIsProcessing(Language.ES)

        assertThat(isProcessing).isTrue
    }

    @Test
    fun `not marked as processed for another language`() = runTest {
        givenMarkedAsProcessed()

        whenIsProcessing(Language.EN)

        assertThat(isProcessing).isFalse
    }

    @Test
    fun `set ttl when mark as processed`() = runTest {
        whenIsProcessing()

        thenTtlIsSet(key = PROCESSED_KEY)
    }

    private suspend fun givenMarkedAsProcessed() {
        repository.isProcessing(Language.ES.name)
    }

    private suspend fun givenSavedEpisodes() {
        repository.save(Language.ES.name, EPISODES)
    }

    private suspend fun whenSave(language: Language? = null) {
        repository.save(language?.name, EPISODES)
    }

    private suspend fun whenFind(offset: Int, limit: Int) {
        result = repository.find(Language.ES.name, offset, limit)
    }

    private suspend fun whenIsProcessing(language: Language? = null) {
        isProcessing = repository.isProcessing(language?.name)
    }

    private suspend fun thenEpisodesAreSaved(language: Language? = null) {
        val savedEpisodes = repository.find(language?.name, 0, 10)
        assertThat(savedEpisodes).isEqualTo(EPISODES)
    }

    private suspend fun thenTtlIsSet(language: Language? = null, key: String = KEY) {
        val ttl = redisAsync.ttl("$key:${language ?: DEFAULT}").await()
        assertThat(ttl).isGreaterThanOrEqualTo(1)
    }

    private companion object {
        const val KEY = "pr:e:re"
        const val PROCESSED_KEY = "pr:e:re:pk"
        const val DEFAULT = "vne"
        const val TEN_SECONDS = "PT10S"
        val EPISODES = (1..10).map { "E-$it" }
    }
}
