package com.etermax.preguntados.episodes.core.action.channel.episodes

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.episode.ChannelEpisodeOrder
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesOrderRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.ChannelEpisodesNewOrderCannotBeEmptyException
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.PlayerNotOwnChannelException
import com.etermax.preguntados.episodes.core.domain.order.OrderItemCalculator
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UpdateEpisodesOrderInChannelShould {
    private lateinit var action: UpdateEpisodesOrderInChannel
    private lateinit var episodesRepository: EpisodeRepository
    private lateinit var channelEpisodesRepository: ChannelEpisodesRepository
    private lateinit var channelEpisodesOrderRepository: ChannelEpisodesOrderRepository
    private lateinit var channelRepository: ChannelRepository
    private lateinit var orderItemCalculator: OrderItemCalculator

    private lateinit var newEpisodesOrder: Map<Int, String>
    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        episodesRepository = mockk()
        channelEpisodesRepository = mockk()
        channelEpisodesOrderRepository = mockk(relaxUnitFun = true)
        orderItemCalculator = mockk()
        channelRepository = mockk()

        action = UpdateEpisodesOrderInChannel(
            episodesRepository,
            channelEpisodesRepository,
            channelEpisodesOrderRepository,
            channelRepository,
            orderItemCalculator
        )

        givenChannel()
        givenEpisodes()
        every { orderItemCalculator.calculate() } returns MOST_RECENT_ORDER

        newEpisodesOrder = mapOf()
        error = null
    }

    @Test
    fun `fail when channel not found`() = runTest {
        newEpisodesOrder = mapOf(0 to EPISODE_1.episodeId)
        givenNoChannel()
        whenUpdateOrder(withErrorCatching = true)
        thenThrowsException<ChannelNotFoundException>()
    }

    @Test
    fun `fail when channel not owned by current player`() = runTest {
        newEpisodesOrder = mapOf(0 to EPISODE_1.episodeId)
        givenChannel(ownerId = 200L)
        whenUpdateOrder(withErrorCatching = true)
        thenThrowsException<PlayerNotOwnChannelException>()
    }

    @Test
    fun `fail when new orders is empty`() = runTest {
        newEpisodesOrder = emptyMap()
        whenUpdateOrder(withErrorCatching = true)
        thenThrowsException<ChannelEpisodesNewOrderCannotBeEmptyException>()
    }

    @Test
    fun `fail when all episodes do not belong to channel`() = runTest {
        givenOnlyEpisodeOneNotBelongingToChannel()
        newEpisodesOrder = mapOf(0 to EPISODE_1.episodeId)
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 2,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5)

        whenUpdateOrder(withErrorCatching = true)

        thenThrowsException<ChannelEpisodesNewOrderCannotBeEmptyException>()
    }

    @Test
    fun `discard only episode which does not belong to channel`() = runTest {
        givenEpisodesWithEpisodeOneNotBelongingToChannel()
        newEpisodesOrder = mapOf(
            0 to EPISODE_1.episodeId,
            1 to EPISODE_2.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 3,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5)

        whenUpdateOrder(withErrorCatching = true)

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 5500)
        )
        coVerify(exactly = 1) { channelEpisodesOrderRepository.put(updatedOrders) }
    }

    @Test
    fun `discard only episode which does not belong to current user`() = runTest {
        givenEpisodesWithEpisodeOneNotBelongingToCurrentUser()
        newEpisodesOrder = mapOf(
            0 to EPISODE_1.episodeId,
            1 to EPISODE_2.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 3,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5)

        whenUpdateOrder(withErrorCatching = true)

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 5500)
        )
        coVerify(exactly = 1) { channelEpisodesOrderRepository.put(updatedOrders) }
    }

    @Test
    fun `discard only episode which does not exist`() = runTest {
        givenEpisodesWithEpisodeOneNotExisting()
        newEpisodesOrder = mapOf(
            0 to EPISODE_1.episodeId,
            1 to EPISODE_2.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 3,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5)

        whenUpdateOrder(withErrorCatching = true)

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 5500)
        )
        coVerify(exactly = 1) { channelEpisodesOrderRepository.put(updatedOrders) }
    }

    @Test
    fun `episode changed to position 0`() = runTest {
        newEpisodesOrder = mapOf(0 to EPISODE_1.episodeId)
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 2,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5)

        whenUpdateOrder()

        verify(exactly = 1) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(
                channelId = ChannelMother.ID,
                episodeId = EPISODE_1.episodeId,
                _episodeOrder = MOST_RECENT_ORDER
            )
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `episode changed to position 1`() = runTest {
        newEpisodesOrder = mapOf(1 to EPISODE_2.episodeId)
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 3,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 5500)
        )
        coVerify(exactly = 1) { channelEpisodesOrderRepository.put(updatedOrders) }
    }

    @Test
    fun `episode changed to position 2 for episode contained`() = runTest {
        newEpisodesOrder = mapOf(2 to EPISODE_6.episodeId)
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 4,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 3500)
        )
        coVerify(exactly = 1) { channelEpisodesOrderRepository.put(updatedOrders) }
    }

    @Test
    fun `one episode changed to position 1 and other to position 3`() = runTest {
        newEpisodesOrder = mapOf(
            1 to EPISODE_2.episodeId,
            3 to EPISODE_6.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 5,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 4500),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 3500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed to adjacent positions 1 and 2`() = runTest {
        newEpisodesOrder = mapOf(
            1 to EPISODE_2.episodeId,
            2 to EPISODE_6.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 4,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 4667),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 4334)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed to adjacent positions 1 and 2 and another to 4`() = runTest {
        newEpisodesOrder = mapOf(
            1 to EPISODE_2.episodeId,
            2 to EPISODE_6.episodeId,
            4 to EPISODE_5.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 6,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 3667),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 3334),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_5.episodeId, _episodeOrder = 2000)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed to last position`() = runTest {
        newEpisodesOrder = mapOf(
            5 to EPISODE_2.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 7,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed from last to last -1 position`() = runTest {
        newEpisodesOrder = mapOf(
            4 to EPISODE_1.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 6,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_1.episodeId, _episodeOrder = 2500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed from last to last -2 position`() = runTest {
        newEpisodesOrder = mapOf(
            3 to EPISODE_1.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 5,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_1.episodeId, _episodeOrder = 3500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `one episode changed from first to last position`() = runTest {
        newEpisodesOrder = mapOf(
            5 to EPISODE_6.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 7,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `multiple episodes changed with two adjacent including first position`() = runTest {
        newEpisodesOrder = mapOf(
            0 to EPISODE_1.episodeId,
            1 to EPISODE_4.episodeId,
            4 to EPISODE_6.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 6,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 1) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(
                channelId = ChannelMother.ID,
                episodeId = EPISODE_1.episodeId,
                _episodeOrder = MOST_RECENT_ORDER
            ),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_4.episodeId, _episodeOrder = 7500),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 2500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `multiple episodes changed with three adjacent including first position`() = runTest {
        newEpisodesOrder = mapOf(
            0 to EPISODE_1.episodeId,
            1 to EPISODE_4.episodeId,
            2 to EPISODE_5.episodeId,
            4 to EPISODE_6.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 6,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 1) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(
                channelId = ChannelMother.ID,
                episodeId = EPISODE_1.episodeId,
                _episodeOrder = MOST_RECENT_ORDER
            ),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_4.episodeId, _episodeOrder = 7667),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_5.episodeId, _episodeOrder = 5334),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 2500)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `invert order from entire set when one remains in same place, set of 5 items`() = runTest {
        newEpisodesOrder = mapOf(
            0 to EPISODE_1.episodeId,
            1 to EPISODE_2.episodeId,
            3 to EPISODE_4.episodeId,
            4 to EPISODE_5.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 6,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 1) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(
                channelId = ChannelMother.ID,
                episodeId = EPISODE_1.episodeId,
                _episodeOrder = MOST_RECENT_ORDER
            ),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 6500),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_4.episodeId, _episodeOrder = 2000),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_5.episodeId, _episodeOrder = 1000)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `multiple adjacent episodes`() = runTest {
        newEpisodesOrder = mapOf(
            2 to EPISODE_6.episodeId,
            3 to EPISODE_4.episodeId,
            4 to EPISODE_3.episodeId,
            5 to EPISODE_2.episodeId,
            6 to EPISODE_8.episodeId,
            7 to EPISODE_10.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 9,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_9, EPISODE_8, EPISODE_7, EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 6715),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_4.episodeId, _episodeOrder = 6430),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_3.episodeId, _episodeOrder = 6145),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_2.episodeId, _episodeOrder = 5860),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_8.episodeId, _episodeOrder = 5575),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_10.episodeId, _episodeOrder = 5290)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    @Test
    fun `multiple adjacent episodes in two batches`() = runTest {
        newEpisodesOrder = mapOf(
            2 to EPISODE_6.episodeId,
            3 to EPISODE_4.episodeId,
            4 to EPISODE_3.episodeId,
            6 to EPISODE_8.episodeId,
            7 to EPISODE_10.episodeId
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                ChannelMother.ID,
                episodesLimit = 9,
                ChannelOrderType.CUSTOM_ORDER
            )
        } returns setOf(EPISODE_9, EPISODE_8, EPISODE_7, EPISODE_6, EPISODE_5, EPISODE_4, EPISODE_3, EPISODE_2, EPISODE_1)

        whenUpdateOrder()

        verify(exactly = 0) { orderItemCalculator.calculate() }

        val updatedOrders = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_6.episodeId, _episodeOrder = 6500),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_4.episodeId, _episodeOrder = 6000),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_3.episodeId, _episodeOrder = 5500),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_8.episodeId, _episodeOrder = 4000),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = EPISODE_10.episodeId, _episodeOrder = 3000)
        )
        coVerify(exactly = 1) {
            channelEpisodesOrderRepository.put(updatedOrders)
        }
    }

    private fun givenChannel(ownerId: Long = PLAYER_ID) {
        coEvery { channelRepository.findById(ChannelMother.ID) } returns ChannelMother.aChannel(ownerId = ownerId)
    }

    private fun givenEpisodes() {
        val episode1 = EpisodeMother.buildEpisode(id = "episode_1", channelId = ChannelMother.ID, ownerId = PLAYER_ID)
        val episode2 = EpisodeMother.buildEpisode(id = "episode_2", channelId = ChannelMother.ID, ownerId = PLAYER_ID)
        val episode3 = EpisodeMother.buildEpisode(id = "episode_3", channelId = ChannelMother.ID, ownerId = PLAYER_ID)
        val episode4 = EpisodeMother.buildEpisode(id = "episode_4", channelId = ChannelMother.ID, ownerId = PLAYER_ID)
        val episode5 = EpisodeMother.buildEpisode(id = "episode_5", channelId = ChannelMother.ID, ownerId = PLAYER_ID)
        val episode6 = EpisodeMother.buildEpisode(id = "episode_6", channelId = ChannelMother.ID, ownerId = PLAYER_ID)
        val episodes = listOf(episode1, episode2, episode3, episode4, episode5, episode6)

        coEvery { episodesRepository.findByIds(any()) } returns episodes
    }

    private fun givenOnlyEpisodeOneNotBelongingToChannel() {
        val episode1 = EpisodeMother.buildEpisode(id = "episode_1", channelId = "another_channel", ownerId = PLAYER_ID)
        val episodes = listOf(episode1)

        coEvery { episodesRepository.findByIds(any()) } returns episodes
    }

    private fun givenEpisodesWithEpisodeOneNotBelongingToChannel() {
        val episode1 = EpisodeMother.buildEpisode(id = "episode_1", channelId = "another_channel", ownerId = PLAYER_ID)
        val episode2 = EpisodeMother.buildEpisode(id = "episode_2", channelId = ChannelMother.ID, ownerId = PLAYER_ID)
        val episodes = listOf(episode1, episode2)

        coEvery { episodesRepository.findByIds(any()) } returns episodes
    }

    private fun givenEpisodesWithEpisodeOneNotBelongingToCurrentUser() {
        val episode1 = EpisodeMother.buildEpisode(
            id = "episode_1",
            channelId = ChannelMother.ID,
            ownerId = ANOTHER_PLAYER_ID
        )
        val episode2 = EpisodeMother.buildEpisode(id = "episode_2", channelId = ChannelMother.ID, ownerId = PLAYER_ID)
        val episodes = listOf(episode1, episode2)

        coEvery { episodesRepository.findByIds(any()) } returns episodes
    }

    private fun givenEpisodesWithEpisodeOneNotExisting() {
        val episode2 = EpisodeMother.buildEpisode(id = "episode_2", channelId = ChannelMother.ID, ownerId = PLAYER_ID)
        val episodes = listOf(episode2)

        coEvery { episodesRepository.findByIds(any()) } returns episodes
    }

    private fun givenNoChannel() {
        coEvery { channelRepository.findById(any<String>()) } returns null
    }

    private suspend fun whenUpdateOrder(withErrorCatching: Boolean = false) {
        runCatching {
            val data = UpdateEpisodesOrderInChannel.ActionData(PLAYER_ID, ChannelMother.ID, newEpisodesOrder)
            action.invoke(data)
        }.onFailure {
            if (withErrorCatching) {
                error = it
            } else {
                throw it
            }
        }
    }

    private inline fun <reified T : RuntimeException> thenThrowsException() {
        Assertions.assertThat(error)
            .isNotNull
            .isInstanceOf(T::class.java)
    }

    private companion object {
        const val PLAYER_ID = 100L
        const val ANOTHER_PLAYER_ID = 200L
        const val MOST_RECENT_ORDER = 10000L
        val EPISODE_1 = ChannelMother.aChannelEpisode(episodeId = "episode_1", episodeOrder = 1000)
        val EPISODE_2 = ChannelMother.aChannelEpisode(episodeId = "episode_2", episodeOrder = 2000)
        val EPISODE_3 = ChannelMother.aChannelEpisode(episodeId = "episode_3", episodeOrder = 3000)
        val EPISODE_4 = ChannelMother.aChannelEpisode(episodeId = "episode_4", episodeOrder = 4000)
        val EPISODE_5 = ChannelMother.aChannelEpisode(episodeId = "episode_5", episodeOrder = 5000)
        val EPISODE_6 = ChannelMother.aChannelEpisode(episodeId = "episode_6", episodeOrder = 6000)
        val EPISODE_7 = ChannelMother.aChannelEpisode(episodeId = "episode_7", episodeOrder = 7000)
        val EPISODE_8 = ChannelMother.aChannelEpisode(episodeId = "episode_8", episodeOrder = 8000)
        val EPISODE_9 = ChannelMother.aChannelEpisode(episodeId = "episode_9", episodeOrder = 9000)
        val EPISODE_10 = ChannelMother.aChannelEpisode(episodeId = "episode_10", episodeOrder = 10000)
    }
}
