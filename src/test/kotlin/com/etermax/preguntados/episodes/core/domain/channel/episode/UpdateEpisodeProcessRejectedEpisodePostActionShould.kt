package com.etermax.preguntados.episodes.core.domain.channel.episode

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UpdateEpisodeProcessRejectedEpisodePostActionShould {
    private lateinit var action: UpdateEpisodeProcessRejectedChannelEpisodePostAction
    private lateinit var unpublishedRepository: ChannelUnpublishedEpisodesRepository
    private lateinit var channelEpisodesService: ChannelEpisodesService

    private var episode: Episode? = null
    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        unpublishedRepository = mockk(relaxUnitFun = true)
        channelEpisodesService = mockk(relaxUnitFun = true)
        action = UpdateEpisodeProcessRejectedChannelEpisodePostAction(unpublishedRepository, channelEpisodesService)

        episode = null
        error = null
    }

    @Test
    fun `remove from unpublished episodes`() = runTest {
        whenExecute(newStatus = EpisodeStatus.REJECTED)
        thenDeleteFromUnpublished()
    }

    @Test
    fun `remove channel from episode`() = runTest {
        whenExecute(newStatus = EpisodeStatus.REJECTED)
        thenRemoveChannelFromEpisode()
    }

    @Test
    fun `not remove when status not changed`() = runTest {
        whenExecute(newStatus = null)
        thenDoNothing()
    }

    @Test
    fun `not remove when is not REJECTED`() = runTest {
        val status = EpisodeStatus.values().toMutableList()
        status.remove(EpisodeStatus.REJECTED)

        status.forEach { s ->
            whenExecute(newStatus = s)
            thenDoNothing()
        }
    }

    @Test
    fun `not remove when has no channel id`() = runTest {
        whenExecute(newStatus = EpisodeStatus.PENDING, channelId = null)
        thenDoNothing()
    }

    @Test
    fun `not throw error when fails`() = runTest {
        givenAnError()
        whenExecute()
        thenNotThrowError()
    }

    private fun givenAnError() {
        coEvery { unpublishedRepository.delete(any()) } throws RuntimeException("error")
    }

    private suspend fun whenExecute(
        newStatus: EpisodeStatus? = EpisodeStatus.REJECTED,
        channelId: String? = CHANNEL_ID
    ) {
        error = runCatching {
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, status = EpisodeStatus.PENDING)
            action.execute(episode!!, UpdateChannelEpisodePostActionData(newStatus, channelId = channelId))
        }.exceptionOrNull()
    }

    private fun thenDeleteFromUnpublished() {
        val item = ChannelUnpublishedEpisode(CHANNEL_ID, EPISODE_ID)
        coVerify(exactly = 1) { unpublishedRepository.delete(item) }
    }

    private fun thenRemoveChannelFromEpisode() {
        coVerify(exactly = 1) { channelEpisodesService.removeChannelFromEpisode(EPISODE_ID) }
    }

    private fun thenDoNothing() {
        coVerify(exactly = 0) { unpublishedRepository.delete(any()) }
        coVerify(exactly = 0) { channelEpisodesService.removeChannelFromEpisode(any()) }
    }

    private fun thenNotThrowError() {
        assertThat(error).isNull()
    }

    private companion object {
        const val CHANNEL_ID = ChannelMother.ID
        const val EPISODE_ID = "episode_id"
    }
}
