package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.ChallengeMother.aChallengeDetail
import com.etermax.preguntados.episodes.core.action.challenge.FindSortedChallenges.PlayerSortedChallengeSummary
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.challenge.CustomSortValueCalculator
import com.etermax.preguntados.episodes.core.infrastructure.time.DefaultClock
import com.etermax.preguntados.sortable.games.core.domain.pagination.SortedGames
import com.etermax.preguntados.sortable.games.core.domain.pagination.sortValue.FullSortValueCalculator
import com.etermax.preguntados.sortable.games.core.infrastructure.clock.Clock
import com.etermax.preguntados.sortable.games.core.infrastructure.pagination.InMemorySortedGames
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.AssertionsForInterfaceTypes.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FindSortedChallengesShould {

    private lateinit var sortedGames: SortedGames
    private lateinit var challengeService: ChallengeService
    private lateinit var summaryService: SummaryService

    private lateinit var result: PlayerSortedChallengeSummary

    @BeforeEach
    fun setUp() {
        sortedGames = InMemorySortedGames(
            sortValueCalculator = CustomSortValueCalculator(),
            clock = object : Clock {
                override fun now() = DefaultClock().now()
            }
        )
        challengeService = mockk()
        summaryService = SummaryService(mockk(relaxed = true), mockk())
    }

    @Test
    fun `find sorted challenges`() = runTest {
        givenChallenges(PLAYER_ID)

        whenFind(PLAYER_ID)

        thenChallengesAreFound()
    }

    private fun givenChallenges(playerId: Long) {
        coEvery { challengeService.getChallengeDetailsByUser(any()) } returns listOf(
            aChallengeDetail(CHALLENGE_1, playerId),
            aChallengeDetail(CHALLENGE_2, playerId)
        )
    }

    private suspend fun whenFind(playerId: Long) {
        val actionData = FindSortedChallenges.ActionData(
            playerId,
            FindSortedChallenges.ActionData.Section.ALL,
            FindSortedChallenges.ActionData.Pagination(10, 0),
            FindSortedChallenges.ActionData.Pagination(10, 0)
        )
        val findSortedChallenges = FindSortedChallenges(sortedGames, challengeService, summaryService)
        result = findSortedChallenges(actionData)
    }

    private fun thenChallengesAreFound() {
        assertThat(result.challenges).hasSize(2)
        assertThat(result.pendingChallenges).hasSize(0)
    }

    companion object {
        const val PLAYER_ID = 666L
        const val CHALLENGE_1 = "1"
        const val CHALLENGE_2 = "2"
    }
}
