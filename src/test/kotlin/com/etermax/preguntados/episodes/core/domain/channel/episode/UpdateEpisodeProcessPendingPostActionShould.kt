package com.etermax.preguntados.episodes.core.domain.channel.episode

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UpdateEpisodeProcessPendingPostActionShould {
    private lateinit var action: UpdateChannelEpisodeProcessPendingPostAction
    private lateinit var repository: ChannelUnpublishedEpisodesRepository

    private var episode: Episode? = null
    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        repository = mockk(relaxUnitFun = true)
        action = UpdateChannelEpisodeProcessPendingPostAction(repository)

        episode = null
        error = null
    }

    @Test
    fun `mark channel when is PENDING and channelId changed`() = runTest {
        whenExecute(oldStatus = EpisodeStatus.DRAFT, newStatus = EpisodeStatus.PENDING)
        thenMarkEpisodeToNewChannel()
    }

    @Test
    fun `mark channel when status not changed but channelId did`() = runTest {
        whenExecute(
            oldStatus = EpisodeStatus.PENDING,
            newStatus = EpisodeStatus.PENDING,
            oldChannelId = ANOTHER_CHANNEL_ID,
            newChannelId = CHANNEL_ID
        )
        thenUnMarkEpisodeFromOldChannel()
        thenMarkEpisodeToNewChannel()
    }

    @Test
    fun `mark old channel when status changed and new channelId is null`() = runTest {
        whenExecute(
            oldStatus = EpisodeStatus.PENDING,
            newStatus = EpisodeStatus.PENDING,
            oldChannelId = CHANNEL_ID,
            newChannelId = null
        )
        thenMarkEpisodeToNewChannel()
    }

    @Test
    fun `un-mark previous channel id when changed`() = runTest {
        whenExecute(oldChannelId = ANOTHER_CHANNEL_ID, newChannelId = CHANNEL_ID)
        thenUnMarkEpisodeFromOldChannel()
    }

    @Test
    fun `un-mark previous channel id when removed`() = runTest {
        whenExecute(oldChannelId = ANOTHER_CHANNEL_ID, newChannelId = "")
        thenUnMarkEpisodeFromOldChannel()
    }

    @Test
    fun `not mark channel when is PENDING but new channel id is empty`() = runTest {
        whenExecute(oldStatus = EpisodeStatus.DRAFT, newStatus = EpisodeStatus.PENDING, newChannelId = "")
        thenNotMarkEpisodeToChannel()
    }

    @Test
    fun `not mark channel when is PUBLISHED`() = runTest {
        whenExecute(newStatus = EpisodeStatus.PUBLISHED)
        thenNotMarkEpisodeToChannel()
    }

    @Test
    fun `not mark channel when is REJECTED`() = runTest {
        whenExecute(newStatus = EpisodeStatus.REJECTED)
        thenNotMarkEpisodeToChannel()
    }

    @Test
    fun `not mark channel when is DRAFT`() = runTest {
        whenExecute(newStatus = EpisodeStatus.DRAFT)
        thenNotMarkEpisodeToChannel()
    }

    @Test
    fun `mark channel when not change status but change channel id`() = runTest {
        whenExecute(
            oldChannelId = null,
            newChannelId = CHANNEL_ID,
            oldStatus = EpisodeStatus.PENDING,
            newStatus = EpisodeStatus.PENDING
        )
        thenMarkEpisodeToNewChannel()
    }

    @Test
    fun `not mark channel when has no channel id`() = runTest {
        whenExecute(newChannelId = null)
        thenNotMarkEpisodeToChannel()
    }

    @Test
    fun `not mark channel when new status is null, old status is PENDING but no changed channelId`() = runTest {
        whenExecute(newStatus = null)
        thenNotMarkEpisodeToChannel()
    }

    @Test
    fun `not throw error when fail`() = runTest {
        givenAnError()
        whenExecute()
        thenNotThrowError()
    }

    private fun givenAnError() {
        coEvery { repository.add(any()) } throws RuntimeException("error")
    }

    private suspend fun whenExecute(
        oldChannelId: String? = null,
        newChannelId: String? = CHANNEL_ID,
        oldStatus: EpisodeStatus = EpisodeStatus.DRAFT,
        newStatus: EpisodeStatus? = EpisodeStatus.PENDING
    ) {
        error = runCatching {
            episode = EpisodeMother.buildEpisode(status = oldStatus, channelId = oldChannelId)
            action.execute(episode!!, UpdateChannelEpisodePostActionData(status = newStatus, channelId = newChannelId))
        }.exceptionOrNull()
    }

    private fun thenMarkEpisodeToNewChannel() {
        val item = ChannelUnpublishedEpisode(CHANNEL_ID, episode!!.id)
        coVerify(exactly = 1) { repository.add(item) }
    }

    private fun thenUnMarkEpisodeFromOldChannel() {
        val item = ChannelUnpublishedEpisode(ANOTHER_CHANNEL_ID, episode!!.id)
        coVerify(exactly = 1) { repository.delete(item) }
    }

    private fun thenNotMarkEpisodeToChannel() {
        coVerify(exactly = 0) { repository.add(any()) }
    }

    private fun thenNotThrowError() {
        Assertions.assertThat(error).isNull()
    }

    private companion object {
        const val CHANNEL_ID = ChannelMother.ID
        const val ANOTHER_CHANNEL_ID = "another_channel"
    }
}
