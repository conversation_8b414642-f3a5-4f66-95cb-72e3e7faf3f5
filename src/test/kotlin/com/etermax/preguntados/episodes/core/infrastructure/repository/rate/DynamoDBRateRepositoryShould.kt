package com.etermax.preguntados.episodes.core.infrastructure.repository.rate

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.episode.Rate.Type.LIKE
import com.etermax.preguntados.episodes.core.domain.episode.rate.RateRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.rate.tables.RateItem
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoDBRateRepositoryShould {

    private lateinit var table: DynamoDbAsyncTable<RateItem>
    private lateinit var repository: RateRepository

    private var result: Rate.Type? = null

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        repository =
            DynamoDBRateRepository(dynamoDbEnhancedClient, createTable())
        table = dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(RateItem::class.java))
    }

    @ParameterizedTest(name = "save rate {0}")
    @EnumSource(value = Rate.Type::class, mode = EnumSource.Mode.INCLUDE)
    fun `save rate`(rate: Rate.Type) = runTest {
        whenSave(rate == LIKE)

        thenSavedRateIs(rate)
    }

    @ParameterizedTest(name = "find rate {0}")
    @EnumSource(value = Rate.Type::class, mode = EnumSource.Mode.INCLUDE)
    fun `find rate`(rate: Rate.Type) = runTest {
        givenARate(rate == LIKE)

        whenFindBy()

        thenRateIs(rate)
    }

    @Test
    fun `find not existing rate`() = runTest {
        whenFindBy()

        thenRateDoesNotExist()
    }

    private suspend fun givenARate(liked: Boolean) {
        repository.save(PLAYER_ID, EPISODE_ID, liked)
    }

    private suspend fun whenSave(liked: Boolean) {
        repository.save(PLAYER_ID, EPISODE_ID, liked)
    }

    private suspend fun whenFindBy() {
        result = repository.findBy(PLAYER_ID, EPISODE_ID)
    }

    private suspend fun thenSavedRateIs(rate: Rate.Type) {
        result = repository.findBy(PLAYER_ID, EPISODE_ID)
        thenRateIs(rate)
    }

    private fun thenRateIs(rate: Rate.Type) {
        assertThat(result).isEqualTo(rate)
    }

    private fun thenRateDoesNotExist() {
        assertThat(result).isNull()
    }

    private fun createTable(): DynamoDbAsyncTable<RateItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(TABLE_NAME, TableSchema.fromBean(RateItem::class.java))
    }

    private companion object {
        const val TABLE_NAME = "dev_episodes"
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "ABC-666"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(RateItem::class.java to TABLE_NAME))
    }
}
