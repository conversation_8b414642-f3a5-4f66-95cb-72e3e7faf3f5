package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.ANOTHER_PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.CREATION_DATE
import com.etermax.preguntados.episodes.core.ChannelMother.MODIFICATION_DATE
import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.aChannel
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelSearchFilters
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoDBChannelRepositoryShould {
    private lateinit var repository: DynamoDBChannelRepository
    private var channel: Channel? = null
    private lateinit var channels: PaginatedItems<Channel>
    private var hasChannels = false

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        repository = DynamoDBChannelRepository(dynamoDbEnhancedClient, createTable())
        hasChannels = false
    }

    @Test
    fun `add a channel`() = runTest {
        whenAdd()
        thenIsAdded()
    }

    @Test
    fun `update a channel`() = runTest {
        givenAChannel()
        whenUpdate()
        thenIsUpdated()
    }

    @Test
    fun `return multiple channels for owner ordered by modification date`() = runTest {
        givenMultipleChannels()
        whenFindForOwner()
        thenReturnsOwnerChannels()
    }

    @Test
    fun `return multiple channels paginated`() = runTest {
        givenMultipleChannels()
        whenFindForOwner(itemSize = 1)
        thenReturnsPaginatedOwnerChannels()
    }

    @Test
    fun `return multiple channels paginated from last evaluated key`() = runTest {
        givenMultipleChannels()
        whenFindForOwner(itemSize = 1, MODIFICATION_DATE.toMillis().toString())
        thenReturnsPaginatedFromLastEvaluatedKey()
    }

    @Test
    fun `return multiple channels paginated only with episodes`() = runTest {
        givenMultipleChannels(withEpisodes = true)
        whenFindForOwner(onlyWithEpisodes = true)
        thenReturnSingleChannelWithEpisodesForOwner(lastEvaluatedKey = null)
    }

    @Test
    fun `return multiple channels paginated only with episodes limited by 1`() = runTest {
        givenMultipleChannels(withEpisodes = true)
        whenFindForOwner(itemSize = 1, onlyWithEpisodes = true)
        thenReturnsOwnerChannelsWithEpisodesLimitedByOne()
    }

    @Test
    fun `return multiple channels paginated only with episodes limited by 1 for another user`() = runTest {
        givenMultipleChannels(withEpisodes = false)
        whenFindForOwner(itemSize = 1, onlyWithEpisodes = true, playerId = ANOTHER_PLAYER_ID)
        thenReturnsEmptyChannelsForAnotherOwnerChannels()
    }

    @Test
    fun `return multiple channels paginated only with episodes limited by 1 from last evaluated key`() = runTest {
        givenMultipleChannels(withEpisodes = true)
        whenFindForOwner(
            itemSize = 1,
            onlyWithEpisodes = true,
            lastEvaluatedKey = MODIFICATION_DATE.toMillis().toString()
        )
        thenReturnSingleChannelWithEpisodesForOwner(
            lastEvaluatedKey = CREATION_DATE.toMillis().toString()
        )
    }

    @Test
    fun `return owner has no channels`() = runTest {
        givenMultipleChannels()
        whenCheckHasChannels(ownerId = 999)
        thenHasChannels(false)
    }

    @Test
    fun `return owner has channels`() = runTest {
        givenMultipleChannels()
        whenCheckHasChannels()
        thenHasChannels(true)
    }

    @Test
    fun `return owner has no channels with episodes`() = runTest {
        givenMultipleChannels()
        whenCheckHasChannels(onlyWithEpisodes = true)
        thenHasChannels(false)
    }

    @Test
    fun `return owner has channels with episodes`() = runTest {
        givenMultipleChannels(withEpisodes = true)
        whenCheckHasChannels(onlyWithEpisodes = true)
        thenHasChannels(true)
    }

    private suspend fun givenMultipleChannels(withEpisodes: Boolean = false) {
        val episodesCount = if (withEpisodes) 10 else 0
        repository.add(aChannel(episodesCount = episodesCount))
        repository.add(aChannel(id = ANOTHER_CHANNEL_ID, lastModificationDate = MODIFICATION_DATE))
        repository.add(aChannel(ownerId = ANOTHER_PLAYER_ID, id = "third_channel"))
    }

    private suspend fun givenAChannel() {
        repository.add(aChannel())
    }

    private suspend fun whenAdd() {
        repository.add(aChannel())
    }

    private suspend fun whenUpdate() {
        val updatedChannel = aChannel(
            lastModificationDate = MODIFICATION_DATE,
            language = Language.FR,
            episodesCount = 10
        )
        repository.put(updatedChannel)
    }

    private suspend fun whenFindForOwner(
        itemSize: Int = 20,
        lastEvaluatedKey: String? = null,
        onlyWithEpisodes: Boolean = false,
        playerId: Long = PLAYER_ID
    ) {
        val filters = ChannelSearchFilters(playerId, onlyWithEpisodes)
        val pagination = PaginationFilter(itemSize, lastEvaluatedKey)
        channels = repository.search(filters, pagination)
    }

    private suspend fun whenCheckHasChannels(ownerId: Long = PLAYER_ID, onlyWithEpisodes: Boolean = false) {
        val filters = ChannelSearchFilters(ownerId, onlyWithEpisodes)
        hasChannels = repository.hasChannels(filters)
    }

    private suspend fun thenIsAdded() {
        channel = repository.findById(CHANNEL_ID)
        assertThat(channel).isEqualTo(aChannel())
    }

    private suspend fun thenIsUpdated() {
        channel = repository.findById(CHANNEL_ID)
        assertThat(channel).isEqualTo(
            aChannel(
                lastModificationDate = MODIFICATION_DATE,
                language = Language.FR,
                episodesCount = 10
            )
        )
    }

    private fun thenReturnsOwnerChannels() {
        val items = channels.items.toList()
        assertThat(items.count()).isEqualTo(2)

        val lastChannel = aChannel()
        assertThat(channels.lastEvaluatedKey).isNull()
        assertThat(items[0]).isEqualTo(aChannel(id = ANOTHER_CHANNEL_ID, lastModificationDate = MODIFICATION_DATE))
        assertThat(items[1]).isEqualTo(lastChannel)
    }

    private fun thenReturnSingleChannelWithEpisodesForOwner(lastEvaluatedKey: String?) {
        val items = channels.items.toList()
        assertThat(channels.lastEvaluatedKey).isEqualTo(lastEvaluatedKey)
        assertThat(items.count()).isEqualTo(1)
        assertThat(items.first()).isEqualTo(aChannel(episodesCount = 10))
    }

    private fun thenReturnsOwnerChannelsWithEpisodesLimitedByOne() {
        val items = channels.items.toList()
        assertThat(channels.lastEvaluatedKey!!).isEqualTo(CREATION_DATE.toMillis().toString())
        assertThat(items.count()).isEqualTo(1)
    }

    private fun thenReturnsEmptyChannelsForAnotherOwnerChannels() {
        val items = channels.items.toList()
        assertThat(channels.lastEvaluatedKey).isNull()
        assertThat(items.count()).isEqualTo(0)
    }

    private fun thenReturnsPaginatedOwnerChannels() {
        val items = channels.items.toList()
        assertThat(items.count()).isEqualTo(1)

        val onlyChannel = aChannel(id = ANOTHER_CHANNEL_ID, lastModificationDate = MODIFICATION_DATE)
        assertThat(channels.lastEvaluatedKey).isEqualTo(onlyChannel.lastModificationDate.toMillis().toString())
        assertThat(items[0]).isEqualTo(onlyChannel)
    }

    private fun thenReturnsPaginatedFromLastEvaluatedKey() {
        val items = channels.items.toList()
        assertThat(items.count()).isEqualTo(1)

        val onlyChannel = aChannel()
        assertThat(channels.lastEvaluatedKey).isEqualTo(onlyChannel.lastModificationDate.toMillis().toString())
        assertThat(items[0]).isEqualTo(onlyChannel)
    }

    private fun thenHasChannels(flag: Boolean) {
        assertThat(hasChannels).isEqualTo(flag)
    }

    private fun createTable(): DynamoDbAsyncTable<ChannelItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(TABLE_NAME, TableSchema.fromBean(ChannelItem::class.java))
    }

    private companion object {
        const val TABLE_NAME = "dev_channels"
        const val CHANNEL_ID = ChannelMother.ID
        const val ANOTHER_CHANNEL_ID = "another_channel_id"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(ChannelItem::class.java to TABLE_NAME))
    }
}
