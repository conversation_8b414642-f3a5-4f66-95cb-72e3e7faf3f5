package com.etermax.preguntados.episodes.core.doubles

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import io.mockk.coEvery
import io.mockk.mockk

class ChannelUnpublishedEpisodesFactory {
    companion object {
        fun mock(
            channelId: String = ChannelMother.ID,
            count: Int = ChannelMother.UNPUBLISHED_EPISODES,
            sameAnswerForAll: Boolean = false
        ): ChannelUnpublishedEpisodesService {
            val mock = mockk<ChannelUnpublishedEpisodesService>()

            if (sameAnswerForAll) {
                coEvery { mock.count(any()) } returns count
            } else {
                coEvery { mock.count(channelId) } returns count
            }
            return mock
        }
    }
}
