package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.COVER_URL
import com.etermax.preguntados.episodes.core.ChannelMother.CREATION_DATE
import com.etermax.preguntados.episodes.core.ChannelMother.DESCRIPTION
import com.etermax.preguntados.episodes.core.ChannelMother.LANGUAGE
import com.etermax.preguntados.episodes.core.ChannelMother.NAME
import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.WEBSITE
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.exception.*
import com.etermax.preguntados.episodes.core.domain.moderation.ModerationService
import com.etermax.preguntados.episodes.core.domain.order.OrderItemCalculator
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.doubles.ChannelUnpublishedEpisodesFactory
import com.etermax.preguntados.episodes.core.infrastructure.moderation.RegexUrlValidatorService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CreateChannelShould {

    private lateinit var uuidSequencer: UUIDSequencer
    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService
    private lateinit var moderationService: ModerationService
    private lateinit var channelRepository: ChannelRepository
    private lateinit var orderItemCalculator: OrderItemCalculator
    private lateinit var clock: Clock

    private lateinit var action: CreateChannel
    private var result: ChannelSummary? = null
    private var thrownException: RuntimeException? = null

    @BeforeEach
    fun setUp() {
        initializeMocks()

        action = CreateChannel(
            uuidSequencer,
            SummaryService(profileService, unpublishedEpisodesService),
            channelRepository,
            orderItemCalculator,
            ChannelValidatorService(moderationService, RegexUrlValidatorService()),
            clock
        )

        result = null
        thrownException = null
    }

    @Test
    fun `return channel summary`() = runTest {
        whenCreate()
        thenReturnsSummary()
    }

    @Test
    fun `return channel summary without description`() = runTest {
        whenCreate(description = null)
        thenReturnsSummaryWithoutDescription()
    }

    @Test
    fun `return channel summary without website`() = runTest {
        whenCreate(website = null)
        thenReturnsSummaryWithoutWebsite()
    }

    @Test
    fun `return channel summary without language`() = runTest {
        whenCreate()
        thenReturnsSummaryWithoutLanguage()
    }

    @Test
    fun `save channel`() = runTest {
        whenCreate()
        thenSavesChannel()
    }

    @Test
    fun `not save channel when profile fails`() = runTest {
        givenFindProfileFail()
        whenCreate()
        thenNotSaveChannel()
    }

    @Test
    fun `validations - name cannot be empty`() = runTest {
        whenCreate(name = "")
        thenThrowsException<ChannelNameEmptyException>()
    }

    @Test
    fun `validations - name cannot be blank`() = runTest {
        whenCreate(name = "   ")
        thenThrowsException<ChannelNameEmptyException>()
    }

    @Test
    fun `validations - name cannot be longer than 30 characters`() = runTest {
        whenCreate(name = 'A'.repeat(31))
        thenThrowsException<ChannelNameLongerTooLongException>()
    }

    @Test
    fun `validations - name must accomplish moderation`() = runTest {
        givenNoModeratedName()
        whenCreate(name = NO_MODERATED_NAME)
        thenThrowsException<ChannelNameNotAllowedException>()
    }

    @Test
    fun `validations - description must accomplish moderation`() = runTest {
        givenNoModeratedDescription()
        whenCreate(description = NO_MODERATED_DESCRIPTION)
        thenThrowsException<ChannelDescriptionNotAllowedException>()
    }

    @Test
    fun `validations - description cannot be longer than 80 characters`() = runTest {
        whenCreate(description = 'A'.repeat(81))
        thenThrowsException<ChannelDescriptionLongerTooLongException>()
    }

    @Test
    fun `validations - no validate description when is null`() = runTest {
        whenCreate(description = null)
        thenDoesNotModerateDescription()
    }

    @Test
    fun `validations - website cannot be empty`() = runTest {
        whenCreate(website = "")
        thenThrowsException<ChannelInvalidWebsiteException>()
    }

    @Test
    fun `validations - website cannot be blank`() = runTest {
        whenCreate(website = "   ")
        thenThrowsException<ChannelInvalidWebsiteException>()
    }

    @Test
    fun `validations - website must be a valid url`() = runTest {
        whenCreate(website = "not-a-url")
        thenThrowsException<ChannelInvalidWebsiteException>()
    }

    @Test
    fun `validations - coverUrl cannot be empty`() = runTest {
        whenCreate(coverUrl = "")
        thenThrowsException<ChannelInvalidCoverUrlException>()
    }

    @Test
    fun `validations - coverUrl cannot be blank`() = runTest {
        whenCreate(coverUrl = "   ")
        thenThrowsException<ChannelInvalidCoverUrlException>()
    }

    @Test
    fun `validations - coverUrl must be a valid URL`() = runTest {
        whenCreate(coverUrl = "not-a-url")
        thenThrowsException<ChannelInvalidCoverUrlException>()
    }

    private fun givenNoModeratedName() {
        coEvery { moderationService.isTextAllowed(PLAYER_ID, LANGUAGE, NO_MODERATED_NAME) } returns false
    }

    private fun givenNoModeratedDescription() {
        coEvery { moderationService.isTextAllowed(PLAYER_ID, LANGUAGE, NO_MODERATED_DESCRIPTION) } returns false
    }

    private fun givenFindProfileFail() {
        coEvery { profileService.find(any()) } throws RuntimeException("error")
    }

    private suspend fun whenCreate(
        name: String = NAME,
        description: String? = DESCRIPTION,
        website: String? = WEBSITE,
        coverUrl: String = COVER_URL
    ) {
        val actionData = ActionData(
            playerId = PLAYER_ID,
            _name = name,
            _description = description,
            _website = website,
            type = null,
            coverUrl = coverUrl,
            moderationLanguage = LANGUAGE
        )
        try {
            result = action.invoke(actionData)
        } catch (e: RuntimeException) {
            thrownException = e
        }
    }

    private fun thenReturnsSummary() {
        assertThat(result).isEqualTo(ChannelMother.aChannelSummary())
    }

    private fun thenReturnsSummaryWithoutDescription() {
        assertThat(result!!.description).isNull()
    }

    private fun thenReturnsSummaryWithoutWebsite() {
        assertThat(result!!.website).isNull()
    }

    private fun thenReturnsSummaryWithoutLanguage() {
        assertThat(result!!.language).isNull()
    }

    private fun thenSavesChannel() {
        coVerify(exactly = 1) { channelRepository.add(ChannelMother.aChannel()) }
    }

    private fun thenNotSaveChannel() {
        coVerify(exactly = 0) { channelRepository.add(any()) }
    }

    private fun thenDoesNotModerateDescription() {
        val onceForName = 1
        coVerify(exactly = onceForName) { moderationService.isTextAllowed(any(), any(), any()) }
    }

    private inline fun <reified T : RuntimeException> thenThrowsException() {
        assertThat(thrownException)
            .isNotNull
            .isInstanceOf(T::class.java)

        coVerify(exactly = 0) { channelRepository.add(any()) }
    }

    private fun initializeMocks() {
        uuidSequencer = mockk()
        profileService = mockk()
        moderationService = mockk()
        channelRepository = mockk(relaxed = true)
        orderItemCalculator = mockk()
        clock = mockk()
        unpublishedEpisodesService = ChannelUnpublishedEpisodesFactory.mock()

        every { clock.now() } returns CREATION_DATE
        every { orderItemCalculator.calculate() } returns ChannelMother.ORDER
        coEvery { profileService.find(PLAYER_ID) } returns PROFILE
        coEvery { uuidSequencer.next() } returns CHANNEL_ID
        coEvery { moderationService.isTextAllowed(PLAYER_ID, LANGUAGE, NAME) } returns true
        coEvery { moderationService.isTextAllowed(PLAYER_ID, LANGUAGE, DESCRIPTION) } returns true
    }

    private fun Char.repeat(count: Int): String = this.toString().repeat(count)

    private companion object {
        const val CHANNEL_ID = ChannelMother.ID
        const val NO_MODERATED_NAME = "no_moderated_channel_name"
        const val NO_MODERATED_DESCRIPTION = "no_moderated_channel_description"
        val PROFILE = ProfileMother.aProfile(playerId = PLAYER_ID)
    }
}
