package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.action.episode.DeleteEpisode.ActionData
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.delete.EpisodeDeleteService
import com.etermax.preguntados.episodes.core.domain.exception.PlayerNotOwnEpisodeException
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DeleteEpisodeShould {

    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var deleteService: EpisodeDeleteService

    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk(relaxed = true)
        deleteService = mockk(relaxed = true)

        givenAnEpisode()
        error = null
    }

    @Test
    fun `delete an episode`() = runTest {
        whenDelete()
        thenEpisodeIsDeleted()
    }

    @Test
    fun `do nothing when episode not exist`() = runTest {
        givenNoEpisode()
        whenDelete()
        thenDoNothing()
    }

    @Test
    fun `not delete when player not own episode`() = runTest {
        givenAnEpisode()
        whenDelete(playerId = ANOTHER_PLAYER_ID)
        thenDoNothing()
    }

    @Test
    fun `throw error when player not own episode`() = runTest {
        givenAnEpisode()
        whenDelete(playerId = ANOTHER_PLAYER_ID)
        assertThat(error).isExactlyInstanceOf(PlayerNotOwnEpisodeException::class.java)
    }

    private fun givenAnEpisode() {
        coEvery { episodeRepository.findById(EPISODE_ID) } returns EPISODE
    }

    private fun givenNoEpisode() {
        coEvery { episodeRepository.findById(any()) } returns null
    }

    private suspend fun whenDelete(playerId: Long = PLAYER_ID) {
        error = runCatching {
            val actionData = ActionData(playerId, EPISODE_ID)
            val deleteEpisode = DeleteEpisode(episodeRepository, deleteService)
            deleteEpisode(actionData)
        }.exceptionOrNull()
    }

    private fun thenEpisodeIsDeleted() {
        coVerify(exactly = 1) { deleteService.delete(EPISODE) }
    }

    private fun thenDoNothing() {
        coVerify(exactly = 0) { episodeRepository.delete(any()) }
        coVerify(exactly = 0) { deleteService.delete(any()) }
    }

    private companion object {
        const val PLAYER_ID = 100L
        const val ANOTHER_PLAYER_ID = 200L
        const val EPISODE_ID = "ABC-123"
        val EPISODE = EpisodeMother.buildEpisode(id = EPISODE_ID, ownerId = PLAYER_ID)
    }
}
