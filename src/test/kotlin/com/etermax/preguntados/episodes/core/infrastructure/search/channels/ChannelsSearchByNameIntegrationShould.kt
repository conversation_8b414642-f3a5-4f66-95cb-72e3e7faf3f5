package com.etermax.preguntados.episodes.core.infrastructure.search.channels

import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.DynamoDBChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.search.SearchChannelsByName
import com.etermax.preguntados.episodes.core.infrastructure.search.Tokenizer
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class ChannelsSearchByNameIntegrationShould : AbstractChannelsSearchIntegrationShould() {

    private val channelsRepository: ChannelRepository by lazy {
        DynamoDBChannelRepository(dbClient, dbTable)
    }
    private val search by lazy {
        SearchChannelsByName(
            repository = channelsRepository,
            searchClient = osClient,
            indexName = indexNamePerTest,
            tokenizer = Tokenizer()
        )
    }

    @Test
    fun `no channels for this language`() = runTest {
        givenAChannel(name = "Back to the Future", language = Language.EN)

        val result = search.search("Back to the Future", Language.AR, 0, 10)

        assertEquals(emptyList(), result)
    }

    @Test
    fun `no channels for that text`() = runTest {
        givenAChannel(name = "Back to the Future", language = Language.EN)

        val result = search.search("Ironman", Language.EN, 0, 10)

        assertEquals(emptyList(), result)
    }

    @Test
    fun `not enough episodes`() = runTest {
        givenAChannel(name = "Back to the Future", language = Language.EN, episodesCount = 1)

        val result = search.search("Future", Language.EN, 0, 10)

        assertEquals(emptyList(), result)
    }

    @Test
    fun `channel found`() = runTest {
        val channel = givenAChannel(name = "Back to the Future", language = Language.EN, episodesCount = 2)

        val result = search.search("Future", Language.EN, 0, 10)

        assertEquals(listOf(channel), result)
    }

    @Test
    fun `channel pagination`() = runTest {
        val channel1 = givenAChannel(name = "Back to the Future 1", language = Language.EN, episodesCount = 2)
        val channel2 = givenAChannel(name = "Back to the Future 2", language = Language.EN, episodesCount = 2)
        val channel3 = givenAChannel(name = "Back to the Future 3", language = Language.EN, episodesCount = 2)
        val channel4 = givenAChannel(name = "Futureworld", language = Language.EN, episodesCount = 2)
        val channel5 = givenAChannel(name = "X-Men: Days of Future Past", language = Language.EN, episodesCount = 2)
        val channel6 = givenAChannel(name = "The Future", language = Language.EN, episodesCount = 2)

        givenAChannel(name = "Jurassic Park 1", language = Language.EN, episodesCount = 2)
        givenAChannel(name = "Jurassic Park 2", language = Language.EN, episodesCount = 2)
        givenAChannel(name = "Jurassic Park 3", language = Language.EN, episodesCount = 2)

        val resultPage1 = search.search("Future", Language.EN, 0, 4)
        assertEquals(setOf(channel1, channel2, channel3, channel4), resultPage1.toSet())

        val resultPage2 = search.search("Future", Language.EN, 4, 4)
        assertEquals(setOf(channel5, channel6), resultPage2.toSet())
    }
}
