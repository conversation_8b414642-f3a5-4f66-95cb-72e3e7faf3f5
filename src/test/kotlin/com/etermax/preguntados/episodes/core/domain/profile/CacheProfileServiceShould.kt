package com.etermax.preguntados.episodes.core.domain.profile

import com.etermax.preguntados.episodes.core.domain.metric.CounterMetric
import com.etermax.preguntados.episodes.core.domain.profile.repository.ProfileRepository
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother.aProfile
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CacheProfileServiceShould {

    private lateinit var service: CacheProfileService

    private lateinit var profileService: ProfileService
    private lateinit var repository: ProfileRepository
    private lateinit var metric: CounterMetric

    private lateinit var profile: Profile
    private lateinit var profiles: List<Profile>
    private lateinit var cachedProfile: Profile
    private lateinit var profileFromService: Profile

    @BeforeEach
    fun setUp() {
        profileService = mockk(relaxed = true)
        repository = mockk(relaxed = true)
        metric = mockk(relaxed = true)

        service = CacheProfileService(profileService, repository, metric)

        cachedProfile = aProfile(playerId = PLAYER_ID_CACHED)
        profileFromService = aProfile(playerId = PLAYER_ID_FROM_SERVICE)
    }

    @Test
    fun `return profile from service when it is not cached`() = runTest {
        givenAnExistingButNotCachedProfile()

        whenFindWith(PLAYER_ID_FROM_SERVICE)

        thenGetsProfileFromProfileService()
    }

    @Test
    fun `track miss cache metric when it is not cached`() = runTest {
        givenAnExistingButNotCachedProfile()

        whenFindWith(PLAYER_ID_FROM_SERVICE)

        coVerify(exactly = 1) { metric.count(MISS) }
    }

    @Test
    fun `not track hit cache metric when it is not cached`() = runTest {
        givenAnExistingButNotCachedProfile()

        whenFindWith(PLAYER_ID_FROM_SERVICE)

        coVerify(exactly = 0) { metric.count(HIT) }
    }

    @Test
    fun `track hit cache metric when it is cached`() = runTest {
        givenACachedProfile()

        whenFindWith(PLAYER_ID_CACHED)

        coVerify(exactly = 1) { metric.count(HIT) }
    }

    @Test
    fun `not track miss cache metric when it is cached`() = runTest {
        givenACachedProfile()

        whenFindWith(PLAYER_ID_CACHED)

        coVerify(exactly = 0) { metric.count(MISS) }
    }

    @Test
    fun `cache profile when it is not cached`() = runTest {
        givenAnExistingButNotCachedProfile()

        whenFindWith(PLAYER_ID_FROM_SERVICE)

        thenProfileIsCached()
    }

    @Test
    fun `cache profiles when it is not cached`() = runTest {
        givenAnExistingButNotCachedProfile()

        whenFindManyWith(listOf(PLAYER_ID_FROM_SERVICE))

        thenProfileIsCached()
    }

    @Test
    fun `return cached profile`() = runTest {
        givenACachedProfile()

        whenFindWith(PLAYER_ID_CACHED)

        thenCachedProfileIsReturned()
    }

    @Test
    fun `return cached profiles`() = runTest {
        givenACachedProfile()

        whenFindManyWith(listOf(PLAYER_ID_CACHED))

        thenCachedProfilesAreReturned()
    }

    @Test
    fun `not call service when profile is cached`() = runTest {
        givenACachedProfile()

        whenFindWith(PLAYER_ID_CACHED)

        thenDoesNotProfileFromProfileService()
    }

    private fun givenAnExistingButNotCachedProfile() {
        coEvery { repository.find(PLAYER_ID_FROM_SERVICE) } returns null
        coEvery { profileService.find(PLAYER_ID_FROM_SERVICE) } returns profileFromService
        coEvery { profileService.findMany(listOf(PLAYER_ID_FROM_SERVICE)) } returns listOf(profileFromService)
    }

    private fun givenACachedProfile() {
        coEvery { repository.find(PLAYER_ID_CACHED) } returns cachedProfile
        coEvery { repository.find(listOf(PLAYER_ID_CACHED)) } returns mapOf(cachedProfile.playerId to cachedProfile)
    }

    private suspend fun whenFindWith(playerId: Long) {
        profile = service.find(playerId)
    }

    private suspend fun whenFindManyWith(playerIds: List<Long>) {
        profiles = service.findMany(playerIds)
    }

    private fun thenGetsProfileFromProfileService() {
        coVerify(exactly = 1) { profileService.find(PLAYER_ID_FROM_SERVICE) }
        assertThat(profile).isEqualTo(profileFromService)
    }

    private fun thenProfileIsCached() {
        coVerify(exactly = 1) { repository.save(profileFromService) }
    }

    private fun thenCachedProfileIsReturned() {
        assertThat(profile).isEqualTo(cachedProfile)
    }

    private fun thenCachedProfilesAreReturned() {
        assertThat(profiles).isEqualTo(listOf(cachedProfile))
    }

    private fun thenDoesNotProfileFromProfileService() {
        coVerify(exactly = 0) { profileService.find(any()) }
    }

    private companion object {
        const val PLAYER_ID_FROM_SERVICE = ProfileMother.PLAYER_ID
        const val PLAYER_ID_CACHED = PLAYER_ID_FROM_SERVICE + 1
        const val HIT = "hit"
        const val MISS = "miss"
    }
}
