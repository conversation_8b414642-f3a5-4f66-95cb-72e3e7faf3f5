package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.AssignChannelIdToEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelEpisodesCountItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import kotlin.test.assertTrue

class DynamoDbAddEpisodesToChannelRepositoryShould {
    private lateinit var channelRepository: DynamoDBChannelRepository
    private lateinit var channelEpisodesRepository: DynamoChannelEpisodesRepository
    private lateinit var addEpisodesToChannelRepositories: DynamoDbAddEpisodesToChannelRepository

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        val channelTable = createChannelTable()
        val channelEpisodesTable = createChannelEpisodeTable()
        val assignChannelIdToEpisode = createAssignChannelIdToEpisodesTable()

        channelRepository = DynamoDBChannelRepository(dynamoDbEnhancedClient, channelTable)

        channelEpisodesRepository = DynamoChannelEpisodesRepository(
            dynamoDbEnhancedClient,
            createUpdateChannelEpisodesCountTable(),
            channelEpisodesTable,
            channelEpisodesOrderNormalizer = mockk(relaxed = true)
        )

        addEpisodesToChannelRepositories = DynamoDbAddEpisodesToChannelRepository(
            dynamoDbEnhancedClient,
            assignChannelIdToEpisode,
            channelTable,
            channelEpisodesTable
        )
    }

    @Test
    fun `add channel with 3 episodes`() = runTest {
        whenAdd(3)
        thenSaved(3)
    }

    @Test
    fun `add channel with 100 episodes`() = runTest {
        whenAdd(100)
        thenSaved(100)
    }

    private suspend fun whenAdd(count: Int) {
        val channel = ChannelMother.aChannel(id = CHANNEL_ID)
        val episodes = List(count) {
            ChannelMother.aChannelEpisode(channelId = CHANNEL_ID, episodeId = "episode_$it")
        }
        addEpisodesToChannelRepositories.add(channel, episodes.toSet())
    }

    private suspend fun thenSaved(count: Int) {
        val channel = ChannelMother.aChannel(id = CHANNEL_ID)
        val episodesIds = List(count) { "episode_$it" }

        val persistedChannel = channelRepository.findById(CHANNEL_ID)
        assertThat(persistedChannel).isEqualTo(channel)

        val persistedEpisodes = channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL_ID)

        assertThat(persistedEpisodes.count()).isEqualTo(count)
        episodesIds.forEach { episodeId ->
            assertTrue { persistedEpisodes.contains(episodeId) }
        }
    }

    private fun createChannelTable(): DynamoDbAsyncTable<ChannelItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(ChannelItem::class.java))
    }

    private fun createChannelEpisodeTable(): DynamoDbAsyncTable<ChannelEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_EPISODES_TABLE_NAME, TableSchema.fromBean(ChannelEpisodeItem::class.java))
    }

    private fun createAssignChannelIdToEpisodesTable(): DynamoDbAsyncTable<AssignChannelIdToEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(
                EPISODES_TABLE_NAME,
                TableSchema.fromBean(
                    AssignChannelIdToEpisodeItem::class.java
                )
            )
    }

    private fun createUpdateChannelEpisodesCountTable(): DynamoDbAsyncTable<UpdateChannelEpisodesCountItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(
                CHANNELS_TABLE_NAME,
                TableSchema.fromBean(
                    UpdateChannelEpisodesCountItem::class.java
                )
            )
    }

    private companion object {
        const val TABLE_PREFIX = "dev"
        const val EPISODES_TABLE_NAME = "${TABLE_PREFIX}_episodes"
        const val CHANNELS_TABLE_NAME = "${TABLE_PREFIX}_channels"
        const val CHANNELS_EPISODES_TABLE_NAME = "${TABLE_PREFIX}_channels_episodes"
        const val CHANNEL_ID = ChannelMother.ID

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                EpisodeItem::class.java to EPISODES_TABLE_NAME,
                ChannelItem::class.java to CHANNELS_TABLE_NAME,
                ChannelEpisodeItem::class.java to CHANNELS_EPISODES_TABLE_NAME
            )
        )
    }
}
