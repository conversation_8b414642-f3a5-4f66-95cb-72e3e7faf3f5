package com.etermax.preguntados.episodes.core.infrastructure.search.channels

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.search.ChannelCreationSearchV2
import com.etermax.preguntados.episodes.core.infrastructure.search.v2.OpenSearchEpisodeItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch.core.SearchRequest
import org.opensearch.client.opensearch.core.SearchResponse
import org.opensearch.client.opensearch.core.search.Hit
import org.opensearch.client.opensearch.core.search.HitsMetadata
import java.util.concurrent.CompletableFuture

class ChannelCreationSearchV2Should {

    private lateinit var channelCreationSearchV2: ChannelCreationSearchV2
    private lateinit var client: OpenSearchAsyncClient
    private lateinit var episodeRepository: EpisodeRepository

    private lateinit var searchParameters: SearchParameters
    private lateinit var result: List<Episode>
    private var match: Boolean = false

    @BeforeEach
    fun setUp() {
        client = mockk()
        episodeRepository = mockk()
        channelCreationSearchV2 = ChannelCreationSearchV2(
            client = client,
            episodesIndexName = INDEX_NAME,
            episodeRepository = episodeRepository
        )
    }

    @Test
    fun `match when sort is episodes for channel creation v2`() = runTest {
        givenParameters()

        whenMatch()

        thenShouldMatch()
    }

    @Test
    fun `not match when sort is not episodes for channel creation v2`() = runTest {
        givenParameters(sort = SortEpisode.LIKES)

        whenMatch()

        thenShouldNotMatch()
    }

    @Test
    fun `return channel creation episodes`() = runTest {
        givenParameters()
        givenEpisodeIdsFromOpenSearch(EPISODES)
        givenEpisodesFromDynamo(EPISODES)

        whenSearch()

        thenShouldReturnEpisodesFilteredBy(EPISODES)
    }

    @Test
    fun `return episodes without channel id`() = runTest {
        givenParameters()
        givenEpisodeIdsFromOpenSearch(EPISODES)
        givenEpisodesFromDynamo(EPISODES_WITH_CHANNEL_ID)

        whenSearch()

        thenShouldReturnEpisodesFilteredBy(listOf())
    }
    private fun givenParameters(
        sort: SortEpisode = SortEpisode.EPISODES_FOR_CHANNEL_CREATION_V2,
        language: Language? = EpisodeMother.LANGUAGE,
        offset: Int = 0,
        limit: Int = 10
    ) {
        searchParameters = SearchParameters(
            playerId = 1L,
            language = language,
            sort = sort,
            offset = offset,
            limit = limit,
            episodeType = EpisodeType.PUBLIC
        )
    }

    private fun givenEpisodeIdsFromOpenSearch(episodes: List<Episode>) {
        coEvery {
            client.search(
                any<SearchRequest>(),
                OpenSearchEpisodeItem::class.java
            )
        } returns CompletableFuture.completedFuture(
            buildSearchResponse(episodes)
        )
    }

    private fun givenEpisodesFromDynamo(episodes: List<Episode>) {
        coEvery {
            episodeRepository.findByIds(any())
        } returns episodes
    }

    private suspend fun whenMatch() {
        match = channelCreationSearchV2.match(searchParameters)
    }

    private suspend fun whenSearch() {
        result = channelCreationSearchV2.search(searchParameters)
    }

    private fun thenShouldMatch() {
        Assertions.assertThat(match).isTrue()
    }

    private fun thenShouldNotMatch() {
        Assertions.assertThat(match).isFalse()
    }

    private fun thenShouldReturnEpisodesFilteredBy(expectedEpisodes: List<Episode>) {
        Assertions.assertThat(result).isEqualTo(expectedEpisodes)
    }

    private companion object {

        const val INDEX_NAME = "test-index"

        val EPISODES = listOf(
            EpisodeMother.buildEpisode(id = "E_123", likes = 150, views = 2000),
            EpisodeMother.buildEpisode(id = "E_456", likes = 200, views = 3000),
            EpisodeMother.buildEpisode(
                id = "E_789",
                likes = 200,
                views = 3000,
                language = Language.ES,
                country = Country.AR
            )
        )

        val EPISODES_WITH_CHANNEL_ID = listOf(
            EpisodeMother.buildEpisode(id = "E_123", likes = 150, views = 2000, channelId = "channel"),
            EpisodeMother.buildEpisode(id = "E_456", likes = 200, views = 3000, channelId = "channel"),
            EpisodeMother.buildEpisode(
                id = "E_789",
                likes = 200,
                views = 3000,
                language = Language.ES,
                country = Country.AR,
                channelId = "channel"
            )
        )

        fun buildSearchResponse(episodes: List<Episode>): SearchResponse<OpenSearchEpisodeItem> {
            val hits = episodes.map { episode ->
                Hit.Builder<OpenSearchEpisodeItem>()
                    .index(INDEX_NAME)
                    .id("E#" + episode.id)
                    .source(
                        OpenSearchEpisodeItem(episodeId = episode.id)
                    )
                    .build()
            }

            return SearchResponse.Builder<OpenSearchEpisodeItem>()
                .took(1)
                .timedOut(false)
                .shards {
                    it.failed(0).successful(1).total(1)
                }
                .hits(
                    HitsMetadata.Builder<OpenSearchEpisodeItem>()
                        .hits(hits)
                        .build()
                )
                .build()
        }
    }
}
