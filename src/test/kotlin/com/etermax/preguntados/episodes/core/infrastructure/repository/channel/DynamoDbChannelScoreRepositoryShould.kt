package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.quality.ChannelQuality
import com.etermax.preguntados.episodes.core.domain.quality.Quality
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelQualityItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelScoreItem
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoDbChannelScoreRepositoryShould {
    private lateinit var repository: DynamoDbChannelScoreRepository
    private lateinit var channelRepository: DynamoDBChannelRepository
    private lateinit var dynamoDbEnhancedAsyncClient: DynamoDbEnhancedAsyncClient
    private lateinit var dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient

    @BeforeEach
    fun setUp() {
        val dynamoDbClient = dynamoDbTestServer.buildAsyncClient()
        dynamoDbEnhancedClient = DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbClient).build()
        val scoreTable = createScoreTable()
        val table = createTable()
        dynamoDbEnhancedAsyncClient = dynamoDbTestServer.buildEnhancedAsyncClient(dynamoDbClient)
        channelRepository = DynamoDBChannelRepository(dynamoDbEnhancedClient, createChannelTable())
        repository = DynamoDbChannelScoreRepository(scoreTable, table, dynamoDbEnhancedAsyncClient)
    }

    @Test
    fun `update channel score`() = runTest {
        givenAChannel(ChannelMother.aChannel())
        whenUpdateScore()
        thenScoreIsUpdated()
    }

    @Test
    fun `update quality`() = runTest {
        val channel1 = ChannelMother.aChannel()
        val channel2 = ChannelMother.aChannel(id = ANOTHER_CHANNEL_ID)
        givenTwoChannels(channel1, channel2)

        whenUpdateTwoChannelScores()

        thenTwoScoresAreUpdated(channel1, channel2)
    }

    @Test
    fun `update many channel scores`() = runTest {
        val channelQuantity = 30 // Más que el límite de 25 por transacción
        val channels = givenManyChannels(channelQuantity)

        whenUpdateManyChannelScores(channels)

        thenManyScoresAreUpdated(channels)
    }

    @Test
    fun `should update second transaction chunk when first one fails due to duplicate channel id`() = runTest {
        // given
        val channelQuantity = 28
        val channels = givenManyChannels(channelQuantity)
        val scores = channels.mapIndexed { index, channel ->
            val channelId = if (index == 20) "1" else channel.id // Simulate duplicate channel id in first chunk
            ChannelQuality(id = channelId, quality = index * 10)
        }

        // when
        repository.updateQuality(scores)

        // then
        thenChunkScoresRemainUnchanged(channels, 0, 25)
        thenChunkScoresAreUpdated(channels, 25, channels.size)
    }

    @Test
    fun `should update first transaction chunk when second one fails due to duplicate channel id`() = runTest {
        // given
        val channelQuantity = 28
        val channels = givenManyChannels(channelQuantity)
        val scores = channels.mapIndexed { index, channel ->
            val channelId = if (index == 26) "25" else channel.id // Simulate duplicate channel id in second chunk
            ChannelQuality(id = channelId, quality = index * 10)
        }

        // when
        repository.updateQuality(scores)

        // then
        thenChunkScoresAreUpdated(channels, 0, 25)
        thenChunkScoresRemainUnchanged(channels, 25, channels.size)
    }

    @Test
    fun `should handle exact transaction limit of 25 items`() = runTest {
        // given
        val channelQuantity = 25 // Exact transaction limit
        val channels = givenManyChannels(channelQuantity)
        val scores = channels.mapIndexed { index, channel ->
            ChannelQuality(id = channel.id, quality = index * 10)
        }

        // when
        repository.updateQuality(scores)

        // then
        thenChunkScoresAreUpdated(channels, 0, channels.size)
    }

    @Test
    fun `should handle more than two transaction chunks`() = runTest {
        // given
        val channelQuantity = 60 // More than two chunks
        val channels = givenManyChannels(channelQuantity)
        val scores = channels.mapIndexed { index, channel ->
            ChannelQuality(id = channel.id, quality = index * 10)
        }

        // when
        repository.updateQuality(scores)

        // then
        thenChunkScoresAreUpdated(channels, 0, channels.size)
    }

    @Test
    fun `should handle empty list of scores`() = runTest {
        // given
        val scores = emptyList<Quality>()

        // when
        repository.updateQuality(scores)

        // then
        // No assertions needed as we just want to verify it doesn't throw
    }

    @Test
    fun `should handle single score update`() = runTest {
        // given
        val channel = ChannelMother.aChannel()
        givenAChannel(channel)
        val score = ChannelQuality(channel.id, 100)

        // when
        repository.updateQuality(listOf(score))

        // then
        val updatedChannel = channelRepository.findById(channel.id)!!
        assertThat(updatedChannel.statistics.quality).isEqualTo(100)
        thenOtherChannelFieldsHasNotBeenUpdated(updatedChannel, channel)
    }

    @Test
    fun `should handle non-existent channel gracefully`() = runTest {
        // given
        val nonExistentChannelId = "non-existent"
        val score = ChannelQuality(nonExistentChannelId, 100)

        // when
        repository.updateQuality(listOf(score))

        // then
        // No assertions needed as we just want to verify it doesn't throw
    }

    @Test
    fun `should handle failure in last chunk of multiple chunks`() = runTest {
        // given
        val channelQuantity = 60
        val channels = givenManyChannels(channelQuantity)
        val scores = channels.mapIndexed { index, channel ->
            val channelId = if (index == 55) "54" else channel.id // Simulate duplicate in last chunk
            ChannelQuality(id = channelId, quality = index * 10)
        }

        // when
        repository.updateQuality(scores)

        // then
        thenChunkScoresAreUpdated(channels, 0, 50) // First two chunks should be updated
        thenChunkScoresRemainUnchanged(channels, 50, channels.size) // Last chunk should remain unchanged
    }

    @Test
    fun `should keep scores unchanged when all chunks fail`() = runTest {
        // given
        val channelQuantity = 28
        val channels = givenManyChannels(channelQuantity)
        val scores = channels.mapIndexed { index, channel ->
            // Use a non-existent channel ID to ensure the transaction fails
            ChannelQuality(id = "non-existent-$index", quality = index * 10)
        }

        // when
        repository.updateQuality(scores)

        // then
        thenChunkScoresRemainUnchanged(channels, 0, channels.size)
    }

    @Test
    fun `retry failed transaction excluding id's that caused the failure`() = runTest {
        val channelQuantity = 30 // Más que el límite de 25 por transacción
        val avoidSaveIndexes = listOf(26, 27)
        val channels = givenManyChannels(channelQuantity, avoidSaveIndexes)

        whenUpdateManyChannelScores(channels)

        thenChunkScoresAreUpdated(channels, 0, 25)
        thenChunkScoresAreUpdated(channels, listOf(25, 28, 29))
        thenChunkQualityChannelsAreNotCreated(avoidSaveIndexes)
    }

    private suspend fun givenAChannel(channel: Channel) {
        channelRepository.add(channel)
    }

    private suspend fun givenTwoChannels(channel1: Channel, channel2: Channel) {
        channelRepository.add(channel1)
        channelRepository.add(channel2)
    }

    private suspend fun givenManyChannels(quantity: Int, avoidSaveIndexes: List<Int> = emptyList()): List<Channel> {
        val channels = (0 until quantity).map {
            ChannelMother.aChannel(id = it.toString())
        }
        channels
            .filterIndexed { index, _ -> index !in avoidSaveIndexes }
            .forEach { channelRepository.add(it) }
        return channels
    }

    private suspend fun whenUpdateScore() {
        repository.updateScore(CHANNEL_ID, NEW_SCORE)
    }

    private suspend fun whenUpdateTwoChannelScores() {
        repository.updateQuality(
            listOf(
                ChannelQuality(CHANNEL_ID, NEW_SCORE),
                ChannelQuality(ANOTHER_CHANNEL_ID, ANOTHER_SCORE)
            )
        )
    }

    private suspend fun whenUpdateManyChannelScores(channels: List<Channel>) {
        val scores = channels.mapIndexed { index, channel ->
            ChannelQuality(channel.id, index * 10)
        }
        repository.updateQuality(scores)
    }

    private suspend fun thenScoreIsUpdated() {
        val channel = channelRepository.findById(CHANNEL_ID)
        assertThat(channel).isNotNull
        assertThat(channel!!.statistics.score).isEqualTo(NEW_SCORE)
    }

    private suspend fun thenTwoScoresAreUpdated(channel1: Channel, channel2: Channel) {
        val updatedChannel1 = channelRepository.findById(channel1.id)
        val updatedChannel2 = channelRepository.findById(channel2.id)

        assertThat(updatedChannel1).isNotNull
        assertThat(updatedChannel1!!.statistics.quality).isEqualTo(NEW_SCORE)
        thenOtherChannelFieldsHasNotBeenUpdated(updatedChannel1, channel1)

        assertThat(updatedChannel2).isNotNull
        assertThat(updatedChannel2!!.statistics.quality).isEqualTo(ANOTHER_SCORE)
        thenOtherChannelFieldsHasNotBeenUpdated(updatedChannel1, channel1)
    }

    private suspend fun thenManyScoresAreUpdated(channels: List<Channel>) {
        val updatedChannels = channels.mapNotNull { channelRepository.findById(it.id) }
        assertThat(updatedChannels).hasSize(channels.size)

        updatedChannels.forEachIndexed { index, channel ->
            assertThat(channel.statistics.quality).isEqualTo(index * 10)
            thenOtherChannelFieldsHasNotBeenUpdated(channel, channels[index])
        }
    }

    private fun thenOtherChannelFieldsHasNotBeenUpdated(
        updatedChannel: Channel,
        channel: Channel
    ) {
        assertThat(updatedChannel.id).isEqualTo(channel.id)
        assertThat(updatedChannel.name).isEqualTo(channel.name)
        assertThat(updatedChannel.description).isEqualTo(channel.description)
        assertThat(updatedChannel.coverUrl).isEqualTo(channel.coverUrl)
        assertThat(updatedChannel.statistics.episodes).isEqualTo(channel.statistics.episodes)
        assertThat(updatedChannel.statistics.subscribers).isEqualTo(channel.statistics.subscribers)
        assertThat(updatedChannel.statistics.score).isEqualTo(channel.statistics.score)
        assertThat(updatedChannel.ownerId).isEqualTo(channel.ownerId)
        assertThat(updatedChannel.creationDate).isEqualTo(channel.creationDate)
        assertThat(updatedChannel.lastModificationDate).isEqualTo(channel.lastModificationDate)
        assertThat(updatedChannel.type).isEqualTo(channel.type)
        assertThat(updatedChannel.language).isEqualTo(channel.language)
        assertThat(updatedChannel.order).isEqualTo(channel.order)
        assertThat(updatedChannel.subscribed).isEqualTo(channel.subscribed)
        assertThat(updatedChannel.website).isEqualTo(channel.website)
    }

    private suspend fun thenChunkScoresAreUpdated(channels: List<Channel>, startIndex: Int, endIndex: Int) {
        val indexes = (startIndex until endIndex).map { it }
        thenChunkScoresAreUpdated(channels, indexes)
    }

    private suspend fun thenChunkScoresAreUpdated(channels: List<Channel>, indexes: List<Int>) {
        indexes.forEach { index ->
            val channel = channelRepository.findById(index.toString())!!
            assertThat(channel.statistics.quality).isEqualTo(index * 10)
            thenOtherChannelFieldsHasNotBeenUpdated(channel, channels[index])
        }
    }

    private suspend fun thenChunkScoresRemainUnchanged(channels: List<Channel>, startIndex: Int, endIndex: Int) {
        (startIndex until endIndex).forEach { index ->
            val channel = channelRepository.findById(index.toString())!!
            assertThat(channel.statistics.quality).isEqualTo(0)
            thenOtherChannelFieldsHasNotBeenUpdated(channel, channels[index])
        }
    }

    private suspend fun thenChunkQualityChannelsAreNotCreated(indexes: List<Int>) {
        indexes.forEach { index ->
            val episode = channelRepository.findById(index.toString())
            assertThat(episode).isNull()
        }
    }

    private fun createScoreTable(): DynamoDbAsyncTable<UpdateChannelScoreItem> {
        return dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(UpdateChannelScoreItem::class.java))
    }

    private fun createTable(): DynamoDbAsyncTable<UpdateChannelQualityItem> {
        return dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(UpdateChannelQualityItem::class.java))
    }

    private fun createChannelTable(): DynamoDbAsyncTable<ChannelItem> {
        return dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(ChannelItem::class.java))
    }

    private companion object {
        const val TABLE_NAME = "dev_channels"
        const val CHANNEL_ID = ChannelMother.ID
        const val ANOTHER_CHANNEL_ID = "another_channel_id"
        const val NEW_SCORE = 87
        const val ANOTHER_SCORE = 42

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                ChannelItem::class.java to TABLE_NAME,
                UpdateChannelScoreItem::class.java to TABLE_NAME
            )
        )
    }
}
