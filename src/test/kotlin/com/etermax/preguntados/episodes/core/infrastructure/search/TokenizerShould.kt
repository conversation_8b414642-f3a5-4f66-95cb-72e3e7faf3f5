package com.etermax.preguntados.episodes.core.infrastructure.search

import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class TokenizerShould {

    private lateinit var tokenizer: Tokenizer
    private lateinit var result: List<String>

    @BeforeEach
    fun setUp() {
        givenATokenizer()
    }

    @Test
    fun `username query case is fixed`() {
        whenTokenizeForExactUserName("Whistler")

        thenTokensAre("whistler")
    }

    @Test
    fun `username query accents are removed`() {
        whenTokenizeForExactUserName("Íñigo")

        thenTokensAre("inigo")
    }

    @Test
    fun `username query separators are kept`() {
        whenTokenizeForExactUserName("count.orlok")

        thenTokensAre("count.orlok")
    }

    @Test
    fun `username query numbers are kept`() {
        whenTokenizeForExactUserName("count.orlok.123")

        thenTokensAre("count.orlok.123")
    }

    @Test
    fun `prefixes query case is fixed`() {
        whenTokenizeForPrefixes("Blade")

        thenTokensAre("blade")
    }

    @Test
    fun `prefixes query accents are removed`() {
        whenTokenizeForPrefixes("Piñón")

        thenTokensAre("pinon")
    }

    @Test
    fun `prefixes query is tokenized`() {
        whenTokenizeForPrefixes("count orlok")

        thenTokensAre("count", "orlok")
    }

    @Test
    fun `prefixes query with dots is tokenized`() {
        whenTokenizeForPrefixes("count.orlok")

        thenTokensAre("count", "orlok")
    }

    @Test
    fun `prefixes query with underscore is tokenized`() {
        whenTokenizeForPrefixes("count_orlok")

        thenTokensAre("count", "orlok")
    }

    @Test
    fun `prefixes query with numbers is tokenized`() {
        whenTokenizeForPrefixes("count.orlok.456")

        thenTokensAre("count", "orlok")
    }

    private fun givenATokenizer() {
        tokenizer = Tokenizer()
    }

    private fun whenTokenizeForExactUserName(query: String) {
        result = tokenizer.tokenizeForExactUsername(query)
    }

    private fun whenTokenizeForPrefixes(query: String) {
        result = tokenizer.tokenizeForPrefixes(query)
    }

    private fun thenTokensAre(vararg tokens: String) {
        Assertions.assertThat(result).containsExactly(*tokens)
    }
}
