package com.etermax.preguntados.episodes.core.infrastructure.repository.episode

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.core.domain.episode.pending.PendingEpisodeRepository
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import io.mockk.clearMocks
import io.mockk.spyk
import io.mockk.verify
import kotlinx.coroutines.future.await
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration

abstract class PendingEpisodeRepositoryShould {

    internal lateinit var repository: PendingEpisodeRepository
    internal var result: Boolean? = null

    abstract fun getRepository(): PendingEpisodeRepository

    @BeforeEach
    fun setUp() {
        repository = getRepository()
    }

    @Test
    fun `mark episode as processed`() = runTest {
        whenMarkAsProcessed()

        thenEpisodeIsMarkedAsProcessed()
    }

    @Test
    fun `an already event mark as processed should return false`() = runTest {
        givenAMarkedEpisode()

        whenMarkAsProcessed()

        thenEpisodeIsMarkedAsNotProcessed()
    }

    internal suspend fun givenAMarkedEpisode() {
        repository.markAsProcessed(EPISODE_ID)
    }

    internal suspend fun whenMarkAsProcessed() {
        result = repository.markAsProcessed(EPISODE_ID)
    }

    private fun thenEpisodeIsMarkedAsProcessed() {
        assertThat(result).isTrue()
    }

    private fun thenEpisodeIsMarkedAsNotProcessed() {
        assertThat(result).isFalse()
    }

    internal companion object {
        const val EPISODE_ID = "ABC-123"
    }
}

@ExtendWith(RedisTestServer::class)
class RedisPendingEpisodeRepositoryShould : PendingEpisodeRepositoryShould() {
    private val redisAsync = spyk(EmbeddedRedisUtils.buildClient())

    override fun getRepository(): PendingEpisodeRepository {
        return RedisPendingEpisodeRepository(redisAsync, Duration.parse(TEN_SECONDS))
    }

    @Test
    fun `set ttl when marking as processed`() = runTest {
        givenAMarkedEpisode()

        whenMarkAsProcessed()

        thenTtlIsSet()
    }

    @Test
    fun `an already event mark as processed should not set ttl again`() = runTest {
        givenAMarkedEpisode()
        clearMocks(redisAsync)

        whenMarkAsProcessed()

        thenMarkedEpisodeNotSetTtlTwice()
    }

    private suspend fun thenTtlIsSet() {
        val ttl = redisAsync.ttl("$KEY:$EPISODE_ID").await()
        assertThat(ttl).isGreaterThan(1)
    }

    private fun thenMarkedEpisodeNotSetTtlTwice() {
        assertThat(result).isFalse()
        verify(exactly = 0) { redisAsync.ttl(any()) }
    }

    private companion object {
        const val TEN_SECONDS = "PT10S"
        const val KEY = "pr:e:pe:p"
    }
}
