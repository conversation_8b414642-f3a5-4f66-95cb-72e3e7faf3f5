package com.etermax.preguntados.episodes.core.action

import com.etermax.preguntados.episodes.core.EpisodeMother.COUNTRY
import com.etermax.preguntados.episodes.core.EpisodeMother.EPISODE_STATUS
import com.etermax.preguntados.episodes.core.EpisodeMother.EPISODE_TYPE
import com.etermax.preguntados.episodes.core.EpisodeMother.LANGUAGE
import com.etermax.preguntados.episodes.core.EpisodeMother.NAME
import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.NewEpisode
import com.etermax.preguntados.episodes.core.domain.episode.NewEpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode.LIKES
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode.MINE
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.profile.social.SocialNetwork
import com.etermax.preguntados.episodes.core.domain.profile.social.SocialProfile
import com.etermax.preguntados.episodes.core.domain.search.DeliveryService
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.doubles.ChannelUnpublishedEpisodesFactory
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class SearchEpisodesShould {

    private lateinit var result: List<EpisodeSummary>
    private lateinit var actionData: SearchEpisodes.ActionData

    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService
    private lateinit var summaryService: SummaryService
    private lateinit var deliveryService: DeliveryService
    private lateinit var newEpisodeRepository: NewEpisodeRepository

    private lateinit var searchEpisodes: SearchEpisodes

    @BeforeEach
    fun setUp() {
        profileService = mockk()
        unpublishedEpisodesService = ChannelUnpublishedEpisodesFactory.mock()
        deliveryService = mockk()
        newEpisodeRepository = mockk()
        summaryService = SummaryService(profileService, unpublishedEpisodesService)

        searchEpisodes = SearchEpisodes(
            summaryService,
            deliveryService,
            newEpisodeRepository
        )
    }

    @Test
    fun `return no episodes when index is empty `() = runTest {
        givenAFilter()
        givenDeliveryServiceResponse()

        whenSearch()

        thenDeliveryServiceIsCalledWithSearchParameters()
        thenRetrieveNoEpisodes()
    }

    @Test
    fun `return episodes when search matches`() = runTest {
        givenAFilter()
        givenDeliveryServiceResponse(EPISODES)
        givenProfiles()

        whenSearch()

        thenDeliveryServiceIsCalledWithSearchParameters()
        thenRetrieveEpisodes(listOf(EPISODE_SUMMARY_1, EPISODE_SUMMARY_2))
    }

    @Test
    fun `returns recently added episode`() = runTest {
        givenAFilter(MINE)
        givenProfiles()
        givenDeliveryServiceResponse(EPISODES)
        givenAddedEpisode(EPISODE_1.copy(id = "ABC_30"))

        whenSearch()

        thenRetrieveEpisodes(listOf(EPISODE_SUMMARY_1, EPISODE_SUMMARY_2, EPISODE_SUMMARY_1.copy(id = "ABC_30")))
    }

    @Test
    fun `do not repeat episodes`() = runTest {
        givenAFilter(MINE)
        givenProfiles()
        givenDeliveryServiceResponse(EPISODES)
        givenAddedEpisode(EPISODE_1)

        whenSearch()

        thenRetrieveEpisodes(listOf(EPISODE_SUMMARY_1, EPISODE_SUMMARY_2))
    }

    @Test
    fun `should not search new episodes for anonymous request`() = runTest {
        givenAFilter(playerId = null)
        givenProfiles()
        givenDeliveryServiceResponse(EPISODES)

        whenSearch()

        coVerify(exactly = 0) { newEpisodeRepository.find(any()) }
        thenRetrieveEpisodes(listOf(EPISODE_SUMMARY_1, EPISODE_SUMMARY_2))
    }

    private fun givenAFilter(sortEpisode: SortEpisode = LIKES, playerId: Long? = PLAYER_ID) {
        actionData = SearchEpisodes.ActionData(
            playerId = playerId,
            language = LANGUAGE,
            name = NAME,
            sort = sortEpisode,
            country = COUNTRY,
            episodeId = EPISODE_1.id,
            isRestricted = false,
            offset = OFFSET,
            limit = LIMIT
        )
    }

    private fun givenDeliveryServiceResponse(response: List<Episode> = emptyList()) {
        coEvery {
            deliveryService.search(any())
        } returns response
    }

    private fun givenAddedEpisode(episode: Episode) {
        coEvery { newEpisodeRepository.find(PLAYER_ID) }.returns(
            listOf(
                NewEpisode(
                    episode
                )
            )
        )
    }

    private fun givenProfiles(profiles: List<Profile> = listOf(PROFILE_1, PROFILE_2)) {
        coEvery { profileService.findMany(any()) } returns profiles
    }

    private suspend fun whenSearch() {
        result = searchEpisodes(actionData)
    }

    private fun thenDeliveryServiceIsCalledWithSearchParameters(sortEpisode: SortEpisode = LIKES) {
        val searchParameters = SearchParameters(
            playerId = PLAYER_ID,
            language = LANGUAGE,
            country = COUNTRY,
            episodeType = EPISODE_TYPE,
            status = EPISODE_STATUS,
            name = NAME,
            episodeId = EPISODE_1.id,
            sort = sortEpisode,
            isRestricted = false,
            offset = OFFSET,
            limit = LIMIT
        )
        coVerify {
            deliveryService.search(searchParameters)
        }
    }

    private fun thenRetrieveEpisodes(episodes: List<EpisodeSummary> = emptyList()) {
        assertThat(result).isEqualTo(episodes)
    }

    private fun thenRetrieveNoEpisodes() {
        assertThat(result).isEmpty()
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val ANOTHER_PLAYER_ID = 999L
        const val OFFSET = 0
        const val LIMIT = 10
        val ONE_YEAR_AGO: OffsetDateTime = OffsetDateTime.now().minusYears(1)

        val EPISODE_1 = buildEpisode(id = "ABC_10", ownerId = PLAYER_ID)
        val EPISODE_2 = buildEpisode(id = "ABC_20", ownerId = ANOTHER_PLAYER_ID)
        val EPISODES = listOf(EPISODE_1, EPISODE_2)

        val PROFILE_1 = Profile(
            playerId = PLAYER_ID,
            name = "",
            country = "",
            photoUrl = "",
            socialProfile = SocialProfile(SocialNetwork.FACEBOOK, "a", "name"),
            joinDate = ONE_YEAR_AGO,
            restriction = null
        )
        val PROFILE_2 = Profile(
            playerId = ANOTHER_PLAYER_ID,
            name = "",
            country = "",
            photoUrl = "",
            socialProfile = SocialProfile(SocialNetwork.FACEBOOK, "a", "name"),
            joinDate = ONE_YEAR_AGO,
            restriction = null
        )

        val EPISODE_SUMMARY_1 = EpisodeSummary.from(EPISODE_1, PROFILE_1)
        val EPISODE_SUMMARY_2 = EpisodeSummary.from(EPISODE_2, PROFILE_2)
    }
}
