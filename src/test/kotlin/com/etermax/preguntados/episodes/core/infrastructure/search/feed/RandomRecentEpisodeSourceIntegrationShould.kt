package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.infrastructure.search.service.RecentEpisodeSearchService
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class RandomRecentEpisodeSourceIntegrationShould :
    AbstractOpenSearchSourceIntegrationShould(indexDescriptor = "opensearch/mapping/episodes-index-mapping-v1.0.json") {

    private lateinit var recentEpisodeSearchService: RecentEpisodeSearchService
    private lateinit var source: Source<String>

    @BeforeEach
    fun setUp() {
        recentEpisodeSearchService = mockk(relaxed = true)
        source = RandomRecentEpisodeSource(recentEpisodeSearchService)
    }

    @Test
    fun `use recent episodes cache`() = runTest {
        givenEpisodesCache()

        val result = whenRetrieve()

        thenEpisodesCacheIsUsed(result.items)
    }

    @Test
    fun `retrieve episodes filtered by language`() = runTest {
        coEvery { recentEpisodeSearchService.search(Language.EN, Country.US, 0, 20) } returns listOf("3", "1")

        val result = whenRetrieve(country = Country.US, language = Language.EN)

        assertEquals(listOf("3", "1").toSet(), result.items.toSet())
        assertTrue((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `retrieve episodes respecting offset and limit`() = runTest {
        coEvery {
            recentEpisodeSearchService.search(null, null, 5, 20)
        } returns (6..20).map { it.toString() }

        val result = whenRetrieve(offset = 5, limit = 10)

        // Should return 10 random episodes between 6 and 20
        assertEquals(10, result.items.size)
        result.items.forEach { assertTrue(it.toInt() in 6..20, "Episode $it is not in the expected range") }
        assertTrue((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `retrieve episodes respecting fetch cursor`() = runTest {
        coEvery {
            recentEpisodeSearchService.search(null, null, 0, 10)
        } returns (1..10).map { it.toString() }
        coEvery {
            recentEpisodeSearchService.search(null, null, 10, 10)
        } returns (11..20).map { it.toString() }

        val result = whenRetrieve(offset = 0, limit = 5)

        val secondResult = whenRetrieve(
            offset = 0,
            limit = 5,
            fetchCursor = result.fetchCursor
        )

        val thirdResult = whenRetrieve(
            offset = 0,
            limit = 5,
            fetchCursor = secondResult.fetchCursor
        )

        assertFalse((result.fetchCursor as OffsetFetchCursor).exhausted)
        result.items.forEach { assertTrue(it.toInt() in 1..10, "Episode $it is not in the expected range") }

        assertFalse((secondResult.fetchCursor as OffsetFetchCursor).exhausted)
        secondResult.items.forEach { assertTrue(it.toInt() in 11..20, "Episode $it is not in the expected range") }

        assertTrue((thirdResult.fetchCursor as OffsetFetchCursor).exhausted)
        assertEquals(emptyList(), thirdResult.items)
    }

    @Test
    fun `retrieve an specified quantity of episodes till exhausted`() = runTest {
        coEvery {
            recentEpisodeSearchService.search(null, null, 0, 20)
        } returns (1..5).map { it.toString() }

        // Try to retrieve episodes (more than available)
        val result = whenRetrieve(limit = 10)

        // Should return all 5 episodes
        assertEquals(listOf("1", "2", "3", "4", "5").toSet(), result.items.toSet())
        assertTrue((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `answer successfully when trying to retrieve ZERO episodes`() = runTest {
        givenAnEpisode(id = "1")

        val result = whenRetrieve(limit = 0)

        assertEquals(listOf(), result.items)
    }

    private fun givenEpisodesCache() {
        coEvery { recentEpisodeSearchService.search(null, null, 0, 20) } returns listOf("E-100", "E-200", "E-300")
        source = RandomRecentEpisodeSource(recentEpisodeSearchService)
    }

    private suspend fun whenRetrieve(
        offset: Int = 0,
        limit: Int = 10,
        country: Country? = null,
        language: Language? = null,
        fetchCursor: FetchCursor? = null
    ): SourceResponse<String> {
        return source.fetch(
            SourceRequest(
                OffsetLimit(
                    offset = offset,
                    limit = limit
                ),
                userId = 1,
                country = country,
                language = language,
                fetchCursor = fetchCursor
            )
        )
    }

    private fun thenEpisodesCacheIsUsed(actualEpisodes: List<String>) {
        coVerify(exactly = 1) { recentEpisodeSearchService.search(null, null, 0, 20) }
        assertEquals(listOf("E-100", "E-200", "E-300").toSet(), actualEpisodes.toSet())
    }
}
