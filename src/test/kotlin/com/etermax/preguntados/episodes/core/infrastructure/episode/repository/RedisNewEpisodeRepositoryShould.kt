package com.etermax.preguntados.episodes.core.infrastructure.episode.repository

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.NewEpisode
import com.etermax.preguntados.episodes.core.domain.episode.NewEpisodeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.RedisNewEpisodeRepository
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration

@ExtendWith(RedisTestServer::class)
class RedisNewEpisodeRepositoryShould {
    private val redisAsync = EmbeddedRedisUtils.buildClient()

    private val repository: NewEpisodeRepository = RedisNewEpisodeRepository(redisAsync, Duration.ofSeconds(10))

    @Test
    fun `adds updated episode`() = runTest {
        repository.add(PLAYER_ID, EpisodeMother.buildEpisode())

        val returnedEpisodes = repository.find(PLAYER_ID)
        Assertions.assertThat(returnedEpisodes)
            .isEqualTo(listOf(NewEpisode(episode = EpisodeMother.buildEpisode())))
    }

    private companion object {
        const val PLAYER_ID = 123L
    }
}
