package com.etermax.preguntados.episodes.core.infrastructure.repository.episode

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.delete.BlackListRecommendationService
import com.etermax.preguntados.episodes.core.domain.quality.EpisodeQuality
import com.etermax.preguntados.episodes.core.domain.quality.Quality
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.UpdateEpisodeQualityItem
import com.etermax.preguntados.episodes.modules.DynamoDBProvider.byOwnerIndex
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoDbEpisodeQualityRepositoryShould {
    private lateinit var repository: DynamoDbEpisodeQualityRepository
    private lateinit var episodeRepository: DynamoDBEpisodeRepository
    private lateinit var dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient
    private lateinit var blackListRecommendationService: BlackListRecommendationService

    @BeforeEach
    fun setUp() {
        blackListRecommendationService = mockk(relaxed = true)
        val dynamoDbClient = dynamoDbTestServer.buildAsyncClient()
        dynamoDbEnhancedClient = DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbClient).build()
        val table = createTable()
        val episodeTable = createEpisodeTable()
        val dynamoDbEnhancedAsyncClient = dynamoDbTestServer.buildEnhancedAsyncClient(dynamoDbClient)
        episodeRepository = DynamoDBEpisodeRepository(dynamoDbEnhancedClient, episodeTable, byOwnerIndex)
        repository = DynamoDbEpisodeQualityRepository(table, dynamoDbEnhancedAsyncClient, blackListRecommendationService)
    }

    @Test
    fun `update two episode quality`() = runTest {
        val episode1 = EpisodeMother.buildEpisode()
        val episode2 = EpisodeMother.buildEpisode(id = ANOTHER_EPISODE_ID)
        givenTwoEpisodes(episode1, episode2)

        whenUpdateTwoEpisodesQuality()

        thenQualityOfTwoEpisodesAreUpdated(episode1, episode2)
    }

    @Test
    fun `update many episode quality`() = runTest {
        val episodeQuantity = 30 // Más que el límite de 25 por transacción
        val episodes = givenManyEpisodes(episodeQuantity)

        whenUpdateManyEpisodeQuality(episodes)

        thenQualityOfManyEpisodesAreUpdated(episodes)
    }

    @Test
    fun `should update second transaction chunk when first one fails due to duplicate episode id`() = runTest {
        // given
        val episodeQuantity = 28
        val episodes = givenManyEpisodes(episodeQuantity)
        val qualities = episodes.mapIndexed { index, episode ->
            val episodeId = if (index == 20) "1" else episode.id // Simulate duplicate channel id in first chunk
            EpisodeQuality(id = episodeId, quality = index * 10)
        }

        // when
        repository.updateQuality(qualities)

        // then
        thenChunkQualityEpisodesRemainUnchanged(episodes, 0, 25)
        thenChunkQualityEpisodesAreUpdated(episodes, 25, episodes.size)
    }

    @Test
    fun `should update first transaction chunk when second one fails due to duplicate episode id`() = runTest {
        // given
        val episodeQuantity = 28
        val episodes = givenManyEpisodes(episodeQuantity)
        val qualities = episodes.mapIndexed { index, episode ->
            val episodeId = if (index == 26) "25" else episode.id // Simulate duplicate episode id in second chunk
            EpisodeQuality(id = episodeId, quality = index * 10)
        }

        // when
        repository.updateQuality(qualities)

        // then
        thenChunkQualityEpisodesAreUpdated(episodes, 0, 25)
        thenChunkQualityEpisodesRemainUnchanged(episodes, 25, episodes.size)
    }

    @Test
    fun `should handle exact transaction limit of 25 items`() = runTest {
        // given
        val episodeQuantity = 25 // Exact transaction limit
        val episodes = givenManyEpisodes(episodeQuantity)
        val qualities = episodes.mapIndexed { index, episode ->
            EpisodeQuality(id = episode.id, quality = index * 10)
        }

        // when
        repository.updateQuality(qualities)

        // then
        thenChunkQualityEpisodesAreUpdated(episodes, 0, episodes.size)
    }

    @Test
    fun `should handle more than two transaction chunks`() = runTest {
        // given
        val episodeQuantity = 60 // More than two chunks
        val episodes = givenManyEpisodes(episodeQuantity)
        val qualities = episodes.mapIndexed { index, episode ->
            EpisodeQuality(id = episode.id, quality = index * 10)
        }

        // when
        repository.updateQuality(qualities)

        // then
        thenChunkQualityEpisodesAreUpdated(episodes, 0, episodes.size)
    }

    @Test
    fun `should handle empty list of qualities`() = runTest {
        // given
        val qualities = emptyList<Quality>()

        // when
        repository.updateQuality(qualities)

        // then
        // No assertions needed as we just want to verify it doesn't throw
    }

    @Test
    fun `should handle single quality update`() = runTest {
        // given
        val episode = EpisodeMother.buildEpisode()
        givenAnEpisode(episode)
        val quality = EpisodeQuality(episode.id, 100)

        // when
        repository.updateQuality(listOf(quality))

        // then
        val updatedEpisode = episodeRepository.findById(episode.id)!!
        assertThat(updatedEpisode.quality).isEqualTo(100)
        thenOtherEpisodeFieldsHasNotBeenUpdated(updatedEpisode, episode)
    }

    @Test
    fun `should handle non-existent episode gracefully`() = runTest {
        // given
        val nonExistentEpisodeId = "non-existent"
        val quality = EpisodeQuality(nonExistentEpisodeId, 100)

        // when
        repository.updateQuality(listOf(quality))

        // then
        // No assertions needed as we just want to verify it doesn't throw
    }

    @Test
    fun `should handle failure in last chunk of multiple chunks`() = runTest {
        // given
        val episodeQuantity = 60
        val episodes = givenManyEpisodes(episodeQuantity)
        val qualities = episodes.mapIndexed { index, episode ->
            val episodeId = if (index == 55) "54" else episode.id // Simulate duplicate in last chunk
            EpisodeQuality(id = episodeId, quality = index * 10)
        }

        // when
        repository.updateQuality(qualities)

        // then
        thenChunkQualityEpisodesAreUpdated(episodes, 0, 50) // First two chunks should be updated
        thenChunkQualityEpisodesRemainUnchanged(episodes, 50, episodes.size) // Last chunk should remain unchanged
    }

    @Test
    fun `should keep quality episodes unchanged when all chunks fail`() = runTest {
        // given
        val episodeQuantity = 28
        val episodes = givenManyEpisodes(episodeQuantity)
        val qualities = episodes.mapIndexed { index, episode ->
            // Use a non-existent episode ID to ensure the transaction fails
            EpisodeQuality(id = "non-existent-$index", quality = index * 10)
        }

        // when
        repository.updateQuality(qualities)

        // then
        thenChunkQualityEpisodesRemainUnchanged(episodes, 0, episodes.size)
    }

    @Test
    fun `retry failed transaction excluding id's that caused the failure`() = runTest {
        // given
        val episodeQuantity = 30
        val avoidSaveIndexes = listOf(27, 28)
        val episodes = givenManyEpisodes(episodeQuantity, avoidSaveIndexes)
        val qualities = episodes.mapIndexed { index, episode ->
            EpisodeQuality(id = episode.id, quality = index * 10)
        }

        // when
        repository.updateQuality(qualities)

        // then
        thenChunkQualityEpisodesAreUpdated(episodes, 0, 25)
        thenChunkQualityEpisodesAreUpdated(episodes, listOf(25, 26, 29))
        thenChunkQualityEpisodesAreNotCreated(avoidSaveIndexes)
    }

    @Test
    fun `handle failed episodes`() = runTest {
        // given
        val episodeQuantity = 30
        val avoidSaveIndexes = listOf(27, 28)
        val episodes = givenManyEpisodes(episodeQuantity, avoidSaveIndexes)
        val qualities = episodes.mapIndexed { index, episode ->
            EpisodeQuality(id = episode.id, quality = index * 10)
        }

        // when
        repository.updateQuality(qualities)

        // then
        coVerify(exactly = 1) { blackListRecommendationService.deleteEpisodes(listOf("27", "28")) }
    }

    private suspend fun givenAnEpisode(episode: Episode) {
        episodeRepository.save(episode)
    }

    private suspend fun givenTwoEpisodes(episode1: Episode, episode2: Episode) {
        episodeRepository.save(episode1)
        episodeRepository.save(episode2)
    }

    private suspend fun givenManyEpisodes(quantity: Int, avoidSaveIndexes: List<Int> = emptyList()): List<Episode> {
        val episodes = (0 until quantity).map {
            EpisodeMother.buildEpisode(id = it.toString())
        }
        episodes
            .filterIndexed { index, _ -> index !in avoidSaveIndexes }
            .forEach { episodeRepository.save(it) }
        return episodes
    }

    private suspend fun whenUpdateTwoEpisodesQuality() {
        repository.updateQuality(
            listOf(
                EpisodeQuality(EPISODE_ID, NEW_QUALITY),
                EpisodeQuality(ANOTHER_EPISODE_ID, ANOTHER_QUALITY)
            )
        )
    }

    private suspend fun whenUpdateManyEpisodeQuality(episodes: List<Episode>) {
        val qualities = episodes.mapIndexed { index, episode ->
            EpisodeQuality(episode.id, index * 10)
        }
        repository.updateQuality(qualities)
    }

    private suspend fun thenQualityOfTwoEpisodesAreUpdated(episode1: Episode, episode2: Episode) {
        val updatedEpisode1 = episodeRepository.findById(episode1.id)
        val updatedEpisode2 = episodeRepository.findById(episode2.id)

        assertThat(updatedEpisode1).isNotNull
        assertThat(updatedEpisode1!!.quality).isEqualTo(NEW_QUALITY)
        thenOtherEpisodeFieldsHasNotBeenUpdated(updatedEpisode1, episode1)

        assertThat(updatedEpisode2).isNotNull
        assertThat(updatedEpisode2!!.quality).isEqualTo(ANOTHER_QUALITY)
        thenOtherEpisodeFieldsHasNotBeenUpdated(updatedEpisode1, episode1)
    }

    private suspend fun thenQualityOfManyEpisodesAreUpdated(episodes: List<Episode>) {
        val updatedEpisodes = episodes.mapNotNull { episodeRepository.findById(it.id) }
        assertThat(updatedEpisodes).hasSize(episodes.size)

        updatedEpisodes.forEachIndexed { index, episode ->
            assertThat(episode.quality).isEqualTo(index * 10)
            thenOtherEpisodeFieldsHasNotBeenUpdated(episode, episodes[index])
        }
    }

    private fun thenOtherEpisodeFieldsHasNotBeenUpdated(
        updatedEpisode: Episode,
        episode: Episode
    ) {
        assertThat(updatedEpisode.id).isEqualTo(episode.id)
        assertThat(updatedEpisode.name).isEqualTo(episode.name)
        assertThat(updatedEpisode.language).isEqualTo(episode.language)
        assertThat(updatedEpisode.country).isEqualTo(episode.country)
        assertThat(updatedEpisode.type).isEqualTo(episode.type)
        assertThat(updatedEpisode.startDate).isEqualTo(episode.startDate)
        assertThat(updatedEpisode.cover).isEqualTo(episode.cover)
        assertThat(updatedEpisode.banner).isEqualTo(episode.banner)
        assertThat(updatedEpisode.ownerId).isEqualTo(episode.ownerId)
        assertThat(updatedEpisode.contents).isEqualTo(episode.contents)
        assertThat(updatedEpisode.channelId).isEqualTo(episode.channelId)
        assertThat(updatedEpisode.rate).isEqualTo(episode.rate)
        assertThat(updatedEpisode.views).isEqualTo(episode.views)
        assertThat(updatedEpisode.reports).isEqualTo(episode.reports)
        assertThat(updatedEpisode.status).isEqualTo(episode.status)
    }

    private suspend fun thenChunkQualityEpisodesAreUpdated(episodes: List<Episode>, startIndex: Int, endIndex: Int) {
        (startIndex until endIndex).forEach { index ->
            val episode = episodeRepository.findById(index.toString())!!
            assertThat(episode.quality).isEqualTo(index * 10)
            thenOtherEpisodeFieldsHasNotBeenUpdated(episode, episodes[index])
        }
    }

    private suspend fun thenChunkQualityEpisodesAreUpdated(episodes: List<Episode>, indexes: List<Int>) {
        indexes.forEach { index ->
            val episode = episodeRepository.findById(index.toString())!!
            assertThat(episode.quality).isEqualTo(index * 10)
            thenOtherEpisodeFieldsHasNotBeenUpdated(episode, episodes[index])
        }
    }

    private suspend fun thenChunkQualityEpisodesRemainUnchanged(episodes: List<Episode>, startIndex: Int, endIndex: Int) {
        (startIndex until endIndex).forEach { index ->
            val episode = episodeRepository.findById(index.toString())!!
            assertThat(episode.quality).isEqualTo(0)
            thenOtherEpisodeFieldsHasNotBeenUpdated(episode, episodes[index])
        }
    }

    private suspend fun thenChunkQualityEpisodesAreNotCreated(indexes: List<Int>) {
        indexes.forEach { index ->
            val episode = episodeRepository.findById(index.toString())
            assertThat(episode).isNull()
        }
    }

    private fun createTable(): DynamoDbAsyncTable<UpdateEpisodeQualityItem> {
        return dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(UpdateEpisodeQualityItem::class.java))
    }

    private fun createEpisodeTable(): DynamoDbAsyncTable<EpisodeItem> {
        return dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(EpisodeItem::class.java))
    }

    private companion object {
        const val TABLE_NAME = "dev_episodes"
        const val EPISODE_ID = EpisodeMother.SEQUENCE_ID
        const val ANOTHER_EPISODE_ID = "another_episode_id"
        const val NEW_QUALITY = 87
        const val ANOTHER_QUALITY = 42

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                EpisodeItem::class.java to TABLE_NAME,
                UpdateEpisodeQualityItem::class.java to TABLE_NAME
            )
        )
    }
}
