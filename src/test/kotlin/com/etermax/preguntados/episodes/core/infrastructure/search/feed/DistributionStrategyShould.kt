package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class DistributionStrategyShould {
    @Test
    fun `evenly distribute values with no remainder`() {
        val strategy = EvenlyDistributionStrategy()
        val result = strategy.distribute(10, 5)
        assertEquals(listOf(2, 2, 2, 2, 2), result)
    }

    @Test
    fun `evenly distribute values with remainder`() {
        val strategy = EvenlyDistributionStrategy()
        val result = strategy.distribute(11, 5)
        assertEquals(listOf(3, 2, 2, 2, 2), result)
    }

    @Test
    fun `evenly distribute values with multiple remainder`() {
        val strategy = EvenlyDistributionStrategy()
        val result = strategy.distribute(13, 5)
        assertEquals(listOf(3, 3, 3, 2, 2), result)
    }

    @Test
    fun `handle zero parts in evenly distribution`() {
        val strategy = EvenlyDistributionStrategy()
        val result = strategy.distribute(10, 0)
        assertEquals(emptyList<Int>(), result)
    }

    @Test
    fun `distribute values according to weights`() {
        val strategy = WeightedDistributionStrategy(listOf(3, 2, 1))
        val result = strategy.distribute(60, 3)
        assertEquals(listOf(30, 20, 10), result)
    }

    @Test
    fun `handle rounding in weighted distribution`() {
        val strategy = WeightedDistributionStrategy(listOf(3, 2, 1))
        val result = strategy.distribute(10, 3)
        assertEquals(listOf(5, 3, 2), result)
    }

    @Test
    fun `handle more weights than parts`() {
        val strategy = WeightedDistributionStrategy(listOf(3, 2, 1, 4))
        val result = strategy.distribute(10, 3)
        assertEquals(listOf(5, 3, 2), result)
    }

    @Test
    fun `handle fewer weights than parts`() {
        val strategy = WeightedDistributionStrategy(listOf(3, 2))
        val result = strategy.distribute(10, 3)
        // Should use the last weight for missing parts
        assertEquals(listOf(4, 3, 3), result)
    }

    @Test
    fun `handle zero total in weighted distribution`() {
        val strategy = WeightedDistributionStrategy(listOf(3, 2, 1))
        val result = strategy.distribute(0, 3)
        assertEquals(listOf(0, 0, 0), result)
    }

    @Test
    fun `handle zero weights in weighted distribution`() {
        val strategy = WeightedDistributionStrategy(listOf(0, 0, 0))
        val result = strategy.distribute(10, 3)
        assertEquals(listOf(0, 0, 0), result)
    }
}
