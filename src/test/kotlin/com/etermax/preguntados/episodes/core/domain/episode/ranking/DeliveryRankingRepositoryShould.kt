package com.etermax.preguntados.episodes.core.domain.episode.ranking

import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.profile.social.SocialNetwork
import com.etermax.preguntados.episodes.core.domain.profile.social.SocialProfile
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class DeliveryRankingRepositoryShould {

    private lateinit var profileService: ProfileService
    private lateinit var deliveryRankingService: DeliveryRankingService
    private lateinit var ranking: Ranking
    private lateinit var result: DeliveryRanking

    @BeforeEach
    fun setUp() {
        profileService = mockk()
        deliveryRankingService = DeliveryRankingService(profileService)
    }

    @Test
    fun `return delivery ranking`() = runTest {
        givenAProfile()
        givenARanking()

        whenComplete()

        thenRankingIsReturned()
    }

    @Test
    fun `recalculate positions due to invalid profile`() = runTest {
        givenRankingPlayers()
        givenProfilesOfRankingPlayers(exceptPlayerId = 50)

        whenComplete()

        thenPositionsAreRecalculated()
    }

    @Test
    fun `do not recalculate positions when all profiles are found`() = runTest {
        givenRankingPlayers()
        givenProfilesOfRankingPlayers()

        whenComplete()

        thenPositionsAreNotRecalculated()
    }

    @Test
    fun `recalculate ranked player position`() = runTest {
        givenRankingPlayers(rankedPlayer = 60)
        givenProfilesOfRankingPlayers(exceptPlayerId = 50)

        whenComplete()

        thenPlayerPositionsIsRecalculated()
    }

    @Test
    fun `do not recalculate ranked player position if it is out of the top`() = runTest {
        givenRankingPlayers()
        givenProfilesOfRankingPlayers(exceptPlayerId = 50)

        whenComplete()

        thenPlayerPositionIsNotRecalculated()
    }

    private fun givenAProfile() {
        coEvery { profileService.findMany(listOf(PROFILE.playerId)) } returns listOf(
            PROFILE
        )
    }

    private fun givenProfilesOfRankingPlayers(exceptPlayerId: Long? = null) {
        val allPlayersId = createRankingPlayers().map { it.playerId }
        val allProfiles = allPlayersId.filter { it != exceptPlayerId }.map { PROFILE.copy(playerId = it) }
        coEvery { profileService.findMany(allPlayersId) } returns allProfiles
    }

    private fun givenARanking() {
        ranking = Ranking(
            players = listOf(RankedPlayer(RANKED_PLAYER_ID, RankingEntry(1, 100))),
            player = RankedPlayer(PLAYER_ID, RankingEntry(2, 50))
        )
    }

    private fun givenRankingPlayers(rankedPlayer: Long = RANKED_PLAYER_ID) {
        ranking = Ranking(
            createRankingPlayers(),
            RankedPlayer(rankedPlayer, RankingEntry(20, 50))
        )
    }

    private suspend fun whenComplete() {
        result = deliveryRankingService.complete(ranking)
    }

    private fun thenRankingIsReturned() {
        assertThat(result).isEqualTo(DELIVERY_RANKING)
    }

    private fun thenPositionsAreRecalculated() {
        val recalculatedPositions = result.players.map { it.rankingEntry.position }
        assertThat(recalculatedPositions).containsExactly(1, 2, 3, 4, 5, 6, 7, 8, 9)
    }

    private fun thenPositionsAreNotRecalculated() {
        val recalculatedPositions = result.players.map { it.rankingEntry.position }
        assertThat(recalculatedPositions).containsExactly(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
    }

    private fun thenPlayerPositionsIsRecalculated() {
        val newPosition = result.myRankingEntry?.position
        assertThat(newPosition).isEqualTo(5)
    }

    private fun thenPlayerPositionIsNotRecalculated() {
        val newPosition = result.myRankingEntry?.position
        assertThat(newPosition).isEqualTo(20)
    }

    private fun createRankingPlayers(): List<RankedPlayer> {
        return (1..10).map {
            RankedPlayer(it * 10L, RankingEntry(it, 100L - it))
        }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val RANKED_PLAYER_ID = 2L

        val ONE_YEAR_AGO: OffsetDateTime = OffsetDateTime.now().minusYears(1)
        val PROFILE = Profile(
            playerId = RANKED_PLAYER_ID,
            name = "",
            country = "",
            photoUrl = "",
            socialProfile = SocialProfile(SocialNetwork.FACEBOOK, "a", "name"),
            joinDate = ONE_YEAR_AGO,
            restriction = null
        )
        val DELIVERY_RANKING = DeliveryRanking(
            listOf(
                DeliveryRankedPlayer(PROFILE, RankingEntry(1, 100))
            ),
            RankingEntry(2, 50)
        )
    }
}
