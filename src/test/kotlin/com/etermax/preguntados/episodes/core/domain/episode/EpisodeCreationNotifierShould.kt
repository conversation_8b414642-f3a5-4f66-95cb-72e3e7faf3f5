package com.etermax.preguntados.episodes.core.domain.episode

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.notification.EpisodeNotificationService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@ExperimentalCoroutinesApi
class EpisodeCreationNotifierShould {

    private lateinit var episodeNotificationService: EpisodeNotificationService
    private lateinit var episodePlayersByOwnerRepository: EpisodePlayersByOwnerRepository
    private lateinit var profileService: ProfileService
    private lateinit var episodeCreationNotifier: EpisodeCreationNotifier

    @BeforeEach
    fun setUp() {
        episodeNotificationService = mockk(relaxed = true)
        episodePlayersByOwnerRepository = mockk(relaxed = true)
        profileService = mockk(relaxed = true)
        coEvery { profileService.find(OWNER_ID) } returns OWNER_PROFILE
        episodeCreationNotifier = EpisodeCreationNotifier(
            episodeNotificationService,
            episodePlayersByOwnerRepository,
            profileService
        )
    }

    @Test
    fun `not send any notification is there are no players`() = runTest {
        givenNoPlayers()

        whenNotify()

        thenNotificationIsNotSent()
    }

    @Test
    fun `send notification to episode players`() = runTest {
        givenPlayers()

        whenNotify()

        thenNotificationIsSent()
    }

    private fun givenNoPlayers() {
        coEvery { episodePlayersByOwnerRepository.get(OWNER_ID) } returns emptyList()
    }

    private fun givenPlayers() {
        coEvery { episodePlayersByOwnerRepository.get(OWNER_ID) } returns PLAYERS
    }

    private suspend fun whenNotify() {
        episodeCreationNotifier.notifyEpisodeCreationToPlayers(EPISODE)
    }

    private fun thenNotificationIsNotSent() {
        coVerify(exactly = 0) { episodeNotificationService.notifyPlayersAtEpisodeCreation(any(), any(), any()) }
    }

    private fun thenNotificationIsSent() {
        coVerify(exactly = 1) {
            episodeNotificationService
                .notifyPlayersAtEpisodeCreation(PLAYERS, OWNER_PROFILE, EPISODE_ID)
        }
    }

    private companion object {
        const val EPISODE_ID = "episode_id"
        const val OWNER_ID = 123L
        val PLAYERS = listOf(1L, 2L, 3L)
        val EPISODE = EpisodeMother.buildEpisode(id = EPISODE_ID, ownerId = OWNER_ID)
        val OWNER_PROFILE = ProfileMother.aProfile(playerId = OWNER_ID)
    }
}
