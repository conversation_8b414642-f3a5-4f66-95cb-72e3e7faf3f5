package com.etermax.preguntados.episodes.core.infrastructure.http.error

import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.InvalidProfileException
import com.etermax.preguntados.episodes.core.domain.exception.InvalidUpdateNotOwnChannelException
import io.ktor.http.*
import kotlinx.serialization.SerializationException
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class ExceptionMapperTest {

    private lateinit var mapper: ExceptionMapper
    private lateinit var serializationRequestCode: HttpStatusCode
    private lateinit var illegalArgumentRequestCode: HttpStatusCode
    private lateinit var episodeNotFoundRequestCode: HttpStatusCode
    private lateinit var invalidProfileRequestCode: HttpStatusCode
    private lateinit var invalidUpdateNotOwnChannelRequestCode: HttpStatusCode
    private lateinit var internalCode: HttpStatusCode
    private lateinit var serializationCode: String
    private lateinit var illegalArgumentCode: String
    private lateinit var episodeNotFoundCode: String
    private lateinit var invalidProfileCode: String
    private lateinit var invalidUpdateNotOwnChannelCode: String
    private lateinit var unknownCode: String

    @Test
    fun `map correctly to http code`() {
        givenExceptionMapper()

        whenMapExceptionsToHttpCodes()

        thenValidateCorrectHttpCodes()
    }

    @Test
    fun `map correctly to domain code`() {
        givenExceptionMapper()

        whenMapExceptionsToDomainCodes()

        thenValidateCorrectDomainCodes()
    }

    private fun givenExceptionMapper() {
        mapper = ExceptionMapper()
    }

    private fun whenMapExceptionsToHttpCodes() {
        serializationRequestCode = mapper.toHttpCode(SerializationException())
        illegalArgumentRequestCode = mapper.toHttpCode(IllegalArgumentException())
        episodeNotFoundRequestCode = mapper.toHttpCode(EpisodeNotFoundException(EPISODE_ID))
        invalidProfileRequestCode = mapper.toHttpCode(InvalidProfileException(PLAYER_ID))
        invalidUpdateNotOwnChannelRequestCode = mapper.toHttpCode(
            InvalidUpdateNotOwnChannelException(
                playerId = PLAYER_ID,
                ownerId = 123,
                channelId = "channelId"
            )
        )
        internalCode = mapper.toHttpCode(RuntimeException())
    }

    private fun whenMapExceptionsToDomainCodes() {
        serializationCode = mapper.toDomainCode(SerializationException())
        illegalArgumentCode = mapper.toDomainCode(IllegalArgumentException())
        episodeNotFoundCode = mapper.toDomainCode(EpisodeNotFoundException(EPISODE_ID))
        invalidProfileCode = mapper.toDomainCode(InvalidProfileException(PLAYER_ID))
        invalidUpdateNotOwnChannelCode = mapper.toDomainCode(
            InvalidUpdateNotOwnChannelException(
                playerId = PLAYER_ID,
                ownerId = 123,
                channelId = "channelId"
            )
        )
        unknownCode = mapper.toDomainCode(RuntimeException())
    }

    private fun thenValidateCorrectHttpCodes() {
        assertEquals(HttpStatusCode.BadRequest, episodeNotFoundRequestCode)
        assertEquals(HttpStatusCode.BadRequest, invalidProfileRequestCode)
        assertEquals(HttpStatusCode.BadRequest, invalidUpdateNotOwnChannelRequestCode)
        assertEquals(HttpStatusCode.InternalServerError, serializationRequestCode)
        assertEquals(HttpStatusCode.InternalServerError, illegalArgumentRequestCode)
        assertEquals(HttpStatusCode.InternalServerError, internalCode)
    }

    private fun thenValidateCorrectDomainCodes() {
        assertEquals("2000", serializationCode)
        assertEquals("2001", illegalArgumentCode)
        assertEquals("2002", episodeNotFoundCode)
        assertEquals("2003", invalidProfileCode)
        assertEquals("3021", invalidUpdateNotOwnChannelCode)
        assertEquals("UNDEFINED", unknownCode)
    }

    private companion object {
        const val EPISODE_ID = "episodeId"
        const val PLAYER_ID = 666L
    }
}
