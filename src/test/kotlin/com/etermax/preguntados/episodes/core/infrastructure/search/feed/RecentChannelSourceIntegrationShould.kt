package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.opensearch.client.opensearch._types.Refresh
import java.time.OffsetDateTime
import java.time.OffsetDateTime.now
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class RecentChannelSourceIntegrationShould :
    AbstractOpenSearchSourceIntegrationShould(indexDescriptor = "opensearch/mapping/channels-index-mapping-v1.1.json") {

    protected lateinit var source: Source<String>

    @BeforeEach
    fun setUp() {
        source = RecentChannelSource(osClient, indexName)
    }

    @Test
    fun `retrieve channels filtered by language`() = runTest {
        givenAChannel(id = "1", language = Language.EN, lastModificationDate = now().minusHours(1))
        givenAChannel(id = "2", language = Language.ES)
        givenAChannel(id = "3", language = Language.EN, lastModificationDate = now())

        val result = whenRetrieve(language = Language.EN)

        assertEquals(listOf("3", "1"), result.items)
        assertTrue((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `retrieve channels sorted by date`() = runTest {
        givenAChannel(id = "1", lastModificationDate = now().minusHours(2))
        givenAChannel(id = "2", lastModificationDate = now().minusHours(1))
        givenAChannel(id = "3", lastModificationDate = now())

        val result = whenRetrieve()

        assertEquals(listOf("3", "2", "1"), result.items)
        assertTrue((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `retrieve only channels with type PUBLIC`() = runTest {
        givenAChannel(id = "1", type = ChannelType.PUBLIC, lastModificationDate = now().minusHours(2))
        givenAChannel(id = "2", type = ChannelType.PUBLIC, lastModificationDate = now().minusHours(1))
        givenAChannel(id = "3", type = ChannelType.PRIVATE, lastModificationDate = now())

        val result = whenRetrieve()

        assertEquals(listOf("2", "1"), result.items)
        assertTrue((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `retrieve only channels with minimum content`() = runTest {
        givenAChannel(id = "1", episodesCount = 3, lastModificationDate = now().minusHours(2))
        givenAChannel(id = "2", episodesCount = 2, lastModificationDate = now().minusHours(1))
        givenAChannel(id = "3", episodesCount = 1, lastModificationDate = now())

        val result = whenRetrieve()

        assertEquals(listOf("2", "1"), result.items)
        assertTrue((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `retrieve channels respecting offset and limit`() = runTest {
        (1..20).forEach { i ->
            givenAChannel(
                id = i.toString(),
                lastModificationDate = now().minusHours(i.toLong())
            )
        }

        val result = whenRetrieve(offset = 5, limit = 10)

        // Should return channels 6-15 (because they're sorted by date descending)
        assertEquals((6..15).map { it.toString() }, result.items)
        assertFalse((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `retrieve channels respecting fetch cursor`() = runTest {
        (1..20).forEach { i ->
            givenAChannel(
                id = i.toString(),
                lastModificationDate = now().minusHours(i.toLong())
            )
        }

        val result = whenRetrieve(offset = 0, limit = 10)

        val secondResult = whenRetrieve(
            offset = 0,
            limit = 10,
            fetchCursor = result.fetchCursor
        )

        val thirdResult = whenRetrieve(
            offset = 0,
            limit = 10,
            fetchCursor = secondResult.fetchCursor
        )

        assertFalse((result.fetchCursor as OffsetFetchCursor).exhausted)
        assertEquals((1..10).map { it.toString() }, result.items)

        assertFalse((secondResult.fetchCursor as OffsetFetchCursor).exhausted)
        assertEquals((11..20).map { it.toString() }, secondResult.items)

        assertTrue((thirdResult.fetchCursor as OffsetFetchCursor).exhausted)
        assertEquals(emptyList(), thirdResult.items)
    }

    @Test
    fun `retrieve an specified quantity of channels till exhausted`() = runTest {
        (1..5).forEach { i ->
            givenAChannel(
                id = i.toString(),
                lastModificationDate = now().minusHours(i.toLong())
            )
        }

        // Try to retrieve channels (more than available)
        val result = whenRetrieve(limit = 10)

        // Should return all 5 channels
        assertEquals((1..5).map { it.toString() }, result.items)
        assertEquals(OffsetFetchCursor(5, exhausted = true), result.fetchCursor)

        val exhaustedResult = whenRetrieve(
            limit = 10,
            fetchCursor = result.fetchCursor
        )

        assertEquals(emptyList(), exhaustedResult.items)
        assertEquals(OffsetFetchCursor(5, exhausted = true), result.fetchCursor)
    }

    @Test
    fun `answer successfully when trying to retrieve ZERO channels`() = runTest {
        givenAChannel(id = "1")

        val result = whenRetrieve(limit = 0)

        assertEquals(emptyList(), result.items)
    }

    private suspend fun whenRetrieve(
        offset: Int = 0,
        limit: Int = 10,
        language: Language? = null,
        fetchCursor: FetchCursor? = null
    ): SourceResponse<String> {
        return source.fetch(
            SourceRequest(
                OffsetLimit(
                    offset = offset,
                    limit = limit
                ),
                userId = 1,
                language = language,
                fetchCursor = fetchCursor
            )
        )
    }

    private fun givenAChannel(
        id: String,
        language: Language = Language.EN,
        type: ChannelType = ChannelType.PUBLIC,
        episodesCount: Int = 3,
        lastModificationDate: OffsetDateTime = now()
    ) {
        val channelItem = mapOf(
            ChannelItemAttributes.CHANNEL_ID to id,
            ChannelItemAttributes.LANGUAGE to language.name,
            ChannelItemAttributes.TYPE to type.name,
            ChannelItemAttributes.EPISODES_COUNT to episodesCount,
            ChannelItemAttributes.LAST_MODIFICATION_DATE to lastModificationDate.toMillis()
        )

        osClient.index {
            it.index(indexName)
            it.id(id)
            it.document(channelItem)
            it.refresh(Refresh.True)
        }.get()
    }
}
