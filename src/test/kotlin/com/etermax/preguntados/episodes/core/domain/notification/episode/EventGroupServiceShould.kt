package com.etermax.preguntados.episodes.core.domain.notification.episode

import com.etermax.preguntados.episodes.core.domain.episode.notification.*
import com.etermax.preguntados.episodes.core.domain.time.Clock
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.OffsetDateTime

class EventGroupServiceShould {

    private lateinit var clock: Clock
    private lateinit var eventGroupsRepository: EventGroupsRepository
    private lateinit var eventGroupService: EventGroupService

    @BeforeEach
    fun setUp() {
        eventGroupsRepository = mockk(relaxed = true)
        coEvery { eventGroupsRepository.increment(EPISODE_ID, EVENT_TYPE, PLAYER_ID) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            1,
            null,
            null
        )

        clock = mockk()
        coEvery { clock.now() } returns NOW

        eventGroupService = EventGroupService(eventGroupsRepository, clock, EVENT_GROUPS_CONFIGURATION)
    }

    @Test
    fun `return one on first notification`() = runTest {
        val count = eventGroupService.onEvent(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        assertThat(count).isEqualTo(1)
    }

    @Test
    fun `return one when is about to reach group`() = runTest {
        coEvery { eventGroupsRepository.increment(EPISODE_ID, EVENT_TYPE, PLAYER_ID) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            4,
            null,
            null
        )

        val count = eventGroupService.onEvent(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        assertThat(count).isEqualTo(1)
    }

    @Test
    fun `return zero when does not reached step`() = runTest {
        coEvery { eventGroupsRepository.increment(EPISODE_ID, EVENT_TYPE, PLAYER_ID) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            4,
            FIRST_STEP_COUNT,
            null
        )

        val count = eventGroupService.onEvent(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        assertThat(count).isEqualTo(0)
    }

    @Test
    fun `returns count when group size reached`() = runTest {
        coEvery { eventGroupsRepository.increment(EPISODE_ID, EVENT_TYPE, PLAYER_ID) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            FIRST_STEP_COUNT,
            FIRST_STEP_COUNT,
            null
        )

        val count = eventGroupService.onEvent(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        assertThat(count).isEqualTo(5)
    }

    @Test
    fun `return accumulated group size when time limit reached`() = runTest {
        coEvery { eventGroupsRepository.increment(EPISODE_ID, EVENT_TYPE, PLAYER_ID) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            3,
            FIRST_STEP_COUNT,
            NOW.minus(GROUP_DURATION).minusSeconds(1)
        )

        val count = eventGroupService.onEvent(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        assertThat(count).isEqualTo(3)
    }

    @Test
    fun `does not change count when reset without min count to group reached`() = runTest {
        coEvery { eventGroupsRepository.find(EPISODE_ID, EVENT_TYPE) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            3,
            null,
            null
        )

        eventGroupService.reset(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        coVerify(exactly = 0) { eventGroupsRepository.save(any(), any(), any()) }
    }

    @Test
    fun `does not change count when reset and group count not reached`() = runTest {
        coEvery { eventGroupsRepository.find(EPISODE_ID, EVENT_TYPE) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            3,
            FIRST_STEP_COUNT,
            clock.now()
        )

        eventGroupService.reset(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        coVerify(exactly = 0) { eventGroupsRepository.save(any(), any(), any()) }
    }

    @Test
    fun `reset when reset time reached`() = runTest {
        coEvery { eventGroupsRepository.find(EPISODE_ID, EVENT_TYPE) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            3,
            FIRST_STEP_COUNT,
            clock.now().minus(GROUP_DURATION)
        )

        eventGroupService.reset(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        coVerify(exactly = 1) {
            eventGroupsRepository.save(
                EPISODE_ID,
                EVENT_TYPE,
                EventGroup(
                    EPISODE_ID,
                    EVENT_TYPE,
                    PLAYER_ID,
                    0,
                    FIRST_STEP_COUNT,
                    NOW
                )
            )
        }
    }

    @Test
    fun `set first step when reach min group count`() = runTest {
        coEvery { eventGroupsRepository.find(EPISODE_ID, EVENT_TYPE) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            FIRST_STEP_COUNT,
            null,
            null
        )

        eventGroupService.reset(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        coVerify {
            eventGroupsRepository.save(
                EPISODE_ID,
                EVENT_TYPE,
                EventGroup(
                    EPISODE_ID,
                    EVENT_TYPE,
                    PLAYER_ID,
                    0,
                    FIRST_STEP_COUNT,
                    NOW
                )
            )
        }
    }

    @Test
    fun `reset group count on second step`() = runTest {
        coEvery { eventGroupsRepository.find(EPISODE_ID, EVENT_TYPE) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            5,
            SECOND_STEP_COUNT,
            NOW.minus(GROUP_DURATION)
        )

        eventGroupService.reset(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        coVerify {
            eventGroupsRepository.save(
                EPISODE_ID,
                EVENT_TYPE,
                EventGroup(
                    EPISODE_ID,
                    EVENT_TYPE,
                    PLAYER_ID,
                    0,
                    SECOND_STEP_COUNT,
                    NOW
                )
            )
        }
    }

    @Test
    fun `move to second step when reset`() = runTest {
        coEvery { eventGroupsRepository.find(EPISODE_ID, EVENT_TYPE) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            FIRST_STEP_COUNT,
            FIRST_STEP_COUNT,
            NOW.minusSeconds(1)
        )

        eventGroupService.reset(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        coVerify {
            eventGroupsRepository.save(
                EPISODE_ID,
                EVENT_TYPE,
                EventGroup(
                    EPISODE_ID,
                    EVENT_TYPE,
                    PLAYER_ID,
                    0,
                    SECOND_STEP_COUNT,
                    NOW
                )
            )
        }
    }

    @Test
    fun `does not overflow last step`() = runTest {
        coEvery { eventGroupsRepository.find(EPISODE_ID, EVENT_TYPE) } returns EventGroup(
            EPISODE_ID,
            EVENT_TYPE,
            PLAYER_ID,
            LAST_STEP_COUNT + 1,
            LAST_STEP_COUNT,
            NOW.minusSeconds(1)
        )

        eventGroupService.reset(EPISODE_ID, EVENT_TYPE, PLAYER_ID)

        coVerify {
            eventGroupsRepository.save(
                EPISODE_ID,
                EVENT_TYPE,
                EventGroup(
                    EPISODE_ID,
                    EVENT_TYPE,
                    PLAYER_ID,
                    0,
                    LAST_STEP_COUNT,
                    NOW
                )
            )
        }
    }

    private companion object {
        const val PLAYER_ID = 123L
        const val EPISODE_ID = "episodeId"
        val EVENT_TYPE = EventType.PLAY
        val NOW: OffsetDateTime = OffsetDateTime.now()
        val GROUP_DURATION: Duration = Duration.ofDays(1)

        const val MIN_COUNT_TO_GROUP = 5
        const val FIRST_STEP_COUNT = 5
        const val SECOND_STEP_COUNT = 10
        const val LAST_STEP_COUNT = 15

        val EVENT_GROUPS_CONFIGURATION = EventGroupsConfiguration(
            mapOf(
                EventType.PLAY to EventGroupConfiguration(
                    isEnabled = true,
                    MIN_COUNT_TO_GROUP,
                    listOf(FIRST_STEP_COUNT, SECOND_STEP_COUNT, LAST_STEP_COUNT),
                    GROUP_DURATION
                )
            )
        )
    }
}
