package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.episode.ChannelEpisodeOrder
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.ChannelEpisodeOrderItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelEpisodesCountItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoChannelEpisodesOrderRepositoryShould {
    private lateinit var repository: DynamoChannelEpisodesOrderRepository
    private lateinit var channelEpisodesRepository: DynamoChannelEpisodesRepository

    private val client: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        channelEpisodesRepository = DynamoChannelEpisodesRepository(
            client,
            createUpdateChannelEpisodesCountTable(),
            createChannelEpisodeTable(),
            channelEpisodesOrderNormalizer = mockk(relaxed = true)
        )

        repository = DynamoChannelEpisodesOrderRepository(
            client,
            createChannelEpisodeOrderTable()
        )
    }

    @Test
    fun `update order`() = runTest {
        givenEpisodes()
        whenPut()
        thenUpdateOrder()
    }

    private suspend fun givenEpisodes() {
        channelEpisodesRepository.add(EPISODE_1)
        channelEpisodesRepository.add(EPISODE_2)
        channelEpisodesRepository.add(EPISODE_3)
        channelEpisodesRepository.add(EPISODE_4)
    }

    private suspend fun whenPut() {
        val updated = listOf(
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = "episode_1", _episodeOrder = 1500),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = "episode_2", _episodeOrder = 2500),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = "episode_3", _episodeOrder = 3500),
            ChannelEpisodeOrder(channelId = ChannelMother.ID, episodeId = "episode_4", _episodeOrder = 4500)
        )
        repository.put(updated)
    }

    private suspend fun thenUpdateOrder() {
        val updated = channelEpisodesRepository.findChannelEpisodesLimited(
            ChannelMother.ID,
            episodesLimit = 10,
            ChannelOrderType.CUSTOM_ORDER
        )

        assertThat(updated.size).isEqualTo(4)
        assertThat(updated).containsExactly(
            ChannelMother.aChannelEpisode(channelId = ChannelMother.ID, episodeId = "episode_4", episodeOrder = 4500),
            ChannelMother.aChannelEpisode(channelId = ChannelMother.ID, episodeId = "episode_3", episodeOrder = 3500),
            ChannelMother.aChannelEpisode(channelId = ChannelMother.ID, episodeId = "episode_2", episodeOrder = 2500),
            ChannelMother.aChannelEpisode(channelId = ChannelMother.ID, episodeId = "episode_1", episodeOrder = 1500)
        )
    }

    private fun createChannelEpisodeTable(): DynamoDbAsyncTable<ChannelEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_EPISODES_TABLE_NAME, TableSchema.fromBean(ChannelEpisodeItem::class.java))
    }

    private fun createChannelEpisodeOrderTable(): DynamoDbAsyncTable<ChannelEpisodeOrderItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_EPISODES_TABLE_NAME, TableSchema.fromBean(ChannelEpisodeOrderItem::class.java))
    }

    private fun createUpdateChannelEpisodesCountTable(): DynamoDbAsyncTable<UpdateChannelEpisodesCountItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(
                CHANNELS_TABLE_NAME,
                TableSchema.fromBean(
                    UpdateChannelEpisodesCountItem::class.java
                )
            )
    }

    private companion object {
        const val TABLE_PREFIX = "dev"
        const val EPISODES_TABLE_NAME = "${TABLE_PREFIX}_episodes"
        const val CHANNELS_TABLE_NAME = "${TABLE_PREFIX}_channels"
        const val CHANNELS_EPISODES_TABLE_NAME = "${TABLE_PREFIX}_channels_episodes"

        val EPISODE_1 = ChannelMother.aChannelEpisode(episodeId = "episode_1", episodeOrder = 1000)
        val EPISODE_2 = ChannelMother.aChannelEpisode(episodeId = "episode_2", episodeOrder = 2000)
        val EPISODE_3 = ChannelMother.aChannelEpisode(episodeId = "episode_3", episodeOrder = 3000)
        val EPISODE_4 = ChannelMother.aChannelEpisode(episodeId = "episode_4", episodeOrder = 4000)

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                EpisodeItem::class.java to EPISODES_TABLE_NAME,
                ChannelItem::class.java to CHANNELS_TABLE_NAME,
                ChannelEpisodeItem::class.java to CHANNELS_EPISODES_TABLE_NAME
            )
        )
    }
}
