package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.ChannelEpisodeMother
import com.etermax.preguntados.episodes.core.ChannelMother.ANOTHER_PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.aChannel
import com.etermax.preguntados.episodes.core.ChannelMother.aSearchChannelSummary
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.SearchChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelSearchFilters
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.doubles.ChannelUnpublishedEpisodesFactory
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SearchChannelsShould {
    private lateinit var channelRepository: ChannelRepository
    private lateinit var channelEpisodesRepository: ChannelEpisodesRepository
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService
    private lateinit var action: SearchChannels

    private var channels: PaginatedItems<SearchChannelSummary>? = null

    @BeforeEach
    fun setUp() {
        profileService = mockk()
        unpublishedEpisodesService = ChannelUnpublishedEpisodesFactory.mock(sameAnswerForAll = true)
        channelRepository = mockk()
        channelEpisodesRepository = mockk()
        episodeRepository = mockk()

        action = SearchChannels(
            channelRepository,
            channelEpisodesRepository,
            episodeRepository,
            SummaryService(profileService, unpublishedEpisodesService),
            ITEMS_SIZE
        )

        channels = null

        givenProfiles()
    }

    @Test
    fun `return empty channels`() = runTest {
        givenPlayerHasNoChannels()
        whenSearch()
        thenChannelsIsEmpty()
    }

    @Test
    fun `return player with channels`() = runTest {
        givenPlayerWithChannels()
        whenSearch()
        thenReturnsChannels()
    }

    @Test
    fun `return covers ordered`() = runTest {
        givenPlayerWithOneChannelAndThreeEpisodes()
        whenSearch()
        thenReturnsCoversOrdered()
    }

    @Test
    fun `search channels for current player when profile owner is null`() = runTest {
        givenPlayerWithChannels()
        whenSearch(profileOwnerId = null)
        thenFindAllFor(PLAYER_ID)
    }

    @Test
    fun `search channels for profile owner`() = runTest {
        givenAnotherPlayerWithChannels()
        whenSearch(profileOwnerId = ANOTHER_PLAYER_ID)
        thenFindAllFor(ANOTHER_PLAYER_ID, onlyWithEpisodes = true)
    }

    @Test
    fun `search channels for current player when profile owner is current player`() = runTest {
        givenPlayerWithChannels()
        whenSearch(profileOwnerId = PLAYER_ID)
        thenFindAllFor(PLAYER_ID)
    }

    @Test
    fun `discard inconsistent episodes without channel id attribute`() = runTest {
        givenPlayerWithInconsistentEpisodeInChannels()
        whenSearch(profileOwnerId = PLAYER_ID)
        thenReturnsChannelsDiscardingInconsistencies()
    }

    private fun givenPlayerHasNoChannels() {
        val filters = ChannelSearchFilters(PLAYER_ID, onlyWithEpisodes = false)
        coEvery { channelRepository.search(filters, PAGINATION) } returns PaginatedItems(
            lastEvaluatedKey = null,
            items = listOf()
        )
        coEvery { channelEpisodesRepository.findChannelEpisodesLimited(any<List<Channel>>(), any()) } returns emptySet()
        coEvery { episodeRepository.findByIds(any()) } returns emptyList()
    }

    private fun givenPlayerWithChannels() {
        val filters = ChannelSearchFilters(PLAYER_ID, onlyWithEpisodes = false)
        val channels = listOf(CHANNEL_1, CHANNEL_2)
        coEvery { channelRepository.search(filters, PAGINATION) } returns PaginatedItems(
            LAST_EVALUATED_KEY,
            channels
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                listOf(CHANNEL_1, CHANNEL_2),
                episodesPerChannel = 3
            )
        } returns setOf(CHANNEL_EPISODE)

        coEvery { episodeRepository.findByIds(listOf(EPISODE.id)) } returns listOf(EPISODE)
    }

    private fun givenPlayerWithInconsistentEpisodeInChannels() {
        val filters = ChannelSearchFilters(PLAYER_ID, onlyWithEpisodes = false)
        val channels = listOf(CHANNEL_1, CHANNEL_2)
        coEvery { channelRepository.search(filters, PAGINATION) } returns PaginatedItems(
            LAST_EVALUATED_KEY,
            channels
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                listOf(CHANNEL_1, CHANNEL_2),
                episodesPerChannel = 3
            )
        } returns setOf(CHANNEL_EPISODE, CHANNEL_EPISODE_2)

        coEvery {
            episodeRepository.findByIds(
                listOf(
                    EPISODE.id,
                    EPISODE_2.id
                )
            )
        } returns listOf(EPISODE.copy(channelId = null), EPISODE_2)
    }

    private fun givenAnotherPlayerWithChannels() {
        val filters = ChannelSearchFilters(ANOTHER_PLAYER_ID, onlyWithEpisodes = true)
        val channel1 = CHANNEL_1.copy(ownerId = ANOTHER_PLAYER_ID)
        val channel2 = CHANNEL_2.copy(ownerId = ANOTHER_PLAYER_ID)
        val channels = listOf(channel1, channel2)
        coEvery { channelRepository.search(filters, PAGINATION) } returns PaginatedItems(
            LAST_EVALUATED_KEY,
            channels
        )
        coEvery {
            channelEpisodesRepository.findChannelEpisodesLimited(
                listOf(channel1, channel2),
                3
            )
        } returns setOf(CHANNEL_EPISODE)

        coEvery { episodeRepository.findByIds(listOf(EPISODE.id)) } returns listOf(EPISODE.copy(ownerId = ANOTHER_PLAYER_ID))
    }

    private fun givenPlayerWithOneChannelAndThreeEpisodes() {
        val filters = ChannelSearchFilters(PLAYER_ID, onlyWithEpisodes = false)
        val channels = listOf(CHANNEL_1)
        coEvery { channelRepository.search(filters, PAGINATION) } returns PaginatedItems(
            LAST_EVALUATED_KEY,
            channels
        )
        coEvery { channelEpisodesRepository.findChannelEpisodesLimited(listOf(CHANNEL_1), 3) } returns setOf(
            CHANNEL_EPISODE_1,
            CHANNEL_EPISODE_2,
            CHANNEL_EPISODE_3
        )

        coEvery { episodeRepository.findByIds(listOf(EPISODE_1.id, EPISODE_2.id, EPISODE_3.id)) } returns listOf(
            EPISODE_1,
            EPISODE_3,
            EPISODE_2
        )
    }

    private fun givenProfiles() {
        val profile1 = ProfileMother.aProfile(PLAYER_ID)
        val profile2 = ProfileMother.aProfile(ANOTHER_PLAYER_ID)

        coEvery { profileService.find(PLAYER_ID) } returns profile1
        coEvery { profileService.find(ANOTHER_PLAYER_ID) } returns profile2

        coEvery { profileService.findMany(listOf(PLAYER_ID)) } returns listOf(profile1)
        coEvery { profileService.findMany(listOf(ANOTHER_PLAYER_ID)) } returns listOf(profile2)
        coEvery { profileService.findMany(listOf(PLAYER_ID, ANOTHER_PLAYER_ID)) } returns listOf(profile1, profile2)
    }

    private suspend fun whenSearch(profileOwnerId: Long? = null) {
        val actionData = SearchChannels.ActionData(PLAYER_ID, profileOwnerId, LAST_EVALUATED_KEY)
        channels = action(actionData)
    }

    private fun thenChannelsIsEmpty() {
        assertThat(channels!!.hasItems()).isFalse()
    }

    private fun thenReturnsChannels() {
        assertThat(channels!!.items).containsExactly(SUMMARY_1, SUMMARY_2)
    }

    private fun thenReturnsCoversOrdered() {
        val items = channels!!.items
        assertThat(items.first().episodeCovers).containsExactly(EPISODE_1.cover, EPISODE_2.cover, EPISODE_3.cover)
    }

    private fun thenFindAllFor(playerId: Long, onlyWithEpisodes: Boolean = false) {
        val filters = ChannelSearchFilters(playerId, onlyWithEpisodes)
        coVerify(exactly = 1) { channelRepository.search(filters, PAGINATION) }
    }

    private fun thenReturnsChannelsDiscardingInconsistencies() {
        assertThat(channels!!.items).containsExactly(SUMMARY_1.copy(episodeCovers = listOf("coverM")), SUMMARY_2)
    }

    private companion object {
        const val LAST_EVALUATED_KEY = "key"
        const val ITEMS_SIZE = 20
        val PAGINATION = PaginationFilter(ITEMS_SIZE, LAST_EVALUATED_KEY)
        val CHANNEL_1 = aChannel()
        val CHANNEL_2 = aChannel(id = "another_channel", ownerId = ANOTHER_PLAYER_ID)
        val SUMMARY_1 = aSearchChannelSummary()
        val SUMMARY_2 = aSearchChannelSummary(id = "another_channel", ownerId = ANOTHER_PLAYER_ID, covers = emptyList())
        val EPISODE = EpisodeMother.buildEpisode(channelId = CHANNEL_1.id)
        val CHANNEL_EPISODE = ChannelEpisodeMother.aChannelEpisode(channelId = CHANNEL_1.id, episodeId = EPISODE.id)
        val EPISODE_1 = EpisodeMother.buildEpisode(id = "episode1", channelId = CHANNEL_1.id, cover = "coverZ")
        val EPISODE_2 = EpisodeMother.buildEpisode(id = "episode2", channelId = CHANNEL_1.id, cover = "coverM")
        val EPISODE_3 = EpisodeMother.buildEpisode(id = "episode3", channelId = CHANNEL_1.id, cover = "coverA")
        val CHANNEL_EPISODE_1 = ChannelEpisodeMother.aChannelEpisode(channelId = CHANNEL_1.id, episodeId = EPISODE_1.id)
        val CHANNEL_EPISODE_2 = ChannelEpisodeMother.aChannelEpisode(channelId = CHANNEL_1.id, episodeId = EPISODE_2.id)
        val CHANNEL_EPISODE_3 = ChannelEpisodeMother.aChannelEpisode(channelId = CHANNEL_1.id, episodeId = EPISODE_3.id)
    }
}
