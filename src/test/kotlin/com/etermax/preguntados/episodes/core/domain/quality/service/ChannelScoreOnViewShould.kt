package com.etermax.preguntados.episodes.core.domain.quality.service

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelScoreRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.quality.likerate.EpisodeLikeRateAvgCalculator
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ChannelScoreOnViewShould {

    private lateinit var channelEpisodesRepository: ChannelEpisodesRepository
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var channelRepository: ChannelRepository
    private lateinit var defaultQualityService: DefaultQualityService
    private lateinit var episodeLikeRateAvgCalculator: EpisodeLikeRateAvgCalculator
    private lateinit var channelScoreRepository: ChannelScoreRepository

    @BeforeEach
    fun setUp() {
        channelEpisodesRepository = mockk()
        episodeRepository = mockk()
        channelRepository = mockk()
        episodeLikeRateAvgCalculator = EpisodeLikeRateAvgCalculator()
        channelScoreRepository = mockk()
        defaultQualityService =
            DefaultQualityService(
                channelEpisodesRepository,
                episodeRepository,
                channelRepository,
                episodeLikeRateAvgCalculator,
                channelScoreRepository,
                minimumRates = 30,
                minimumViews = 100
            )
    }

    @Test
    fun `should calculate channel score when user view one episode and meet minimum rate threshold`() = runTest {
        // given
        val episode = givenEpisodeWithViews(EPISODE_ID_1, views = 99) // 0% like rate
        val episodeWithOneMoreView = givenViewedEpisodeFrom(episode) // Inc views to 100
        givenRepositoriesWith(episodeWithOneMoreView)

        // when rated episode
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode,
            episodeAfterUpdate = episodeWithOneMoreView
        )

        // then
        thenChannelScoreIsUpdatedTo(score = 0)
    }

    @Test
    fun `should calculate channel score when user view one episode with any likes and meet minimum rate threshold`() =
        runTest {
            // given
            val episode =
                givenEpisodeWithViewsAndRates(EPISODE_ID_1, views = 99, likes = 10, dislikes = 5) // 66% like rate
            val episodeWithOneMoreView = givenViewedEpisodeFrom(episode) // Inc views to 100
            givenRepositoriesWith(episodeWithOneMoreView)

            // when rated episode
            whenToProcessChannelScore(
                episodeBeforeUpdate = episode,
                episodeAfterUpdate = episodeWithOneMoreView
            )

            // then
            thenChannelScoreIsUpdatedTo(score = 66)
        }

    @Test
    fun `should not calculate channel score when user view one episode and no meet minimum rate threshold`() = runTest {
        // given
        val episode = givenEpisodeWithViews(EPISODE_ID_1, views = 98) // 0% like rate
        val episodeWithOneMoreView = givenViewedEpisodeFrom(episode) // Inc views to 99, not enough views
        givenRepositoriesWith(episodeWithOneMoreView)

        // when rated episode
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode,
            episodeAfterUpdate = episodeWithOneMoreView
        )

        // then
        thenChannelScoreIsNotUpdated()
    }

    @Test
    fun `should calculate channel score when user view an episode & channel have all valid episodes`() = runTest {
        // given
        val episode1 =
            givenEpisodeWithViewsAndRates(EPISODE_ID_1, views = 101, likes = 10, dislikes = 0) // 100% like rate
        val episode2 = givenEpisodeWithViews(EPISODE_ID_2, views = 102) // 0% like rate
        val episode3 = givenEpisodeWithViews(EPISODE_ID_3, views = 103) // 0% like rate
        val episode1WithOneMoreView = givenViewedEpisodeFrom(episode1) // Inc views by 1 -> 100% like rate
        val episodes = listOf(episode1WithOneMoreView, episode2, episode3)
        givenRepositoriesWith(episodes)

        // when rated episode 1
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode1,
            episodeAfterUpdate = episode1WithOneMoreView
        )

        // then
        thenChannelScoreIsUpdatedTo(score = 33) // (100 + 0 + 0) / 3 = 33
    }

    @Test
    fun `should calculate channel score when user view an episode & channel have mixed valid episodes`() = runTest {
        // given
        val episode1 =
            givenEpisodeWithViewsAndRates(EPISODE_ID_1, views = 101, likes = 10, dislikes = 0) // 100% like rate
        val episode2 = givenEpisodeWithViews(EPISODE_ID_2, views = 30) // not enough views
        val episode3 = givenEpisodeWithViews(EPISODE_ID_3, views = 103) // 0% like rate
        val episode1WithOneMoreView = givenViewedEpisodeFrom(episode1) // Inc views by 1 -> 100% like rate
        val episodes = listOf(episode1WithOneMoreView, episode2, episode3)
        givenRepositoriesWith(episodes)

        // when rated episode 1
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode1,
            episodeAfterUpdate = episode1WithOneMoreView
        )

        // then
        thenChannelScoreIsUpdatedTo(score = 50) // (100 + 0) / 2 = 50
    }

    @Test
    fun `should not calculate channel score when user view an episode & channel have all invalid episodes`() = runTest {
        // given
        val episode1 = givenEpisodeWithViewsAndRates(
            EPISODE_ID_1,
            views = 5,
            likes = 10,
            dislikes = 0
        ) // 0% like rate, not enough rates and views
        val episode2 = givenEpisodeWithViews(EPISODE_ID_2, views = 6) // 0% like rate, not enough rates
        val episode3 = givenEpisodeWithViews(EPISODE_ID_3, views = 50) // 0% like rate, not enough rates and views
        val episode1WithOneMoreView = givenViewedEpisodeFrom(episode1) // Inc views by 1 -> not enough views
        val episodes = listOf(episode1WithOneMoreView, episode2, episode3)
        givenRepositoriesWith(episodes)

        // when rated episode 1
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode1,
            episodeAfterUpdate = episode1WithOneMoreView
        )

        // then
        thenChannelScoreIsNotUpdated()
    }

    private fun givenRepositoriesWith(allEpisodes: List<Episode>) {
        coEvery { channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL.id) } returns allEpisodes.map { it.id }
        coEvery { episodeRepository.findByIds(allEpisodes.map { it.id }) } returns allEpisodes
        coEvery { channelRepository.findById(CHANNEL.id) } returns CHANNEL
    }

    private fun givenEpisodeWithViews(id: String, views: Long) =
        EpisodeMother.buildEpisode(
            id = id,
            channelId = CHANNEL.id,
            likes = 0,
            dislikes = 0,
            views = views
        )

    private fun givenEpisodeWithViewsAndRates(id: String, views: Long, likes: Long, dislikes: Long) =
        EpisodeMother.buildEpisode(
            id = id,
            channelId = CHANNEL.id,
            likes = likes,
            dislikes = dislikes,
            views = views
        )

    /**
     * ***************
     * ***************
     * Given viewed episode
     * ***************
     * ***************
     */
    private fun givenViewedEpisodeFrom(episode: Episode) =
        episode.copy(views = episode.views + 1)

    private fun givenRepositoriesWith(episode: Episode) {
        coEvery { channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL.id) } returns listOf(
            episode.id
        )
        coEvery { episodeRepository.findByIds(listOf(episode.id)) } returns listOf(episode)
        coEvery { channelRepository.findById(CHANNEL.id) } returns CHANNEL
    }

    /**
     * ***************
     * ***************
     * When to process channel score
     * ***************
     * ***************
     */
    private suspend fun whenToProcessChannelScore(episodeBeforeUpdate: Episode, episodeAfterUpdate: Episode) {
        defaultQualityService.calculateChannelScore(
            episodeBeforeUpdate = episodeBeforeUpdate,
            episodeAfterUpdate = episodeAfterUpdate
        )
    }

    /**
     * ***************
     * ***************
     * Then channel score is updated to
     * ***************
     * ***************
     */
    private fun thenChannelScoreIsUpdatedTo(score: Int) {
        coVerify { channelScoreRepository.updateScore(channelId = CHANNEL.id, score = score) }
    }

    private fun thenChannelScoreIsNotUpdated() {
        coVerify(exactly = 0) { channelScoreRepository.updateScore(any(), any()) }
    }

    private companion object {
        val CHANNEL = ChannelMother.aChannel(id = "channel-id")
        val EPISODE_ID_1 = "episode-id-1"
        val EPISODE_ID_2 = "episode-id-2"
        val EPISODE_ID_3 = "episode-id-3"
    }
}
