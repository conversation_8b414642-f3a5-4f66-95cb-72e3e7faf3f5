package com.etermax.preguntados.episodes.core.infrastructure.search.filter

import com.etermax.preguntados.episodes.core.domain.search.filter.Filter
import io.mockk.mockk
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.opensearch.client.opensearch.OpenSearchAsyncClient

@Suppress("UNCHECKED_CAST", "BETA_API_USAGE")
abstract class DuplicateFilterShould {

    abstract fun getFilter(): Filter

    internal lateinit var repository: InMemoryFilterDataRepository
    private lateinit var filter: Filter
    private val testItems = listOf("item1", "item2", "item3", "item4", "item5")
    private lateinit var result: List<String>

    @BeforeEach
    open fun setUp() {
        filter = getFilter()
    }

    @AfterEach
    fun tearDown() {
        repository.clear()
    }

    @Test
    fun `Filter out no items when bloom filter is empty`() {
        // Given an empty bloom filter (default state after initialization)

        // When
        whenApplyingFilterTo(testItems)

        // Then
        thenAllItemsAreReturned(testItems)
    }

    @Test
    fun `Filter out items that have been seen before`() {
        // Given
        val firstBatch = listOf("item1", "item2", "item3")
        val secondBatch = listOf("item2", "item3", "item4", "item5")
        givenItemsHaveBeenProcessed(firstBatch)

        // When
        whenApplyingFilterTo(secondBatch)

        // Then
        thenOnlyNewItemsAreReturned(expected = listOf("item4", "item5"), notExpected = listOf("item2", "item3"))
    }

    @Test
    fun `Handle empty input list`() {
        // Given an empty list

        // When
        whenApplyingFilterTo(emptyList())

        // Then
        thenResultIsEmpty()
    }

    @Test
    fun `Handle large number of items`() {
        // Given
        val largeList = (1..400).map { "item$it" }

        // When
        whenApplyingFilterTo(largeList)
        thenAllItemsAreReturned(largeList)

        // And when applying the filter again
        whenApplyingFilterAgainTo(largeList)

        // Then
        thenResultIsEmpty()
    }

    @Test
    fun `Filter maintains state between calls`() {
        // Given
        val firstBatch = listOf("item1", "item2")
        val secondBatch = listOf("item3", "item4")
        val thirdBatch = listOf("item1", "item2", "item3", "item4", "item5")
        givenItemsHaveBeenProcessedInSequence(firstBatch, secondBatch)

        // When
        whenApplyingFilterTo(thirdBatch)

        // Then
        thenOnlyItemsNotSeenBeforeAreReturned(expected = listOf("item5"))
    }

    private fun givenItemsHaveBeenProcessed(items: List<String>) {
        filter.apply(items)
    }

    private fun givenItemsHaveBeenProcessedInSequence(vararg batches: List<String>) {
        batches.forEach { filter.apply(it) }
    }

    private fun whenApplyingFilterTo(items: List<String>) {
        result = filter.apply(items)
    }

    private fun whenApplyingFilterAgainTo(items: List<String>) {
        result = filter.apply(items)
    }

    private fun thenAllItemsAreReturned(expectedItems: List<String>) {
        Assertions.assertEquals(expectedItems.size, result.size)
        Assertions.assertEquals(expectedItems, result)
    }

    private fun thenOnlyNewItemsAreReturned(expected: List<String>, notExpected: List<String>) {
        Assertions.assertEquals(expected.size, result.size)
        expected.forEach { Assertions.assertTrue(result.contains(it)) }
        notExpected.forEach { Assertions.assertFalse(result.contains(it)) }
    }

    private fun thenResultIsEmpty() {
        Assertions.assertTrue(result.isEmpty())
    }

    private fun thenOnlyItemsNotSeenBeforeAreReturned(expected: List<String>) {
        Assertions.assertEquals(expected.size, result.size)
        expected.forEach { Assertions.assertTrue(result.contains(it)) }
    }
}

class OnlyDuplicateFilterShould : DuplicateFilterShould() {

    @BeforeEach
    override fun setUp() {
        repository = InMemoryFilterDataRepository()
        super.setUp()
    }

    override fun getFilter(): Filter {
        return DuplicateFilter("scope", repository)
    }
}

class PlayedDuplicatedFilterShould : DuplicateFilterShould() {

    private lateinit var osClient: OpenSearchAsyncClient

    @BeforeEach
    override fun setUp() {
        repository = InMemoryFilterDataRepository()
        osClient = mockk()
        super.setUp()
    }

    override fun getFilter(): Filter {
        return PlayedDuplicatedFilter(USER_ID, repository, osClient, EPISODES_INDEX_NAME)
    }

    private companion object {
        const val USER_ID = 666L
        const val EPISODES_INDEX_NAME = "episodesIndexName"
    }
}
