package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.AssignChannelIdToEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.RemoveChannelFromEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelEpisodesCountItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.DynamoDBEpisodeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.tables.ProgressItem
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoDbChannelRemoveEpisodeRepositoryShould {
    private lateinit var repository: DynamoDbChannelRemoveEpisodeRepository

    private lateinit var episodesRepository: DynamoDBEpisodeRepository
    private lateinit var channelRepository: DynamoDBChannelRepository
    private lateinit var channelEpisodesRepository: DynamoChannelEpisodesRepository

    private var episode: Episode? = null
    private var channel: Channel? = null

    private val client: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()

    @BeforeEach
    fun setUp() {
        episode = null
        channel = null

        val episodeTable = createEpisodeTable()
        val channelTable = createChannelTable()
        val channelEpisodeTable = createChannelEpisodeTable()
        val removeChannelIdFromChannelTable = createRemoveChannelIdFromChannelTable()
        val updateChannelEpisodesCountTable = createUpdateChannelEpisodesCountTable()

        episodesRepository = DynamoDBEpisodeRepository(client, episodeTable, mockk())
        channelRepository = DynamoDBChannelRepository(client, channelTable)
        channelEpisodesRepository = DynamoChannelEpisodesRepository(
            client,
            updateChannelEpisodesCountTable,
            channelEpisodeTable,
            channelEpisodesOrderNormalizer = mockk(relaxed = true)
        )

        repository = DynamoDbChannelRemoveEpisodeRepository(
            client,
            removeChannelIdFromChannelTable,
            updateChannelEpisodesCountTable,
            channelEpisodeTable
        )
    }

    @Test
    fun `remove items`() = runTest {
        givenAnEpisode()
        givenAChannel(episodesCount = 10)
        givenChannelEpisode()

        whenRemove(episodesCount = 9)

        thenRemove()
    }

    private suspend fun givenAChannel(episodesCount: Int) {
        channel = ChannelMother.aChannel(id = CHANNEL_ID, episodesCount = episodesCount)
        channelRepository.add(channel!!)
    }

    private suspend fun givenAnEpisode() {
        episode = EpisodeMother.buildEpisode(id = EPISODE_ID, channelId = CHANNEL_ID)
        episodesRepository.save(episode!!)
    }

    private suspend fun givenChannelEpisode() {
        val channelEpisode = ChannelMother.aChannelEpisode(CHANNEL_ID, EPISODE_ID)
        channelEpisodesRepository.add(channelEpisode)
    }

    private suspend fun whenRemove(episodesCount: Int) {
        val channel = ChannelMother.aChannel(id = CHANNEL_ID, episodesCount = episodesCount)
        repository.remove(EPISODE_ID, channel)
    }

    private suspend fun thenRemove() {
        val episode = episodesRepository.findById(EPISODE_ID)!!
        assertThat(episode.channelId).isNull()

        val channel = channelRepository.findById(CHANNEL_ID)!!
        assertThat(channel.episodesCount).isEqualTo(9)

        val channelEpisodes = channelEpisodesRepository.findAllFrom(CHANNEL_ID)
        assertThat(channelEpisodes.items).isEmpty()
    }

    private fun createEpisodeTable(): DynamoDbAsyncTable<EpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()
            .table(EPISODES_TABLE_NAME, TableSchema.fromBean(EpisodeItem::class.java))
    }

    private fun createEpisodeProgressTable(): DynamoDbAsyncTable<ProgressItem> {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()
            .table(EPISODES_TABLE_NAME, TableSchema.fromBean(ProgressItem::class.java))
    }

    private fun createChannelTable(): DynamoDbAsyncTable<ChannelItem> {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(ChannelItem::class.java))
    }

    private fun createUpdateChannelEpisodesCountTable(): DynamoDbAsyncTable<UpdateChannelEpisodesCountItem> {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()
            .table(
                CHANNELS_TABLE_NAME,
                TableSchema.fromBean(
                    UpdateChannelEpisodesCountItem::class.java
                )
            )
    }

    private fun createChannelEpisodeTable(): DynamoDbAsyncTable<ChannelEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()
            .table(
                CHANNELS_TABLE_NAME,
                TableSchema.fromBean(
                    ChannelEpisodeItem::class.java
                )
            )
    }

    private fun createAssignChannelIdToEpisodeTable(): DynamoDbAsyncTable<AssignChannelIdToEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()
            .table(
                CHANNELS_TABLE_NAME,
                TableSchema.fromBean(
                    AssignChannelIdToEpisodeItem::class.java
                )
            )
    }

    private fun createRemoveChannelIdFromChannelTable(): DynamoDbAsyncTable<RemoveChannelFromEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()
            .table(
                EPISODES_TABLE_NAME,
                TableSchema.fromBean(
                    RemoveChannelFromEpisodeItem::class.java
                )
            )
    }

    private companion object {
        const val EPISODE_ID = "episode_id"
        const val CHANNEL_ID = ChannelMother.ID

        const val TABLE_PREFIX = "dev"
        const val EPISODES_TABLE_NAME = "${TABLE_PREFIX}_episodes"
        const val CHANNELS_TABLE_NAME = "${TABLE_PREFIX}_channels"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                EpisodeItem::class.java to EPISODES_TABLE_NAME,
                ChannelItem::class.java to CHANNELS_TABLE_NAME
            )
        )
    }
}
