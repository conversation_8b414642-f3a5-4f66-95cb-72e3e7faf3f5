package com.etermax.preguntados.episodes.core.infrastructure.repository.challenge

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChallengeMother
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayerRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables.ChallengeIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables.ChallengePlayerItem
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import kotlin.test.assertNotNull

class DynamoDBChallengePlayerRepositoryShould {
    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    private lateinit var repository: ChallengePlayerRepository

    @BeforeEach
    fun setUp() {
        repository = DynamoDBChallengePlayerRepository(dynamoDbEnhancedClient, createTable(), createIndex())
    }

    @Test
    fun `Save a challenge player`() = runTest {
        givenAChallengePlayer()
        thenIsAdded()
    }

    private suspend fun givenAChallengePlayer() {
        repository.save(aChallengePlayer())
    }

    private fun aChallengePlayer(): ChallengePlayer {
        return ChallengePlayer(PLAYER_ID, CHALLENGE_ID, ChallengePlayer.Status.PENDING)
    }

    private suspend fun thenIsAdded() {
        val challengePlayer = repository.findBy(PLAYER_ID, CHALLENGE_ID)
        assertNotNull(challengePlayer)
    }

    private fun createTable(): DynamoDbAsyncTable<ChallengePlayerItem> {
        return dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(ChallengePlayerItem::class.java))
    }

    private fun createIndex(): DynamoDbAsyncIndex<ChallengePlayerItem> {
        return dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(ChallengePlayerItem::class.java))
            .index(ChallengeIndexes.BY_PLAYER)
    }

    private companion object {
        const val TABLE_NAME = "dev_challenges"
        const val CHALLENGE_ID = ChallengeMother.ID
        const val PLAYER_ID = 666L

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(ChallengePlayerItem::class.java to TABLE_NAME))
    }
}
