package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.episode.*
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus.*
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionSingleExecutor
import com.etermax.preguntados.episodes.core.domain.exception.*
import com.etermax.preguntados.episodes.core.domain.moderation.ModerationService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother.aProfile
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class UpdateEpisodeShould {

    private lateinit var newEpisodeRepository: NewEpisodeRepository
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var profileService: ProfileService
    private lateinit var moderationService: ModerationService
    private lateinit var clock: Clock
    private lateinit var postActionExecutor: UpdateChannelEpisodePostActionSingleExecutor
    private lateinit var episodeCreationNotifier: EpisodeCreationNotifier

    private lateinit var result: EpisodeSummary
    private lateinit var actionData: UpdateEpisode.ActionData
    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk(relaxed = true)
        profileService = mockk(relaxed = true)
        moderationService = mockk(relaxed = true)
        newEpisodeRepository = mockk(relaxed = true)
        clock = mockk(relaxed = true)
        postActionExecutor = mockk(relaxUnitFun = true)
        episodeCreationNotifier = mockk(relaxed = true)
        coEvery { newEpisodeRepository.find(any()) }.returns(listOf())
        givenAClock()
    }

    @Test
    fun `throw exception when episode does not exist`() = runTest {
        givenDataToUpdate()
        givenNoEpisode()

        whenUpdate()

        thenEpisodeNotFoundExceptionIsThrown()
    }

    @Test
    fun `throw exception when update an episode of another player`() = runTest {
        givenDataToUpdate(playerId = ANOTHER_PLAYER_ID)
        givenAnEpisode()

        whenUpdate()

        thenInvalidUpdateNotOwnEpisodeExceptionIsThrown()
    }

    @Test
    fun `throw exception when name is empty`() = runTest {
        givenDataToUpdate(name = " ")
        givenAnEpisode()

        whenUpdate()

        thenEpisodeNameEmptyExceptionIsThrown()
    }

    @Test
    fun `throw exception when name is not allowed`() = runTest {
        givenDataToUpdate()
        givenAnEpisode()
        givenATextNotAllowed()

        whenUpdate()

        thenEpisodeNameNotAllowedExceptionIsThrown()
    }

    @Test
    fun `throw exception when content is empty`() = runTest {
        givenDataToUpdate(contents = emptyList())
        givenAnEpisode()
        givenATextAllowed()

        whenUpdate()

        thenEpisodeContentEmptyExceptionIsThrown()
    }

    @Test
    fun `update episode`() = runTest {
        givenDataToUpdate()
        givenAnEpisode()
        givenATextAllowed()

        whenUpdate()

        thenEpisodeIsUpdated()
    }

    @Test
    fun `notify to players if episode is being published`() = runTest {
        givenDataToUpdate(status = PUBLISHED)
        givenAnEpisode(status = PENDING)
        givenATextAllowed()

        whenUpdate()

        thenNotificationIsSent()
    }

    @Test
    fun `do not notify to players if episode is already published`() = runTest {
        givenDataToUpdate(status = PUBLISHED)
        givenAnEpisode(status = PUBLISHED)
        givenATextAllowed()

        whenUpdate()

        thenNotificationIsNotSent()
    }

    @Test
    fun `do not notify to players if episode is not being published`() = runTest {
        givenDataToUpdate(status = PENDING)
        givenAnEpisode(status = PENDING)
        givenATextAllowed()

        whenUpdate()

        thenNotificationIsNotSent()
    }

    @Test
    fun `do not notify to players if episode new status is null`() = runTest {
        givenDataToUpdate(status = null)
        givenAnEpisode(status = PENDING)
        givenATextAllowed()

        whenUpdate()

        thenNotificationIsNotSent()
    }

    @Test
    fun `update channel id from episode`() = runTest {
        givenDataToUpdate(channelId = ChannelMother.ID)
        givenAnEpisode()
        givenATextAllowed()

        whenUpdate()

        thenEpisodeIsUpdated(channelId = ChannelMother.ID)
    }

    @Test
    fun `remove channelId when is blank`() = runTest {
        givenDataToUpdate(channelId = "")
        givenAnEpisode(channelId = ChannelMother.ID)
        givenATextAllowed()

        whenUpdate()

        thenEpisodeIsUpdated(channelId = null)
    }

    @Test
    fun `not remove channelId when is null`() = runTest {
        givenDataToUpdate(channelId = null)
        givenAnEpisode(channelId = ChannelMother.ID)
        givenATextAllowed()

        whenUpdate()

        thenEpisodeIsUpdated(channelId = ChannelMother.ID)
    }

    @Test
    fun `execute post actions after updating episode`() = runTest {
        givenDataToUpdate()
        givenAnEpisode()
        givenATextAllowed()

        whenUpdate()

        thenExecutePostActionsAfterEpisodeIsUpdated()
    }

    @Test
    fun `retrieve updated episode summary`() = runTest {
        givenDataToUpdate()
        givenAProfile()
        givenAnEpisode()
        givenATextAllowed()

        whenUpdate()

        thenEpisodeSummaryIsRetrieved(UPDATED_EPISODE_SUMMARY)
    }

    @Test
    fun `update start date when episode is published`() = runTest {
        givenDataToUpdate(status = PUBLISHED)
        givenAProfile()
        givenAnEpisode(status = DRAFT)
        givenATextAllowed()

        whenUpdate()

        thenStartDateIsUpdated()
    }

    @Test
    fun `do not update episode if no value to update`() = runTest {
        givenDataToUpdate(name = null, cover = null, banner = null, contents = null, status = null, type = null)
        givenAnEpisode()
        givenATextAllowed()

        whenUpdate()

        coVerify(exactly = 0) { episodeRepository.updateItem(any()) }
    }

    @Test
    fun `retrieve same episode summary if no value to update`() = runTest {
        givenDataToUpdate(name = null, cover = null, banner = null, contents = null, status = null, type = null)
        givenAProfile()
        givenAnEpisode()
        givenATextAllowed()

        whenUpdate()

        thenEpisodeSummaryIsRetrieved(EPISODE_SUMMARY)
    }

    @Test
    fun `do not update episode if values does not change`() = runTest {
        givenDataToUpdate(
            name = EPISODE.name,
            cover = EPISODE.cover,
            banner = EPISODE.banner,
            contents = EPISODE.contents,
            status = EPISODE.status,
            type = EPISODE.type
        )
        givenAProfile()
        givenAnEpisode()
        givenATextAllowed(name = EPISODE.name)

        whenUpdate()

        coVerify(exactly = 0) { episodeRepository.updateItem(any()) }
    }

    @Test
    fun `retrieve same episode summary if values does not change`() = runTest {
        givenDataToUpdate(
            name = EPISODE.name,
            cover = EPISODE.cover,
            banner = EPISODE.banner,
            contents = EPISODE.contents,
            status = EPISODE.status
        )
        givenAProfile()
        givenAnEpisode()
        givenATextAllowed()

        whenUpdate()

        thenEpisodeSummaryIsRetrieved(EPISODE_SUMMARY)
    }

    private fun givenDataToUpdate(
        playerId: Long = PLAYER_ID,
        name: String? = NAME,
        cover: String? = COVER,
        banner: String? = BANNER,
        contents: List<String>? = UPDATED_CONTENTS,
        status: EpisodeStatus? = UPDATED_STATUS,
        type: EpisodeType? = UPDATED_TYPE,
        channelId: String? = null
    ) {
        actionData = UpdateEpisode.ActionData(
            playerId,
            EPISODE_ID,
            name,
            cover,
            banner,
            contents,
            status,
            type,
            channelId
        )
    }

    private fun givenAClock() {
        coEvery { clock.now() } returns NOW
    }

    private fun givenNoEpisode() {
        coEvery { episodeRepository.findById(any()) } returns null
    }

    private fun givenAnEpisode(status: EpisodeStatus = PUBLISHED, channelId: String? = null) {
        coEvery { episodeRepository.findById(EPISODE_ID) } returns EPISODE.copy(status = status, channelId = channelId)
    }

    private fun givenATextNotAllowed() {
        coEvery { moderationService.isTextAllowed(PLAYER_ID, EPISODE.language, NAME) } returns false
    }

    private fun givenATextAllowed(name: String = NAME) {
        coEvery { moderationService.isTextAllowed(PLAYER_ID, EPISODE.language, name) } returns true
    }

    private fun givenAProfile() {
        coEvery { profileService.find(PLAYER_ID) } returns PROFILE
    }

    private suspend fun whenUpdate() {
        val updateEpisode = UpdateEpisode(
            episodeRepository = episodeRepository,
            profileService = profileService,
            moderationService = moderationService,
            clock = clock,
            postActionExecutor = postActionExecutor,
            episodeCreationNotifier = episodeCreationNotifier
        )
        error = kotlin.runCatching {
            result = updateEpisode(actionData)
        }.exceptionOrNull()
    }

    private fun thenEpisodeNotFoundExceptionIsThrown() {
        assertThat(error).isExactlyInstanceOf(EpisodeNotFoundException::class.java)
    }

    private fun thenInvalidUpdateNotOwnEpisodeExceptionIsThrown() {
        assertThat(error).isExactlyInstanceOf(InvalidUpdateNotOwnEpisodeException::class.java)
    }

    private fun thenEpisodeNameEmptyExceptionIsThrown() {
        assertThat(error).isExactlyInstanceOf(EpisodeNameEmptyException::class.java)
    }

    private fun thenEpisodeNameNotAllowedExceptionIsThrown() {
        assertThat(error).isExactlyInstanceOf(EpisodeNameNotAllowedException::class.java)
    }

    private fun thenEpisodeContentEmptyExceptionIsThrown() {
        assertThat(error).isExactlyInstanceOf(EpisodeEmptyContentException::class.java)
    }

    private fun thenEpisodeIsUpdated(channelId: String? = null) {
        coVerify(exactly = 1) { episodeRepository.updateItem(UPDATED_EPISODE.copy(channelId = channelId)) }
    }

    private fun thenExecutePostActionsAfterEpisodeIsUpdated() {
        val oldEpisode = EPISODE.copy(status = PUBLISHED)
        val postActionData = UpdateChannelEpisodePostActionData(PENDING)
        coVerifyOrder {
            episodeRepository.updateItem(UPDATED_EPISODE)
            postActionExecutor.execute(oldEpisode, postActionData)
        }
    }

    private fun thenStartDateIsUpdated() {
        coVerify(exactly = 1) {
            episodeRepository.updateItem(UPDATED_EPISODE.copy(startDate = NOW, status = PUBLISHED))
        }
    }

    private fun thenEpisodeSummaryIsRetrieved(summary: EpisodeSummary) {
        assertThat(result).isEqualTo(summary)
    }

    private fun thenNotificationIsSent() {
        coVerify(exactly = 1) { episodeCreationNotifier.notifyEpisodeCreationToPlayers(any()) }
    }

    private fun thenNotificationIsNotSent() {
        coVerify(exactly = 0) { episodeCreationNotifier.notifyEpisodeCreationToPlayers(any()) }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val ANOTHER_PLAYER_ID = 999L
        const val EPISODE_ID = "ABC-123"
        const val NAME = "Title"
        const val COVER = "http://image/cover"
        const val BANNER = "http://image/banner"
        val NOW: OffsetDateTime = OffsetDateTime.now()
        val UPDATED_CONTENTS = listOf("CONTENT_10", "CONTENT_20", "CONTENT_30")
        val UPDATED_STATUS = PENDING
        val UPDATED_TYPE = EpisodeType.PUBLIC
        val PROFILE = aProfile(playerId = PLAYER_ID)
        val EPISODE = buildEpisode(
            id = EPISODE_ID,
            ownerId = PLAYER_ID
        )
        val UPDATED_EPISODE = buildEpisode(
            id = EPISODE_ID,
            ownerId = PLAYER_ID,
            name = NAME,
            cover = COVER,
            banner = BANNER,
            contents = UPDATED_CONTENTS,
            status = UPDATED_STATUS
        )
        val EPISODE_SUMMARY = EpisodeSummary.from(EPISODE, PROFILE)
        val UPDATED_EPISODE_SUMMARY = EpisodeSummary.from(UPDATED_EPISODE, PROFILE)
    }
}
