package com.etermax.preguntados.episodes.core

import com.etermax.preguntados.episodes.core.domain.channel.*
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import java.time.OffsetDateTime

object ChannelMother {
    const val PLAYER_ID = EpisodeMother.OWNER_ID
    const val ANOTHER_PLAYER_ID = 200L
    const val ID = "channel_id"
    const val NAME = "channel_name"
    const val DESCRIPTION = "channel_description"
    const val WEBSITE = "https://taringa.com"
    const val COVER_URL = "https://google.com"
    const val ORDER = 1000L
    const val UNPUBLISHED_EPISODES = 5
    val LANGUAGE = Language.EN
    val CREATION_DATE: OffsetDateTime = OffsetDateTime.parse("2025-01-01T18:03:00.000Z")
    val MODIFICATION_DATE: OffsetDateTime = OffsetDateTime.parse("2025-02-20T20:00:00.000Z")

    fun aChannel(
        id: String = ID,
        name: String = NAME,
        ownerId: Long = PLAYER_ID,
        episodesCount: Int = 0,
        language: Language? = null,
        creationDate: OffsetDateTime = CREATION_DATE,
        lastModificationDate: OffsetDateTime = CREATION_DATE,
        score: Int = 0,
        quality: Int = 0,
        type: ChannelType = ChannelType.PUBLIC,
        orderType: ChannelOrderType = ChannelOrderType.DATE_ADDED
    ): Channel {
        return Channel(
            id = id,
            name = name,
            description = DESCRIPTION,
            website = WEBSITE,
            coverUrl = COVER_URL,
            subscribed = true,
            ownerId = ownerId,
            statistics = ChannelStatistics(subscribers = 0, episodesCount, score = score, quality = quality),
            creationDate = creationDate,
            lastModificationDate = lastModificationDate,
            type = type,
            language = language,
            order = ORDER,
            orderType = orderType
        )
    }

    fun aChannelSummary(
        id: String = ID,
        ownerId: Long = PLAYER_ID,
        episodesCount: Int = 0,
        language: Language? = null,
        lastModificationDate: OffsetDateTime = CREATION_DATE,
        unpublishedEpisodes: Int = UNPUBLISHED_EPISODES
    ): ChannelSummary {
        return ChannelSummary(
            id = id,
            name = NAME,
            description = DESCRIPTION,
            website = WEBSITE,
            coverUrl = COVER_URL,
            subscribed = true,
            ownerId = ownerId,
            owner = ProfileMother.aProfile(ownerId),
            statistics = ChannelSummaryStatistics(subscribers = 0, episodesCount, unpublishedEpisodes),
            creationDate = CREATION_DATE,
            lastModificationDate = lastModificationDate,
            type = ChannelType.PUBLIC,
            language = language,
            orderType = ChannelOrderType.DATE_ADDED
        )
    }

    fun aChannelSummaryReduced(
        id: String = ID
    ): ChannelReduced {
        return ChannelReduced(
            id = id,
            name = NAME,
            coverUrl = COVER_URL
        )
    }

    fun aSearchChannelSummary(
        id: String = ID,
        ownerId: Long = PLAYER_ID,
        episodesCount: Int = 0,
        language: Language? = null,
        lastModificationDate: OffsetDateTime = CREATION_DATE,
        covers: List<String> = listOf(EpisodeMother.COVER)
    ): SearchChannelSummary {
        return SearchChannelSummary(
            aChannelSummary(
                id,
                ownerId,
                episodesCount,
                language,
                lastModificationDate,
                unpublishedEpisodes = UNPUBLISHED_EPISODES
            ),
            episodeCovers = covers
        )
    }

    fun aChannelEpisode(channelId: String = ID, episodeId: String, episodeOrder: Long = 1000L): ChannelEpisode {
        return ChannelEpisode(
            channelId = channelId,
            episodeId = episodeId,
            dateAdded = CREATION_DATE,
            episodeOrder = episodeOrder
        )
    }

    fun aChannelByLanguageSummary(): ChannelReduced {
        return ChannelReduced(
            id = ID,
            name = NAME,
            coverUrl = COVER_URL
        )
    }
}
