package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.delete.BlackListRecommendationService
import com.etermax.preguntados.episodes.core.domain.search.UserBasedRepository
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.infrastructure.search.service.PlayedEpisodeSearchService
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertTrue

class UserBasedEpisodeSourceShould {

    private lateinit var userBasedRepository: UserBasedRepository
    private lateinit var episodesRepository: EpisodeRepository
    private lateinit var blackListRecommendationService: BlackListRecommendationService
    private lateinit var playedEpisodeSearchService: PlayedEpisodeSearchService
    private lateinit var request: SourceRequest
    private lateinit var response: SourceResponse<String>

    @BeforeEach
    fun setUp() {
        userBasedRepository = mockk(relaxed = true)
        episodesRepository = mockk(relaxed = true)
        playedEpisodeSearchService = mockk(relaxed = true)
        blackListRecommendationService = mockk(relaxed = true)
    }

    @Test
    fun `empty episodes if limit is zero`() = runTest {
        givenLimitZero()
        givenReferenceEpisodes()

        whenFetch()

        thenEpisodesAreEmpty()
    }

    @Test
    fun `empty episodes if limit is lower than zero`() = runTest {
        givenNegativeLimit()
        givenReferenceEpisodes()

        whenFetch()

        thenEpisodesAreEmpty()
    }

    @Test
    fun `empty episodes if cursor is exhausted`() = runTest {
        givenExhaustedCursor()
        givenReferenceEpisodes()

        whenFetch()

        thenEpisodesAreEmpty(CURSOR_EXHAUSTED)
    }

    @Test
    fun `use offset of the range`() = runTest {
        givenNoCursor()
        givenReferenceEpisodes()

        whenFetch()

        thenOffsetUsedIs(RANGE.offset)
    }

    @Test
    fun `use offset of the cursor`() = runTest {
        givenRequest()
        givenReferenceEpisodes()

        whenFetch()

        thenOffsetUsedIs(FETCH_CURSOR.offset)
    }

    @Test
    fun `return user based episodes`() = runTest {
        givenRequest()
        givenUserBasedEpisodes()
        givenReferenceEpisodes()
        coEvery {
            episodesRepository.findByIds(listOf(EPISODE_ID_RESULT_1, EPISODE_ID_RESULT_2, EPISODE_ID_RESULT_3))
        } returns listOf(buildEpisode(id = EPISODE_ID_RESULT_1), buildEpisode(id = EPISODE_ID_RESULT_2), buildEpisode(id = EPISODE_ID_RESULT_3))

        whenFetch()

        assertThat(response.items).containsExactly(EPISODE_ID_RESULT_1, EPISODE_ID_RESULT_2, EPISODE_ID_RESULT_3)
    }

    @Test
    fun `return empty episodes if reference episodes is empty`() = runTest {
        givenRequest()
        givenUserBasedEpisodes()
        givenReferenceEpisodes(emptyList())
        coEvery {
            episodesRepository.findByIds(listOf(EPISODE_ID_RESULT_1, EPISODE_ID_RESULT_2, EPISODE_ID_RESULT_3))
        } returns listOf(buildEpisode(id = EPISODE_ID_RESULT_1), buildEpisode(id = EPISODE_ID_RESULT_3))

        whenFetch()

        thenEpisodesAreEmpty(cursor = CURSOR_EXHAUSTED)
    }

    @Test
    fun `return user based existing episodes`() = runTest {
        givenRequest()
        givenUserBasedEpisodes()
        givenReferenceEpisodes()
        coEvery {
            episodesRepository.findByIds(listOf(EPISODE_ID_RESULT_1, EPISODE_ID_RESULT_2, EPISODE_ID_RESULT_3))
        } returns listOf(buildEpisode(id = EPISODE_ID_RESULT_1), buildEpisode(id = EPISODE_ID_RESULT_3))

        whenFetch()

        assertThat(response.items).containsExactly(EPISODE_ID_RESULT_1, EPISODE_ID_RESULT_3)
    }

    @Test
    fun `cursor exhausted if episodes are less than limit`() = runTest {
        givenRequest()
        givenUserBasedEpisodes()
        givenReferenceEpisodes()

        whenFetch()

        assertThat(response.fetchCursor?.exhausted).isTrue
    }

    @Test
    fun `cursor not exhausted if episodes are equals than limit`() = runTest {
        givenLimit()
        givenUserBasedEpisodes()
        givenReferenceEpisodes()

        whenFetch()

        assertThat(response.fetchCursor?.exhausted).isFalse
    }

    @Test
    fun `handle invalid episodes`() = runTest {
        givenRequest()
        givenUserBasedEpisodes()
        givenReferenceEpisodes()
        coEvery {
            episodesRepository.findByIds(listOf(EPISODE_ID_RESULT_1, EPISODE_ID_RESULT_2, EPISODE_ID_RESULT_3))
        } returns listOf(buildEpisode(id = EPISODE_ID_RESULT_1), buildEpisode(id = EPISODE_ID_RESULT_3))

        whenFetch()

        coVerify(exactly = 1) { blackListRecommendationService.deleteEpisodes(listOf(EPISODE_ID_RESULT_2)) }
    }

    private fun givenLimitZero() {
        request = SOURCE_REQUEST.copy(range = OffsetLimit(0, 0), fetchCursor = FETCH_CURSOR)
    }

    private fun givenNegativeLimit() {
        request = SOURCE_REQUEST.copy(range = OffsetLimit(0, -5), fetchCursor = FETCH_CURSOR)
    }

    private fun givenExhaustedCursor() {
        request = SOURCE_REQUEST.copy(range = RANGE, fetchCursor = CURSOR_EXHAUSTED)
    }

    private fun givenNoCursor() {
        request = SOURCE_REQUEST.copy(range = RANGE, fetchCursor = null)
    }

    private fun givenRequest() {
        request = SOURCE_REQUEST
    }

    private fun givenLimit() {
        request = SOURCE_REQUEST.copy(range = RANGE.copy(limit = 3), fetchCursor = null)
    }

    private fun givenUserBasedEpisodes() {
        coEvery {
            userBasedRepository.find(any(), any(), any(), any())
        } returns USER_BASED_EPISODES
    }

    private fun givenReferenceEpisodes(referenceEpisodes: List<String> = REFERENCE_EPISODES) {
        coEvery {
            playedEpisodeSearchService.search(USER_ID, LANGUAGE.name, any<Boolean>())
        } returns referenceEpisodes
    }

    private suspend fun whenFetch() {
        val source = UserBasedEpisodeSource(
            userBasedRepository,
            playedEpisodeSearchService,
            episodesRepository,
            blackListRecommendationService
        )
        response = source.fetch(request)
    }

    private fun thenEpisodesAreEmpty(cursor: FetchCursor = FETCH_CURSOR) {
        assertTrue(response.isEmpty())
        assertThat(response.fetchCursor).isEqualTo(cursor)
    }

    private fun thenOffsetUsedIs(offsetUsed: Int) {
        coVerify(exactly = 1) {
            userBasedRepository.find(
                episodesId = REFERENCE_EPISODES,
                offset = offsetUsed,
                perPage = RANGE.limit,
                language = LANGUAGE
            )
        }
    }

    private companion object {
        const val USER_ID = 666L
        const val EPISODE_ID_1 = "4e385400-09f8-497b-9ffc-0ad11e308f4b"
        const val EPISODE_ID_2 = "e4b70f64-72d9-4afa-90ff-6a3f7e0fbc9c"
        const val EPISODE_ID_3 = "0e700043-9c89-42c3-92ab-2550a6720b5d"
        const val EPISODE_ID_RESULT_1 = "47702760-7ed4-4799-8c2c-93c30832d9d7"
        const val EPISODE_ID_RESULT_2 = "70d113bc-867c-4c1a-b83f-8ba3fdcd8f58"
        const val EPISODE_ID_RESULT_3 = "7a1c2a6a-1693-49ef-acaa-9b176aab75d8"
        val REFERENCE_EPISODES = listOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3)
        val USER_BASED_EPISODES = listOf(EPISODE_ID_RESULT_1, EPISODE_ID_RESULT_2, EPISODE_ID_RESULT_3)
        val RANGE = OffsetLimit(0, 10)
        val FETCH_CURSOR = OffsetFetchCursor(5, false)
        val CURSOR_EXHAUSTED = FETCH_CURSOR.copy(exhausted = true)
        val LANGUAGE = Language.EN
        val SOURCE_REQUEST = SourceRequest(RANGE, USER_ID, LANGUAGE, Country.US, FETCH_CURSOR)
    }
}
