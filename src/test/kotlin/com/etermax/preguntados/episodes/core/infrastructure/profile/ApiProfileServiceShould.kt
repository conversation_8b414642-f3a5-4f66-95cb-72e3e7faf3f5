package com.etermax.preguntados.episodes.core.infrastructure.profile

import com.etermax.preguntados.episodes.core.domain.exception.InvalidProfileException
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.external.services.core.domain.api.APIService
import com.etermax.preguntados.external.services.core.domain.api.profile.SlimProfile
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class ApiProfileServiceShould {

    private lateinit var result: Profile
    private lateinit var profiles: List<Profile>
    private lateinit var apiService: APIService
    private lateinit var apiProfileService: ApiProfileService

    @Test
    fun `a profile can be found`() = runTest {
        givenApiProfileService()
        whenFindProfile()
        thenProfileIsFound()
    }

    @Test
    fun `many profiles can be found`() = runTest {
        givenApiProfileService()
        whenFindProfiles()
        thenProfilesAreFound()
    }

    @Test
    fun `when failing to find profile should throw an exception`() = runTest {
        givenAnFailingApiProfileService()
        val exception = org.junit.jupiter.api.assertThrows<RuntimeException> {
            whenFindProfile()
        }
        assertThat(exception).isInstanceOf(InvalidProfileException::class.java)
    }

    private fun thenProfileIsFound() {
        assertThat(profile).isEqualTo(result)
    }

    private fun thenProfilesAreFound() {
        assertThat(listOf(profile)).isEqualTo(profiles)
    }

    private suspend fun whenFindProfile() {
        result = apiProfileService.find(PROFILE_ID)
    }

    private suspend fun whenFindProfiles() {
        profiles = apiProfileService.findMany(listOf(PROFILE_ID))
    }

    private fun givenApiProfileService() {
        apiService = mockk<APIService>()
        apiProfileService = ApiProfileService(apiService)
        coEvery { apiService.findSlimProfile(PROFILE_ID) } returns apiProfile
        coEvery { apiService.findSlimProfiles(listOf(PROFILE_ID)) } returns listOf(apiProfile)
    }

    private fun givenAnFailingApiProfileService() {
        apiService = mockk<APIService>()
        apiProfileService = ApiProfileService(apiService)
        coEvery { apiService.findProfile(PROFILE_ID) } throws RuntimeException("error")
    }

    companion object {
        private const val PROFILE_ID = 1L
        val joinDate = OffsetDateTime.now()
        val profile = Profile.build(
            PROFILE_ID,
            name = "user",
            country = "OT",
            pictureUrl = "url",
            facebookData = Profile.FacebookData("fid", "fname", showFacebookPicture = true, showFacebookName = true),
            joinDate = joinDate,
            restriction = "NON_RESTRICTED"
        )
        val apiProfile = SlimProfile(
            PROFILE_ID,
            username = "user",
            facebookId = "fid",
            facebookName = "fname",
            showFacebookPicture = true,
            showFacebookName = true,
            photoUrl = "url",
            joinDate = joinDate,
            restriction = "NON_RESTRICTED"
        )
    }
}
