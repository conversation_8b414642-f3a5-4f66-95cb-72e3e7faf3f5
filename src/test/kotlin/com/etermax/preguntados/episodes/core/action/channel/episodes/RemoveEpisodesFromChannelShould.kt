package com.etermax.preguntados.episodes.core.action.channel.episodes

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.ANOTHER_PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.MODIFICATION_DATE
import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.UNPUBLISHED_EPISODES
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRemoveEpisodeRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.PlayerNotOwnChannelException
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.quality.QualityService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.doubles.channel.repository.InMemoryChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class RemoveEpisodesFromChannelShould {
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var channelRepository: ChannelRepository
    private lateinit var channelRemoveEpisodeRepository: ChannelRemoveEpisodeRepository
    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService
    private lateinit var qualityService: QualityService
    private lateinit var clock: Clock
    private lateinit var actionRemove: RemoveEpisodesFromChannel

    private var summary: ChannelSummary? = null
    private var thrownException: RuntimeException? = null

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk()
        channelRepository = InMemoryChannelRepository()
        channelRemoveEpisodeRepository = mockk(relaxUnitFun = true)
        profileService = mockk()
        unpublishedEpisodesService = mockk()
        clock = mockk()
        qualityService = mockk(relaxed = true)

        val summaryService = SummaryService(profileService, unpublishedEpisodesService)

        actionRemove = RemoveEpisodesFromChannel(
            episodeRepository,
            channelRepository,
            channelRemoveEpisodeRepository,
            summaryService,
            clock,
            qualityService
        )

        summary = null
        thrownException = null

        givenAModificationDate()
        givenAProfile()
        givenEpisodesInChannel()
        coEvery { unpublishedEpisodesService.count(CHANNEL_ID) } returns UNPUBLISHED_EPISODES
    }

    @Test
    fun `remove episodes`() = runTest {
        givenAnExistingChannelWithEpisodes()
        whenRemove(setOf(EPISODE_ID_1))
        thenRemoveEpisode()
    }

    @Test
    fun `remove multiple episodes`() = runTest {
        givenAnExistingChannelWithEpisodes()
        whenRemove(setOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3))
        thenRemoveAllEpisodes()
    }

    @Test
    fun `remove multiple episodes despite one fail`() = runTest {
        coEvery { channelRemoveEpisodeRepository.remove(EPISODE_ID_2, any()) } throws RuntimeException("error")
        givenAnExistingChannelWithEpisodes()

        whenRemove(setOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3))

        thenRemoveAllEpisodesDespiteOneFail()
    }

    @Test
    fun `remove multiple episodes despite one not found`() = runTest {
        givenAnExistingChannelWithEpisodes()
        givenEpisodesInChannelWithOneMissing()

        whenRemove(setOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3))

        thenRemoveAllEpisodesExceptOne()
    }

    @Test
    fun `remove multiple episodes despite one not in channel`() = runTest {
        givenAnExistingChannelWithEpisodes()
        givenEpisodesInChannelWithOneNotInChannel()

        whenRemove(setOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3))

        thenRemoveAllEpisodesExceptOne()
    }

    @Test
    fun `remove multiple episodes despite one not owned by player`() = runTest {
        givenAnExistingChannelWithEpisodes()
        givenEpisodesInChannelWithOneNotOwnedByPlayer()

        whenRemove(setOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3))

        thenRemoveAllEpisodesExceptOne()
    }

    @Test
    fun `fail when channel does not exist`() = runTest {
        whenRemove(setOf(EPISODE_ID_1))
        thenThrowsException<ChannelNotFoundException>()
    }

    @Test
    fun `fail when not player not own channel`() = runTest {
        givenChannelOwnedByAnotherPlayer()
        whenRemove(setOf(EPISODE_ID_1))
        thenThrowsException<PlayerNotOwnChannelException>()
    }

    @Test
    fun `should calculate channel score when adding episodes`() = runTest {
        givenAnExistingChannelWithEpisodes()
        whenRemove(setOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3))
        thenVerifyChannelScoreCalculation()
    }

    private suspend fun givenAnExistingChannelWithEpisodes() {
        channelRepository.add(ChannelMother.aChannel(episodesCount = 3))
    }

    private suspend fun givenChannelOwnedByAnotherPlayer() {
        channelRepository.add(ChannelMother.aChannel(ownerId = ANOTHER_PLAYER_ID))
    }

    private fun givenAModificationDate() {
        every { clock.now() } returns MODIFICATION_DATE
    }

    private fun givenAProfile() {
        coEvery { profileService.find(PLAYER_ID) } returns ProfileMother.aProfile(PLAYER_ID)
    }

    private fun givenEpisodesInChannel() {
        val episode2 = EpisodeMother.buildEpisode(id = EPISODE_ID_2, channelId = CHANNEL_ID, ownerId = PLAYER_ID)

        val episodesIds = listOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3)
        val episodes = listOf(EPISODE_1, episode2, EPISODE_3)

        coEvery { episodeRepository.findByIds(listOf(EPISODE_ID_1)) } returns listOf(EPISODE_1)
        coEvery { episodeRepository.findByIds(episodesIds) } returns episodes
    }

    private fun givenEpisodesInChannelWithOneMissing() {
        val episodesIds = listOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3)
        val episodes = listOf(EPISODE_1, EPISODE_3)

        coEvery { episodeRepository.findByIds(episodesIds) } returns episodes
    }

    private fun givenEpisodesInChannelWithOneNotInChannel() {
        val episode2 = EpisodeMother.buildEpisode(id = EPISODE_ID_2, channelId = null, ownerId = PLAYER_ID)

        val episodesIds = listOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3)
        val episodes = listOf(EPISODE_1, episode2, EPISODE_3)

        coEvery { episodeRepository.findByIds(episodesIds) } returns episodes
    }

    private fun givenEpisodesInChannelWithOneNotOwnedByPlayer() {
        val episode2 = EpisodeMother.buildEpisode(
            id = EPISODE_ID_2,
            channelId = CHANNEL_ID,
            ownerId = ANOTHER_PLAYER_ID
        )

        val episodesIds = listOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3)
        val episodes = listOf(EPISODE_1, episode2, EPISODE_3)

        coEvery { episodeRepository.findByIds(episodesIds) } returns episodes
    }

    private suspend fun whenRemove(episodesIds: Set<String>) {
        try {
            val actionData = RemoveEpisodesFromChannel.ActionData(
                PLAYER_ID,
                CHANNEL_ID,
                episodesIds
            )

            summary = actionRemove(actionData)
        } catch (e: RuntimeException) {
            thrownException = e
        }
    }

    private fun thenRemoveEpisode() {
        val expectedChannelUpdated = ChannelMother.aChannel(
            episodesCount = 2,
            lastModificationDate = clock.now()
        )

        coVerify(exactly = 1) {
            channelRemoveEpisodeRepository.remove(EPISODE_ID_1, expectedChannelUpdated)
        }
        assertEquals(2, summary!!.statistics.episodes)
    }

    private fun thenRemoveAllEpisodes() {
        coVerify(exactly = 1) {
            channelRemoveEpisodeRepository.remove(
                EPISODE_ID_1,
                ChannelMother.aChannel(
                    episodesCount = 2,
                    lastModificationDate = MODIFICATION_DATE
                )
            )
        }
        coVerify(exactly = 1) {
            channelRemoveEpisodeRepository.remove(
                EPISODE_ID_2,
                ChannelMother.aChannel(
                    episodesCount = 1,
                    lastModificationDate = MODIFICATION_DATE
                )
            )
        }
        coVerify(exactly = 1) {
            channelRemoveEpisodeRepository.remove(
                EPISODE_ID_3,
                ChannelMother.aChannel(
                    episodesCount = 0,
                    lastModificationDate = MODIFICATION_DATE
                )
            )
        }

        assertEquals(0, summary!!.statistics.episodes)
    }

    private fun thenRemoveAllEpisodesDespiteOneFail() {
        coVerify(exactly = 1) {
            channelRemoveEpisodeRepository.remove(
                EPISODE_ID_1,
                ChannelMother.aChannel(
                    episodesCount = 2,
                    lastModificationDate = MODIFICATION_DATE
                )
            )
        }
        coVerify(exactly = 1) {
            channelRemoveEpisodeRepository.remove(
                EPISODE_ID_2,
                ChannelMother.aChannel(
                    episodesCount = 1,
                    lastModificationDate = MODIFICATION_DATE
                )
            )
        }

        // keeps the previous count
        coVerify(exactly = 1) {
            channelRemoveEpisodeRepository.remove(
                EPISODE_ID_3,
                ChannelMother.aChannel(
                    episodesCount = 1,
                    lastModificationDate = MODIFICATION_DATE
                )
            )
        }

        assertEquals(1, summary!!.statistics.episodes)
    }

    private fun thenRemoveAllEpisodesExceptOne() {
        coVerify(exactly = 1) {
            channelRemoveEpisodeRepository.remove(
                EPISODE_ID_1,
                ChannelMother.aChannel(
                    episodesCount = 2,
                    lastModificationDate = MODIFICATION_DATE
                )
            )
        }

        coVerify(exactly = 0) {
            channelRemoveEpisodeRepository.remove(
                EPISODE_ID_2,
                any()
            )
        }

        // keeps the previous count
        coVerify(exactly = 1) {
            channelRemoveEpisodeRepository.remove(
                EPISODE_ID_3,
                ChannelMother.aChannel(
                    episodesCount = 1,
                    lastModificationDate = MODIFICATION_DATE
                )
            )
        }

        assertEquals(1, summary!!.statistics.episodes)
    }

    private fun thenVerifyChannelScoreCalculation() {
        coVerify(exactly = 1) {
            qualityService.calculateChannelScore(
                channelId = CHANNEL_ID,
                episodes = listOf(EPISODE_1, EPISODE_2, EPISODE_3)
            )
        }
    }

    private inline fun <reified T : RuntimeException> thenThrowsException() {
        Assertions.assertThat(thrownException)
            .isNotNull
            .isInstanceOf(T::class.java)

        coVerify(exactly = 0) { channelRemoveEpisodeRepository.remove(any(), any()) }
    }

    private companion object {
        const val CHANNEL_ID = ChannelMother.ID
        const val EPISODE_ID_1 = "episode_1"
        const val EPISODE_ID_2 = "episode_2"
        const val EPISODE_ID_3 = "episode_3"

        val EPISODE_1 = EpisodeMother.buildEpisode(id = EPISODE_ID_1, channelId = CHANNEL_ID, ownerId = PLAYER_ID)
        val EPISODE_2 = EpisodeMother.buildEpisode(id = EPISODE_ID_2, channelId = CHANNEL_ID, ownerId = PLAYER_ID)
        val EPISODE_3 = EpisodeMother.buildEpisode(id = EPISODE_ID_3, channelId = CHANNEL_ID, ownerId = PLAYER_ID)
    }
}
