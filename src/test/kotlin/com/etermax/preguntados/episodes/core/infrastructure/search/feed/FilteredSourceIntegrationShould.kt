package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.filter.Filter
import com.etermax.preguntados.episodes.core.infrastructure.search.filter.DuplicateFilter
import com.etermax.preguntados.episodes.core.infrastructure.search.filter.InMemoryFilterDataRepository
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime.now
import kotlin.test.assertTrue

class FilteredSourceIntegrationShould :
    AbstractOpenSearchSourceIntegrationShould(indexDescriptor = "opensearch/mapping/episodes-index-mapping-v1.0.json") {

    private lateinit var filter: Filter
    private lateinit var source: Source<String>

    @BeforeEach
    fun setUp() {
        filter = DuplicateFilter("test", InMemoryFilterDataRepository())
        source = FilteredSource(RecentEpisodeSource(osClient, indexName), filter)
    }

    @Test
    fun `retrieve an specified quantity of items using backfill for filtered items`() = runTest {
        (1..10).forEach { i ->
            givenAnEpisode(
                id = i.toString(),
                startDate = now().minusHours(i.toLong())
            )
        }

        // Filter out episode 1
        filter.apply("1")

        val results = whenRetrieve(limit = 5)

        thenSameElements(listOf("2", "3", "4", "5", "6"), results.items)
    }

    @Test
    fun `retrieve an specified quantity of items using backfill for filtered items and respecting offset`() = runTest {
        (1..15).forEach { i ->
            givenAnEpisode(
                id = i.toString(),
                startDate = now().minusHours(i.toLong())
            )
        }

        // Filter out episodes
        filter.apply("1")
        filter.apply("6")

        val result = whenRetrieve(offset = 5, limit = 5)

        thenSameElements(listOf("7", "8", "9", "10", "11"), result.items)
    }

    @Test
    fun `retrieve less quantity of items if source is exhausted`() = runTest {
        (1..10).forEach { i ->
            givenAnEpisode(
                id = i.toString(),
                startDate = now().minusHours(i.toLong())
            )
        }

        // Filter out episodes
        filter.apply("1")
        filter.apply("6")

        val result = whenRetrieve(offset = 5, limit = 5)

        thenSameElements(listOf("7", "8", "9", "10"), result.items)
        assertTrue((result.fetchCursor as FilteredFetchCursor).exhausted)
    }

    @Test
    fun `answer successfully when trying to retrieve ZERO items`() = runTest {
        givenAnEpisode(id = "1")

        val result = whenRetrieve(limit = 0)

        thenSameElements(listOf(), result.items)
    }

    @Test
    fun `handle case when all items are filtered`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(
                id = i.toString(),
                startDate = now().minusHours(i.toLong())
            )
        }

        // Filter out all episodes
        (1..5).forEach { i -> filter.apply(i.toString()) }

        val result = whenRetrieve(limit = 5)

        thenSameElements(listOf(), result.items)
    }

    @Test
    fun `handle large offset beyond available data`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(
                id = i.toString(),
                startDate = now().minusHours(i.toLong())
            )
        }

        val result = whenRetrieve(offset = 10, limit = 5)

        thenSameElements(listOf(), result.items)
    }

    @Test
    fun `handle multiple batches with filtered items`() = runTest {
        // Create 20 episodes
        (1..20).forEach { i ->
            givenAnEpisode(
                id = i.toString(),
                startDate = now().minusHours(i.toLong())
            )
        }

        // Filter out every third episode
        (1..20 step 3).forEach { i -> filter.apply(i.toString()) }

        val result = whenRetrieve(limit = 10)

        // Should return 10 non-filtered episodes (2,3,5,6,8,9,11,12,14,15)
        thenSameElements(listOf("2", "3", "5", "6", "8", "9", "11", "12", "14", "15"), result.items)
    }

    private suspend fun whenRetrieve(
        offset: Int = 0,
        limit: Int = 10,
        country: Country? = null,
        language: Language? = null,
        fetchCursor: FetchCursor? = null
    ): SourceResponse<String> {
        return source.fetch(
            SourceRequest(
                OffsetLimit(
                    offset = offset,
                    limit = limit
                ),
                userId = 1,
                country = country,
                language = language,
                fetchCursor = fetchCursor
            )
        )
    }
}
