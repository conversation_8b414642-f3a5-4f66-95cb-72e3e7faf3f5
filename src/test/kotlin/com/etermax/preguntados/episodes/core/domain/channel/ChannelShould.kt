package com.etermax.preguntados.episodes.core.domain.channel

import com.etermax.preguntados.episodes.core.ChannelMother
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ChannelShould {

    @Test
    fun `remove episodes - return 0 episodes count`() {
        val channel = ChannelMother.aChannel(episodesCount = 1)
        val updated = channel.removeEpisodes(1)
        assertThat(updated.episodesCount).isEqualTo(0)
    }

    @Test
    fun `remove episodes - return 8 episodes count`() {
        val channel = ChannelMother.aChannel(episodesCount = 10)
        val updated = channel.removeEpisodes(2)
        assertThat(updated.episodesCount).isEqualTo(8)
    }

    @Test
    fun `remove episodes - default to 0 when is negative`() {
        val channel = ChannelMother.aChannel(episodesCount = 0)
        val updated = channel.removeEpisodes(10)
        assertThat(updated.episodesCount).isEqualTo(0)
    }
}
