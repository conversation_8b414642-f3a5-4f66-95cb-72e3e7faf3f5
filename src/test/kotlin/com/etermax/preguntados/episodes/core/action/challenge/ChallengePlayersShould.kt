package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.action.challenge.ChallengePlayers.ActionData
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayerRepository
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayersSummary
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.AssertionsForInterfaceTypes.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ChallengePlayersShould {

    private lateinit var action: ChallengePlayers
    private lateinit var response: ChallengePlayersSummary
    private var error: Throwable? = null

    private lateinit var challengePlayerRepository: ChallengePlayerRepository
    private lateinit var summaryService: SummaryService
    private lateinit var profileService: ProfileService

    @BeforeEach
    fun setUp() {
        challengePlayerRepository = mockk()
        coEvery { challengePlayerRepository.save(any<List<ChallengePlayer>>()) } returns Unit

        profileService = mockk()
        coEvery { profileService.find(PLAYER_ID) } returns ProfileMother.aProfile(PLAYER_ID)
        coEvery { profileService.find(PLAYER_ID_2) } returns ProfileMother.aProfile(PLAYER_ID_2)
        coEvery { profileService.find(PLAYER_ID_3) } returns ProfileMother.aProfile(PLAYER_ID_3)

        summaryService = SummaryService(profileService, mockk())
    }

    @Test
    fun `persist new players`() = runTest {
        val players = setOf(PLAYER_ID_2, PLAYER_ID_3)
        coEvery { challengePlayerRepository.findAllByChallengeId(CHALLENGE_ID) } returns emptyList()

        whenChallengePlayers(PLAYER_ID, CHALLENGE_ID, players)

        thenPlayersArePersisted(players)
    }

    @Test
    fun `return all players`() = runTest {
        val players = setOf(PLAYER_ID_2)
        val allPlayers = setOf(PLAYER_ID) + players

        coEvery { challengePlayerRepository.findAllByChallengeId(CHALLENGE_ID) } returns allPlayers.map {
            ChallengePlayer(
                challengeId = CHALLENGE_ID,
                playerId = it,
                status = ChallengePlayer.Status.PENDING
            )
        }

        whenChallengePlayers(PLAYER_ID, CHALLENGE_ID, players)

        thenPlayersAreReturned(allPlayers)
    }

    suspend fun whenChallengePlayers(playerId: Long, challengeId: String, playerIds: Set<Long>) {
        val request = ActionData(playerId, challengeId, playerIds)

        action = ChallengePlayers(challengePlayerRepository, summaryService)

        error = kotlin.runCatching {
            response = action(request)
        }.exceptionOrNull()
    }

    private fun thenPlayersArePersisted(playerIds: Set<Long>) {
        assertThat(error).isNull()

        coVerify(exactly = 1) {
            challengePlayerRepository.save(
                withArg<List<ChallengePlayer>> { players ->
                    assertThat(players).hasSize(playerIds.size)
                    players.forEach {
                        assertThat(playerIds).contains(it.playerId)
                    }
                }
            )
        }
    }

    private fun thenPlayersAreReturned(playerIds: Set<Long>) {
        assertThat(error).isNull()

        assertThat(response.players).hasSize(playerIds.size)
        response.players.forEach {
            assertThat(playerIds).contains(it.profile.playerId)
        }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val PLAYER_ID_2 = 667L
        const val PLAYER_ID_3 = 668L
        const val CHALLENGE_ID = "CHA-123"
    }
}
