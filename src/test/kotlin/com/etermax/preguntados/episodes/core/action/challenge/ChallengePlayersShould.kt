package com.etermax.preguntados.episodes.core.action.challenge

import com.etermax.preguntados.episodes.core.action.challenge.ChallengePlayers.ActionData
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayer
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengePlayersSummary
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.AssertionsForInterfaceTypes.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime.now

class ChallengePlayersShould {

    private lateinit var action: ChallengePlayers
    private lateinit var response: ChallengePlayersSummary
    private var error: Throwable? = null

    private lateinit var challengeService: ChallengeService
    private lateinit var summaryService: SummaryService
    private lateinit var profileService: ProfileService

    @BeforeEach
    fun setUp() {
        challengeService = mockk()

        profileService = mockk()
        coEvery { profileService.find(PLAYER_ID) } returns ProfileMother.aProfile(PLAYER_ID)
        coEvery { profileService.find(PLAYER_ID_2) } returns ProfileMother.aProfile(PLAYER_ID_2)
        coEvery { profileService.find(PLAYER_ID_3) } returns ProfileMother.aProfile(PLAYER_ID_3)

        summaryService = SummaryService(profileService, mockk())
    }

    @Test
    fun `persist new players`() = runTest {
        val players = setOf(PLAYER_ID_2, PLAYER_ID_3)

        coEvery { challengeService.getChallengeContext(any(), any()) } returns mockk(relaxed = true)
        coEvery { challengeService.ensurePlayersInitialized(players, any()) } returns emptyList()

        whenChallengePlayers(PLAYER_ID, CHALLENGE_ID, players)

        thenPlayersArePersisted(players)
    }

    @Test
    fun `return all players`() = runTest {
        val players = setOf(PLAYER_ID_2)
        val allPlayers = setOf(PLAYER_ID) + players

        coEvery { challengeService.getChallengeContext(any(), any()) } returns mockk(relaxed = true)
        coEvery { challengeService.ensurePlayersInitialized(players, any()) } returns allPlayers.map {
            ChallengePlayer(
                it,
                CHALLENGE_ID,
                ChallengePlayer.Status.PLAYING,
                now()
            )
        }

        whenChallengePlayers(PLAYER_ID, CHALLENGE_ID, players)

        thenPlayersAreReturned(allPlayers)
    }

    suspend fun whenChallengePlayers(playerId: Long, challengeId: String, playerIds: Set<Long>) {
        val request = ActionData(playerId, challengeId, playerIds)

        action = ChallengePlayers(challengeService, summaryService)

        error = kotlin.runCatching {
            response = action(request)
        }.exceptionOrNull()
    }

    private fun thenPlayersArePersisted(playerIds: Set<Long>) {
        assertThat(error).isNull()

        coVerify(exactly = 1) {
            challengeService.ensurePlayersInitialized(
                withArg<Set<Long>> { players ->
                    assertThat(players).hasSize(playerIds.size)
                    players.forEach {
                        assertThat(playerIds).contains(it)
                    }
                },
                any()
            )
        }
    }

    private fun thenPlayersAreReturned(playerIds: Set<Long>) {
        assertThat(error).isNull()

        assertThat(response.players).hasSize(playerIds.size)
        response.players.forEach {
            assertThat(playerIds).contains(it.profile.playerId)
        }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val PLAYER_ID_2 = 667L
        const val PLAYER_ID_3 = 668L
        const val CHALLENGE_ID = "CHA-123"
    }
}
