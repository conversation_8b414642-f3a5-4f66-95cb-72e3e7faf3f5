package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.domain.profile.PlayerFriendsService
import com.etermax.preguntados.episodes.core.infrastructure.search.FriendsEpisodeSearch
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FindFriendsPlayedEpisodeShould {

    private lateinit var playerFriendsService: PlayerFriendsService
    private lateinit var friendsEpisodeSearch: FriendsEpisodeSearch
    private lateinit var findFriendsPlayedEpisode: FindFriendsPlayedEpisode
    private lateinit var result: List<Long>

    @BeforeEach
    fun setUp() {
        playerFriendsService = mockk()
        friendsEpisodeSearch = mockk()
        findFriendsPlayedEpisode = FindFriendsPlayedEpisode(playerFriendsService, friendsEpisodeSearch)
    }

    @Test
    fun `return empty list when player has no friends`() = runTest {
        givenPlayerHasNoFriends()

        whenFindFriendsPlayedEpisode()

        thenNoFriendsReturned()
    }

    @Test
    fun `return empty list when no friends played the episode`() = runTest {
        givenPlayerHasFriends()
        givenNoFriendsPlayedEpisode()

        whenFindFriendsPlayedEpisode()

        thenNoFriendsReturned()
    }

    @Test
    fun `return list of friends who played the episode`() = runTest {
        givenPlayerHasFriends()
        givenFriendsPlayedEpisode()

        whenFindFriendsPlayedEpisode()

        thenFriendsPlayedEpisodeReturned()
    }

    private fun givenPlayerHasNoFriends() {
        coEvery { playerFriendsService.findFollowedIds(PLAYER_ID) } returns emptyList()
    }

    private fun givenPlayerHasFriends() {
        coEvery { playerFriendsService.findFollowedIds(PLAYER_ID) } returns FRIENDS
    }

    private fun givenNoFriendsPlayedEpisode() {
        coEvery { friendsEpisodeSearch.search(FRIENDS, EPISODE_ID) } returns emptyList()
    }

    private fun givenFriendsPlayedEpisode() {
        coEvery { friendsEpisodeSearch.search(FRIENDS, EPISODE_ID) } returns FRIENDS_PLAYED_EPISODE
    }

    private suspend fun whenFindFriendsPlayedEpisode() {
        val actionData = FindFriendsPlayedEpisode.ActionData(PLAYER_ID, EPISODE_ID)
        result = findFriendsPlayedEpisode(actionData)
    }

    private fun thenNoFriendsReturned() {
        assertThat(result).isEmpty()
    }

    private fun thenFriendsPlayedEpisodeReturned() {
        assertThat(result).containsExactlyInAnyOrderElementsOf(FRIENDS_PLAYED_EPISODE)
    }

    private companion object {
        const val PLAYER_ID = 123L
        const val EPISODE_ID = "episode-123"
        val FRIENDS = listOf(456L, 789L, 101112L)
        val FRIENDS_PLAYED_EPISODE = listOf(456L, 789L)
    }
}
