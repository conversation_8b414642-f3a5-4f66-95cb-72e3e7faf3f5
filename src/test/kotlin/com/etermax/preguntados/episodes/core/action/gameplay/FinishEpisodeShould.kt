package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.analytics.actions.TrackEpisodeAnalytics
import com.etermax.preguntados.analytics.service.FinishedEvent
import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.action.gameplay.FinishEpisode.ActionData
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.episode.finish.FinishEpisodeDetails
import com.etermax.preguntados.episodes.core.domain.episode.ranking.*
import com.etermax.preguntados.episodes.core.domain.episode.rate.RateRepository
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother.aProfile
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class FinishEpisodeShould {

    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var profileService: ProfileService
    private lateinit var rateRepository: RateRepository
    private lateinit var rankingService: RankingService
    private lateinit var trackAnalytics: TrackEpisodeAnalytics
    private lateinit var finishEpisode: FinishEpisode

    private var error: Throwable? = null
    private lateinit var result: FinishEpisodeDetails

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk()
        profileService = mockk(relaxed = true)
        rateRepository = mockk(relaxed = true)
        rankingService = mockk(relaxed = true)

        trackAnalytics = mockk()
        coEvery { trackAnalytics(any<FinishedEvent>()) } returns Unit

        finishEpisode = FinishEpisode(
            episodeRepository = episodeRepository,
            profileService = profileService,
            rateRepository = rateRepository,
            rankingService = rankingService,
            trackMetric = trackAnalytics
        )
    }

    @Test
    fun `throw exception if episode does not exist`() = runTest {
        givenNoEpisode()

        whenFinish()

        thenEpisodeNotFoundExceptionIsThrown()
    }

    @Test
    fun `find episodes`() = runTest {
        givenAnEpisode()
        givenAProfile()

        whenFinish()

        thenEpisodesAreFound()
    }

    @ParameterizedTest(name = "player rate {0}")
    @EnumSource(value = Rate.Type::class, mode = EnumSource.Mode.INCLUDE)
    fun `find player rate`(rateType: Rate.Type) = runTest {
        givenAnEpisode()
        givenPlayerRate(rateType)

        whenFinish()

        thenPlayerRateIs(rateType)
    }

    @Test
    fun `find ranking`() = runTest {
        givenAnEpisode()
        givenARanking()

        whenFinish()

        thenRankingIs(DELIVERY_RANKING)
    }

    @Test
    fun `find ranking with friends`() = runTest {
        givenAnEpisode()
        givenARankingWithFriends()

        whenFinish()

        thenRankingWithFriendsIs(DELIVERY_RANKING)
    }

    @Test
    fun `track analytics`() = runTest {
        givenAnEpisode()

        whenFinish()

        thenAnalyticsAreRecorded()
    }

    private fun givenNoEpisode() {
        coEvery { episodeRepository.findById(EPISODE_ID) } returns null
    }

    private fun givenAnEpisode() {
        coEvery { episodeRepository.findById(EPISODE_ID) } returns EPISODE
    }

    private fun givenAProfile() {
        coEvery { profileService.find(EPISODE.ownerId) } returns PROFILE
    }

    private fun givenPlayerRate(rateType: Rate.Type) {
        coEvery { rateRepository.findBy(PLAYER_ID, EPISODE_ID) } returns rateType
    }

    private fun givenARanking() {
        coEvery { rankingService.findRanking(PLAYER_ID, DOMAIN) } returns DELIVERY_RANKING
    }

    private fun givenARankingWithFriends() {
        coEvery { rankingService.findRankingWithFriends(PLAYER_ID, DOMAIN) } returns DELIVERY_RANKING
    }

    private suspend fun whenFinish() {
        error = kotlin.runCatching {
            val actionData = ActionData(PLAYER_ID, EPISODE_ID)
            result = finishEpisode(actionData)
        }.exceptionOrNull()
    }

    private fun thenEpisodeNotFoundExceptionIsThrown() {
        assertThat(error).isExactlyInstanceOf(EpisodeNotFoundException::class.java)
    }

    private fun thenEpisodesAreFound() {
        assertThat(result.episodeSummary).isEqualTo(EpisodeSummary.from(EPISODE, PROFILE))
    }

    private fun thenPlayerRateIs(rateType: Rate.Type) {
        assertThat(result.rate).isEqualTo(rateType)
    }

    private fun thenRankingIs(ranking: DeliveryRanking) {
        assertThat(result.ranking).isEqualTo(ranking)
    }

    private fun thenRankingWithFriendsIs(ranking: DeliveryRanking) {
        assertThat(result.rankingWithFriends).isEqualTo(ranking)
    }

    private fun thenAnalyticsAreRecorded() {
        coVerify {
            trackAnalytics(FinishedEvent(PLAYER_ID, EPISODE_ID, EPISODE.ownerId))
        }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "ABC-123"
        val DOMAIN = Domain(EPISODE_ID)
        val EPISODE = buildEpisode(id = EPISODE_ID)
        val PROFILE = aProfile(playerId = EPISODE.ownerId)
        val RANKING_ENTRY = RankingEntry(10, 200)
        val DELIVERY_RANKING = DeliveryRanking(listOf(DeliveryRankedPlayer(PROFILE, RANKING_ENTRY)), RANKING_ENTRY)
    }
}
