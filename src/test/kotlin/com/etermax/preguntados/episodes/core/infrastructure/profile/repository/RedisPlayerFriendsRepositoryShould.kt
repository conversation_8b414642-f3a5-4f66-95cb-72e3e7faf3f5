package com.etermax.preguntados.episodes.core.infrastructure.profile.repository

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.core.domain.profile.repository.PlayerFriendsRepository
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import kotlinx.coroutines.future.await
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration

@ExtendWith(RedisTestServer::class)
class RedisPlayerFriendsRepositoryShould {
    private val redisAsync = EmbeddedRedisUtils.buildClient()

    private val repository: PlayerFriendsRepository = RedisPlayerFriendsRepository(redisAsync, Duration.parse(TEN_SECONDS))

    @Test
    fun `save and find friends`() = runTest {
        whenSaveFriends()

        thenFriendsAreSaved()
    }

    @Test
    fun `return null when friends do not exist`() = runTest {
        val result = repository.find(PLAYER_ID)

        assertThat(result).isNull()
    }

    @Test
    fun `update friends when saving with same player id`() = runTest {
        givenExistingFriends()

        whenSaveNewFriends()

        thenNewFriendsAreSaved()
    }

    @Test
    fun `set ttl when saving friends`() = runTest {
        whenSaveFriends()

        thenTtlIsSet()
    }

    @Test
    fun `save empty friends list`() = runTest {
        whenSaveEmptyFriendsList()

        thenEmptyFriendsListIsSaved()
    }

    @Test
    fun `different players have different friends`() = runTest {
        repository.save(PLAYER_ID, FRIENDS)
        repository.save(ANOTHER_PLAYER_ID, ANOTHER_FRIENDS)

        val firstPlayerFriends = repository.find(PLAYER_ID)
        val secondPlayerFriends = repository.find(ANOTHER_PLAYER_ID)

        assertThat(firstPlayerFriends).isEqualTo(FRIENDS)
        assertThat(secondPlayerFriends).isEqualTo(ANOTHER_FRIENDS)
    }

    private suspend fun givenExistingFriends() {
        repository.save(PLAYER_ID, FRIENDS)
    }

    private suspend fun whenSaveFriends() {
        repository.save(PLAYER_ID, FRIENDS)
    }

    private suspend fun whenSaveNewFriends() {
        repository.save(PLAYER_ID, NEW_FRIENDS)
    }

    private suspend fun whenSaveEmptyFriendsList() {
        repository.save(PLAYER_ID, emptyList())
    }

    private suspend fun thenFriendsAreSaved() {
        val savedFriends = repository.find(PLAYER_ID)
        assertThat(savedFriends).isEqualTo(FRIENDS)
    }

    private suspend fun thenNewFriendsAreSaved() {
        val savedFriends = repository.find(PLAYER_ID)
        assertThat(savedFriends).isEqualTo(NEW_FRIENDS)
    }

    private suspend fun thenEmptyFriendsListIsSaved() {
        val savedFriends = repository.find(PLAYER_ID)
        assertThat(savedFriends).isEmpty()
    }

    private suspend fun thenTtlIsSet() {
        val ttl = redisAsync.ttl("$KEY:$PLAYER_ID").await()
        assertThat(ttl).isGreaterThan(1)
    }

    private companion object {
        const val TEN_SECONDS = "PT10S"
        const val KEY = "pr:e:pf"
        const val PLAYER_ID = 123L
        const val ANOTHER_PLAYER_ID = 456L
        val FRIENDS = listOf(789L, 101112L)
        val NEW_FRIENDS = listOf(131415L, 161718L)
        val ANOTHER_FRIENDS = listOf(192021L, 222324L)
    }
}
