package com.etermax.preguntados.episodes.core.infrastructure.search.repository

import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.github.resilience4j.circuitbreaker.CircuitBreaker
import io.github.resilience4j.retry.Retry
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class HttpUserBasedRepositoryShould {

    private lateinit var httpClient: HttpClient

    private var error: Throwable? = null
    private var result: List<String>? = null

    @Test
    fun `empty episodes when fails`() = runTest {
        givenAFailingClient()

        whenValidate()

        thenValidateNothing()
    }

    @Test
    fun `empty episodes when code is not OK`() = runTest {
        givenAClient(HttpStatusCode.NoContent)

        whenValidate()

        thenValidateNothing()
    }

    @Test
    fun `validate episodes`() = runTest {
        givenAClient(HttpStatusCode.OK)

        whenValidate()

        assertThat(result).isEqualTo(EPISODES_RESPONSE)
    }

    private fun givenAFailingClient() {
        httpClient = HttpClient(
            MockEngine {
                respondError(HttpStatusCode.BadRequest, "this is the body of the error response")
            }
        ) {
            install(ContentNegotiation) {
                json(Json { this.ignoreUnknownKeys = true })
            }
            install(HttpTimeout) {
                requestTimeoutMillis = 3000
            }
        }
    }

    private fun givenAClient(statusCode: HttpStatusCode) {
        httpClient = HttpClient(
            MockEngine {
                assert(it.url.encodedPathAndQuery == URL) { "Unexpected URL: $URL" }

                val requestBody = it.body.toByteArray().toString(Charsets.UTF_8)
                assert(requestBody == REQUEST) { "Unexpected body: $requestBody" }

                request {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    expectSuccess = true
                }

                respond(
                    RESPONSE,
                    statusCode,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        ) {
            install(ContentNegotiation) {
                json(Json { this.ignoreUnknownKeys = true })
            }
            install(HttpTimeout) {
                requestTimeoutMillis = 3000
            }
        }
    }

    private suspend fun whenValidate() {
        val service = HttpUserBasedRepository(httpClient, RESILIENCE_BUNDLE)
        error = kotlin.runCatching {
            result = service.find(EPISODES, 0, 10, Language.ES)
        }.exceptionOrNull()
    }

    private fun thenValidateNothing() {
        assertThat(result).isEmpty()
    }

    private companion object {
        const val URL = "/ds-api-episode-recommendations/api/episode-recommendations"
        const val EPISODE_ID_1 = "4e385400-09f8-497b-9ffc-0ad11e308f4b"
        const val EPISODE_ID_2 = "e4b70f64-72d9-4afa-90ff-6a3f7e0fbc9c"
        const val EPISODE_ID_3 = "0e700043-9c89-42c3-92ab-2550a6720b5d"
        val EPISODES = listOf(EPISODE_ID_1, EPISODE_ID_2, EPISODE_ID_3)
        const val EPISODE_ID_RESULT_1 = "47702760-7ed4-4799-8c2c-93c30832d9d7"
        const val EPISODE_ID_RESULT_2 = "70d113bc-867c-4c1a-b83f-8ba3fdcd8f58"
        const val EPISODE_ID_RESULT_3 = "7a1c2a6a-1693-49ef-acaa-9b176aab75d8"
        val RESILIENCE_BUNDLE = EndpointResilienceBundle(CircuitBreaker.ofDefaults("p"), Retry.ofDefaults("s"))
        val REQUEST = """
            {
                "episode_ids": ["$EPISODE_ID_1", "$EPISODE_ID_2", "$EPISODE_ID_3"],
                "recos_weight": 0.7,
                "likes_weight": 0.25,
                "plays_weight": 0.05,
                "language": "ES",
                "offset": 0,
                "per_page": 10
            }
        """.trimIndent().replace(Regex("\\s+"), "")
        const val RESPONSE = """
            [
                [
                    "$EPISODE_ID_RESULT_1",
                    0.7000272
                ],
                [
                    "$EPISODE_ID_RESULT_2",
                    0.6556287844390869
                ],
                [
                    "$EPISODE_ID_RESULT_3",
                    0.6544771772705077
                ]
            ]
        """
        val EPISODES_RESPONSE = listOf(EPISODE_ID_RESULT_1, EPISODE_ID_RESULT_2, EPISODE_ID_RESULT_3)
    }
}
