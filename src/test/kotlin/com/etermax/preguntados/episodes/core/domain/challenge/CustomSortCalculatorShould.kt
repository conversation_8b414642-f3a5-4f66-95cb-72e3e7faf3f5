package com.etermax.preguntados.episodes.core.domain.challenge

import com.etermax.preguntados.episodes.core.ChallengeMother
import com.etermax.preguntados.episodes.core.ChallengeMother.EPISODE_ID
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingEntry
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime.now

class CustomSortCalculatorShould {
    private lateinit var calculator: CustomSortValueCalculator

    @Test
    fun `return value for won challenge`() = runTest {
        calculator = CustomSortValueCalculator()

        val games = listOf(
            givenAChallengeInProgressWithPlayerInProgress(PLAYER_ID),
            givenAChallengeFinishedWithPlayerInTop3(PLAYER_ID),
            givenAChallengeFinishedWithPlayerNotInTop3(PLAYER_ID),
            givenAChallengeUpcoming(PLAYER_ID),
            givenAChallengeInProgressWithPlayerFinished(PLAYER_ID)
        )

        val values = games.map {
            calculator.calculateFor(ChallengeSortableGame(it), PLAYER_ID)
        }

        assertThat(values).isEqualTo(values.sorted())
        assertThat(values).isInStrictlyAscendingOrder()
    }

    fun givenAChallengeInProgressWithPlayerInProgress(forPlayerId: Long): ChallengeDetails {
        val challenge = ChallengeMother.aChallenge(startDate = now().minusDays(1), endDate = now().plusDays(1))

        return ChallengeDetails(
            playerId = 123L,
            challenge = challenge,
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID),
            players = listOf(
                ChallengePlayer(challenge.ownerId, challenge.id, ChallengePlayer.Status.PLAYING, challenge.expireDate),
                ChallengePlayer(forPlayerId, challenge.id, ChallengePlayer.Status.PLAYING, challenge.expireDate)
            ),
            ranking = DeliveryRanking(
                emptyList(),
                RankingEntry(1, 100)
            )
        )
    }

    private fun givenAChallengeFinishedWithPlayerInTop3(forPlayerId: Long): ChallengeDetails {
        val challenge = ChallengeMother.aChallenge(startDate = now().minusDays(1), endDate = now().minusMinutes(1))

        return ChallengeDetails(
            playerId = 123L,
            challenge = challenge,
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID),
            players = listOf(
                ChallengePlayer(challenge.ownerId, challenge.id, ChallengePlayer.Status.FINISHED, challenge.expireDate),
                ChallengePlayer(forPlayerId, challenge.id, ChallengePlayer.Status.FINISHED, challenge.expireDate)
            ),
            ranking = DeliveryRanking(
                emptyList(),
                RankingEntry(1, 100)
            )
        )
    }

    private fun givenAChallengeFinishedWithPlayerNotInTop3(forPlayerId: Long): ChallengeDetails {
        val challenge = ChallengeMother.aChallenge(startDate = now().minusDays(1), endDate = now().minusMinutes(1))

        return ChallengeDetails(
            playerId = 123L,
            challenge = challenge,
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID),
            players = listOf(
                ChallengePlayer(challenge.ownerId, challenge.id, ChallengePlayer.Status.FINISHED, challenge.expireDate),
                ChallengePlayer(forPlayerId, challenge.id, ChallengePlayer.Status.FINISHED, challenge.expireDate)
            ),
            ranking = DeliveryRanking(
                emptyList(),
                RankingEntry(1, 100)
            )
        )
    }

    private fun givenAChallengeUpcoming(forPlayerId: Long): ChallengeDetails {
        val challenge = ChallengeMother.aChallenge(startDate = now().plusDays(1), endDate = now().plusDays(2))

        return ChallengeDetails(
            playerId = 123L,
            challenge = challenge,
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID),
            players = listOf(
                ChallengePlayer(challenge.ownerId, challenge.id, ChallengePlayer.Status.PENDING, challenge.expireDate),
                ChallengePlayer(forPlayerId, challenge.id, ChallengePlayer.Status.PENDING, challenge.expireDate)
            ),
            ranking = DeliveryRanking.EMPTY
        )
    }

    private fun givenAChallengeInProgressWithPlayerFinished(forPlayerId: Long): ChallengeDetails {
        val challenge = ChallengeMother.aChallenge(startDate = now().minusDays(1), endDate = now().plusDays(1))

        return ChallengeDetails(
            playerId = 123L,
            challenge = challenge,
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID),
            players = listOf(
                ChallengePlayer(challenge.ownerId, challenge.id, ChallengePlayer.Status.PLAYING, challenge.expireDate),
                ChallengePlayer(forPlayerId, challenge.id, ChallengePlayer.Status.FINISHED, challenge.expireDate)
            ),
            ranking = DeliveryRanking(
                emptyList(),
                RankingEntry(1, 100)
            )
        )
    }

    companion object {
        const val PLAYER_ID = 666L
    }
}