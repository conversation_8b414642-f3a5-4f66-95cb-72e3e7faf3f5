package com.etermax.preguntados.episodes.core.domain.challenge

import com.etermax.preguntados.episodes.core.ChallengeMother
import com.etermax.preguntados.episodes.core.ChallengeMother.EPISODE_ID
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingEntry

class CustomSortCalculatorShould {
    private lateinit var calculator: CustomSortValueCalculator

    @Test
    fun `return value for won challenge`() {
        calculator = CustomSortValueCalculator()

        val games = listOf(
            givenAChallengeInProgressWithPlayerInProgress(),
            givenAChallengeFinishedWithPlayerInTop3(),
            givenAChallengeFinishedWithPlayerNotInTop3(),
            givenAChallengeUpcoming(),
            givenAChallengeInProgressWithPlayerFinished()
        )


    }

    fun givenAChallengeInProgressWithPlayerInProgress(): ChallengeDetails {
        val challenge = aChalleng
        e()

        return ChallengeDetails(
            playerId = 123L,
            challenge = challenge,
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID),
            players = listOf(
                ChallengePlayer(challenge.ownerId, challenge.id, ChallengePlayer.Status.PLAYING, challenge.expireDate),
                ChallengePlayer(forPlayerId, challenge.id, ChallengePlayer.Status.PLAYING, challenge.expireDate)
            ),
            ranking = DeliveryRanking(
                emptyList(),
                RankingEntry(1, 100)
            )
        )
    }

    private fun givenAChallengeFinishedWithPlayerInTop3(): ChallengeDetails {
        TODO("Not yet implemented")
    }

    private fun givenAChallengeFinishedWithPlayerNotInTop3(): ChallengeDetails {
        TODO("Not yet implemented")
    }

    private fun givenAChallengeUpcoming(): ChallengeDetails {
        TODO("Not yet implemented")
    }

    private fun givenAChallengeInProgressWithPlayerFinished() {

    }

    companion object {
        const val PLAYER_ID = 666L
    }
}