package com.etermax.preguntados.episodes.core.action.channel.episodes

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.MODIFICATION_DATE
import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.UNPUBLISHED_EPISODES
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.PositiveNumber
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.ChannelEpisode
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.repository.AddEpisodesToChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelEpisodesService
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.exception.ChannelCannotAddEmptyEpisodesException
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import com.etermax.preguntados.episodes.core.domain.order.OrderItemCalculator
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.quality.QualityService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.doubles.channel.repository.InMemoryChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.temporal.ChronoUnit
import kotlin.test.assertNull

class AddEpisodesToChannelShould {
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var channelRepository: ChannelRepository
    private lateinit var addEpisodesToChannelRepository: AddEpisodesToChannelRepository
    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService
    private lateinit var orderItemCalculator: OrderItemCalculator
    private lateinit var qualityService: QualityService
    private lateinit var clock: Clock
    private lateinit var action: AddEpisodesToChannel

    private var summary: ChannelSummary? = null
    private var thrownException: RuntimeException? = null

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk()
        channelRepository = InMemoryChannelRepository()
        addEpisodesToChannelRepository = mockk(relaxUnitFun = true)
        profileService = mockk()
        unpublishedEpisodesService = mockk()
        orderItemCalculator = mockk()
        qualityService = mockk()
        clock = mockk(relaxUnitFun = true)

        val summaryService = SummaryService(profileService, unpublishedEpisodesService)

        action = AddEpisodesToChannel(
            episodeRepository,
            channelRepository,
            ChannelEpisodesService(
                episodeRepository,
                channelRepository,
                orderItemCalculator,
                addEpisodesToChannelRepository,
                clock
            ),
            summaryService,
            qualityService
        )

        summary = null
        thrownException = null

        givenAModificationDate()
        givenAProfile()
        givenAnOrder()
        givenEpisodes()
        coEvery { unpublishedEpisodesService.count(CHANNEL_ID) } returns UNPUBLISHED_EPISODES
    }

    @Test
    fun `fail when episodes is empty`() = runTest {
        whenAdd(episodesIds = setOf())
        thenThrowsException<ChannelCannotAddEmptyEpisodesException>()
    }

    @Test
    fun `fail when episodes not found`() = runTest {
        givenAnExistingChannel()
        givenNotExistingEpisodes()
        whenAdd()
        thenThrowsException<EpisodeNotFoundException>()
    }

    @Test
    fun `fail when channel does not exist`() = runTest {
        whenAdd()
        thenThrowsException<ChannelNotFoundException>()
    }

    @Test
    fun `add episodes`() = runTest {
        givenAnExistingChannel()
        whenAdd()
        thenAddEpisodes()
    }

    @Test
    fun `add episodes without channelId`() = runTest {
        givenAnExistingChannel()
        whenAdd(FRENCH_EPISODES_IDS.plus(EPISODE_WITH_CHANNEL.id))
        thenAddEpisodes()
        thenEpisodesCounterIsUpdated()
    }

    @Test
    fun `add only existing episodes`() = runTest {
        givenAnExistingChannel()
        whenAdd(FRENCH_EPISODES_IDS.plus(NOT_EXISTING_EPISODE_ID))
        thenAddEpisodes()
        thenEpisodesCounterIsUpdated()
    }

    @Test
    fun `update episodes count`() = runTest {
        givenAnExistingChannel()
        whenAdd()
        thenEpisodesCounterIsUpdated()
    }

    @Test
    fun `calculate order for each episode`() = runTest {
        givenAnExistingChannel()
        whenAdd()
        verify(exactly = 1) { orderItemCalculator.calculateFor(PositiveNumber(0)) }
        verify(exactly = 1) { orderItemCalculator.calculateFor(PositiveNumber(1)) }
    }

    @Test
    fun `return updated episodes count in channel`() = runTest {
        givenAnExistingChannel()
        whenAdd()
        thenChannelIsReturned()
    }

    @Test
    fun `not update language when channel already has language`() = runTest {
        givenAnExistingChannel()
        whenAdd()
        thenPersistedLanguageIs()
    }

    @Test
    fun `update language when channel does not have language`() = runTest {
        givenAnExistingChannelWithoutLanguage()
        whenAdd(ENGLISH_EPISODES_IDS, language = EN_LANGUAGE)
        thenPersistedLanguageIs(language = EN_LANGUAGE)
    }

    @Test
    fun `return channel with already set language`() = runTest {
        givenAnExistingChannelWithoutLanguage()
        whenAdd()
        thenChannelIsReturned()
    }

    @Test
    fun `add episodes with correct fields for channel without language`() = runTest {
        givenAnExistingChannelWithoutLanguage()
        whenAdd(DIVERSE_EPISODES_IDS)
        thenChannelIsReturned()
    }

    @Test
    fun `add episodes with correct fields for channel with language`() = runTest {
        givenAnExistingChannel()
        whenAdd(DIVERSE_EPISODES_IDS)
        thenChannelIsReturned()
    }

    @Test
    fun `add episode with incorrect language to channel`() = runTest {
        givenAnExistingChannelWithoutLanguage()
        whenAdd(FRENCH_EPISODES_IDS, language = EN_LANGUAGE)
        thenChannelIsNotUpdated()
    }

    @Test
    fun `not add episodes on not owner channel`() = runTest {
        givenAnExistingChannelWithOwner(CHANNEL_OTHER_OWNER_ID)
        whenAdd()
        thenUserNotHasChannels()
        thenNotAddEpisodes()
    }

    @Test
    fun `not add episodes if is not owner of episodes`() = runTest {
        givenAnExistingChannel()
        whenAdd(EPISODE_ID_WITH_OTHER_OWNER)
        thenNotAddEpisodes()
    }

    @Test
    fun `should calculate channel score when adding episodes`() = runTest {
        givenAnExistingChannelWithoutLanguage()
        givenExistingEpisode(EPISODE_1)
        whenAdd(setOf(EPISODE_1.id))
        thenVerifyChannelScoreCalculation()
    }

    private fun givenExistingEpisode(episode: Episode) {
        coEvery { episodeRepository.findByIds(listOf(episode.id)) } returns listOf(episode)
    }

    private suspend fun givenAnExistingChannelWithOwner(ownerId: Long) {
        channelRepository.add(ChannelMother.aChannel(language = null, ownerId = ownerId))
    }

    private fun thenChannelIsNotUpdated() {
        val expected =
            ChannelMother.aChannelSummary(
                lastModificationDate = ChannelMother.CREATION_DATE,
                episodesCount = 0,
                language = null
            )
        assertThat(summary).isEqualTo(expected)
    }

    private suspend fun givenAnExistingChannel(language: Language = FR_LANGUAGE) {
        channelRepository.add(ChannelMother.aChannel(language = language))
    }

    private suspend fun givenAnExistingChannelWithoutLanguage() {
        channelRepository.add(ChannelMother.aChannel(language = null))
    }

    private fun givenAModificationDate() {
        every { clock.now() } returns MODIFICATION_DATE
    }

    private fun givenAProfile() {
        coEvery { profileService.find(PLAYER_ID) } returns ProfileMother.aProfile(PLAYER_ID)
    }

    private fun givenAnOrder() {
        every { orderItemCalculator.calculateFor(any()) } returns ChannelMother.ORDER
    }

    private fun givenEpisodes() {
        coEvery { episodeRepository.findByIds(FRENCH_EPISODES_IDS.toList()) } returns FRENCH_EPISODES
        coEvery { episodeRepository.findByIds(EPISODE_ID_WITH_OTHER_OWNER.toList()) } returns listOf(
            EPISODE_WITH_OTHER_OWNER
        )
        coEvery { episodeRepository.findByIds(ENGLISH_EPISODES_IDS.toList()) } returns ENGLISH_EPISODES
        coEvery {
            episodeRepository.findByIds(
                FRENCH_EPISODES_IDS.plus(NOT_EXISTING_EPISODE_ID).toList()
            )
        } returns FRENCH_EPISODES
        coEvery {
            episodeRepository.findByIds(
                FRENCH_EPISODES_IDS.plus(ENGLISH_EPISODES_IDS).toList()
            )
        } returns FRENCH_EPISODES.plus(ENGLISH_EPISODES)
        coEvery {
            episodeRepository.findByIds(
                FRENCH_EPISODES_IDS.plus(EPISODE_WITH_CHANNEL.id).toList()
            )
        } returns FRENCH_EPISODES.plus(EPISODE_WITH_CHANNEL)
        coEvery { episodeRepository.findByIds(DIVERSE_EPISODES_IDS.toList()) } returns DIVERSE_EPISODES.toList()
    }

    private fun givenNotExistingEpisodes() {
        coEvery { episodeRepository.findByIds(any()) } returns listOf()
    }

    private suspend fun whenAdd(
        episodesIds: Set<String> = FRENCH_EPISODES_IDS,
        language: Language = FR_LANGUAGE,
        ownerId: Long = PLAYER_ID
    ) {
        try {
            val actionData = AddEpisodesToChannel.ActionData(
                ownerId,
                CHANNEL_ID,
                language,
                episodesIds
            )

            summary = action(actionData)
        } catch (e: RuntimeException) {
            thrownException = e
        }
    }

    private inline fun <reified T : RuntimeException> thenThrowsException() {
        assertThat(thrownException)
            .isNotNull
            .isInstanceOf(T::class.java)

        coVerify(exactly = 0) { addEpisodesToChannelRepository.add(any(), any()) }
    }

    private fun thenAddEpisodes(language: Language = FR_LANGUAGE) {
        coVerify(exactly = 1) {
            addEpisodesToChannelRepository.add(
                ChannelMother.aChannel(
                    language = language,
                    episodesCount = 2,
                    lastModificationDate = MODIFICATION_DATE
                ),
                CHANNEL_EPISODES
            )
        }
    }

    private suspend fun thenEpisodesCounterIsUpdated() {
        coVerify(exactly = 1) { addEpisodesToChannelRepository.add(match { it.episodesCount == 2 }, any()) }
    }

    private fun thenChannelIsReturned(language: Language = FR_LANGUAGE) {
        val expected =
            ChannelMother.aChannelSummary(
                lastModificationDate = MODIFICATION_DATE,
                episodesCount = 2,
                language = language
            )
        assertThat(summary).isEqualTo(expected)
    }

    private suspend fun thenPersistedLanguageIs(language: Language = FR_LANGUAGE) {
        coVerify(exactly = 1) { addEpisodesToChannelRepository.add(match { it.language == language }, any()) }
    }

    private suspend fun thenNotAddEpisodes() {
        coVerify(exactly = 0) { addEpisodesToChannelRepository.add(any(), any()) }
    }

    private fun thenUserNotHasChannels() {
        assertNull(summary)
    }

    private fun thenVerifyChannelScoreCalculation() {
        coVerify {
            qualityService.calculateChannelScore(
                channelId = CHANNEL_ID,
                episodes = match { it.size == 1 && it.first().id == EPISODE_1.id }
            )
        }
    }

    private companion object {
        const val NOT_EXISTING_EPISODE_ID = "not_existing_episode"
        const val CHANNEL_ID = ChannelMother.ID
        const val CHANNEL_OTHER_OWNER_ID = 1234L
        const val EPISODE_ORDER = 1000L
        val FR_LANGUAGE = Language.FR
        val EN_LANGUAGE = Language.EN
        val EPISODE_1 = EpisodeMother.buildEpisode(id = "episode_1", language = FR_LANGUAGE)
        val EPISODE_2 = EpisodeMother.buildEpisode(id = "episode_2", language = FR_LANGUAGE)
        val EPISODE_3 = EpisodeMother.buildEpisode(id = "episode_3", language = EN_LANGUAGE)
        val EPISODE_4 = EpisodeMother.buildEpisode(id = "episode_4", language = EN_LANGUAGE)
        val PRIVATE_EPISODE =
            EpisodeMother.buildEpisode(id = "episode_private", language = FR_LANGUAGE, type = EpisodeType.PRIVATE)
        val EPISODE_WITH_CHANNEL =
            EpisodeMother.buildEpisode(id = "episode_with_ch", language = FR_LANGUAGE, channelId = CHANNEL_ID)
        val EPISODE_WITH_OTHER_OWNER = EpisodeMother.buildEpisode(
            id = "episode_with_ch",
            language = FR_LANGUAGE,
            channelId = CHANNEL_ID,
            ownerId = CHANNEL_OTHER_OWNER_ID
        )
        val EPISODE_ID_WITH_OTHER_OWNER = setOf(EPISODE_WITH_OTHER_OWNER.id)
        val FRENCH_EPISODES_IDS = setOf(EPISODE_1.id, EPISODE_2.id)
        val ENGLISH_EPISODES_IDS = setOf(EPISODE_3.id, EPISODE_4.id)
        val DIVERSE_EPISODES_IDS =
            setOf(EPISODE_1.id, EPISODE_2.id, EPISODE_3.id, EPISODE_4.id, EPISODE_WITH_CHANNEL.id, PRIVATE_EPISODE.id)
        val FRENCH_EPISODES = listOf(EPISODE_1, EPISODE_2)
        val ENGLISH_EPISODES = listOf(EPISODE_3, EPISODE_4)
        val DIVERSE_EPISODES = setOf(EPISODE_1, EPISODE_2, EPISODE_3, EPISODE_4, EPISODE_WITH_CHANNEL, PRIVATE_EPISODE)
        val CHANNEL_EPISODES = setOf(
            ChannelEpisode(CHANNEL_ID, episodeId = EPISODE_1.id, MODIFICATION_DATE, EPISODE_ORDER),
            ChannelEpisode(
                CHANNEL_ID,
                episodeId = EPISODE_2.id,
                MODIFICATION_DATE.plus(50L, ChronoUnit.MILLIS),
                EPISODE_ORDER
            )
        )
    }
}
