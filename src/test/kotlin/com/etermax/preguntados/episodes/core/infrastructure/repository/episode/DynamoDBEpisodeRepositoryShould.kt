package com.etermax.preguntados.episodes.core.infrastructure.repository.episode

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.EpisodeMother.COUNTRY
import com.etermax.preguntados.episodes.core.EpisodeMother.LANGUAGE
import com.etermax.preguntados.episodes.core.EpisodeMother.NAME
import com.etermax.preguntados.episodes.core.EpisodeMother.OWNER_ID
import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeByOwnerItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.services.dynamodb.model.*

class DynamoDBEpisodeRepositoryShould {

    private lateinit var repository: EpisodeRepository
    private var result: Episode? = null
    private lateinit var results: List<Episode>
    private var hasEpisodes = false

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()

    @BeforeEach
    fun setUp() {
        createEpisodesTable()
        repository = DynamoDBEpisodeRepository(dynamoDbEnhancedClient, dynamoDbAsyncTable(), byOwnerIndex())
        hasEpisodes = false
    }

    @Test
    fun `save an episode`() = runTest {
        whenSave()

        thenEpisodeIsSaved(EPISODE)
    }

    @Test
    fun `save existing episode`() = runTest {
        givenAnEpisode(EPISODE)

        whenSave(UPDATED_EPISODE)

        thenEpisodeIsSaved(UPDATED_EPISODE)
    }

    @Test
    fun `save another episode`() = runTest {
        givenAnEpisode(ANOTHER_EPISODE)

        whenSave()

        thenEpisodeIsSaved(EPISODE)
    }

    @Test
    fun `delete episode`() = runTest {
        givenAnEpisode(EPISODE)

        whenDelete()

        thenThereIsNoneEpisode()
    }

    @Test
    fun `delete not existing episode`() = runTest {
        whenDelete()

        thenThereIsNoneEpisode()
    }

    @Test
    fun `find episode by id`() = runTest {
        givenAnEpisode(EPISODE)
        givenAnEpisode(ANOTHER_EPISODE)

        whenFindById(EPISODE.id)

        assertThat(result).isEqualTo(EPISODE)
    }

    @Test
    fun `find episodes by ids`() = runTest {
        givenAnEpisode(EPISODE)
        givenAnEpisode(ANOTHER_EPISODE)

        whenFindByIds(EPISODE.id, ANOTHER_EPISODE.id)

        assertThat(results).containsExactlyInAnyOrder(EPISODE, ANOTHER_EPISODE)
    }

    @Test
    fun `find not existing episode by id`() = runTest {
        givenAnEpisode(EPISODE)

        whenFindById(ANOTHER_EPISODE.id)

        assertThat(result).isNull()
    }

    @Test
    fun `find all episodes by filters`() = runTest {
        givenAnEpisode(EPISODE)
        givenAnEpisode(ANOTHER_EPISODE)

        whenFindAll(OWNER_ID, LANGUAGE, NAME, COUNTRY)

        assertThat(results).containsExactlyInAnyOrder(EPISODE)
    }

    @Test
    fun `return owner has no episodes`() = runTest {
        givenAnEpisode(EPISODE)
        whenCheckHasEpisodes(ownerId = 200L)
        thenHasEpisodes(false)
    }

    @Test
    fun `return owner has episodes`() = runTest {
        givenAnEpisode(EPISODE)
        whenCheckHasEpisodes()
        thenHasEpisodes(true)
    }

    @Test
    fun `increase like`() = runTest {
        givenAnEpisode(EPISODE.copy(rate = Rate(LIKES, DISLIKES)))

        whenUpdateRate(like = 1)

        thenRateIsUpdated(likes = LIKES + 1, dislikes = DISLIKES)
    }

    @Test
    fun `increase dislike`() = runTest {
        givenAnEpisode(EPISODE.copy(rate = Rate(LIKES, DISLIKES)))

        whenUpdateRate(dislike = 1)

        thenRateIsUpdated(likes = LIKES, dislikes = DISLIKES + 1)
    }

    @Test
    fun `decrease like`() = runTest {
        givenAnEpisode(EPISODE.copy(rate = Rate(LIKES, DISLIKES)))

        whenUpdateRate(like = -1)

        thenRateIsUpdated(likes = LIKES - 1, dislikes = DISLIKES)
    }

    @Test
    fun `decrease dislike`() = runTest {
        givenAnEpisode(EPISODE.copy(rate = Rate(LIKES, DISLIKES)))

        whenUpdateRate(dislike = -1)

        thenRateIsUpdated(likes = LIKES, dislikes = DISLIKES - 1)
    }

    @Test
    fun `increase reports`() = runTest {
        givenAnEpisode(EPISODE)

        repository.plusOneReport(EPISODE.id)

        thenReportsIsUpdated(reports = EPISODE.reports + 1)
    }

    @Test
    fun `increase views`() = runTest {
        givenAnEpisode(EPISODE)

        whenUpdateViews(views = 2)

        thenViewIsUpdated(views = EPISODE.views + 2)
    }

    @Test
    fun `increase existing views`() = runTest {
        givenAnEpisode(EPISODE.copy(views = VIEWS))

        whenUpdateViews(views = 1)

        thenViewIsUpdated(views = VIEWS + 1)
    }

    private suspend fun givenAnEpisode(episode: Episode) {
        repository.save(episode)
    }

    private suspend fun whenSave(episode: Episode = EPISODE) {
        repository.save(episode)
    }

    private suspend fun whenDelete() {
        repository.delete(EPISODE.id)
    }

    private suspend fun whenFindById(episodeId: String) {
        result = repository.findById(episodeId)
    }

    private suspend fun whenFindByIds(vararg episodes: String) {
        results = repository.findByIds(episodes.toList())
    }

    private suspend fun whenFindAll(ownerId: Long?, language: Language?, name: String?, country: Country?) {
        results = repository.findAll(ownerId, language, name, country)
    }

    private suspend fun whenCheckHasEpisodes(ownerId: Long = OWNER_ID) {
        hasEpisodes = repository.hasEpisodes(ownerId)
    }

    private suspend fun whenUpdateRate(like: Int = 0, dislike: Int = 0) {
        repository.updateRate(EPISODE.id, like, dislike)
    }

    private suspend fun whenUpdateViews(views: Int = 1) {
        repository.updateView(EPISODE.id, views)
    }

    private suspend fun thenEpisodeIsSaved(episode: Episode) {
        val savedEpisode = repository.findById(episode.id)
        assertThat(savedEpisode).isEqualTo(episode)
    }

    private suspend fun thenThereIsNoneEpisode() {
        val deletedEpisode = repository.findById(EPISODE.id)
        assertThat(deletedEpisode).isNull()
    }

    private fun thenHasEpisodes(flag: Boolean) {
        assertThat(hasEpisodes).isEqualTo(flag)
    }

    private suspend fun thenRateIsUpdated(likes: Long, dislikes: Long) {
        val episode = repository.findById(EPISODE.id)
        assertThat(episode?.rate?.likes).isEqualTo(likes)
        assertThat(episode?.rate?.dislikes).isEqualTo(dislikes)
    }

    private suspend fun thenReportsIsUpdated(reports: Long) {
        val episode = repository.findById(EPISODE.id)
        assertThat(episode?.reports).isEqualTo(reports)
    }

    private suspend fun thenViewIsUpdated(views: Long) {
        val episode = repository.findById(EPISODE.id)
        assertThat(episode?.views).isEqualTo(views)
    }

    private fun dynamoDbAsyncTable(): DynamoDbAsyncTable<EpisodeItem> {
        return dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(EpisodeItem::class.java))
    }

    private fun byOwnerIndex(): DynamoDbAsyncIndex<EpisodeByOwnerItem> {
        return dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(EpisodeByOwnerItem::class.java))
            .index(INDEX_NAME)
    }

    fun createEpisodesTable() {
        val request = CreateTableRequest.builder()
            .tableName(TABLE_NAME)
            .keySchema(
                listOf(
                    KeySchemaElement.builder().attributeName("PK").keyType(KeyType.HASH).build(),
                    KeySchemaElement.builder().attributeName("SK").keyType(KeyType.RANGE).build()
                )
            )
            .attributeDefinitions(
                listOf(
                    AttributeDefinition.builder().attributeName("PK").attributeType(ScalarAttributeType.S).build(),
                    AttributeDefinition.builder().attributeName("SK").attributeType(ScalarAttributeType.S).build(),
                    AttributeDefinition.builder().attributeName("owner_id").attributeType(ScalarAttributeType.N)
                        .build()
                )
            )
            .globalSecondaryIndexes(
                listOf(
                    GlobalSecondaryIndex.builder()
                        .indexName(INDEX_NAME)
                        .keySchema(
                            listOf(
                                KeySchemaElement.builder().attributeName("owner_id").keyType(KeyType.HASH).build()
                            )
                        )
                        .projection(
                            Projection.builder()
                                .projectionType(ProjectionType.INCLUDE)
                                .nonKeyAttributes("PK")
                                .build()
                        )
                        .provisionedThroughput(
                            ProvisionedThroughput.builder()
                                .readCapacityUnits(10)
                                .writeCapacityUnits(10)
                                .build()
                        )
                        .build()
                )
            )
            .provisionedThroughput(
                ProvisionedThroughput.builder()
                    .readCapacityUnits(10)
                    .writeCapacityUnits(10)
                    .build()
            )
            .build()

        dynamoDbTestServer.buildClient().createTable(request)
    }

    private companion object {
        const val TABLE_NAME = "dev_episodes"
        const val INDEX_NAME = "by-owner-episode-index"
        const val LIKES = 50L
        const val DISLIKES = 10L
        const val VIEWS = 25L
        val EPISODE = buildEpisode()
        val UPDATED_EPISODE = EPISODE.copy(name = "new name")
        val ANOTHER_EPISODE = EPISODE.copy(id = "key1", name = "another name")

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer()
    }
}
