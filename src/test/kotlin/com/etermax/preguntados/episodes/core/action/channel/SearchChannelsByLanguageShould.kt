package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.aChannelByLanguageSummary
import com.etermax.preguntados.episodes.core.domain.channel.ChannelReduced
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelByLanguageRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelByLanguageFilters
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SearchChannelsByLanguageShould {
    private lateinit var action: SearchChannelsByLanguage
    private lateinit var repository: ChannelByLanguageRepository

    private var result: PaginatedItems<ChannelReduced>? = null

    @BeforeEach
    fun setUp() {
        repository = mockk()
        action = SearchChannelsByLanguage(repository, PAGINATION_SIZE)

        result = null
    }

    @Test
    fun `first search`() = runTest {
        givenChannelForFirstSearch()
        whenSearch(lastKey = null)
        thenReturnSummaryForFirstSearch()
    }

    @Test
    fun `first search excluding with no language`() = runTest {
        givenChannelForFirstSearchWithLanguage()
        whenSearch(lastKey = null, includeWithNoLanguage = false)
        thenReturnSummaryForFirstSearchWithLanguage()
    }

    @Test
    fun `search from last key`() = runTest {
        givenChannelFromLastKey()
        whenSearch(LAST_KEY)
        thenReturnSummaryFromLastKey()
    }

    private fun givenChannelForFirstSearch() {
        val filters = ChannelByLanguageFilters(OWNER_ID, LANGUAGE, includeWithNoLanguage = true)
        val pagination = PaginationFilter(PAGINATION_SIZE, lastEvaluatedKey = null)
        val channels = listOf(ChannelMother.aChannelSummaryReduced())
        coEvery { repository.search(filters, pagination) } returns PaginatedItems(
            items = channels,
            lastEvaluatedKey = LAST_KEY
        )
    }

    private fun givenChannelForFirstSearchWithLanguage() {
        val filters = ChannelByLanguageFilters(OWNER_ID, LANGUAGE, includeWithNoLanguage = false)
        val pagination = PaginationFilter(PAGINATION_SIZE, lastEvaluatedKey = null)
        val channels = listOf(
            ChannelMother.aChannelSummaryReduced(id = "another_channel_1"),
            ChannelMother.aChannelSummaryReduced(id = "another_channel_2")
        )
        coEvery { repository.search(filters, pagination) } returns PaginatedItems(
            items = channels,
            lastEvaluatedKey = LAST_KEY
        )
    }

    private fun givenChannelFromLastKey() {
        val filters = ChannelByLanguageFilters(OWNER_ID, LANGUAGE, includeWithNoLanguage = true)
        val pagination = PaginationFilter(PAGINATION_SIZE, lastEvaluatedKey = LAST_KEY)
        val channels = listOf(ChannelMother.aChannelSummaryReduced())
        coEvery { repository.search(filters, pagination) } returns PaginatedItems(
            items = channels,
            lastEvaluatedKey = null
        )
    }

    private suspend fun whenSearch(lastKey: String?, includeWithNoLanguage: Boolean = true) {
        val data = SearchChannelsByLanguage.ActionData(OWNER_ID, LANGUAGE, lastKey, includeWithNoLanguage)
        result = action(data)
    }

    private fun thenReturnSummaryForFirstSearch() {
        PaginatedItems(
            items = listOf(aChannelByLanguageSummary()),
            lastEvaluatedKey = LAST_KEY
        )
    }

    private fun thenReturnSummaryForFirstSearchWithLanguage() {
        PaginatedItems(
            items = listOf(
                ChannelMother.aChannelSummaryReduced(id = "another_channel_1"),
                ChannelMother.aChannelSummaryReduced(id = "another_channel_2")
            ),
            lastEvaluatedKey = LAST_KEY
        )
    }

    private fun thenReturnSummaryFromLastKey() {
        PaginatedItems(
            items = listOf(aChannelByLanguageSummary()),
            lastEvaluatedKey = null
        )
    }

    private companion object {
        const val OWNER_ID = 10L
        const val PAGINATION_SIZE = 10
        const val LAST_KEY = "key"
        val LANGUAGE = Language.ES
    }
}
