package com.etermax.preguntados.episodes.core.infrastructure.profile.repository

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.repository.ProfileRepository
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother.aProfile
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import kotlinx.coroutines.future.await
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration.parse

abstract class ProfileRepositoryShould {
    abstract val repository: ProfileRepository

    @Test
    fun `save not existing profile`() = runTest {
        val profile = aProfile()
        repository.save(profile)

        val foundProfile = repository.find(aProfile().playerId)
        assertThat(foundProfile).isEqualTo(profile)
    }

    @Test
    fun `save existing profile`() = runTest {
        repository.save(aProfile())

        val profile = aProfile(photoUrl = "new_photo")
        repository.save(profile)

        val foundProfile = repository.find(aProfile().playerId)
        assertThat(foundProfile).isEqualTo(profile)
    }

    @Test
    fun `find an existing profile`() = runTest {
        val profile = aProfile()
        repository.save(profile)

        val foundProfile = repository.find(aProfile().playerId)

        assertThat(foundProfile).isEqualTo(profile)
    }

    @Test
    fun `find many existing profiles`() = runTest {
        val profile = aProfile()
        repository.save(profile)

        val foundProfiles = repository.find(listOf(aProfile().playerId))

        assertThat(foundProfiles).isEqualTo(mapOf(profile.playerId to profile))
    }

    @Test
    fun `find a not existing profile`() = runTest {
        val foundProfile = repository.find(aProfile().playerId)

        assertThat(foundProfile).isNull()
    }

    @Test
    fun `find empty list returns empty list`() = runTest {
        val result = repository.find(listOf())
        assertThat(result).isEqualTo(mapOf<Long, Profile>())
    }
}

@ExtendWith(RedisTestServer::class)
class RedisProfileRepositoryShould : ProfileRepositoryShould() {
    private val redisAsync = EmbeddedRedisUtils.buildClient()
    override val repository: ProfileRepository = RedisProfileRepository(redisAsync, parse(TEN_SECONDS))

    @Test
    fun `expire profile after one day`() = runTest {
        val profile = aProfile()
        repository.save(profile)

        val ttl = redisAsync.ttl("pr:e:p:${profile.playerId}").await()
        assertThat(ttl).isEqualTo(ONE_HOUR_IN_SECONDS)
    }

    private companion object {
        const val TEN_SECONDS = "PT10S"
        const val ONE_HOUR_IN_SECONDS = 10L
    }
}
