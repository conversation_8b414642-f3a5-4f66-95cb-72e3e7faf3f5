package com.etermax.preguntados.episodes.core.infrastructure.search.feed.layout

import com.etermax.preguntados.episodes.core.domain.channel.ChannelType
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType
import com.etermax.preguntados.episodes.core.domain.search.feed.EightByTwoFetchCursor
import com.etermax.preguntados.episodes.core.domain.search.feed.OffsetLimit
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceRequest
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelItemAttributes
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.RecentChannelSource
import com.etermax.preguntados.episodes.core.infrastructure.search.feed.RecentEpisodeSource
import com.etermax.preguntados.episodes.utils.LocalOpenSearch
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import java.time.OffsetDateTime.now
import kotlin.test.assertContentEquals
import kotlin.test.assertTrue

class EightByTwoFeedSourceIntegrationShould {
    private val perTestSuffix = "-${System.currentTimeMillis()}"
    private val episodeIndexName = "episode$perTestSuffix"
    private val channelIndexName = "channel$perTestSuffix"

    val osEpisodesConfig =
        LocalOpenSearch.Config(episodeIndexName, "opensearch/mapping/episodes-index-mapping-v2.0.json")
    val osChannelsConfig =
        LocalOpenSearch.Config(channelIndexName, "opensearch/mapping/channels-index-mapping-v1.1.json")
    private val osClient by lazy {
        LocalOpenSearch.buildOpenSearch(listOf(osEpisodesConfig, osChannelsConfig))
    }

    private lateinit var composer: EightByTwoFeedSource<String, String>
    private lateinit var result: SourceResponse<Any>

    @BeforeEach
    fun setUp() {
        val primarySource = RecentEpisodeSource(osClient, episodeIndexName)
        val secondarySource = RecentChannelSource(osClient, channelIndexName)
        composer = EightByTwoFeedSource(primarySource, secondarySource)
    }

    @Test
    fun `compose a feed with the correct pattern when limit is a multiple of 12`() = runTest {
        (1..20).forEach { i ->
            givenAnEpisode(id = i.toString(), startDate = now().minusHours(i.toLong()))
        }

        (100..105).forEach { i ->
            givenAChannel(id = i.toString(), lastModificationDate = now().minusHours(i.toLong()))
        }

        whenCompose(size = 24)

        thenResultIsNotEmpty()
        thenResultsContainsExactly(
            listOf(
                "1", "100", "2", "101", // first pattern
                "3", "4", "5", "6", "7", "8",
                "102", "9", "103", "10", // second pattern
                "11", "12", "13", "14", "15", "16"
            )
        )
    }

    @Test
    fun `compose a feed with the correct pattern when limit is not a multiple of 12`() = runTest {
        (1..10).forEach { i ->
            givenAnEpisode(id = i.toString(), startDate = now().minusHours(i.toLong()))
        }

        (100..105).forEach { i ->
            givenAChannel(id = i.toString(), lastModificationDate = now().minusHours(i.toLong()))
        }

        whenCompose(size = 18)

        thenResultIsNotEmpty()
        thenResultsContainsExactly(
            listOf(
                "1", "100", "2", "101", // first pattern
                "3", "4", "5", "6", "7", "8",
                "102", "9", "103", "10" // partial second pattern
            )
        )
    }

    @Test
    fun `compose a feed with the correct pattern when there are not enough primary items`() = runTest {
        (1..10).forEach { i ->
            givenAnEpisode(id = i.toString(), startDate = now().minusHours(i.toLong()))
        }

        (100..105).forEach { i ->
            givenAChannel(id = i.toString(), lastModificationDate = now().minusHours(i.toLong()))
        }

        whenCompose(size = 24)

        thenResultIsNotEmpty()
        thenResultsContainsExactly(
            listOf(
                "1", "100", "2", "101", // first pattern
                "3", "4", "5", "6", "7", "8",
                "102", "9", "103", "10" // second pattern
            )
        )
    }

    @Test
    fun `compose a feed with the correct pattern when there are not primary items at all`() = runTest {
        (100..105).forEach { i ->
            givenAChannel(id = i.toString(), lastModificationDate = now().minusHours(i.toLong()))
        }

        whenCompose(size = 24)

        thenResultIsEmpty()
        thenResultsContainsExactly(emptyList())
    }

    @Test
    fun `compose a feed with the correct pattern when there are not enough secondary items`() = runTest {
        (1..20).forEach { i ->
            givenAnEpisode(id = i.toString(), startDate = now().minusHours(i.toLong()))
        }

        givenAChannel(id = "100")
        givenAChannel(id = "101")

        whenCompose(size = 24)

        thenResultIsNotEmpty()
        thenResultsContainsExactly(
            listOf(
                "1", "100", "2", "101", // first pattern
                "3", "4", "5", "6", "7", "8",
                "9", "10", "11", "12", "13", "14",
                "15", "16", "17", "18", "19", "20"
            )
        )
    }

    @Test
    fun `compose feed till primary source is exhausted`() = runTest {
        (1..20).forEach { i ->
            givenAnEpisode(id = i.toString(), startDate = now().minusHours(i.toLong()))
        }

        (100..105).forEach { i ->
            givenAChannel(id = i.toString(), lastModificationDate = now().minusHours(i.toLong()))
        }

        whenCompose(size = 48)

        thenResultIsNotEmpty()
        thenResultsContainsExactly(
            listOf(
                "1", "100", "2", "101", // first pattern
                "3", "4", "5", "6", "7", "8",
                "102", "9", "103", "10", // second pattern
                "11", "12", "13", "14", "15", "16",
                "17", "104", "18", "105", // third pattern
                "19", "20"
            )
        )

        assertTrue(result.fetchCursor is EightByTwoFetchCursor)
        assertTrue(result.fetchCursor?.exhausted!!)
    }

    @Test
    fun `verify cursor is the right type`() = runTest {
        (1..20).forEach { i ->
            givenAnEpisode(id = i.toString(), startDate = now().minusHours(i.toLong()))
        }

        (100..105).forEach { i ->
            givenAChannel(id = i.toString(), lastModificationDate = now().minusHours(i.toLong()))
        }

        whenCompose(size = 24)

        assertTrue(result.fetchCursor is EightByTwoFetchCursor)
    }

    private suspend fun whenCompose(size: Int) {
        result = composer.fetch(SourceRequest(OffsetLimit(0, size), 1, Language.EN))
    }

    private fun thenResultIsEmpty() {
        assertTrue(result.isEmpty())
    }

    private fun thenResultIsNotEmpty() {
        assertTrue(result.items.isNotEmpty())
    }

    private fun thenResultsContainsExactly(expected: List<String>) {
        assertContentEquals(expected, result.items)
    }

    private fun givenAnEpisode(
        id: String = "id",
        name: String = "Episode",
        ownerId: Int = 1,
        type: EpisodeType = EpisodeType.PUBLIC,
        status: EpisodeStatus = EpisodeStatus.PUBLISHED,
        country: Country = Country.US,
        language: Language = Language.EN,
        likes: Long = 100,
        dislikes: Long = 0,
        views: Long = 100,
        embedding: FloatArray = FloatArray(0),
        startDate: OffsetDateTime? = null
    ) {
        val document = mutableMapOf<String, Any>(
            "episode_id" to id,
            "name" to name,
            "status" to status,
            "type" to type,
            "language" to language,
            "country" to country,
            "owner_id" to ownerId,
            "rate" to (likes / (likes + dislikes).toDouble() * 100),
            "views" to views,
            "likes" to likes,
            "dislikes" to dislikes,
            "start_date" to (startDate?.toInstant()?.toEpochMilli() ?: 0)
        )

        if (embedding.isNotEmpty()) {
            document["embedding"] = embedding
        }

        LocalOpenSearch.saveToOpenSearch(osClient, episodeIndexName, id, document)
    }

    private fun givenAChannel(
        id: String,
        language: Language = Language.EN,
        type: ChannelType = ChannelType.PUBLIC,
        episodesCount: Int = 3,
        lastModificationDate: OffsetDateTime = now()
    ) {
        val channelItem = mapOf(
            ChannelItemAttributes.CHANNEL_ID to id,
            ChannelItemAttributes.LANGUAGE to language.name,
            ChannelItemAttributes.TYPE to type.name,
            ChannelItemAttributes.EPISODES_COUNT to episodesCount,
            ChannelItemAttributes.LAST_MODIFICATION_DATE to lastModificationDate.toMillis()
        )

        LocalOpenSearch.saveToOpenSearch(osClient, channelIndexName, id, channelItem)
    }
}
