package com.etermax.preguntados.episodes.core.domain.quality.service

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelScoreRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.quality.likerate.EpisodeLikeRateAvgCalculator
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ChannelScoreOnUnrateShould {

    private lateinit var channelEpisodesRepository: ChannelEpisodesRepository
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var channelRepository: ChannelRepository
    private lateinit var defaultQualityService: DefaultQualityService
    private lateinit var episodeLikeRateAvgCalculator: EpisodeLikeRateAvgCalculator
    private lateinit var channelScoreRepository: ChannelScoreRepository

    @BeforeEach
    fun setUp() {
        channelEpisodesRepository = mockk()
        episodeRepository = mockk()
        channelRepository = mockk()
        episodeLikeRateAvgCalculator = EpisodeLikeRateAvgCalculator()
        channelScoreRepository = mockk()
        defaultQualityService =
            DefaultQualityService(
                channelEpisodesRepository,
                episodeRepository,
                channelRepository,
                episodeLikeRateAvgCalculator,
                channelScoreRepository,
                minimumRates = 30,
                minimumViews = 100
            )
    }

    @Test
    fun `should calculate channel score when user un rate one liked episode and continues meet minimum rate threshold`() =
        runTest {
            // given
            val episode = givenEpisodeWithRates(EPISODE_ID_1, likes = 21, dislikes = 10) // 67% like rate
            val episodeWithUnRate = givenUnLikeRatedEpisodeFrom(episode) // Dec likes to 20, // 66% like rate
            givenRepositoriesWith(episodeWithUnRate)

            // when rated episode
            whenToProcessChannelScore(
                episodeBeforeUpdate = episode,
                episodeAfterUpdate = episodeWithUnRate
            )

            // then
            thenChannelScoreIsUpdatedTo(score = 66)
        }

    @Test
    fun `should calculate channel score when user un rate one liked episode & channel have all valid episodes`() =
        runTest {
            // given
            val episode1 = givenEpisodeWithRates(EPISODE_ID_1, likes = 21, dislikes = 10) // 67% like rate
            val episode1WithUnRate = givenUnLikeRatedEpisodeFrom(episode1) // Dec likes to 20, // 66% like rate
            val episode2 = givenEpisodeWithRates(EPISODE_ID_2, likes = 60, dislikes = 40) // 60% like rate
            val episode3 = givenEpisodeWithRates(EPISODE_ID_3, likes = 40, dislikes = 60) // 40% like rate
            val episodes = listOf(episode1WithUnRate, episode2, episode3)
            givenRepositoriesWith(episodes)

            // when rated episode
            whenToProcessChannelScore(
                episodeBeforeUpdate = episode1,
                episodeAfterUpdate = episode1WithUnRate
            )

            // then
            thenChannelScoreIsUpdatedTo(score = 55) // (66 + 60 + 40) / 3 = 55
        }

    @Test
    fun `should calculate channel score when user un rate one liked episode & channel have mixed valid and invalid episodes`() =
        runTest {
            // given
            val episode1 = givenEpisodeWithRates(EPISODE_ID_1, likes = 21, dislikes = 10) // 67% like rate
            val episode1WithUnRate = givenUnLikeRatedEpisodeFrom(episode1) // Dec likes to 20, // 66% like rate
            val invalidEpisode2 = givenEpisodeWithNotEnoughRatesAndViews(EPISODE_ID_2) // not enough rates and views
            val invalidEpisode3 = givenEpisodeWithNotEnoughRatesAndViews(EPISODE_ID_3) // not enough rates and views
            val allEpisodes = listOf(episode1WithUnRate, invalidEpisode2, invalidEpisode3)
            givenRepositoriesWith(allEpisodes)

            // when rated episode
            whenToProcessChannelScore(
                episodeBeforeUpdate = episode1,
                episodeAfterUpdate = episode1WithUnRate
            )

            // then
            thenChannelScoreIsUpdatedTo(score = 66)
        }

    @Test
    fun `should reset channel score to zero when user un rate one liked episode and falling below minimum rate threshold`() =
        runTest {
            // given
            val episode = givenEpisodeWithRates(EPISODE_ID_1, likes = 20, dislikes = 10) // 67% like rate
            val episodeWithUnRate = givenUnLikeRatedEpisodeFrom(episode) // Dec likes to 19, // 66% like rate
            givenRepositoriesWith(episodeWithUnRate)

            // when rated episode
            whenToProcessChannelScore(
                episodeBeforeUpdate = episode,
                episodeAfterUpdate = episodeWithUnRate
            )

            // then
            thenChannelScoreIsUpdatedTo(score = 0)
        }

    @Test
    fun `should skip channel score calculation when user un rate one liked episode and no meet minimum rate threshold`() =
        runTest {
            // given
            val episode =
                givenEpisodeWithRates(EPISODE_ID_1, likes = 10, dislikes = 10) // 50% like rate, not enough rates
            val episodeWithUnRate = givenUnLikeRatedEpisodeFrom(episode) // Dec likes to 9, not enough rates
            givenRepositoriesWith(episodeWithUnRate)

            // when rated episode
            whenToProcessChannelScore(
                episodeBeforeUpdate = episode,
                episodeAfterUpdate = episodeWithUnRate
            )

            // then
            thenChannelScoreIsNotUpdated()
        }

    private fun givenRepositoriesWith(allEpisodes: List<Episode>) {
        coEvery { channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL.id) } returns allEpisodes.map { it.id }
        coEvery { episodeRepository.findByIds(allEpisodes.map { it.id }) } returns allEpisodes
        coEvery { channelRepository.findById(CHANNEL.id) } returns CHANNEL
    }

    private fun givenEpisodeWithRates(id: String, likes: Long, dislikes: Long) =
        EpisodeMother.buildEpisode(
            id = id,
            channelId = CHANNEL.id,
            likes = likes,
            dislikes = dislikes,
            views = 50
        )

    private fun givenEpisodeWithNotEnoughRatesAndViews(id: String) =
        EpisodeMother.buildEpisode(
            id = id,
            channelId = CHANNEL.id,
            likes = 0,
            dislikes = 0,
            views = 50
        )

    /**
     * ***************
     * ***************
     * Given unlike rated episode
     * ***************
     * ***************
     */
    private fun givenUnLikeRatedEpisodeFrom(episode: Episode) =
        episode.copy(rate = episode.rate.copy(_likes = episode.rate.likes - 1))

    private fun givenRepositoriesWith(episode: Episode) {
        coEvery { channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL.id) } returns listOf(
            episode.id
        )
        coEvery { episodeRepository.findByIds(listOf(episode.id)) } returns listOf(episode)
        coEvery { channelRepository.findById(CHANNEL.id) } returns CHANNEL
    }

    /**
     * ***************
     * ***************
     * When to process channel score
     * ***************
     * ***************
     */
    private suspend fun whenToProcessChannelScore(episodeBeforeUpdate: Episode, episodeAfterUpdate: Episode) {
        defaultQualityService.calculateChannelScore(
            episodeBeforeUpdate = episodeBeforeUpdate,
            episodeAfterUpdate = episodeAfterUpdate
        )
    }

    /**
     * ***************
     * ***************
     * Then channel score is updated to
     * ***************
     * ***************
     */
    private fun thenChannelScoreIsUpdatedTo(score: Int) {
        coVerify { channelScoreRepository.updateScore(channelId = CHANNEL.id, score = score) }
    }

    private fun thenChannelScoreIsNotUpdated() {
        coVerify(exactly = 0) { channelScoreRepository.updateScore(any(), any()) }
    }

    private companion object {
        val CHANNEL = ChannelMother.aChannel(id = "channel-id")
        val EPISODE_ID_1 = "episode-id-1"
        val EPISODE_ID_2 = "episode-id-2"
        val EPISODE_ID_3 = "episode-id-3"
    }
}
