package com.etermax.preguntados.episodes.core.domain.profile

import com.etermax.preguntados.episodes.core.domain.profile.repository.PlayerFriendsRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CachePlayerFriendsServiceShould {

    private lateinit var service: CachePlayerFriendsService
    private lateinit var playerFriendsService: PlayerFriendsService
    private lateinit var repository: PlayerFriendsRepository

    private lateinit var result: List<Long>

    @BeforeEach
    fun setUp() {
        playerFriendsService = mockk(relaxed = true)
        repository = mockk(relaxed = true)

        service = CachePlayerFriendsService(playerFriendsService, repository)
    }

    @Test
    fun `return friends from cache when available`() = runTest {
        givenCachedFriends()

        whenFindFollowedIds()

        thenFriendsReturnedFromCache()
    }

    @Test
    fun `return friends from service when not cached`() = runTest {
        givenNoCachedFriends()
        givenFriendsFromService()

        whenFindFollowedIds()

        thenFriendsReturnedFromService()
    }

    @Test
    fun `cache friends when not in cache`() = runTest {
        givenNoCachedFriends()
        givenFriendsFromService()

        whenFindFollowedIds()

        thenFriendsAreCached()
    }

    @Test
    fun `not call service when friends are cached`() = runTest {
        givenCachedFriends()

        whenFindFollowedIds()

        thenServiceIsNotCalled()
    }

    @Test
    fun `return empty list when no friends in cache or service`() = runTest {
        givenNoCachedFriends()
        givenNoFriendsFromService()

        whenFindFollowedIds()

        thenEmptyListReturned()
    }

    private fun givenCachedFriends() {
        coEvery { repository.find(PLAYER_ID) } returns CACHED_FRIENDS
    }

    private fun givenNoCachedFriends() {
        coEvery { repository.find(PLAYER_ID) } returns null
    }

    private fun givenFriendsFromService() {
        coEvery { playerFriendsService.findFollowedIds(PLAYER_ID) } returns SERVICE_FRIENDS
    }

    private fun givenNoFriendsFromService() {
        coEvery { playerFriendsService.findFollowedIds(PLAYER_ID) } returns emptyList()
    }

    private suspend fun whenFindFollowedIds() {
        result = service.findFollowedIds(PLAYER_ID)
    }

    private fun thenFriendsReturnedFromCache() {
        assertThat(result).isEqualTo(CACHED_FRIENDS)
    }

    private fun thenFriendsReturnedFromService() {
        assertThat(result).isEqualTo(SERVICE_FRIENDS)
    }

    private fun thenFriendsAreCached() {
        coVerify(exactly = 1) { repository.save(PLAYER_ID, SERVICE_FRIENDS) }
    }

    private fun thenServiceIsNotCalled() {
        coVerify(exactly = 0) { playerFriendsService.findFollowedIds(any()) }
    }

    private fun thenEmptyListReturned() {
        assertThat(result).isEmpty()
    }

    private companion object {
        const val PLAYER_ID = 123L
        val CACHED_FRIENDS = listOf(456L, 789L)
        val SERVICE_FRIENDS = listOf(101112L, 131415L)
    }
}
