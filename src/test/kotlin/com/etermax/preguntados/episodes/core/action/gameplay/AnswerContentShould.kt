package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.EpisodeMother.LANGUAGE
import com.etermax.preguntados.episodes.core.action.gameplay.AnswerContent.ActionData
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.history.AnswerContentHistoryService
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentService
import com.etermax.preguntados.episodes.core.domain.episode.ranking.CalculatorInfo
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingPointsCalculator
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class AnswerContentShould {

    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var rankingRepository: RankingRepository
    private lateinit var calculator: RankingPointsCalculator
    private lateinit var progressContentService: ProgressContentService
    private lateinit var answerContentHistoryService: AnswerContentHistoryService
    private lateinit var actionData: ActionData

    @BeforeEach
    fun setUp() {
        rankingRepository = mockk(relaxed = true)
        calculator = mockk(relaxed = true)
        progressContentService = mockk(relaxed = true)
        episodeRepository = mockk(relaxed = true)
        answerContentHistoryService = mockk(relaxed = true)
    }

    @Test
    fun `do not increment score when answer incorrectly`() = runTest {
        givenAnAnswer(IS_INCORRECT)
        givenAProgress(hasFinishedEpisode = false)
        givenAnEpisode()

        whenAnswerContent()

        thenScoreIsNotIncremented()
    }

    @Test
    fun `do not calculate score when answer incorrectly`() = runTest {
        givenAnAnswer(IS_INCORRECT)
        givenAProgress(hasFinishedEpisode = false)
        givenAnEpisode()

        whenAnswerContent()

        thenCalculatorIsNotInvoked()
    }

    @Test
    fun `do not increment score when episode is already finished`() = runTest {
        givenAnAnswer()
        givenAProgress(hasFinishedEpisode = true)

        whenAnswerContent()

        thenScoreIsNotIncremented()
    }

    @Test
    fun `invoke calculator`() = runTest {
        givenAnAnswer()
        givenAProgress()
        givenAnEpisode()

        whenAnswerContent()

        thenCalculatorIsInvoked()
    }

    @Test
    fun `assume zero extra points if calculator returns negative value`() = runTest {
        givenAnAnswer()
        givenNoProgress()
        givenACalculator(-60)
        givenAnEpisode()

        whenAnswerContent()

        thenIncrementScoreWith(SCORE_BY_CORRECT_ANSWER)
    }

    @Test
    fun `increment score when answer correctly and has no progress`() = runTest {
        givenAnAnswer()
        givenNoProgress()
        givenACalculator()
        givenAnEpisode()

        whenAnswerContent()

        thenIncrementScoreWith(SCORE_CALCULATED.plus(SCORE_BY_CORRECT_ANSWER))
    }

    @Test
    fun `increment score when answer correctly and episode is not finished`() = runTest {
        givenAnAnswer(contentId = CONTENT_ID_A666)
        givenAnEpisode()
        givenAProgress()
        givenACalculator()

        whenAnswerContent()

        coVerify {
            progressContentService.registerProgress(
                EPISODE_ID,
                PLAYER_ID,
                CONTENT_ID_A666,
                LANGUAGE.name,
                true
            )
        }
    }

    @Test
    fun `finish episode when answer last content`() = runTest {
        givenAnAnswer()
        givenAnEpisode()
        givenAProgress()
        givenACalculator()

        whenAnswerContent()

        thenIncrementScoreWith(SCORE_CALCULATED.plus(SCORE_BY_CORRECT_ANSWER))
    }

    @Test
    fun `add answered content to history when answer`() = runTest {
        givenAnAnswer()
        givenNoProgress()
        givenACalculator()
        givenAnEpisode()

        whenAnswerContent()

        thenAddsAnsweredContentToHistory()
    }

    private fun givenAnEpisode() {
        coEvery { episodeRepository.findById(any()) } returns EpisodeMother.buildEpisode(
            id = EPISODE_ID,
            contents = CONTENTS
        )
    }

    private fun givenAnAnswer(isCorrect: Boolean = IS_CORRECT, contentId: String = CONTENT_ID) {
        actionData = ActionData(PLAYER_ID, EPISODE_ID, contentId, isCorrect, ELAPSED_TIME, TOTAL_TIME)
    }

    private fun givenAProgress(hasFinishedEpisode: Boolean = false) {
        coEvery {
            progressContentService.findProgress(any(), any())
        } returns ProgressContent(EPISODE_ID, PLAYER_ID, CONTENT_ID, LANGUAGE.name, hasFinishedEpisode)
    }

    private fun givenNoProgress() {
        coEvery { progressContentService.findProgress(any(), any()) } returns null
    }

    private fun givenACalculator(score: Int = SCORE_CALCULATED) {
        coEvery { calculator.calculate(any()) } returns score
    }

    private suspend fun whenAnswerContent() {
        val answerContent = AnswerContent(
            rankingRepository,
            calculator,
            progressContentService,
            episodeRepository,
            answerContentHistoryService
        )
        answerContent(actionData)
    }

    private fun thenScoreIsNotIncremented() {
        coVerify(exactly = 0) { rankingRepository.incrementScore(any(), any(), any()) }
    }

    private fun thenCalculatorIsNotInvoked() {
        coVerify(exactly = 0) { calculator.calculate(any()) }
    }

    private fun thenCalculatorIsInvoked() {
        coVerify(exactly = 1) { calculator.calculate(CalculatorInfo(ELAPSED_TIME, TOTAL_TIME)) }
    }

    private fun thenIncrementScoreWith(value: Int) {
        coVerify(exactly = 1) { rankingRepository.incrementScore(PLAYER_ID, Domain(EPISODE_ID), value) }
    }

    private fun thenAddsAnsweredContentToHistory() {
        coVerify(exactly = 0) {
            answerContentHistoryService.addAnsweredContentToHistory(
                PLAYER_ID,
                CONTENT_ID,
                "episodes"
            )
        }
    }

    private companion object {

        const val PLAYER_ID = 666L
        const val CONTENT_ID_A66 = "A66"
        const val CONTENT_ID_A666 = "A666"
        const val EPISODE_ID = "ABC-123"
        const val CONTENT_ID = "XYZ-789"
        val CONTENTS = listOf(
            CONTENT_ID,
            CONTENT_ID_A66,
            CONTENT_ID_A666
        )
        const val IS_CORRECT = true
        const val IS_INCORRECT = false
        const val ELAPSED_TIME = 5000
        const val TOTAL_TIME = 10000
        const val SCORE_CALCULATED = 25
        const val SCORE_BY_CORRECT_ANSWER = 50
    }
}
