package com.etermax.preguntados.episodes.core.infrastructure.repository.notification

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.domain.episode.notification.EventGroup
import com.etermax.preguntados.episodes.core.domain.episode.notification.EventGroupsRepository
import com.etermax.preguntados.episodes.core.domain.episode.notification.EventType
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import java.time.OffsetDateTime

class DynamoDBEventGroupsRepositoryShould {
    private lateinit var repository: EventGroupsRepository

    private val buildAsyncClient = dynamoDbTestServer.buildAsyncClient()
    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(buildAsyncClient).build()

    @BeforeEach
    fun setUp() {
        repository = DynamoDBEventGroupsRepository(
            dynamoDbEnhancedClient,
            createTable()
        )
    }

    @Test
    fun `returns item`() = runTest {
        repository.save(EPISODE_ID, EVENT_TYPE, EventGroup(EPISODE_ID, EVENT_TYPE, PLAYER_ID, 1, 5, NOW))
        val group = repository.find(EPISODE_ID, EVENT_TYPE)
        assertThat(group).isEqualTo(EventGroup(EPISODE_ID, EVENT_TYPE, PLAYER_ID, 1, 5, NOW))
    }

    @Test
    fun `increment item`() = runTest {
        repository.save(EPISODE_ID, EVENT_TYPE, EventGroup(EPISODE_ID, EVENT_TYPE, PLAYER_ID, 1, 5, NOW))
        val group = repository.increment(EPISODE_ID, EVENT_TYPE, PLAYER_ID)
        assertThat(group).isEqualTo(EventGroup(EPISODE_ID, EVENT_TYPE, PLAYER_ID, 2, 5, NOW))
    }

    @Test
    fun `create item on first increment`() = runTest {
        val group = repository.increment(EPISODE_ID, EVENT_TYPE, PLAYER_ID)
        assertThat(group).isEqualTo(EventGroup(EPISODE_ID, EVENT_TYPE, PLAYER_ID, 1, null, null))

        val savedGroup = repository.find(EPISODE_ID, EVENT_TYPE)
        assertThat(savedGroup).isEqualTo(EventGroup(EPISODE_ID, EVENT_TYPE, PLAYER_ID, 1, null, null))
    }

    private fun createTable(): DynamoDbAsyncTable<EventGroupsItem> {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(buildAsyncClient).build()
            .table(TABLE_NAME, TableSchema.fromBean(EventGroupsItem::class.java))
    }

    private companion object {
        const val TABLE_NAME = "dev_event_groups"
        const val EPISODE_ID = "episodeId"
        const val PLAYER_ID = 123L
        val EVENT_TYPE = EventType.LIKE

        val NOW = OffsetDateTime.now().truncatedTo(java.time.temporal.ChronoUnit.SECONDS)

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(EventGroupsItem::class.java to TABLE_NAME))
    }
}
