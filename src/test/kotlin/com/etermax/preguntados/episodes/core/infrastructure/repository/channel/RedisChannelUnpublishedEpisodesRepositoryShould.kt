package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.EpisodeMother.buildUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration

@ExtendWith(RedisTestServer::class)
class RedisChannelUnpublishedEpisodesRepositoryShould {
    private val redisAsync = EmbeddedRedisUtils.buildClient()

    private val repository: RedisChannelUnpublishedEpisodesRepository = RedisChannelUnpublishedEpisodesRepository(
        redisAsync,
        scoreCalculator = { ScoreCalculator().get() },
        ttl = Duration.ofMinutes(1)
    )

    private var count: Int? = null

    @Test
    fun `count items for channel`() = runTest {
        givenUnpublishedEpisodes()
        whenCount()
        thenCountIs(3)
    }

    @Test
    fun `count items for another channel`() = runTest {
        givenUnpublishedEpisodes()
        whenCount("channel_2")
        thenCountIs(1)
    }

    @Test
    fun `count items for channel after deleting one`() = runTest {
        givenUnpublishedEpisodes()
        givenDelete(ChannelMother.ID, EpisodeMother.SEQUENCE_ID)

        whenCount()

        thenCountIs(2)
    }

    @Test
    fun `count items for channel after deleting one from another channel`() = runTest {
        givenUnpublishedEpisodes()
        givenDelete("channel_2", "episode_10")

        whenCount()

        thenCountIs(3)
    }

    @Test
    fun `count items for another channel after deleting one`() = runTest {
        givenUnpublishedEpisodes()
        givenDelete("channel_2", "episode_10")

        whenCount("channel_2")

        thenCountIs(0)
    }

    @Test
    fun `count items for another channel after deleting all`() = runTest {
        givenUnpublishedEpisodes()
        repository.deleteAll(ChannelMother.ID)

        whenCount()

        thenCountIs(0)
    }

    private suspend fun givenUnpublishedEpisodes() {
        repository.add(buildUnpublishedEpisode())
        repository.add(buildUnpublishedEpisode()) // repeated
        repository.add(buildUnpublishedEpisode(episodeId = "episode_2"))
        repository.add(buildUnpublishedEpisode(episodeId = "episode_3"))
        repository.add(buildUnpublishedEpisode(channelId = "channel_2", episodeId = "episode_10"))
    }

    private suspend fun givenDelete(channelId: String, episodeId: String) {
        repository.delete(ChannelUnpublishedEpisode(channelId, episodeId))
    }

    private suspend fun whenCount(channelId: String = ChannelMother.ID) {
        count = repository.count(channelId)
    }

    private fun thenCountIs(count: Int) {
        assertThat(this.count).isEqualTo(count)
    }
}

private class ScoreCalculator {
    var score = 1

    fun get(): Double {
        return score.toDouble().also {
            this.score++
        }
    }
}
