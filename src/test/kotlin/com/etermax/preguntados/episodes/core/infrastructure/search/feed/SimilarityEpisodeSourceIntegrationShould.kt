package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus.PENDING
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus.PUBLISHED
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType.PRIVATE
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType.PUBLIC
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import com.etermax.preguntados.external.services.core.domain.api.profile.Language.EN
import com.etermax.preguntados.external.services.core.domain.api.profile.Language.ES
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime.now
import kotlin.math.abs
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class SimilarityEpisodeSourceIntegrationShould :
    AbstractOpenSearchSourceIntegrationShould(indexDescriptor = "opensearch/mapping/episodes-index-mapping-v1.0.json") {

    protected lateinit var source: Source<String>

    @BeforeEach
    fun setUp() {
        source = SimilarityEpisodeSource(osClient, indexName)
    }

    @Test
    fun `retrieve episodes by similarity`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i))
        }

        val result = whenRetrieve(offset = 0, limit = 5, reference = "E#1", context = listOf())

        thenSameElements(listOf("1", "2", "3", "4", "5"), result.items)
    }

    @Test
    fun `retrieve episodes by similarity excluding context`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i))
        }

        val result = whenRetrieve(offset = 0, limit = 5, reference = "E#1", context = listOf("E#1", "E#2"))

        thenSameElements(listOf("3", "4", "5"), result.items)
    }

    @Test
    fun `retrieve episodes by similarity excluding context and respecting limit`() = runTest {
        (1..10).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i))
        }

        val result = whenRetrieve(offset = 0, limit = 2, reference = "E#1", context = listOf("E#1", "E#2"))

        assertEquals(2, result.size())
    }

    @Test
    fun `retrieve episodes by similarity excluding context and respecting offset`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i))
        }

        val result = whenRetrieve(offset = 2, limit = 5, reference = "E#1", context = listOf("E#1", "E#2"))

        thenSameElements(listOf("5"), result.items)
    }

    @Test
    fun `retrieve episodes by similarity excluding context and respecting offset and limit`() = runTest {
        (1..10).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(1))
        }

        val result = whenRetrieve(offset = 2, limit = 2, reference = "E#1", context = listOf("E#1", "E#2"))

        thenSameElements(listOf("5", "6"), result.items)
        assertEquals(OffsetFetchCursor(4), result.fetchCursor)
    }

    @Test
    fun `retrieve episodes by similarity excluding context and respecting offset and limit and fetch cursor`() =
        runTest {
            (1..10).forEach { i ->
                givenAnEpisode(id = i.toString(), embedding = embedding(1))
            }

            val result = whenRetrieve(offset = 2, limit = 2, reference = "E#1", context = listOf("E#1", "E#2"))

            val nextResult = whenRetrieve(
                offset = 0,
                limit = 2,
                reference = "E#1",
                context = listOf("E#1", "E#2"),
                fetchCursor = result.fetchCursor
            )

            thenSameElements(listOf("7", "8"), nextResult.items)
            assertEquals(OffsetFetchCursor(6), nextResult.fetchCursor)
        }

    @Test
    fun `retrieve an specified quantity of episodes till exhausted`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(1))
        }

        // Try to retrieve episodes (more than available)
        val result = whenRetrieve(
            offset = 0,
            limit = 10,
            reference = "E#1",
            context = listOf("E#1", "E#2")
        )

        // Should return all 5 episodes
        assertEquals(listOf("3", "4", "5"), result.items)
        assertTrue((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `retrieve episodes by similarity filtering by language`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i), language = EN)
        }

        (6..10).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i), language = ES)
        }

        val result =
            whenRetrieve(offset = 0, limit = 5, reference = "E#1", context = listOf("E#1", "E#2"), language = EN)

        thenSameElements(listOf("3", "4", "5"), result.items)
    }

    @Test
    fun `retrieve episodes by similarity filtering by type PUBLIC`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i), type = PUBLIC)
        }

        (6..10).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i), type = PRIVATE)
        }

        val result = whenRetrieve(offset = 0, limit = 5, reference = "E#1", context = listOf("E#1", "E#2"))

        thenSameElements(listOf("3", "4", "5"), result.items)
    }

    @Test
    fun `retrieve episodes by similarity filtering by status PUBLISHED`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i), status = PUBLISHED)
        }

        (6..10).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i), status = PENDING)
        }

        val result = whenRetrieve(offset = 0, limit = 5, reference = "E#1", context = listOf("E#1", "E#2"))

        thenSameElements(listOf("3", "4", "5"), result.items)
    }

    @Test
    fun `not retrieve episodes created in the last 7 days`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i), language = EN, startDate = now().minusDays(1))
        }
        (6..10).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(i), language = ES)
        }

        val result = whenRetrieve(offset = 0, limit = 5, reference = "E#6", context = listOf("E#6"), language = ES)

        thenSameElements((7..10).map { it.toString() }, result.items)
    }

    private suspend fun whenRetrieve(
        offset: Int = 0,
        limit: Int = 10,
        language: Language? = null,
        reference: String,
        context: List<String>,
        fetchCursor: FetchCursor? = null
    ): SourceResponse<String> = source.fetch(
        SimilarityEpisodeSourceRequest(
            OffsetLimit(
                offset = offset,
                limit = limit
            ),
            userId = 1,
            language = language,
            reference = reference,
            context = context,
            fetchCursor = fetchCursor
        )
    )

    fun embedding(seed: Int = 1, size: Int = 384): FloatArray {
        val random = Random(seed)
        val vector = List(size) { random.nextDouble(-1.0, 1.0) }
        val norm1 = vector.sumOf { abs(it) }
        return vector.map { (it / norm1).toFloat() }.toFloatArray()
    }
}
