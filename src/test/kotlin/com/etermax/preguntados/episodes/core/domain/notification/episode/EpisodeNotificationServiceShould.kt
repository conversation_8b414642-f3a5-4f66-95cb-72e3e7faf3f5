package com.etermax.preguntados.episodes.core.domain.notification.episode

import com.etermax.preguntados.episodes.core.domain.episode.notification.EpisodeNotificationService
import com.etermax.preguntados.episodes.core.domain.episode.notification.EventGroupService
import com.etermax.preguntados.episodes.core.domain.eteragent.EterAgentRepository
import com.etermax.preguntados.episodes.core.domain.notification.NotificationConfiguration
import com.etermax.preguntados.episodes.core.domain.notification.NotificationsConfiguration
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import com.etermax.preguntados.external.services.core.domain.clerk.ClerkService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class EpisodeNotificationServiceShould {

    private var config: NotificationsConfiguration = NotificationsConfiguration(
        played = NotificationConfiguration(true, "played_single", "payed_plural"),
        liked = NotificationConfiguration(true, "liked_single", "liked_plural"),
        inviterCanvasId = INVITER_CANVAS_ID,
        creatorToPlayersCanvasId = CREATOR_TO_PLAYERS_CANVAS_ID
    )

    private lateinit var eterAgentRepository: EterAgentRepository
    private lateinit var profileService: ProfileService
    private lateinit var clerkService: ClerkService
    private lateinit var service: EpisodeNotificationService

    @BeforeEach
    fun setUp() {
        profileService = mockk()
        coEvery { profileService.find(SENDER_ID) } returns ProfileMother.aProfile(SENDER_ID)

        eterAgentRepository = mockk()
        coEvery { eterAgentRepository.find(RECEIVER_ID) } returns VALID_VERSION_ETERAGENT

        val configProvider = { config }

        val eventGroupService = mockk<EventGroupService>()
        coEvery { eventGroupService.onEvent(any(), any(), any()) } returns 1
        coEvery { eventGroupService.reset(any(), any(), any()) } returns Unit

        clerkService = mockk(relaxed = true)

        service = EpisodeNotificationService(
            profileService,
            eventGroupService,
            clerkService,
            UnconfinedTestDispatcher(),
            configProvider
        )
    }

    @Test
    fun `send notification`() = runTest {
        service.notifyPlayed(SENDER_ID, RECEIVER_ID, EPISODE_ID)
        coVerify(exactly = 1) { clerkService.sendNotification(any(), any()) }
    }

    @Test
    fun `does not send notification when disabled`() = runTest {
        config = NotificationsConfiguration(
            played = NotificationConfiguration(false, "played_single", "payed_plural"),
            liked = NotificationConfiguration(false, "liked_single", "liked_plural"),
            INVITER_CANVAS_ID,
            CREATOR_TO_PLAYERS_CANVAS_ID
        )

        service.notifyPlayed(SENDER_ID, RECEIVER_ID, EPISODE_ID)
        coVerify(exactly = 0) { clerkService.sendNotification(any(), any()) }
    }

    @Test
    fun `send inviter notification`() = runTest {
        service.notifyInviter(INVITER_ID, PLAYER_NAME, EPISODE_ID, PLAYER_PROFILE)

        coVerify(exactly = 1) {
            clerkService.sendNotification(
                listOf(INVITER_ID),
                mapOf(
                    "canvas_id" to INVITER_CANVAS_ID,
                    "player_name" to PLAYER_NAME,
                    "episode_id" to EPISODE_ID,
                    "profiles" to """[{"id":"12","handle":"playerName","avatar_url":"photoUrl"}]""",
                    "preview_type" to "EPISODE",
                    "preview_id" to EPISODE_ID
                )
            )
        }
    }

    @Test
    fun `send creator to players notification`() = runTest {
        service.notifyPlayersAtEpisodeCreation(PLAYERS, PLAYER_PROFILE, EPISODE_ID)

        coVerify(exactly = 1) {
            clerkService.sendNotification(
                PLAYERS,
                mapOf(
                    "canvas_id" to CREATOR_TO_PLAYERS_CANVAS_ID,
                    "sender_name" to PLAYER_PROFILE.name,
                    "episode_id" to EPISODE_ID,
                    "profiles" to """[{"id":"12","handle":"playerName","avatar_url":"photoUrl"}]""",
                    "preview_type" to "EPISODE",
                    "preview_id" to EPISODE_ID
                )
            )
        }
    }

    @Test
    fun `do not send creator to players notification if there are no players`() = runTest {
        service.notifyPlayersAtEpisodeCreation(emptyList(), PLAYER_PROFILE, EPISODE_ID)

        coVerify(exactly = 0) { clerkService.sendNotification(any(), any()) }
    }

    private companion object {
        const val SENDER_ID = 1L
        const val RECEIVER_ID = 2L
        const val EPISODE_ID = "100"
        const val INVITER_ID = 123L
        const val PLAYER_NAME = "playerName"
        const val PLAYER_ID = 12L
        val PLAYERS = listOf(1L, 2L, 3L)
        val PLAYER_PROFILE = Profile(
            PLAYER_ID,
            PLAYER_NAME,
            "AR",
            "photoUrl",
            null,
            OffsetDateTime.now(),
            null
        )

        const val VALID_VERSION_ETERAGENT = "1|And-And|Google Pixel 3 XL|0|Android 28|0|3.287.0|en|en|US|1"
        const val INVITER_CANVAS_ID = "inviter_canvas_id"
        const val CREATOR_TO_PLAYERS_CANVAS_ID = "creator_to_players_canvas_id"
    }
}
