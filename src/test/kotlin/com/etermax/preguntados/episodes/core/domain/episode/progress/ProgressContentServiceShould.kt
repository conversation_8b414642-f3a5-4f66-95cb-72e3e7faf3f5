package com.etermax.preguntados.episodes.core.domain.episode.progress

import com.etermax.preguntados.episodes.core.EpisodeMother.LANGUAGE
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ProgressContentServiceShould {

    private lateinit var progressContentRepository: ProgressContentRepository
    private lateinit var service: ProgressContentService

    private var playerProgress: ProgressContent? = null

    @BeforeEach
    fun setUp() {
        progressContentRepository = mockk(relaxed = true)
        service = ProgressContentService(progressContentRepository)
    }

    @Test
    fun `register progress content`() = runTest {
        whenRegister(CONTENT_ID)

        thenContentSavedIs(CONTENT_ID)
    }

    @Test
    fun `register another progress content`() = runTest {
        whenRegister(ANOTHER_CONTENT_ID)

        thenContentSavedIs(ANOTHER_CONTENT_ID)
    }

    @Test
    fun `find existing player progress`() = runTest {
        givenAPlayerProgress()

        whenFind()

        thenProgressContentIsReturnedWith(CONTENT_ID)
    }

    @Test
    fun `find not existing player progress`() = runTest {
        givenAPlayerProgress(lastContentId = null)

        whenFind()

        thenProgressContentIsReturnedWith(null)
    }

    private suspend fun givenAPlayerProgress(lastContentId: String? = CONTENT_ID, hasFinishedEpisode: Boolean = false) {
        val progressContent = ProgressContent(EPISODE_ID, PLAYER_ID, lastContentId, LANGUAGE.name, hasFinishedEpisode)
        coEvery { progressContentRepository.findBy(any(), any()) } returns progressContent
    }

    private suspend fun whenRegister(contentId: String) {
        service.registerProgress(EPISODE_ID, PLAYER_ID, contentId, LANGUAGE.name, false)
    }

    private suspend fun whenFind() {
        playerProgress = service.findProgress(EPISODE_ID, PLAYER_ID)
    }

    private fun thenContentSavedIs(contentId: String?, isFinishedEpisode: Boolean = false) {
        val progressContent = ProgressContent(EPISODE_ID, PLAYER_ID, contentId, LANGUAGE.name, isFinishedEpisode)
        coVerify(exactly = 1) { progressContentRepository.save(progressContent) }
    }

    private fun thenProgressContentIsReturnedWith(contentId: String?) {
        val expectedProgressContent = ProgressContent(EPISODE_ID, PLAYER_ID, contentId, LANGUAGE.name)
        assertThat(playerProgress).isEqualTo(expectedProgressContent)
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "ABC-123"
        const val CONTENT_ID = "9876543"
        const val ANOTHER_CONTENT_ID = "123456"
    }
}
