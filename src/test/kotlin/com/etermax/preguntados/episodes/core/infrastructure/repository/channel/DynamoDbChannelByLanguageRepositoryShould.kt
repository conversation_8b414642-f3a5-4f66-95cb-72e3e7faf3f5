package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother.ANOTHER_PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.MODIFICATION_DATE
import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.aChannel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelReduced
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelByLanguageFilters
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.ChannelIndexes
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ByOwnerChannelReducedItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoDbChannelByLanguageRepositoryShould {
    private lateinit var repository: DynamoDbChannelByLanguageRepository
    private lateinit var channelRepository: DynamoDBChannelRepository

    private var paginationResult: PaginatedItems<ChannelReduced>? = null

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()

    @BeforeEach
    fun setUp() {
        val table = createTable()
        repository = DynamoDbChannelByLanguageRepository(dynamoDbEnhancedClient, table, byOwnerReducedIndex())
        channelRepository = DynamoDBChannelRepository(dynamoDbEnhancedClient, table)

        paginationResult = null
    }

    @Test
    fun `return channels for language ES`() = runTest {
        givenChannels()
        whenSearch(Language.ES)
        thenAllReturnsSpanishChannels()
    }

    @Test
    fun `return no channels for language ES from another player`() = runTest {
        givenChannels()
        whenSearch(Language.ES, playerId = ANOTHER_PLAYER_ID)
        thenReturnsNoChannel()
    }

    @Test
    fun `return channels for language ES including with no language`() = runTest {
        givenChannels()
        whenSearch(Language.ES, includeWithNoLanguage = true)
        thenReturnsSpanishChannelsPlusWithNoLanguage()
    }

    @Test
    fun `return no channels for language ES when limited`() = runTest {
        givenChannels()
        whenSearch(Language.ES, itemsSize = 1)
        thenReturnsSingleSpanishChannels()
    }

    @Test
    fun `return channels for language ES when limited`() = runTest {
        givenChannels()
        whenSearch(Language.ES, itemsSize = 2)
        thenAllReturnsSpanishChannelsLimited()
    }

    @Test
    fun `return channels for language ES with exceeded limit`() = runTest {
        givenChannels()
        whenSearch(Language.ES, itemsSize = 4)
        thenAllReturnsSpanishChannels()
    }

    @Test
    fun `return channels for language ES from last evaluated key`() = runTest {
        givenChannels()
        whenSearch(Language.ES, lastEvaluatedKey = MODIFICATION_DATE.plusDays(1).toMillis().toString())
        thenReturnsSpanishChannelsFromLastEvaluatedKey()
    }

    @Test
    fun `return channels for language FR`() = runTest {
        givenChannels()
        whenSearch(Language.FR)
        thenReturnsFrenchChannels()
    }

    @Test
    fun `return channels for language FR from another player`() = runTest {
        givenChannels()
        whenSearch(Language.FR, playerId = ANOTHER_PLAYER_ID)
        thenReturnsFrenchChannelsFromAnotherPlayer()
    }

    @Test
    fun `return no channels for language EN`() = runTest {
        givenChannels()
        whenSearch(Language.EN)
        thenReturnsNoChannel()
    }

    @Test
    fun `return no channels for language EN but including with no language`() = runTest {
        givenChannels()
        whenSearch(Language.EN, includeWithNoLanguage = true)
        thenReturnsOnlyChannelsWithNoLanguage()
    }

    private suspend fun givenChannels() {
        channelRepository.add(
            aChannel(
                id = "channel_1_es",
                language = Language.ES,
                lastModificationDate = MODIFICATION_DATE
            )
        )
        channelRepository.add(
            aChannel(
                id = "channel_2_es",
                language = Language.ES,
                lastModificationDate = MODIFICATION_DATE.plusDays(1)
            )
        )
        channelRepository.add(
            aChannel(
                id = "channel_3_fr",
                language = Language.FR,
                lastModificationDate = MODIFICATION_DATE.plusDays(2)
            )
        )
        channelRepository.add(
            aChannel(
                id = "channel_4_fr",
                language = Language.FR,
                ownerId = ANOTHER_PLAYER_ID,
                lastModificationDate = MODIFICATION_DATE.plusDays(3)
            )
        )
        channelRepository.add(
            aChannel(
                id = "channel_5_null",
                language = null,
                lastModificationDate = MODIFICATION_DATE.plusDays(4)
            )
        )
        channelRepository.add(
            aChannel(
                id = "channel_6_null",
                language = null,
                lastModificationDate = MODIFICATION_DATE.plusDays(5)
            )
        )
        channelRepository.add(
            aChannel(
                id = "channel_7_null",
                language = null,
                ownerId = ANOTHER_PLAYER_ID,
                lastModificationDate = MODIFICATION_DATE.plusDays(6)
            )
        )
    }

    private suspend fun whenSearch(
        language: Language,
        playerId: Long = PLAYER_ID,
        includeWithNoLanguage: Boolean = false,
        itemsSize: Int = 10,
        lastEvaluatedKey: String? = null
    ) {
        val filters = ChannelByLanguageFilters(playerId, language, includeWithNoLanguage)
        val pagination = PaginationFilter(itemsSize, lastEvaluatedKey)
        paginationResult = repository.search(filters, pagination)
    }

    private fun thenAllReturnsSpanishChannelsLimited() {
        assertThat(paginationResult!!.lastEvaluatedKey).isEqualTo(MODIFICATION_DATE.toMillis().toString())
        assertThat(paginationResult!!.items.map { it.id }).containsExactly("channel_2_es", "channel_1_es")
    }

    private fun thenAllReturnsSpanishChannels() {
        assertThat(paginationResult!!.lastEvaluatedKey).isNull()
        assertThat(paginationResult!!.items.map { it.id }).containsExactly("channel_2_es", "channel_1_es")
    }

    private fun thenReturnsSingleSpanishChannels() {
        assertThat(paginationResult!!.lastEvaluatedKey).isEqualTo(MODIFICATION_DATE.plusDays(1).toMillis().toString())
        assertThat(paginationResult!!.items.map { it.id }).containsOnly("channel_2_es")
    }

    private fun thenReturnsSpanishChannelsFromLastEvaluatedKey() {
        assertThat(paginationResult!!.lastEvaluatedKey).isNull()
        assertThat(paginationResult!!.items.map { it.id }).containsOnly("channel_1_es")
    }

    private fun thenReturnsSpanishChannelsPlusWithNoLanguage() {
        assertThat(paginationResult!!.lastEvaluatedKey).isNull()
        assertThat(paginationResult!!.items.map { it.id }).containsExactly(
            "channel_6_null",
            "channel_5_null",
            "channel_2_es",
            "channel_1_es"
        )
    }

    private fun thenReturnsFrenchChannels() {
        assertThat(paginationResult!!.lastEvaluatedKey).isNull()
        assertThat(paginationResult!!.items.map { it.id }).containsOnly("channel_3_fr")
    }

    private fun thenReturnsNoChannel() {
        assertThat(paginationResult!!.lastEvaluatedKey).isNull()
        assertThat(paginationResult!!.items).isEmpty()
    }

    private fun thenReturnsFrenchChannelsFromAnotherPlayer() {
        assertThat(paginationResult!!.lastEvaluatedKey).isNull()
        assertThat(paginationResult!!.items.map { it.id }).containsOnly("channel_4_fr")
    }

    private fun thenReturnsOnlyChannelsWithNoLanguage() {
        assertThat(paginationResult!!.lastEvaluatedKey).isNull()
        assertThat(paginationResult!!.items.map { it.id }).containsExactly(
            "channel_6_null",
            "channel_5_null"
        )
    }

    private fun createTable(): DynamoDbAsyncTable<ChannelItem> {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(dynamoDbTestServer.buildAsyncClient()).build()
            .table(TABLE_NAME, TableSchema.fromBean(ChannelItem::class.java))
    }

    private fun byOwnerReducedIndex(): DynamoDbAsyncIndex<ByOwnerChannelReducedItem> {
        return dynamoDbEnhancedClient.table(
            TABLE_NAME,
            TableSchema.fromBean(
                ByOwnerChannelReducedItem::class.java
            )
        )
            .index(ChannelIndexes.BY_OWNER_AND_LAST_MODIFICATION)
    }

    private companion object {
        const val TABLE_NAME = "dev_channels"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(ChannelItem::class.java to TABLE_NAME))
    }
}
