package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.infrastructure.search.filter.DuplicateFilter
import com.etermax.preguntados.episodes.core.infrastructure.search.filter.InMemoryFilterDataRepository
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import kotlin.math.abs
import kotlin.random.Random
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class MergedSimilaritySourceIntegrationShould :
    AbstractOpenSearchSourceIntegrationShould(indexDescriptor = "opensearch/mapping/episodes-index-mapping-v1.0.json") {

    private lateinit var source: Source<String>

    @Test
    fun `retrieve episodes by similarity from each historical view`() = runTest {
        (1..3).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(1))
        }

        (10..12).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(10))
        }

        (20..22).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(20))
        }

        givenASourceWithHistory(listOf("E#1", "E#10", "E#20"))

        val result = whenRetrieve(offset = 0, limit = 6, userId = 1)

        thenSameElements(listOf("2", "3", "11", "12", "21", "22"), result.items)
    }

    @Test
    fun `retrieve episodes by similarity from each historical view respecting offset and limit`() = runTest {
        (1..3).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(1))
        }

        (10..12).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(10))
        }

        (20..22).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(20))
        }

        givenASourceWithHistory(listOf("E#1", "E#10", "E#20"))

        val result = whenRetrieve(offset = 2, limit = 2, userId = 1)

        thenSameElements(listOf("3", "12"), result.items)
    }

    @Test
    fun `retrieve episodes by similarity from each historical view respecting fetch cursor`() = runTest {
        (1..3).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(1))
        }

        (10..12).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(10))
        }

        (20..22).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(20))
        }

        givenASourceWithHistory(listOf("E#1", "E#10", "E#20"))

        val result = whenRetrieve(offset = 0, limit = 2, userId = 1)

        val nextResult = whenRetrieve(
            offset = 0,
            limit = 2,
            userId = 1,
            fetchCursor = result.fetchCursor
        )

        thenSameElements(listOf("3", "12"), nextResult.items)
    }

    @Test
    fun `retrieve episodes by similarity from each historical view respecting fetch cursor till exhausted`() = runTest {
        (1..3).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(1))
        }

        (10..12).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(10))
        }

        (20..22).forEach { i ->
            givenAnEpisode(id = i.toString(), embedding = embedding(20))
        }

        givenASourceWithHistory(listOf("E#1", "E#10", "E#20"))

        val result = whenRetrieve(offset = 0, limit = 3, userId = 1)
        thenSameElements(listOf("2", "11", "21"), result.items)
        assertFalse((result.fetchCursor as MixedSimilarityFetchCursor).exhausted)

        val nextResult = whenRetrieve(offset = 0, limit = 3, userId = 1, fetchCursor = result.fetchCursor)
        thenSameElements(listOf("3", "12", "22"), nextResult.items)
        assertFalse((nextResult.fetchCursor as MixedSimilarityFetchCursor).exhausted)

        // It is not exhausted because each similarity source can fetch all episodes from the index except the referenced episode
        val thirdResult = whenRetrieve(offset = 0, limit = 3, userId = 1, fetchCursor = nextResult.fetchCursor)
        thenSameElements(emptyList(), thirdResult.items)
        assertTrue((thirdResult.fetchCursor as MixedSimilarityFetchCursor).exhausted)
    }

    fun givenASourceWithHistory(history: List<String>) {
        source = MergedSimilaritySource(
            source = FilteredSource(
                SimilarityEpisodeSource(osClient, indexName),
                DuplicateFilter(
                    "test",
                    InMemoryFilterDataRepository()
                )
            ),
            history = history
        )
    }

    private suspend fun whenRetrieve(
        offset: Int = 0,
        limit: Int = 10,
        userId: Long = 1,
        language: Language? = null,
        fetchCursor: FetchCursor? = null
    ): SourceResponse<String> = source.fetch(
        SourceRequest(
            OffsetLimit(
                offset = offset,
                limit = limit
            ),
            userId = userId,
            language = language,
            fetchCursor = fetchCursor
        )
    )

    fun embedding(seed: Int = 1, size: Int = 384): FloatArray {
        val random = Random(seed)
        val vector = List(size) { random.nextDouble(-1.0, 1.0) }
        val norm1 = vector.sumOf { abs(it) }
        return vector.map { (it / norm1).toFloat() }.toFloatArray()
    }
}
