package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.InMemoryRecentSearchRepository
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.OpenSearchPlayedRepository
import com.etermax.preguntados.episodes.core.infrastructure.search.data.EmbeddingEpisodeData
import com.etermax.preguntados.episodes.core.infrastructure.search.data.EpisodeData
import com.etermax.preguntados.episodes.core.infrastructure.search.filter.DefaultFilterFactory
import com.etermax.preguntados.episodes.core.infrastructure.search.filter.InMemoryFilterDataRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch.core.MgetRequest
import org.opensearch.client.opensearch.core.MgetResponse
import org.opensearch.client.opensearch.core.MsearchRequest
import org.opensearch.client.opensearch.core.MsearchResponse
import org.opensearch.client.opensearch.core.SearchRequest
import org.opensearch.client.opensearch.core.SearchResponse
import org.opensearch.client.opensearch.core.mget.MultiGetResponseItem
import org.opensearch.client.opensearch.core.msearch.MultiSearchItem
import org.opensearch.client.opensearch.core.msearch.MultiSearchResponseItem
import org.opensearch.client.opensearch.core.search.Hit
import org.opensearch.client.opensearch.core.search.HitsMetadata
import java.util.concurrent.CompletableFuture
import kotlin.test.assertEquals

class FeedSearchShould {

    private lateinit var feedSearch: FeedSearch
    private lateinit var playedSearchRepository: OpenSearchPlayedRepository
    private lateinit var recentSearch: RecentSearch
    private lateinit var feedFallbackSearch: FeedFallbackSearch
    private lateinit var filterProvider: DefaultFilterFactory
    private lateinit var client: OpenSearchAsyncClient
    private lateinit var tokenizer: Tokenizer
    private lateinit var recentOffsetRepository: InMemoryRecentSearchRepository
    private lateinit var osClient: OpenSearchAsyncClient

    private lateinit var params: SearchParameters
    private lateinit var result: List<Episode>
    private var match: Boolean = false
    private lateinit var split: SearchSplit
    private val playerId = 1L

    @BeforeEach
    fun setUp() {
        playedSearchRepository = mockk()
        feedFallbackSearch = mockk()
        osClient = mockk()
        filterProvider = DefaultFilterFactory(
            repository = InMemoryFilterDataRepository(),
            osClient = osClient,
            episodesIndexName = EPISODES_INDEX_NAME
        )
        client = mockk(relaxed = true)
        recentSearch = RecentSearch(client, "", Tokenizer())
        tokenizer = mockk()
        recentOffsetRepository = InMemoryRecentSearchRepository()

        feedSearch = FeedSearch(
            tokenizer = tokenizer,
            playedSearchRepository = playedSearchRepository,
            recentSearch = recentSearch,
            fallbackSearch = feedFallbackSearch,
            filterFactory = filterProvider,
            recentOffsetRepository = recentOffsetRepository,
            client = client,
            indexName = INDEX_NAME,
            lastPlayedThreshold = LAST_PLAYED_THRESHOLD
        )
    }

    @Test
    fun `match when sort is null and name is blank`() = runTest {
        givenParameters(sort = null, name = "")

        whenMatch()

        thenShouldMatch()
    }

    @Test
    fun `not match when sort is not null`() = runTest {
        givenParameters(sort = SortEpisode.RECENT, name = "")

        whenMatch()

        thenShouldNotMatch()
    }

    @Test
    fun `not match when name is not blank`() = runTest {
        givenParameters(sort = null, name = "test")

        whenMatch()

        thenShouldNotMatch()
    }

    @Test
    fun `return all search results when history is below threshold`() = runTest {
        givenParameters()
        givenNoHistory()
        givenAllSearchResults(EPISODES)

        whenSearch()

        thenShouldReturnAllSearchResults()
    }

    @Test
    fun `return mixed similar and recent episodes when history is above threshold`() = runTest {
        givenParameters()
        givenOffsetAndLimit(0, 10)
        givenHistoryAboveThreshold()

        givenResponses()

        val actualResult = whenSearch()

        val expectedRecents = RECENT_EPISODES.subList(params.offset, params.limit - SIMILAR_EPISODES.size)
        val expected = SIMILAR_EPISODES + expectedRecents

        thenShouldReturnMixedResults(actualResult, expected)
    }

    @Test
    fun `return similar and recent episodes having history and offset != 0`() = runTest {
        val recentsOffset = 5
        recentOffsetRepository.save(playerId, recentsOffset)
        givenParameters()
        givenOffsetAndLimit(3, 10)
        givenHistoryAboveThreshold()

        givenResponses()

        val actualResult = whenSearch()

        val recentEpisodesLimit = recentsOffset + params.limit - SIMILAR_EPISODES.size
        val expectedRecents = RECENT_EPISODES.subList(recentsOffset, recentEpisodesLimit)
        val expected = SIMILAR_EPISODES + expectedRecents

        thenShouldReturnMixedResults(actualResult, expected)

        val storedOffset = recentOffsetRepository.find(playerId)
        assertEquals(storedOffset, recentEpisodesLimit)
    }

    @Test
    fun `return similar and recent episodes having history and repeated owner on recent episodes`() = runTest {
        RECENT_EPISODES = listOf(
            buildEpisode(id = "withUniqueOwner1", ownerId = 1),
            buildEpisode(id = "withUniqueOwner2", ownerId = 2),
            buildEpisode(id = "withUniqueOwner3", ownerId = 3),
            buildEpisode(id = "withUniqueOwner4", ownerId = 4),
            buildEpisode(id = "withUniqueOwner5", ownerId = 5),
            buildEpisode(id = "withUniqueOwner6", ownerId = 6),
            buildEpisode(id = "withUniqueOwner7", ownerId = 7),
            buildEpisode(id = "withUniqueOwner8", ownerId = 8),
            buildEpisode(id = "withUniqueOwner9", ownerId = 9),
            buildEpisode(id = "withUniqueOwner10", ownerId = 10),
            buildEpisode(id = "withRepeatedOwner1", ownerId = 1),
            buildEpisode(id = "withRepeatedOwner2", ownerId = 2),
            buildEpisode(id = "withRepeatedOwner3", ownerId = 3),
            buildEpisode(id = "withRepeatedOwner4", ownerId = 4),
            buildEpisode(id = "withRepeatedOwner5", ownerId = 5),
            buildEpisode(id = "withRepeatedOwner6", ownerId = 6),
            buildEpisode(id = "withRepeatedOwner7", ownerId = 7),
            buildEpisode(id = "withRepeatedOwner8", ownerId = 8),
            buildEpisode(id = "withRepeatedOwner9", ownerId = 9),
            buildEpisode(id = "withRepeatedOwner10", ownerId = 10)
        )
        val recentsOffset = 5
        recentOffsetRepository.save(playerId, recentsOffset)
        givenParameters()
        givenOffsetAndLimit(3, 20)
        givenHistoryAboveThreshold()

        givenResponses()

        val actualResult = whenSearch()

        val expectedRecents = listOf(
            // Starts from 6th due to saved recentOffset while scrolling
            buildEpisode(id = "withUniqueOwner6", ownerId = 6),
            buildEpisode(id = "withUniqueOwner7", ownerId = 7),
            buildEpisode(id = "withUniqueOwner8", ownerId = 8),
            buildEpisode(id = "withUniqueOwner9", ownerId = 9),
            buildEpisode(id = "withUniqueOwner10", ownerId = 10),
            buildEpisode(id = "withRepeatedOwner1", ownerId = 1),
            buildEpisode(id = "withRepeatedOwner2", ownerId = 2),
            buildEpisode(id = "withRepeatedOwner3", ownerId = 3),
            buildEpisode(id = "withRepeatedOwner4", ownerId = 4),
            buildEpisode(id = "withRepeatedOwner5", ownerId = 5)
            // It ignores the rest because it already has episodes with owners from 6 to 10
        )
        val expected = SIMILAR_EPISODES + expectedRecents

        thenShouldReturnMixedResults(actualResult, expected)
        val storedOffset = recentOffsetRepository.find(playerId)!!
        assertEquals(RECENT_EPISODES.size, storedOffset)
    }

    @Test
    fun `split offset and limit correctly starting from offset 10`() = runTest {
        givenParameters()
        givenOffsetAndLimit(10, 20)
        givenRecentsOffset(30)

        whenSplitOffsetAndLimit()

        val similarOffset = 7 // 10 * 0.75
        val similarLimit = 15 // 20 * 0.75
        val storedRecentsOffset = 30 // It really can be different from the offset send by the app
        val recentLimit = 5 // 20 - 15
        thenShouldSplitCorrectly(similarOffset, similarLimit, storedRecentsOffset, recentLimit)
    }

    @Test
    fun `split offset and limit correctly starting from offset 0`() = runTest {
        givenParameters()
        givenOffsetAndLimit(0, 20)
        givenRecentsOffset(30)

        whenSplitOffsetAndLimit()

        val similarOffset = 0 // 0 * 0.75
        val similarLimit = 15 // 20 * 0.75
        val recentOffset = 0 // It starts from zero with a fresh feed navigation
        val recentLimit = 5 // 20 - 15
        thenShouldSplitCorrectly(similarOffset, similarLimit, recentOffset, recentLimit)
    }

    @Test
    fun `return consistent results across multiple requests`() = runTest {
        givenParameters()
        givenHistoryAboveThreshold()
        givenResponses()

        val firstResult = feedSearch.search(params)
        val secondResult = feedSearch.search(params)

        thenResultsAreConsistent(firstResult, secondResult)
    }

    private fun givenParameters(
        sort: SortEpisode? = null,
        name: String = "",
        offset: Int = 0,
        limit: Int = 10
    ) {
        params = SearchParameters(
            playerId = playerId,
            language = null,
            country = null,
            name = name,
            sort = sort,
            offset = offset,
            limit = limit
        )
    }

    private fun givenNoHistory() {
        coEvery { playedSearchRepository.lastPlayedEpisodesIds(any(), any(), any()) } returns emptyList()
    }

    private fun givenHistoryAboveThreshold() {
        val historyEpisodes = List(LAST_PLAYED_THRESHOLD) { "E#$it" }
        coEvery { playedSearchRepository.lastPlayedEpisodesIds(any(), any(), any()) } returns historyEpisodes
        coEvery {
            client.mget(
                any<MgetRequest>(),
                any<Class<EmbeddingEpisodeData>>()
            )
        } returns CompletableFuture.completedFuture(buildMgetResponse(historyEpisodes))
    }

    private fun givenAllSearchResults(episodes: List<Episode>) {
        coEvery { feedFallbackSearch.search(any()) } returns episodes
    }

    private fun givenResponses() {
        coEvery {
            client.msearch(any<MsearchRequest>(), EpisodeData::class.java)
        } answers {
            val params = firstArg<MsearchRequest>().searches().last()?.body()!!
            val offset = params.from()!!
            val limit = params.size()!!

            val similarEpisodesForEachPlayed: List<List<Episode>> = List(LAST_PLAYED_THRESHOLD) { SIMILAR_EPISODES }
            val recentEpisodes = episodesSublist(offset, limit, RECENT_EPISODES)

            val episodes: List<List<Episode>> = similarEpisodesForEachPlayed.plus(element = recentEpisodes)
            CompletableFuture.completedFuture(buildMSearchResponse(episodes))
        }

        givenRecentEpisodes(RECENT_EPISODES)
    }

    private fun givenRecentEpisodes(episodes: List<Episode>) {
        coEvery {
            client.search(
                any<SearchRequest>(),
                EpisodeData::class.java
            )
        } answers {
            val params = firstArg<SearchRequest>()
            val offset = params.from()!!
            val limit = params.size()!!
            val episodesSublist = episodesSublist(offset, limit, episodes)
            CompletableFuture.completedFuture(
                buildSearchResponse(episodesSublist)
            )
        }
    }

    private fun episodesSublist(
        offset: Int,
        limit: Int,
        all: List<Episode>
    ): List<Episode> {
        val from = minOf(offset, all.size)
        val to = minOf(offset + limit, all.size)
        return all.subList(from, to)
    }

    private fun givenOffsetAndLimit(offsetParam: Int, limitParam: Int) {
        params = params.copy(
            offset = offsetParam,
            limit = limitParam
        )
    }

    private suspend fun givenRecentsOffset(recentOffset: Int) {
        recentOffsetRepository.save(playerId, recentOffset)
    }

    private suspend fun whenMatch() {
        match = feedSearch.match(params)
    }

    private suspend fun whenSearch(): List<Episode> {
        result = feedSearch.search(params)
        return result
    }

    private suspend fun whenSplitOffsetAndLimit() {
        split = feedSearch.splitOffsetAndLimit(params.offset, params.limit, playerId)
    }

    private fun thenShouldMatch() {
        assertThat(match).isTrue()
    }

    private fun thenShouldNotMatch() {
        assertThat(match).isFalse()
    }

    private fun thenShouldReturnAllSearchResults() {
        assertThat(result).isEqualTo(EPISODES)
        coVerify(exactly = 1) { feedFallbackSearch.search(params) }
    }

    private fun thenShouldReturnMixedResults(actualResult: List<Episode>, expected: List<Episode>) {
        assertThat(actualResult).containsExactlyInAnyOrderElementsOf(expected)
    }

    private fun thenShouldSplitCorrectly(similarOffset: Int, similarLimit: Int, recentOffset: Int, recentLimit: Int) {
        assertThat(split.similarOffset).isEqualTo(similarOffset)
        assertThat(split.similarLimit).isEqualTo(similarLimit)
        assertThat(split.recentOffset).isEqualTo(recentOffset)
        assertThat(split.recentLimit).isEqualTo(recentLimit)
    }

    private fun thenResultsAreConsistent(firstResult: List<Episode>, secondResult: List<Episode>) {
        assertThat(firstResult).isEqualTo(secondResult)
        assertThat(firstResult.map { it.id }).isEqualTo(secondResult.map { it.id })
    }

    private companion object {
        const val INDEX_NAME = "test-index"
        const val EPISODES_INDEX_NAME = "episodes-index-name"
        const val LAST_PLAYED_THRESHOLD = 5

        val EPISODES = listOf(
            buildEpisode(id = "E_123"),
            buildEpisode(id = "E_456")
        )

        val SIMILAR_EPISODES = listOf(
            buildEpisode(id = "similar1"),
            buildEpisode(id = "similar2")
        )

        var RECENT_EPISODES = listOf(
            buildEpisode(id = "recent1", ownerId = 1),
            buildEpisode(id = "recent2", ownerId = 2),
            buildEpisode(id = "recent3", ownerId = 3),
            buildEpisode(id = "recent4", ownerId = 4),
            buildEpisode(id = "recent5", ownerId = 5),
            buildEpisode(id = "recent6", ownerId = 6),
            buildEpisode(id = "recent7", ownerId = 7),
            buildEpisode(id = "recent8", ownerId = 8),
            buildEpisode(id = "recent9", ownerId = 9),
            buildEpisode(id = "recent10", ownerId = 10),
            buildEpisode(id = "recent11", ownerId = 11),
            buildEpisode(id = "recent12", ownerId = 12),
            buildEpisode(id = "recent13", ownerId = 13),
            buildEpisode(id = "recent14", ownerId = 14),
            buildEpisode(id = "recent15", ownerId = 15),
            buildEpisode(id = "recent16", ownerId = 16),
            buildEpisode(id = "recent17", ownerId = 17),
            buildEpisode(id = "recent18", ownerId = 18),
            buildEpisode(id = "recent19", ownerId = 19),
            buildEpisode(id = "recent20", ownerId = 20)
        )

        fun buildMgetResponse(episodes: List<String>): MgetResponse<EmbeddingEpisodeData> {
            val docs = episodes.map { episode ->
                MultiGetResponseItem.Builder<EmbeddingEpisodeData>()
                    .result {
                        it.found(true)
                        it.index(INDEX_NAME)
                        it.id(episode)
                        it.source(EmbeddingEpisodeData(episodeId = episode, embedding = FloatArray(0)))
                    }
                    .build()
            }

            return MgetResponse.Builder<EmbeddingEpisodeData>()
                .docs(docs)
                .build()
        }

        fun buildSearchResponse(episodes: List<Episode>): SearchResponse<EpisodeData> {
            val hits = episodes.map { episode ->
                Hit.Builder<EpisodeData>()
                    .index(INDEX_NAME)
                    .id("E#" + episode.id)
                    .source(
                        EpisodeData(
                            episodeId = episode.id,
                            name = episode.name,
                            language = episode.language.name,
                            country = episode.country.name,
                            cover = episode.cover,
                            banner = episode.banner,
                            ownerId = episode.ownerId,
                            contents = episode.contents,
                            startDate = episode.startDate?.toMillis(),
                            likes = episode.rate.likes,
                            dislikes = episode.rate.dislikes,
                            reports = episode.reports,
                            views = episode.views
                        )
                    )
                    .build()
            }

            return SearchResponse.Builder<EpisodeData>()
                .took(1)
                .timedOut(false)
                .shards {
                    it.failed(0).successful(1).total(1)
                }
                .hits(
                    HitsMetadata.Builder<EpisodeData>()
                        .hits(hits)
                        .build()
                ).build()
        }

        fun buildMSearchResponse(listsOfEpisodes: List<List<Episode>>): MsearchResponse<EpisodeData> {
            val multiSearchItems = listsOfEpisodes.map { episodes ->
                val hits = episodes.map { episode ->
                    val data = EpisodeData(
                        episodeId = episode.id,
                        name = episode.name,
                        language = episode.language.name,
                        country = episode.country.name,
                        cover = episode.cover,
                        banner = episode.banner,
                        ownerId = episode.ownerId,
                        contents = episode.contents,
                        startDate = episode.startDate?.toMillis(),
                        likes = episode.rate.likes,
                        dislikes = episode.rate.dislikes,
                        reports = episode.reports,
                        views = episode.views
                    )

                    Hit.Builder<EpisodeData>()
                        .index(INDEX_NAME)
                        .id("E#" + episode.id)
                        .source(data)
                        .build()
                }

                val hitsMetadata = HitsMetadata.Builder<EpisodeData>().hits(hits).build()
                val searchItem = MultiSearchItem.Builder<EpisodeData>()
                    .hits(hitsMetadata)
                    .took(1)
                    .shards {
                        it.failed(0).successful(1).total(1)
                    }
                    .timedOut(false)
                    .build()
                val responseItem = MultiSearchResponseItem.Builder<EpisodeData>().result(searchItem).build()
                responseItem
            }

            val searchResponse: MsearchResponse<EpisodeData> = MsearchResponse.Builder<EpisodeData>()
                .responses(multiSearchItems)
                .took(5)
                .build()

            return searchResponse
        }
    }
}
