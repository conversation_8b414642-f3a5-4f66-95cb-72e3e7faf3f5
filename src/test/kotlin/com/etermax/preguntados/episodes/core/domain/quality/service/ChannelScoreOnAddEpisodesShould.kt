package com.etermax.preguntados.episodes.core.domain.quality.service

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelScoreRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.quality.likerate.EpisodeLikeRateAvgCalculator
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ChannelScoreOnAddEpisodesShould {

    private lateinit var channelEpisodesRepository: ChannelEpisodesRepository
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var channelRepository: ChannelRepository
    private lateinit var defaultQualityService: DefaultQualityService
    private lateinit var episodeLikeRateAvgCalculator: EpisodeLikeRateAvgCalculator
    private lateinit var channelScoreRepository: ChannelScoreRepository

    @BeforeEach
    fun setUp() {
        channelEpisodesRepository = mockk()
        episodeRepository = mockk()
        channelRepository = mockk()
        episodeLikeRateAvgCalculator = EpisodeLikeRateAvgCalculator()
        channelScoreRepository = mockk()
        defaultQualityService =
            DefaultQualityService(
                channelEpisodesRepository,
                episodeRepository,
                channelRepository,
                episodeLikeRateAvgCalculator,
                channelScoreRepository,
                minimumRates = 30,
                minimumViews = 100
            )
    }

    @Test
    fun `should calculate channel score when user add one episode and meet minimum rate threshold`() = runTest {
        // given
        val episode = givenEpisodeWithRates(EPISODE_ID_1, likes = 19, dislikes = 10) // 65% like rate
        val episodeWithOneMoreLike = givenLikeRatedEpisodeFrom(episode) // Inc likes to 20
        givenRepositoriesWith(episodeWithOneMoreLike)

        // when added 1 episode to channel
        whenToProcessChannelScore(episodes = listOf(episodeWithOneMoreLike))

        // then
        thenChannelScoreIsUpdatedTo(66)
    }

    @Test
    fun `should calculate channel score when user add one episode that meet minimum rate threshold and channel have all valid episodes`() =
        runTest {
            // given
            val episode1 = givenEpisodeWithRates(EPISODE_ID_1, likes = 80, dislikes = 20) // 80% like rate
            val episode2 = givenEpisodeWithRates(EPISODE_ID_2, likes = 60, dislikes = 40) // 60% like rate
            val episode3 = givenEpisodeWithRates(EPISODE_ID_3, likes = 40, dislikes = 60) // 40% like rate
            val episodes = listOf(episode1, episode2, episode3)
            givenRepositoriesWith(episodes)

            // when added 1 episode to channel
            whenToProcessChannelScore(episodes = listOf(episode1))

            // then
            thenChannelScoreIsUpdatedTo(60) // (80 + 60 + 40) / 3 = 60
        }

    @Test
    fun `should calculate channel score when user add two episodes that meet minimum rate threshold and channel have all valid episodes`() =
        runTest {
            // given
            val episode1 = givenEpisodeWithRates(EPISODE_ID_1, likes = 80, dislikes = 20) // 80% like rate
            val episode2 = givenEpisodeWithRates(EPISODE_ID_2, likes = 60, dislikes = 40) // 60% like rate
            val episode3 = givenEpisodeWithRates(EPISODE_ID_3, likes = 40, dislikes = 60) // 40% like rate
            val episodes = listOf(episode1, episode2, episode3)
            givenRepositoriesWith(episodes)

            // when added 1 episode to channel
            whenToProcessChannelScore(episodes = listOf(episode1, episode2))

            // then
            thenChannelScoreIsUpdatedTo(60) // (80 + 60 + 40) / 3 = 60
        }

    @Test
    fun `should calculate channel score when user add two episodes that meet minimum rate threshold and channel have all invalid episodes`() =
        runTest {
            // given
            val episode1 = givenEpisodeWithRates(EPISODE_ID_1, likes = 80, dislikes = 20) // 80% like rate
            val episode2 = givenEpisodeWithRates(EPISODE_ID_2, likes = 60, dislikes = 40) // 60% like rate
            val episode3 = givenEpisodeWithRates(EPISODE_ID_3, likes = 0, dislikes = 0) // 0% like rate
            val episodes = listOf(episode1, episode2, episode3)
            givenRepositoriesWith(episodes)

            // when added 1 episode to channel
            whenToProcessChannelScore(episodes = listOf(episode1, episode2))

            // then
            thenChannelScoreIsUpdatedTo(70) // (80 + 60) / 2 = 70
        }

    @Test
    fun `should skip channel score calculation when user add one episode and no meet minimum rate threshold`() =
        runTest {
            // given
            val episode = givenEpisodeWithRates(EPISODE_ID_1, likes = 0, dislikes = 1) // 0% like rate
            val episodeWithOneMoreLike = givenLikeRatedEpisodeFrom(episode) // Inc likes to 1
            givenRepositoriesWith(episodeWithOneMoreLike)

            // when added 1 episode to channel
            whenToProcessChannelScore(episodes = listOf(episodeWithOneMoreLike))

            // then
            thenChannelScoreIsNotUpdated()
        }

    @Test
    fun `should skip channel score calculation when user add one episode that not meet minimum rate threshold and channel have all valid episodes`() =
        runTest {
            // given
            val episode1 = givenEpisodeWithRates(EPISODE_ID_1, likes = 0, dislikes = 0) // 0% like rate
            val episode2 = givenEpisodeWithRates(EPISODE_ID_2, likes = 60, dislikes = 40) // 60% like rate
            val episode3 = givenEpisodeWithRates(EPISODE_ID_3, likes = 40, dislikes = 60) // 40% like rate
            val episodes = listOf(episode1, episode2, episode3)
            givenRepositoriesWith(episodes)

            // when added 1 episode to channel
            whenToProcessChannelScore(episodes = listOf(episode1))

            // then
            thenChannelScoreIsNotUpdated()
        }

    @Test
    fun `should skip channel score calculation when user no add episode & channel have all valid episodes`() =
        runTest {
            // given
            val episode1 = givenEpisodeWithRates(EPISODE_ID_1, likes = 0, dislikes = 0) // 0% like rate
            val episode2 = givenEpisodeWithRates(EPISODE_ID_2, likes = 60, dislikes = 40) // 60% like rate
            val episode3 = givenEpisodeWithRates(EPISODE_ID_3, likes = 40, dislikes = 60) // 40% like rate
            val episodes = listOf(episode1, episode2, episode3)
            givenRepositoriesWith(episodes)

            // when added 1 episode to channel
            whenToProcessChannelScore(episodes = listOf())

            // then
            thenChannelScoreIsNotUpdated()
        }

    @Test
    fun `should skip channel score calculation when user no add episode & channel have no have episodes`() =
        runTest {
            // given
            givenRepositoriesWith(emptyList())

            // when added 1 episode to channel
            whenToProcessChannelScore(episodes = listOf())

            // then
            thenChannelScoreIsNotUpdated()
        }

    /**
     * ***************
     * ***************
     * Given like/dislike rated episode
     * ***************
     * ***************
     */
    private fun givenRepositoriesWith(allEpisodes: List<Episode>) {
        coEvery { channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL.id) } returns allEpisodes.map { it.id }
        coEvery { episodeRepository.findByIds(allEpisodes.map { it.id }) } returns allEpisodes
        coEvery { channelRepository.findById(CHANNEL.id) } returns CHANNEL
    }

    private fun givenEpisodeWithRates(id: String, likes: Long, dislikes: Long) =
        EpisodeMother.buildEpisode(
            id = id,
            channelId = CHANNEL.id,
            likes = likes,
            dislikes = dislikes,
            views = 50
        )

    private fun givenLikeRatedEpisodeFrom(episode: Episode) =
        episode.copy(rate = episode.rate.copy(_likes = episode.rate.likes + 1))

    private fun givenRepositoriesWith(episode: Episode) {
        coEvery { channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL.id) } returns listOf(
            episode.id
        )
        coEvery { episodeRepository.findByIds(listOf(episode.id)) } returns listOf(episode)
        coEvery { channelRepository.findById(CHANNEL.id) } returns CHANNEL
    }

    /**
     * ***************
     * ***************
     * When to process channel score
     * ***************
     * ***************
     */
    private suspend fun whenToProcessChannelScore(episodes: List<Episode>) {
        defaultQualityService.calculateChannelScore(
            episodes = episodes,
            channelId = CHANNEL.id
        )
    }

    /**
     * ***************
     * ***************
     * Then channel score is updated to
     * ***************
     * ***************
     */
    private fun thenChannelScoreIsUpdatedTo(score: Int) {
        coVerify { channelScoreRepository.updateScore(channelId = CHANNEL.id, score = score) }
    }

    private fun thenChannelScoreIsNotUpdated() {
        coVerify(exactly = 0) { channelScoreRepository.updateScore(any(), any()) }
    }

    private companion object {
        val CHANNEL = ChannelMother.aChannel(id = "channel-id")
        val EPISODE_ID_1 = "episode-id-1"
        val EPISODE_ID_2 = "episode-id-2"
        val EPISODE_ID_3 = "episode-id-3"
    }
}
