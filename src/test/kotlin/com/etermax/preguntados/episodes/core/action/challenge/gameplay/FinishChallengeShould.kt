package com.etermax.preguntados.episodes.core.action.challenge.gameplay

import com.etermax.preguntados.episodes.core.ChallengeMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.FinishChallenge.ActionData
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.finish.FinishEpisodeDetails
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertNotNull

class FinishChallengeShould {

    private lateinit var challengeService: ChallengeService
    private lateinit var summaryService: SummaryService
    private lateinit var rankingService: RankingService

    @BeforeEach
    fun setUp() {
        challengeService = mockk()
        summaryService = mockk()
        rankingService = mockk()
    }

    @Test
    fun `call finish challenge`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, "C3", null, false)
        )

        coEvery { challengeService.getChallengeContext(any(), any()) } returns context
        coEvery { challengeService.finishChallenge(any(), any(), any()) } returns Unit
        coEvery { summaryService.toEpisodeSummary(any()) } returns EpisodeSummary.from(
            context.episode,
            ProfileMother.aProfile(PLAYER_ID)
        )
        coEvery { rankingService.findRanking(any(), any()) } returns DeliveryRanking.EMPTY

        // When finish challenge
        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID)
        whenFinishChallenge(actionData)

        // Then challenge is finished
        coVerify(exactly = 1) {
            challengeService.finishChallenge(CHALLENGE_ID, PLAYER_ID, any())
        }
    }

    @Test
    fun `return episode and ranking details`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, "C3", null, false)
        )

        coEvery { challengeService.getChallengeContext(any(), any()) } returns context
        coEvery { challengeService.finishChallenge(any(), any(), any()) } returns Unit
        coEvery { summaryService.toEpisodeSummary(any()) } returns EpisodeSummary.from(
            context.episode,
            ProfileMother.aProfile(PLAYER_ID)
        )
        coEvery { rankingService.findRanking(any(), any()) } returns DeliveryRanking.EMPTY

        // When finish challenge
        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID)
        val response = whenFinishChallenge(actionData)

        // Then finish episode details are returned
        assertNotNull(response.episodeSummary)
        assertNotNull(response.ranking)
    }

    private suspend fun whenFinishChallenge(actionData: ActionData): FinishEpisodeDetails {
        return FinishChallenge(challengeService, summaryService, rankingService)(actionData)
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val CHALLENGE_ID = "CHA-123"
        const val EPISODE_ID = "EPI-123"
    }
}
