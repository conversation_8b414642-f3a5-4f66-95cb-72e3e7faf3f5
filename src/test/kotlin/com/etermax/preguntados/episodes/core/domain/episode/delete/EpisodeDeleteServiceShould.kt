package com.etermax.preguntados.episodes.core.domain.episode.delete

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.MODIFICATION_DATE
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.episode.ChannelEpisodeDeleteRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.time.Clock
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class EpisodeDeleteServiceShould {
    private lateinit var service: EpisodeDeleteService
    private lateinit var channelsRepository: ChannelRepository
    private lateinit var channelEpisodeDeleteRepository: ChannelEpisodeDeleteRepository
    private lateinit var channelUnpublishedEpisodesRepository: ChannelUnpublishedEpisodesRepository
    private lateinit var clock: Clock

    @BeforeEach
    fun setUp() {
        channelsRepository = mockk()
        channelEpisodeDeleteRepository = mockk(relaxUnitFun = true)
        channelUnpublishedEpisodesRepository = mockk(relaxUnitFun = true)
        clock = mockk()

        service = EpisodeDeleteService(
            channelsRepository,
            channelEpisodeDeleteRepository,
            channelUnpublishedEpisodesRepository,
            clock
        )
    }

    @Test
    fun `delete without channel`() = runTest {
        whenDelete(channelId = null)
        thenDeleteWithoutChannel()
    }

    @Test
    fun `not remove from unpublished when has no channel`() = runTest {
        whenDelete(channelId = null)
        thenNotRemoveFromUnpublished()
    }

    @Test
    fun `delete with not existing channel`() = runTest {
        givenNoChannel()
        whenDelete(channelId = CHANNEL_ID)
        thenDeleteWithoutChannel()
    }

    @Test
    fun `delete from unpublished despite channel not exists`() = runTest {
        givenNoChannel()
        whenDelete(channelId = CHANNEL_ID)
        thenRemoveFromUnpublished()
    }

    @Test
    fun `delete with channel`() = runTest {
        givenChannel()
        givenClock()

        whenDelete(channelId = CHANNEL_ID)

        thenDeleteWithUpdatedChannel()
    }

    @Test
    fun `delete without channel when episode is DRAFT`() = runTest {
        givenChannel()
        givenClock()

        whenDelete(channelId = CHANNEL_ID, episodeStatus = EpisodeStatus.DRAFT)

        thenDeleteWithoutChannel()
    }

    @Test
    fun `delete without channel when episode is REJECTED`() = runTest {
        givenChannel()
        givenClock()

        whenDelete(channelId = CHANNEL_ID, episodeStatus = EpisodeStatus.REJECTED)

        thenDeleteWithoutChannel()
    }

    @Test
    fun `delete without channel when episode is PENDING`() = runTest {
        givenChannel()
        givenClock()

        whenDelete(channelId = CHANNEL_ID, episodeStatus = EpisodeStatus.PENDING)

        thenDeleteWithoutChannel()
    }

    @Test
    fun `delete from unpublished`() = runTest {
        givenChannel()
        givenClock()

        whenDelete(channelId = CHANNEL_ID)

        thenRemoveFromUnpublished()
    }

    private fun givenNoChannel() {
        coEvery { channelsRepository.findById(CHANNEL_ID) } returns null
    }

    private fun givenChannel() {
        coEvery { channelsRepository.findById(CHANNEL_ID) } returns ChannelMother.aChannel(
            id = CHANNEL_ID,
            episodesCount = 1
        )
    }

    private fun givenClock() {
        every { clock.now() } returns MODIFICATION_DATE
    }

    private suspend fun whenDelete(channelId: String?, episodeStatus: EpisodeStatus = EpisodeStatus.PUBLISHED) {
        val episode = EpisodeMother.buildEpisode(id = EPISODE_ID, channelId = channelId, status = episodeStatus)
        service.delete(episode)
    }

    private fun thenDeleteWithoutChannel() {
        coVerify(exactly = 1) { channelEpisodeDeleteRepository.delete(EPISODE_ID, channel = null) }
    }

    private fun thenNotRemoveFromUnpublished() {
        coVerify(exactly = 0) { channelUnpublishedEpisodesRepository.delete(any()) }
    }

    private fun thenRemoveFromUnpublished() {
        val item = ChannelUnpublishedEpisode(CHANNEL_ID, EPISODE_ID)
        coVerify(exactly = 1) { channelUnpublishedEpisodesRepository.delete(item) }
    }

    private fun thenDeleteWithUpdatedChannel() {
        val channel = ChannelMother.aChannel(
            id = CHANNEL_ID,
            episodesCount = 0,
            lastModificationDate = MODIFICATION_DATE
        )
        coVerify(exactly = 1) { channelEpisodeDeleteRepository.delete(EPISODE_ID, channel) }
    }

    private companion object {
        const val EPISODE_ID = EpisodeMother.SEQUENCE_ID
        const val CHANNEL_ID = ChannelMother.ID
    }
}
