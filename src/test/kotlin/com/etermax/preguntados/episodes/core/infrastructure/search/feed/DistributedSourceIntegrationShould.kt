package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.episodes.core.domain.search.feed.SourceResponse.Companion.emptyResponse
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime.now
import kotlin.math.abs
import kotlin.random.Random
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class DistributedSourceIntegrationShould :
    AbstractOpenSearchSourceIntegrationShould(indexDescriptor = "opensearch/mapping/episodes-index-mapping-v1.0.json") {

    private lateinit var source: Source<String>

    @Test
    fun `retrieve episodes from multiple sources`() = runTest {
        val others = (1..10).map { i -> i.toString() }.toList()

        (11..20).forEach { i ->
            givenAnEpisode(id = i.toString(), startDate = now().minusHours(i.toLong()), embedding = embedding(i))
        }

        source = DistributedSource(
            sources = listOf(
                OtherEpisodeSource(others),
                RecentEpisodeSource(osClient, indexName)
            ),
            distributionStrategy = WeightedDistributionStrategy(listOf(30, 70))
        )

        val result = whenRetrieve(offset = 0, limit = 10)

        thenSameElements(listOf("1", "2", "3", "11", "12", "13", "14", "15", "16", "17"), result.items)
    }

    @Test
    fun `retrieve episodes from multiple sources respecting fetch cursor`() = runTest {
        val others = (1..10).map { i -> i.toString() }.toList()

        (11..30).forEach { i ->
            givenAnEpisode(id = i.toString(), startDate = now().minusHours(i.toLong()), embedding = embedding(i))
        }

        source = DistributedSource(
            sources = listOf(
                OtherEpisodeSource(others),
                RecentEpisodeSource(osClient, indexName)
            ),
            distributionStrategy = WeightedDistributionStrategy(listOf(30, 70))
        )

        val result = whenRetrieve(offset = 0, limit = 10)

        val nextResult = whenRetrieve(
            offset = 0,
            limit = 10,
            fetchCursor = result.fetchCursor
        )

        thenSameElements(listOf("4", "5", "6", "18", "19", "20", "21", "22", "23", "24"), nextResult.items)
    }

    @Test
    fun `retrieve episodes from multiple sources respecting fetch cursor till exhausted`() = runTest {
        val others = (1..10).map { i -> i.toString() }.toList()

        (11..30).forEach { i ->
            givenAnEpisode(id = i.toString(), startDate = now().minusHours(i.toLong()), embedding = embedding(i))
        }

        source = DistributedSource(
            sources = listOf(
                OtherEpisodeSource(others),
                RecentEpisodeSource(osClient, indexName)
            ),
            distributionStrategy = WeightedDistributionStrategy(listOf(30, 70))
        )

        val result = whenRetrieve(offset = 0, limit = 10)

        val secondResult = whenRetrieve(
            offset = 0,
            limit = 10,
            fetchCursor = result.fetchCursor
        )

        thenSameElements(listOf("4", "5", "6", "18", "19", "20", "21", "22", "23", "24"), secondResult.items)
        assertFalse((secondResult.fetchCursor as DistributedFetchCursor).exhausted)

        val thirdResult = whenRetrieve(
            offset = 0,
            limit = 10,
            fetchCursor = secondResult.fetchCursor
        )

        thenSameElements(listOf("7", "8", "9", "25", "26", "27", "28", "29", "30", "10"), thirdResult.items)
        assertTrue((thirdResult.fetchCursor as DistributedFetchCursor).exhausted)
    }

    private suspend fun whenRetrieve(
        offset: Int = 0,
        limit: Int = 10,
        country: Country? = null,
        language: Language? = null,
        fetchCursor: FetchCursor? = null
    ): SourceResponse<String> {
        return source.fetch(
            SourceRequest(
                OffsetLimit(
                    offset = offset,
                    limit = limit
                ),
                userId = 1,
                country = country,
                language = language,
                fetchCursor = fetchCursor
            )
        )
    }

    fun embedding(seed: Int = 1, size: Int = 384): FloatArray {
        val random = Random(seed)
        val vector = List(size) { random.nextDouble(-1.0, 1.0) }
        val norm1 = vector.sumOf { abs(it) }
        return vector.map { (it / norm1).toFloat() }.toFloatArray()
    }
}

class OtherEpisodeSource(val others: List<String>) : Source<String> {
    override suspend fun fetch(request: SourceRequest): SourceResponse<String> {
        if (request.range.limit <= 0) return emptyResponse(request.fetchCursor)
        val range = applyFetchCursor(request.range, request.fetchCursor)
        return SourceResponse(
            others.subList(range.offset, range.offset + range.limit),
            OffsetFetchCursor(range.offset + range.limit, range.offset + range.limit >= others.size)
        )
    }

    private fun applyFetchCursor(range: OffsetLimit, fetchCursor: FetchCursor?): OffsetLimit {
        with(fetchCursor as OffsetFetchCursor?) {
            return if (fetchCursor != null) {
                OffsetLimit(fetchCursor.offset, range.limit)
            } else {
                range
            }
        }
    }
}
