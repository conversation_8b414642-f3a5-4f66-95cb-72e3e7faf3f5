package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.episode.Rate.Type.DISLIKE
import com.etermax.preguntados.episodes.core.domain.episode.Rate.Type.LIKE
import com.etermax.preguntados.episodes.core.domain.episode.notification.EpisodeNotificationService
import com.etermax.preguntados.episodes.core.domain.episode.rate.RateRepository
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.quality.QualityService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class RateEpisodeShould {
    private lateinit var profileService: ProfileService
    private lateinit var actionData: RateEpisode.ActionData
    private lateinit var rateRepository: RateRepository
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var episodeNotificationService: EpisodeNotificationService
    private lateinit var ratedEpisode: EpisodeSummary
    private lateinit var qualityService: QualityService

    @BeforeEach
    fun setUp() {
        rateRepository = mockk(relaxed = true)
        episodeNotificationService = mockk(relaxed = true)

        episodeRepository = mockk(relaxed = true)
        coEvery { episodeRepository.findById(EPISODE_ID) } returns buildEpisode(id = EPISODE_ID)

        profileService = mockk(relaxed = true)
        coEvery { profileService.find(PLAYER_ID) } returns ProfileMother.aProfile(playerId = PLAYER_ID)

        qualityService = mockk(relaxed = true)
    }

    @Test
    fun `increment likes when first like`() = runTest {
        givenNoExistingRate()
        givenARate(LIKE)

        whenRate()

        thenLikesIncremented()
        thenRatedLike()
        thenEpisodeIsUpdated(likes = 1)
    }

    @Test
    fun `increment unlikes when first dislike`() = runTest {
        givenNoExistingRate()
        givenARate(DISLIKE)

        whenRate()

        thenUnlikesIncremented()
        thenRatedDislike()
        thenEpisodeIsUpdated(dislikes = 1)
    }

    @Test
    fun `switch from like to dislike`() = runTest {
        givenExistingRate(LIKE)
        givenARate(DISLIKE)

        whenRate()

        thenLikesDecremented()
        thenUnlikesIncremented()
        thenRatedDislike()
        thenEpisodeIsUpdated(likes = -1, dislikes = 1)
    }

    @Test
    fun `switch from dislike to like`() = runTest {
        givenExistingRate(DISLIKE)
        givenARate(LIKE)

        whenRate()

        thenUnlikesDecremented()
        thenLikesIncremented()
        thenRatedLike()
        thenEpisodeIsUpdated(likes = 1, dislikes = -1)
    }

    @Test
    fun `remove like`() = runTest {
        givenExistingRate(LIKE)
        givenARate(null)

        whenRate()

        thenLikesDecremented()
        thenRateRemoved()
        thenEpisodeIsUpdated(likes = -1)
    }

    @Test
    fun `remove dislike`() = runTest {
        givenExistingRate(DISLIKE)
        givenARate(null)

        whenRate()

        thenUnlikesDecremented()
        thenRateRemoved()
        thenEpisodeIsUpdated(dislikes = -1)
    }

    @Test
    fun `notify when first like`() = runTest {
        givenNoExistingRate()
        givenARate(LIKE)

        whenRate()

        thenNotifiedLike()
    }

    @Test
    fun `notify when switching to like`() = runTest {
        givenExistingRate(DISLIKE)
        givenARate(LIKE)

        whenRate()

        thenNotifiedLike()
    }

    @Test
    fun `do not notify on dislike`() = runTest {
        givenNoExistingRate()
        givenARate(DISLIKE)

        whenRate()

        thenNotNotified()
    }

    @Test
    fun `do not notify on rate removal`() = runTest {
        givenExistingRate(LIKE)
        givenARate(null)

        whenRate()

        thenNotNotified()
    }

    @Test
    fun `should calculate channel score when rating an episode`() = runTest {
        givenNoExistingRate()
        givenARate(LIKE)

        whenRate()

        thenCalculateChannelScore()
    }

    private fun givenNoExistingRate() {
        coEvery { rateRepository.findBy(PLAYER_ID, EPISODE_ID) } returns null
    }

    private fun givenExistingRate(rateType: Rate.Type) {
        coEvery { rateRepository.findBy(PLAYER_ID, EPISODE_ID) } returns rateType
    }

    private fun givenARate(rateType: Rate.Type?) {
        actionData = RateEpisode.ActionData(PLAYER_ID, EPISODE_ID, rateType)
    }

    private suspend fun whenRate() {
        val rateEpisode = RateEpisode(rateRepository, episodeRepository, episodeNotificationService, profileService, true, qualityService)
        ratedEpisode = rateEpisode(actionData)
    }

    private fun thenLikesIncremented() {
        assertThat(ratedEpisode.rate.likes).isEqualTo(101)
    }

    private fun thenUnlikesIncremented() {
        assertThat(ratedEpisode.rate.dislikes).isEqualTo(31)
    }

    private fun thenLikesDecremented() {
        assertThat(ratedEpisode.rate.likes).isEqualTo(99)
    }

    private fun thenUnlikesDecremented() {
        assertThat(ratedEpisode.rate.dislikes).isEqualTo(29)
    }

    private fun thenRatedLike() {
        coVerify { rateRepository.save(PLAYER_ID, EPISODE_ID, true) }
    }

    private fun thenRatedDislike() {
        coVerify { rateRepository.save(PLAYER_ID, EPISODE_ID, false) }
    }

    private fun thenRateRemoved() {
        coVerify { rateRepository.delete(PLAYER_ID, EPISODE_ID) }
    }

    private fun thenNotifiedLike() {
        coVerify {
            episodeNotificationService.notifyLiked(
                senderId = PLAYER_ID,
                receiverId = ratedEpisode.ownerId,
                episodeId = EPISODE_ID
            )
        }
    }

    private fun thenNotNotified() {
        coVerify(exactly = 0) {
            episodeNotificationService.notifyLiked(any(), any(), any())
        }
    }

    private fun thenEpisodeIsUpdated(likes: Int = 0, dislikes: Int = 0) {
        coVerify(exactly = 1) { episodeRepository.updateRate(EPISODE_ID, likes, dislikes) }
    }

    private fun thenCalculateChannelScore() {
        coVerify {
            qualityService.calculateChannelScore(
                episodeBeforeUpdate = match { it.id == EPISODE_ID && it.rate.likes == 100L },
                episodeAfterUpdate = match { it.id == EPISODE_ID && it.rate.likes == 101L }
            )
        }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "ABC-123"
    }
}
