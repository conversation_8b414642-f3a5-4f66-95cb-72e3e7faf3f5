package com.etermax.preguntados.episodes.core.doubles.channel.repository

import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelStatistics
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.filters.ChannelSearchFilters
import com.etermax.preguntados.episodes.core.domain.pagination.PaginatedItems
import com.etermax.preguntados.episodes.core.domain.pagination.PaginationFilter
import com.etermax.preguntados.episodes.core.domain.time.toMillis

class InMemoryChannelRepository : ChannelRepository {

    private val channels: MutableMap<String, Channel> = mutableMapOf()

    override suspend fun add(channel: Channel) {
        channels[channel.id] = channel
    }

    override suspend fun put(updatedChannel: Channel) {
        val channel = findById(updatedChannel.id) ?: return

        with(channel) {
            val newChannel = Channel(
                id = id,
                name = name,
                description = description,
                website = website,
                coverUrl = coverUrl,
                subscribed = subscribed,
                ownerId = ownerId,
                statistics = ChannelStatistics(
                    subscribers = statistics.subscribers,
                    episodes = updatedChannel.episodesCount,
                    score = updatedChannel.statistics.score,
                    quality = statistics.quality
                ),
                creationDate = creationDate,
                lastModificationDate = lastModificationDate,
                type = type,
                language = updatedChannel.language,
                order = updatedChannel.order,
                orderType = updatedChannel.orderType
            )

            channels[updatedChannel.id] = newChannel
        }
    }

    override suspend fun delete(channel: Channel) {
        channels.remove(channel.id)
    }

    override suspend fun findById(channelId: String): Channel? {
        return channels[channelId]
    }

    override suspend fun findById(channelsId: List<String>): List<Channel> {
        return channelsId.map { channels[it] }.mapNotNull { it }
    }

    override suspend fun search(filters: ChannelSearchFilters, pagination: PaginationFilter): PaginatedItems<Channel> {
        val items = channels.values.filter { it.ownerId == filters.ownerId }
        if (items.isEmpty()) {
            return PaginatedItems.empty()
        }

        val lastEvaluated = items.last().lastModificationDate.toMillis().toString()
        return PaginatedItems(lastEvaluated, items)
    }

    override suspend fun hasChannels(filters: ChannelSearchFilters): Boolean {
        if (filters.onlyWithEpisodes) {
            return channels.values.firstOrNull { it.ownerId == filters.ownerId && it.episodesCount > 0 } != null
        }

        return channels.values.firstOrNull { it.ownerId == filters.ownerId } != null
    }
}
