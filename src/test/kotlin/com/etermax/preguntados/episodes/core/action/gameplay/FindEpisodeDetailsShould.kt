package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.analytics.actions.TrackEpisodeAnalytics
import com.etermax.preguntados.analytics.service.ViewedEvent
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeDetails
import com.etermax.preguntados.episodes.core.domain.episode.Rate
import com.etermax.preguntados.episodes.core.domain.episode.progress.DeliveryProgress
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentRepository
import com.etermax.preguntados.episodes.core.domain.episode.ranking.*
import com.etermax.preguntados.episodes.core.domain.episode.rate.RateRepository
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.social.SocialNetwork
import com.etermax.preguntados.episodes.core.domain.profile.social.SocialProfile
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.time.OffsetDateTime

class FindEpisodeDetailsShould {

    private lateinit var progressContentRepository: ProgressContentRepository
    private lateinit var rateRepository: RateRepository
    private lateinit var rankingService: RankingService
    private lateinit var trackAnalytics: TrackEpisodeAnalytics
    private lateinit var result: EpisodeDetails

    @BeforeEach
    fun setUp() {
        rateRepository = mockk(relaxed = true)
        rankingService = mockk(relaxed = true)
        progressContentRepository = mockk(relaxed = true)
        trackAnalytics = mockk()
        coEvery { trackAnalytics(any<ViewedEvent>()) } returns Unit
    }

    @Test
    fun `empty ranking when there is none`() = runTest {
        givenNoneRanking()

        whenFind()

        thenRankingIs(DeliveryRanking.Companion.EMPTY)
    }

    @Test
    fun `return delivery ranking`() = runTest {
        givenARanking()
        givenAPlayerRate(null)

        whenFind()

        thenRankingIs(DELIVERY_RANKING)
    }

    @ParameterizedTest(name = "player rate {0}")
    @EnumSource(value = Rate.Type::class, mode = EnumSource.Mode.INCLUDE)
    fun `return player rate`(rate: Rate.Type) = runTest {
        givenARanking()
        givenAPlayerRate(rate)

        whenFind()

        thenPlayerRateIs(rate)
    }

    @Test
    fun `return no player rate`() = runTest {
        givenARanking()
        givenAPlayerRate(null)

        whenFind()

        thenPlayerRateIs(null)
    }

    @Test
    fun `return empty delivery progress when player has not played episode`() = runTest {
        givenARanking()
        givenAPlayerRate(null)
        givenNoProgress()

        whenFind()

        thenDeliveryProgressIsEmpty()
    }

    @Test
    fun `return delivery progress when player has played episode`() = runTest {
        givenARanking()
        givenAPlayerRate(null)
        givenAProgress()

        whenFind()

        thenDeliveryProgressIs(PROGRESS_CONTENT)
    }

    @Test
    fun `return hasPlayed false when player has not played episode`() = runTest {
        givenARanking()
        givenAPlayerRate(null)
        givenNoProgress()

        whenFind()

        thenHasPlayedIs(false)
    }

    @Test
    fun `return hasPlayed true when player has played episode`() = runTest {
        givenARanking()
        givenAPlayerRate(null)
        givenAProgress()

        whenFind()

        thenHasPlayedIs(true)
    }

    @Test
    fun `analytics are tracked`() = runTest {
        givenNoneRanking()

        whenFind()

        thenAnalyticsAreRecorded()
    }

    private fun givenNoProgress() {
        coEvery { progressContentRepository.findBy(EPISODE_ID, PLAYER_ID) } returns null
    }

    private fun givenAProgress() {
        coEvery { progressContentRepository.findBy(EPISODE_ID, PLAYER_ID) } returns PROGRESS_CONTENT
    }

    private fun givenNoneRanking() {
        coEvery { rankingService.findRanking(any(), any()) } returns DeliveryRanking.EMPTY
    }

    private fun givenARanking() {
        coEvery { rankingService.findRanking(PLAYER_ID, DOMAIN) } returns DELIVERY_RANKING
    }

    private fun givenAPlayerRate(rate: Rate.Type?) {
        coEvery { rateRepository.findBy(PLAYER_ID, EPISODE_ID) } returns rate
    }

    private suspend fun whenFind() {
        val findEpisodeDetails = FindEpisodeDetails(rateRepository, progressContentRepository, rankingService, trackAnalytics)
        val actionData = FindEpisodeDetails.ActionData(PLAYER_ID, EPISODE_ID)
        result = findEpisodeDetails(actionData)
    }

    private fun thenRankingIs(ranking: DeliveryRanking) {
        assertThat(result.ranking).isEqualTo(ranking)
    }

    private fun thenPlayerRateIs(rate: Rate.Type?) {
        assertThat(result.rate).isEqualTo(rate)
    }

    private fun thenDeliveryProgressIsEmpty() {
        assertThat(result.deliveryProgress).isEqualTo(DeliveryProgress.EMPTY)
    }

    private fun thenDeliveryProgressIs(progressContent: ProgressContent) {
        assertThat(result.deliveryProgress).isEqualTo(DeliveryProgress.from(progressContent))
    }

    private fun thenHasPlayedIs(expected: Boolean) {
        assertThat(result.hasPlayed).isEqualTo(expected)
    }

    private fun thenAnalyticsAreRecorded() {
        coVerify {
            trackAnalytics(ViewedEvent(PLAYER_ID, EPISODE_ID))
        }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "ABC-123"
        const val RANKED_PLAYER_ID = 2L
        const val LAST_CONTENT_ID = "content-123"
        const val LANGUAGE = "en"
        const val HAS_FINISHED = false

        val DOMAIN = Domain(EPISODE_ID)
        val ONE_YEAR_AGO: OffsetDateTime = OffsetDateTime.now().minusYears(1)
        val PROFILE = Profile(
            playerId = RANKED_PLAYER_ID,
            name = "",
            country = "",
            photoUrl = "",
            socialProfile = SocialProfile(SocialNetwork.FACEBOOK, "a", "name"),
            joinDate = ONE_YEAR_AGO,
            restriction = null
        )
        val DELIVERY_RANKING = DeliveryRanking(
            listOf(
                DeliveryRankedPlayer(PROFILE, RankingEntry(1, 100))
            ),
            RankingEntry(2, 50)
        )
        val PROGRESS_CONTENT = ProgressContent(
            episodeId = EPISODE_ID,
            playerId = PLAYER_ID,
            lastContentId = LAST_CONTENT_ID,
            language = LANGUAGE,
            hasFinishedEpisode = HAS_FINISHED
        )
    }
}
