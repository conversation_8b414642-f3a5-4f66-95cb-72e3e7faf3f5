package com.etermax.preguntados.episodes.core.action.challenge.gameplay

import com.etermax.preguntados.episodes.core.ChallengeMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.action.challenge.gameplay.AnswerChallengeContent.ActionData
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeService
import com.etermax.preguntados.episodes.core.domain.episode.history.AnswerContentHistoryService
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingPointsCalculator
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingRepository
import com.etermax.preguntados.episodes.core.infrastructure.ranking.TimeRankingPointsCalculator
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class AnswerChallengeContentShould {

    private lateinit var challengeService: ChallengeService
    private lateinit var rankingRepository: RankingRepository
    private lateinit var calculator: RankingPointsCalculator
    private lateinit var answerContentHistoryService: AnswerContentHistoryService

    @BeforeEach
    fun setUp() {
        challengeService = mockk()
        rankingRepository = mockk(relaxed = true)
        calculator = TimeRankingPointsCalculator()
        answerContentHistoryService = mockk(relaxed = true)
    }

    @Test
    fun `do not increment score when answer incorrectly`() = runTest {
        // Given a ChallengeContext
        coEvery { challengeService.getChallengeContext(any(), any()) } returns ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = CONTENTS),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, CONTENT_ID, null, true)
        )

        // When answer
        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID, CONTENTS.first(), IS_INCORRECT, ELAPSED_TIME, TOTAL_TIME)
        whenAnswerContent(actionData)

        // Then score is not incremented
        coVerify(exactly = 0) { rankingRepository.incrementScore(any(), any(), any()) }
    }

    @Test
    fun `do not increment score when challenge is already finished`() = runTest {
        // Given a ChallengeContext
        coEvery { challengeService.getChallengeContext(any(), any()) } returns ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = CONTENTS),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, CONTENT_ID, null, true)
        )

        // When answer
        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID, CONTENTS.first(), IS_CORRECT, ELAPSED_TIME, TOTAL_TIME)
        whenAnswerContent(actionData)

        // Then score is not incremented
        coVerify(exactly = 0) { rankingRepository.incrementScore(any(), any(), any()) }
    }

    @Test
    fun `increment score when answer correctly and has no progress`() = runTest {
        // Given a ChallengeContext
        coEvery { challengeService.getChallengeContext(any(), any()) } returns ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = CONTENTS),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = null
        )

        // When answer
        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID, CONTENTS.first(), IS_CORRECT, ELAPSED_TIME, TOTAL_TIME)
        whenAnswerContent(actionData)

        coVerify(exactly = 1) {
            rankingRepository.incrementScore(
                PLAYER_ID,
                Domain("${EPISODE_ID}_$CHALLENGE_ID"),
                any()
            )
        }
    }

    @Test
    fun `increment score when answer correctly and challenge is not finished`() = runTest {
        // Given a ChallengeContext
        coEvery { challengeService.getChallengeContext(any(), any()) } returns ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = CONTENTS),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, CONTENT_ID, null, false)
        )

        // When answer
        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID, CONTENTS.first(), IS_CORRECT, ELAPSED_TIME, TOTAL_TIME)
        whenAnswerContent(actionData)

        // Then score is incremented
        coVerify(exactly = 1) {
            rankingRepository.incrementScore(
                PLAYER_ID,
                Domain("${EPISODE_ID}_$CHALLENGE_ID"),
                any()
            )
        }
    }

    @Test
    fun `set as finished when is last content`() = runTest {
        // Given a ChallengeContext
        coEvery { challengeService.getChallengeContext(any(), any()) } returns ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = CONTENTS),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, CONTENT_ID, null, false)
        )
        coEvery { challengeService.finishChallenge(any(), any(), any()) } returns Unit

        // When answer
        val actionData = ActionData(PLAYER_ID, CHALLENGE_ID, CONTENTS.last(), IS_CORRECT, ELAPSED_TIME, TOTAL_TIME)
        whenAnswerContent(actionData)

        // Then challenge is finished
        coVerify(exactly = 1) {
            challengeService.finishChallenge(
                CHALLENGE_ID,
                PLAYER_ID,
                any()
            )
        }
    }

    private suspend fun whenAnswerContent(actionData: ActionData) {
        val answerContent = AnswerChallengeContent(challengeService, rankingRepository, calculator, answerContentHistoryService)
        answerContent(actionData)
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val CHALLENGE_ID = "CHA-123"
        const val EPISODE_ID = "EPI-123"
        const val CONTENT_ID = "XYZ-789"
        const val CONTENT_ID_A66 = "A66"
        const val CONTENT_ID_A666 = "A666"
        val CONTENTS = listOf(CONTENT_ID, CONTENT_ID_A66, CONTENT_ID_A666)
        const val IS_CORRECT = true
        const val IS_INCORRECT = false
        const val ELAPSED_TIME = 5000
        const val TOTAL_TIME = 10000
    }
}
