package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.COVER_URL
import com.etermax.preguntados.episodes.core.ChannelMother.DESCRIPTION
import com.etermax.preguntados.episodes.core.ChannelMother.LANGUAGE
import com.etermax.preguntados.episodes.core.ChannelMother.MODIFICATION_DATE
import com.etermax.preguntados.episodes.core.ChannelMother.NAME
import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.WEBSITE
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.exception.*
import com.etermax.preguntados.episodes.core.domain.moderation.ModerationService
import com.etermax.preguntados.episodes.core.domain.moderation.UrlValidatorService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.doubles.ChannelUnpublishedEpisodesFactory
import com.etermax.preguntados.episodes.core.doubles.channel.repository.InMemoryChannelRepository
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UpdateChannelShould {

    private lateinit var channelRepository: ChannelRepository
    private lateinit var summaryService: SummaryService
    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService
    private lateinit var moderationService: ModerationService
    private lateinit var urlValidatorService: UrlValidatorService
    private lateinit var clock: Clock

    private lateinit var action: UpdateChannel
    private var result: ChannelSummary? = null
    private var thrownException: RuntimeException? = null

    @BeforeEach
    fun setUp() {
        channelRepository = InMemoryChannelRepository()
        summaryService = mockk(relaxed = true)
        moderationService = mockk(relaxed = true)
        urlValidatorService = mockk(relaxed = true)
        profileService = mockk(relaxed = true)
        coEvery { profileService.find(any()) } returns ChannelMother.aChannelSummary().owner!!
        unpublishedEpisodesService = ChannelUnpublishedEpisodesFactory.mock()
        clock = mockk(relaxed = true)

        action = UpdateChannel(
            repository = channelRepository,
            summaryService = SummaryService(profileService, unpublishedEpisodesService),
            channelValidatorService = ChannelValidatorService(moderationService, urlValidatorService),
            clock = clock
        )

        result = null
        thrownException = null

        givenAModificationDate()
        givenAValidUrl()
        givenATextAllowed()
    }

    @Test
    fun `throw exception when channel does not exist`() = runTest {
        whenUpdate()

        thenThrowsException<ChannelNotFoundException>()
    }

    @Test
    fun `throw exception when update a channel of another player`() = runTest {
        givenAnExistingChannel()
        whenUpdate(playerId = ChannelMother.ANOTHER_PLAYER_ID)

        thenThrowsException<InvalidUpdateNotOwnChannelException>()
    }

    @Test
    fun `throw exception when name is empty`() = runTest {
        givenAnExistingChannelWithLanguage()
        whenUpdate(name = " ")

        thenThrowsException<ChannelNameEmptyException>()
    }

    @Test
    fun `throw exception when name is not allowed`() = runTest {
        givenAnExistingChannelWithLanguage()
        givenATextNotAllowed()
        whenUpdate(name = "new name not allowed")

        thenThrowsException<ChannelNameNotAllowedException>()
    }

    @Test
    fun `throw exception when name is longer than 30 characters`() = runTest {
        givenAnExistingChannelWithLanguage()
        whenUpdate(name = 'A'.repeat(31))

        thenThrowsException<ChannelNameLongerTooLongException>()
    }

    @Test
    fun `throw exception when description is longer than 80 characters`() = runTest {
        givenAnExistingChannelWithLanguage()
        whenUpdate(description = 'A'.repeat(81))

        thenThrowsException<ChannelDescriptionLongerTooLongException>()
    }

    @Test
    fun `throw exception when description is not allowed`() = runTest {
        givenAnExistingChannelWithLanguage()
        givenATextNotAllowed()
        whenUpdate(description = "new description not allowed")

        thenThrowsException<ChannelDescriptionNotAllowedException>()
    }

    @Test
    fun `throw exception when website is invalid`() = runTest {
        givenAnExistingChannel()
        givenAnInvalidUrl()
        whenUpdate(website = "invalid-url")

        thenThrowsException<ChannelInvalidWebsiteException>()
    }

    @Test
    fun `throw exception when cover url is invalid`() = runTest {
        givenAnExistingChannel()
        givenAnInvalidUrl()
        whenUpdate(coverUrl = "invalid-url")

        thenThrowsException<ChannelInvalidCoverUrlException>()
    }

    @Test
    fun `update channel`() = runTest {
        givenAnExistingChannel()
        whenUpdate(
            name = "newName",
            website = "newWebsite",
            description = "new description",
            coverUrl = "new cover image"
        )

        assertThat(result!!.website).isEqualTo("newWebsite")
        assertThat(result!!.name).isEqualTo("newName")
        assertThat(result!!.description).isEqualTo("new description")
        assertThat(result!!.coverUrl).isEqualTo("new cover image")
    }

    @Test
    fun `return updated channel summary`() = runTest {
        givenAnExistingChannel()
        whenUpdate()

        thenReturnsSummary()
    }

    private fun givenAModificationDate() {
        every { clock.now() } returns MODIFICATION_DATE
    }

    private fun givenAValidUrl() {
        every { urlValidatorService.isValid(any()) } returns true
    }

    private fun givenAnInvalidUrl() {
        every { urlValidatorService.isValid(any()) } returns false
    }

    private fun givenATextAllowed() {
        coEvery { moderationService.isTextAllowed(any(), any(), any()) } returns true
    }

    private fun givenATextNotAllowed() {
        coEvery { moderationService.isTextAllowed(any(), any(), any()) } returns false
    }

    private suspend fun givenAnExistingChannel() {
        channelRepository.add(ChannelMother.aChannel(language = null))
    }

    private suspend fun givenAnExistingChannelWithLanguage() {
        channelRepository.add(ChannelMother.aChannel(language = LANGUAGE))
    }

    private suspend fun whenUpdate(
        playerId: Long = PLAYER_ID,
        name: String = NAME,
        description: String? = DESCRIPTION,
        website: String? = WEBSITE,
        coverUrl: String = COVER_URL
    ) {
        val actionData = ActionData(
            playerId = playerId,
            channelId = ChannelMother.ID,
            coverUrl = coverUrl,
            moderationLanguage = LANGUAGE,
            type = null,
            _website = website,
            _name = name,
            _description = description
        )
        try {
            result = action.invoke(actionData)
        } catch (e: RuntimeException) {
            thrownException = e
        }
    }

    private fun thenReturnsSummary() {
        assertThat(result).isEqualTo(ChannelMother.aChannelSummary(lastModificationDate = MODIFICATION_DATE))
    }

    private fun Char.repeat(count: Int): String = this.toString().repeat(count)

    private inline fun <reified T : RuntimeException> thenThrowsException() {
        assertThat(thrownException)
            .isNotNull
            .isInstanceOf(T::class.java)
    }
}
