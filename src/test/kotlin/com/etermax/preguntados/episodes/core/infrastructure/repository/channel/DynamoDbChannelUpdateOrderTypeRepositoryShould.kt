package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelOrderTypeItem
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoDbChannelUpdateOrderTypeRepositoryShould {
    private lateinit var repository: DynamoDbChannelUpdateOrderTypeRepository
    private lateinit var channelRepository: DynamoDBChannelRepository

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        val channelTable = createChannelTable()
        val orderTypeTable = createChannelOrderTypeTable()

        channelRepository = DynamoDBChannelRepository(dynamoDbEnhancedClient, channelTable)
        repository = DynamoDbChannelUpdateOrderTypeRepository(orderTypeTable)
    }

    @Test
    fun `update DATE_ADDED to CUSTOM_ORDER`() = runTest {
        givenChannelWith(ChannelOrderType.DATE_ADDED)
        whenPut(ChannelOrderType.CUSTOM_ORDER)
        thenOrderTypeIs(ChannelOrderType.CUSTOM_ORDER)
    }

    @Test
    fun `update CUSTOM_ORDER to DATE_ADDED`() = runTest {
        givenChannelWith(ChannelOrderType.CUSTOM_ORDER)
        whenPut(ChannelOrderType.DATE_ADDED)
        thenOrderTypeIs(ChannelOrderType.DATE_ADDED)
    }

    @Test
    fun `update DATE_ADDED to DATE_ADDED`() = runTest {
        givenChannelWith(ChannelOrderType.DATE_ADDED)
        whenPut(ChannelOrderType.DATE_ADDED)
        thenOrderTypeIs(ChannelOrderType.DATE_ADDED)
    }

    @Test
    fun `update CUSTOM_ORDER to CUSTOM_ORDER`() = runTest {
        givenChannelWith(ChannelOrderType.CUSTOM_ORDER)
        whenPut(ChannelOrderType.CUSTOM_ORDER)
        thenOrderTypeIs(ChannelOrderType.CUSTOM_ORDER)
    }

    private suspend fun givenChannelWith(orderType: ChannelOrderType) {
        val channel = ChannelMother.aChannel(orderType = orderType)
        channelRepository.add(channel)
    }

    private suspend fun whenPut(orderType: ChannelOrderType) {
        repository.put(ChannelMother.ID, orderType)
    }

    private suspend fun thenOrderTypeIs(orderType: ChannelOrderType) {
        val channel = ChannelMother.aChannel(orderType = orderType)
        val updatedChannel = channelRepository.findById(ChannelMother.ID)!!
        assertThat(updatedChannel).isEqualTo(channel)
    }

    private fun createChannelTable(): DynamoDbAsyncTable<ChannelItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(ChannelItem::class.java))
    }

    private fun createChannelOrderTypeTable(): DynamoDbAsyncTable<UpdateChannelOrderTypeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(UpdateChannelOrderTypeItem::class.java))
    }

    private companion object {
        const val CHANNELS_TABLE_NAME = "dev_channels"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                ChannelItem::class.java to CHANNELS_TABLE_NAME
            )
        )
    }
}
