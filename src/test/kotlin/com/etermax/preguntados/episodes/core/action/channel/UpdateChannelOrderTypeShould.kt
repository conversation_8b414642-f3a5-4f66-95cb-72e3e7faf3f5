package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.ChannelMother.aChannel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelOrderType
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUpdateOrderTypeRepository
import com.etermax.preguntados.episodes.core.domain.exception.ChannelNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.PlayerNotOwnChannelException
import com.etermax.preguntados.episodes.core.doubles.channel.repository.InMemoryChannelRepository
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UpdateChannelOrderTypeShould {
    private lateinit var repository: ChannelRepository
    private lateinit var orderTypeRepository: ChannelUpdateOrderTypeRepository
    private lateinit var action: UpdateChannelOrderType

    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        repository = InMemoryChannelRepository()
        orderTypeRepository = mockk(relaxUnitFun = true)
        action = UpdateChannelOrderType(repository, orderTypeRepository)

        error = null
    }

    @Test
    fun `change order from DATE_ADDED to CUSTOM_ORDER`() = runTest {
        givenAChannel(ChannelOrderType.DATE_ADDED)
        whenUpdate(ChannelOrderType.CUSTOM_ORDER)
        thenUpdateOrderTypeTo(ChannelOrderType.CUSTOM_ORDER)
    }

    @Test
    fun `change order from CUSTOM_ORDER to DATE_ADDED`() = runTest {
        givenAChannel(ChannelOrderType.CUSTOM_ORDER)
        whenUpdate(ChannelOrderType.DATE_ADDED)
        thenUpdateOrderTypeTo(ChannelOrderType.DATE_ADDED)
    }

    @Test
    fun `not change order when channel not found`() = runTest {
        whenUpdate(ChannelOrderType.DATE_ADDED)

        thenThrowsException<ChannelNotFoundException>()
        thenNotUpdateOrderType()
    }

    @Test
    fun `not change order when channel not owned by current player`() = runTest {
        givenAChannel(ChannelOrderType.DATE_ADDED, ownerId = ANOTHER_PLAYER_ID)

        whenUpdate(ChannelOrderType.CUSTOM_ORDER)

        thenThrowsException<PlayerNotOwnChannelException>()
        thenNotUpdateOrderType()
    }

    @Test
    fun `not change order from DATE_ADDED to DATE_ADDED`() = runTest {
        givenAChannel(ChannelOrderType.DATE_ADDED)
        whenUpdate(ChannelOrderType.DATE_ADDED)
        thenNotUpdateOrderType()
    }

    @Test
    fun `not change order from CUSTOM_ORDER to CUSTOM_ORDER`() = runTest {
        givenAChannel(ChannelOrderType.CUSTOM_ORDER)
        whenUpdate(ChannelOrderType.CUSTOM_ORDER)
        thenNotUpdateOrderType()
    }

    private suspend fun givenAChannel(orderType: ChannelOrderType, ownerId: Long = PLAYER_ID) {
        val channel = aChannel(id = CHANNEL_ID, orderType = orderType, ownerId = ownerId)
        repository.add(channel)
    }

    private suspend fun whenUpdate(orderType: ChannelOrderType) {
        error = runCatching {
            val data = UpdateChannelOrderType.ActionData(PLAYER_ID, CHANNEL_ID, orderType)
            action(data)
        }.exceptionOrNull()
    }

    private suspend fun thenUpdateOrderTypeTo(orderType: ChannelOrderType) {
        coVerify(exactly = 1) { orderTypeRepository.put(CHANNEL_ID, orderType) }
    }

    private suspend fun thenNotUpdateOrderType() {
        coVerify(exactly = 0) { orderTypeRepository.put(any(), any()) }
    }

    private inline fun <reified T : RuntimeException> thenThrowsException() {
        Assertions.assertThat(error)
            .isNotNull
            .isInstanceOf(T::class.java)
    }

    private companion object {
        const val PLAYER_ID = 100L
        const val ANOTHER_PLAYER_ID = 200L
        const val CHANNEL_ID = "channel_id"
    }
}
