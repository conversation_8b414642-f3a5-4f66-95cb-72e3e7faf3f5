package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.doubles.ChannelUnpublishedEpisodesFactory
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother.aProfile
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FindEpisodeShould {

    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService
    private lateinit var summaryService: SummaryService

    private var result: EpisodeSummary? = null

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk(relaxed = true)
        profileService = mockk(relaxed = true)
        unpublishedEpisodesService = ChannelUnpublishedEpisodesFactory.mock()
        summaryService = SummaryService(profileService, unpublishedEpisodesService)
    }

    @Test
    fun `find none episode`() = runTest {
        givenNoneEpisode()

        whenFind()

        thenThereIsNoneEpisode()
    }

    @Test
    fun `find episodes`() = runTest {
        givenAnEpisode()
        givenAProfile()

        whenFind()

        thenEpisodesAreFound()
    }

    private fun givenNoneEpisode() {
        coEvery { episodeRepository.findById(EPISODE_ID) } returns null
    }

    private fun givenAnEpisode() {
        coEvery { episodeRepository.findById(EPISODE_ID) } returns EPISODE
    }

    private fun givenAProfile() {
        coEvery { profileService.find(EPISODE.ownerId) } returns PROFILE
    }

    private suspend fun whenFind() {
        val actionData = FindEpisode.ActionData(EPISODE_ID)
        val findEpisode = FindEpisode(episodeRepository, summaryService)
        result = findEpisode(actionData)
    }

    private fun thenThereIsNoneEpisode() {
        assertThat(result).isNull()
    }

    private fun thenEpisodesAreFound() {
        assertThat(result).isEqualTo(EpisodeSummary.from(EPISODE, PROFILE))
    }

    private companion object {
        const val EPISODE_ID = "E_123"
        val EPISODE = buildEpisode(id = EPISODE_ID)
        val PROFILE = aProfile(playerId = EPISODE.ownerId)
    }
}
