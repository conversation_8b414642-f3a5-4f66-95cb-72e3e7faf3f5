package com.etermax.preguntados.episodes.core.infrastructure.repository.progress

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.EpisodeMother.LANGUAGE
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.tables.ProgressItem
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoDBProgressRepositoryShould {

    private lateinit var repository: ProgressContentRepository
    private var result: ProgressContent? = null

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        repository = DynamoDBProgressRepository(dynamoDbEnhancedClient, createTable())
    }

    @Test
    fun `save a progress`() = runTest {
        whenSave(LAST_CONTENT_ID)

        thenProgressIsSaved(LAST_CONTENT_ID)
    }

    @Test
    fun `update a progress`() = runTest {
        givenAnProgress(LAST_CONTENT_ID)

        whenSave(ANOTHER_LAST_CONTENT_ID)

        thenProgressIsSaved(ANOTHER_LAST_CONTENT_ID)
    }

    @Test
    fun `find progress`() = runTest {
        givenAnProgress(LAST_CONTENT_ID)
        givenAnProgress(ANOTHER_LAST_CONTENT_ID, ANOTHER_PLAYER_ID)

        whenFindBy(EPISODE_ID, PLAYER_ID)

        thenProgressIs(LAST_CONTENT_ID)
    }

    @Test
    fun `find another progress`() = runTest {
        givenAnProgress(LAST_CONTENT_ID)
        givenAnProgress(ANOTHER_LAST_CONTENT_ID, ANOTHER_PLAYER_ID)

        whenFindBy(EPISODE_ID, ANOTHER_PLAYER_ID)

        thenProgressIs(ANOTHER_LAST_CONTENT_ID)
    }

    @Test
    fun `find not existing progress`() = runTest {
        givenAnProgress(LAST_CONTENT_ID)

        whenFindBy(EPISODE_ID, ANOTHER_PLAYER_ID)

        assertThat(result).isNull()
    }

    private suspend fun givenAnProgress(lastContentId: String, playerId: Long = PLAYER_ID) {
        val progress = ProgressContent(EPISODE_ID, playerId, lastContentId, LANGUAGE.name)
        repository.save(progress)
    }

    private suspend fun whenSave(lastContentId: String) {
        val progress = ProgressContent(EPISODE_ID, PLAYER_ID, lastContentId, LANGUAGE.name)
        repository.save(progress)
    }

    private suspend fun whenFindBy(episodeId: String, playerId: Long) {
        result = repository.findBy(episodeId, playerId)
    }

    private suspend fun thenProgressIsSaved(lastContentId: String) {
        val savedProgress = repository.findBy(EPISODE_ID, PLAYER_ID)
        assertThat(savedProgress?.lastContentId).isEqualTo(lastContentId)
    }

    private fun thenProgressIs(lastContentId: String) {
        assertThat(result?.lastContentId).isEqualTo(lastContentId)
    }

    private fun createTable(): DynamoDbAsyncTable<ProgressItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(TABLE_NAME, TableSchema.fromBean(ProgressItem::class.java))
    }

    private companion object {
        const val TABLE_NAME = "dev_episodes"
        const val PLAYER_ID = 666L
        const val ANOTHER_PLAYER_ID = 999L
        const val EPISODE_ID = "ABC-666"
        const val LAST_CONTENT_ID = "123456"
        const val ANOTHER_LAST_CONTENT_ID = "456789"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(ProgressItem::class.java to "dev_episodes"))
    }
}
