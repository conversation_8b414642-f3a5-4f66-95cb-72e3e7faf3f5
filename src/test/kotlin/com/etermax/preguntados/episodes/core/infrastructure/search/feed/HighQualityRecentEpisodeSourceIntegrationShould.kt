package com.etermax.preguntados.episodes.core.infrastructure.search.feed

import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus.PENDING
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeType.PRIVATE
import com.etermax.preguntados.episodes.core.domain.search.feed.*
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime.now
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class HighQualityRecentEpisodeSourceIntegrationShould :
    AbstractOpenSearchSourceIntegrationShould(indexDescriptor = "opensearch/mapping/episodes-index-mapping-v1.0.json") {

    private lateinit var source: Source<String>

    @BeforeEach
    fun setUp() {
        val requiredLikesForTopRated = 10
        val requiredViewsForTopRated = 10
        source = HighQualityRecentEpisodeSource(osClient, indexName, requiredLikesForTopRated, requiredViewsForTopRated)
    }

    @Test
    fun `retrieve episodes filtered by language`() = runTest {
        givenAnEpisode(
            id = "1",
            country = Country.US,
            language = Language.EN,
            startDate = now().minusHours(1),
            likes = 10,
            views = 10
        )
        givenAnEpisode(id = "2", country = Country.AR, language = Language.ES)
        givenAnEpisode(
            id = "3",
            country = Country.US,
            language = Language.EN,
            startDate = now(),
            likes = 1,
            views = 1
        )

        val result = whenRetrieve(country = Country.US, language = Language.EN)

        assertEquals(listOf("1"), result.items)
    }

    @Test
    fun `retrieve episodes sorted by rate`() = runTest {
        givenAnEpisode(id = "1", startDate = now().minusHours(2), likes = 100, dislikes = 20)
        givenAnEpisode(id = "2", startDate = now().minusHours(1), likes = 100, dislikes = 10)
        givenAnEpisode(id = "3", startDate = now(), likes = 100, dislikes = 2)

        val result = whenRetrieve()

        assertEquals(listOf("3", "2", "1"), result.items)
    }

    @Test
    fun `retrieve only episodes with type PUBLIC`() = runTest {
        givenAnEpisode(id = "1", startDate = now().minusHours(2), likes = 100, dislikes = 10)
        givenAnEpisode(id = "2", startDate = now().minusHours(1), likes = 100, dislikes = 2)
        givenAnEpisode(id = "3", startDate = now(), type = PRIVATE)

        val result = whenRetrieve()

        assertEquals(listOf("2", "1"), result.items)
    }

    @Test
    fun `retrieve only episodes with status PUBLISHED`() = runTest {
        givenAnEpisode(id = "1", startDate = now().minusHours(2), likes = 100, dislikes = 10)
        givenAnEpisode(id = "2", startDate = now().minusHours(1), likes = 100, dislikes = 2)
        givenAnEpisode(id = "3", startDate = now(), status = PENDING)

        val result = whenRetrieve()

        assertEquals(listOf("2", "1"), result.items)
    }

    @Test
    fun `retrieve episodes respecting offset and limit`() = runTest {
        (1..20).forEach { i ->
            givenAnEpisode(
                id = i.toString(),
                startDate = now().minusHours(i.toLong())
            )
        }

        val result = whenRetrieve(offset = 5, limit = 10)

        // Should return episodes 6-15 (because they're sorted by date descending)
        assertEquals((6..15).map { it.toString() }, result.items)
    }

    @Test
    fun `retrieve episodes respecting fetch cursor`() = runTest {
        (1..20).forEach { i ->
            givenAnEpisode(
                id = i.toString(),
                startDate = now().minusHours(i.toLong())
            )
        }

        val result = whenRetrieve(offset = 0, limit = 10)

        val nextResult = whenRetrieve(
            offset = 0,
            limit = 10,
            fetchCursor = result.fetchCursor
        )

        assertEquals((11..20).map { it.toString() }, nextResult.items)
    }

    @Test
    fun `retrieve an specified quantity of episodes till exhausted`() = runTest {
        (1..5).forEach { i ->
            givenAnEpisode(
                id = i.toString(),
                startDate = now().minusHours(i.toLong())
            )
        }

        // Try to retrieve episodes (more than available)
        val result = whenRetrieve(limit = 10)

        // Should return all 5 episodes
        assertEquals(listOf("1", "2", "3", "4", "5"), result.items)
        assertTrue((result.fetchCursor as OffsetFetchCursor).exhausted)
    }

    @Test
    fun `answer successfully when trying to retrieve ZERO episodes`() = runTest {
        givenAnEpisode(id = "1")

        val result = whenRetrieve(limit = 0)

        assertEquals(listOf(), result.items)
    }

    private suspend fun whenRetrieve(
        offset: Int = 0,
        limit: Int = 10,
        country: Country? = null,
        language: Language? = null,
        fetchCursor: FetchCursor? = null
    ): SourceResponse<String> {
        return source.fetch(
            SourceRequest(
                OffsetLimit(
                    offset = offset,
                    limit = limit
                ),
                userId = 1,
                country = country,
                language = language,
                fetchCursor = fetchCursor
            )
        )
    }
}
