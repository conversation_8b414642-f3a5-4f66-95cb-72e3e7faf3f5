package com.etermax.preguntados.episodes.core.infrastructure.episode.repository

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.core.domain.episode.EpisodePlayersByOwnerRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.RedisEpisodePlayersByOwnerRepository
import io.lettuce.core.ClientOptions
import io.lettuce.core.RedisClient
import io.lettuce.core.RedisURI
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration
import java.time.OffsetDateTime
import java.time.ZoneOffset
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@OptIn(ExperimentalCoroutinesApi::class)
@ExtendWith(RedisTestServer::class)
class RedisEpisodePlayersByOwnerRepositoryShould {

    private val uri = RedisURI.create("redis://localhost:${RedisTestServer.port}")
    private val redisAsync = RedisClient.create(uri).also {
        it.options = ClientOptions.builder().autoReconnect(false).build()
    }.connect().async()

    private lateinit var clock: Clock
    private lateinit var repository: EpisodePlayersByOwnerRepository
    private var result: List<Long>? = null

    @BeforeEach
    fun setUp() {
        clock = mockk(relaxed = true)
        givenARepository()
    }

    @Test
    fun `return empty list if there is no persisted players for episode owner`() = runTest {
        whenGet()

        thenEmptyListIsRetrieved()
    }

    @Test
    fun `return players successfully`() = runTest {
        givenPlayers()

        whenGet()

        thenPlayersAreRetrievedCorrectly()
    }

    @Test
    fun `not return players from more than 30 days`() = runTest {
        givenPlayers()

        whenGet()

        thenPlayersFromMoreThan30DaysAreNotReturned()
    }

    @Test
    fun `return empty list if repo is disabled`() = runTest {
        givenARepository(isEnabled = false)
        givenPlayers()

        whenGet()

        thenEmptyListIsRetrieved()
    }

    private fun givenARepository(isEnabled: Boolean = true) {
        repository = RedisEpisodePlayersByOwnerRepository(
            redisAsync,
            clock,
            Duration.ofDays(30),
            isEnabled
        )
    }

    private suspend fun givenPlayers() {
        coEvery { clock.now() } returns NOW.minusDays(31)
        repository.addPlayer(OWNER_ID, 6L)

        coEvery { clock.now() } returns NOW.minusDays(30).minusSeconds(1)
        repository.addPlayer(OWNER_ID, 5L)

        coEvery { clock.now() } returns NOW.minusDays(29).minusHours(23)
        repository.addPlayer(OWNER_ID, 4L)

        coEvery { clock.now() } returns NOW.minusMinutes(2)
        repository.addPlayer(OWNER_ID, 3L)

        coEvery { clock.now() } returns NOW.minusMinutes(1)
        repository.addPlayer(OWNER_ID, 2L)

        coEvery { clock.now() } returns NOW
        repository.addPlayer(OWNER_ID, 1L)
    }

    private fun thenEmptyListIsRetrieved() {
        assertTrue(result!!.isEmpty())
    }

    private fun thenPlayersAreRetrievedCorrectly() {
        assertEquals(listOf(1L, 2L, 3L, 4L), result)
    }

    private fun thenPlayersFromMoreThan30DaysAreNotReturned() {
        assertFalse(result!!.contains(5))
        assertFalse(result!!.contains(6))
    }

    private suspend fun whenGet() {
        coEvery { clock.now() } returns NOW
        result = repository.get(OWNER_ID)
    }

    companion object {
        private const val OWNER_ID = 123L
        private val NOW = OffsetDateTime.of(2025, 1, 1, 12, 0, 0, 0, ZoneOffset.UTC)
    }
}
