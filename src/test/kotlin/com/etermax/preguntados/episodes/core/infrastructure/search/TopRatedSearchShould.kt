package com.etermax.preguntados.episodes.core.infrastructure.search

import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.filters.SortEpisode
import com.etermax.preguntados.episodes.core.domain.search.SearchParameters
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.episodes.utils.LocalOpenSearch
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import kotlin.test.assertEquals

class TopRatedSearchShould {
    private val indexNamePerTest = "episodes-${System.currentTimeMillis()}"
    private val osClient = LocalOpenSearch.buildOpenSearch(
        LocalOpenSearch.Config(indexNamePerTest, "opensearch/mapping/episodes-index-mapping-v2.0.json")
    )
    private val tokenizer: Tokenizer = mockk(relaxed = true)
    private val topRatedSearch: TopRatedSearch = TopRatedSearch(
        client = osClient,
        indexName = indexNamePerTest,
        requiredLikesForTopRated = REQUIRED_LIKES,
        requiredViewsForTopRated = REQUIRED_VIEWS,
        tokenizer = tokenizer
    )

    @ParameterizedTest(name = "Sort: {0}, Name: ''{1}'' → Expected: {2}")
    @CsvSource(
        value = [
            "LIKES, '', true",
            "RECENT, '', false",
            "LIKES, test, false"
        ]
    )
    fun `match only for LIKE and empty text name`(sort: SortEpisode, name: String, expected: Boolean) = runTest {
        val params = givenParameters(sort = sort, name = name)

        val actualResult = whenMatch(params)

        assertEquals(expected, actualResult, "Input: $sort, $name")
    }

    @Test
    fun `return top rated episodes`() = runTest {
        // 90% rate and 2000 views --> 3rd place
        val episode1 = buildEpisode(id = "E_123", likes = 135, dislikes = 15, views = 2000)
        // 100% rate and 2000 views --> Almost the best (tie with rate with episode3, but lesser views)
        val episode2 = buildEpisode(id = "E_999", likes = 200, dislikes = 0, views = 2000)
        // 100% rate and 3000 views --> The best
        val episode3 = buildEpisode(id = "E_456", likes = 200, dislikes = 0, views = 3000)
        // 80% rate and 3000 views --> The worst, 4th place
        val episode4 = buildEpisode(id = "E_789", likes = 200, dislikes = 50, views = 3000, language = Language.ES, country = Country.AR)

        givenSavedEpisodes(listOf(episode1, episode2, episode3, episode4))

        val params = givenParameters()
        val actualResultIds = whenSearch(params).map { it.id }

        val sortedEpisodesByRateAndViews = listOf(episode3, episode2, episode1, episode4)
        val expectedIds = sortedEpisodesByRateAndViews.map { it.id }
        assertEquals(expectedIds, actualResultIds)
    }

    private fun givenParameters(
        sort: SortEpisode = SortEpisode.LIKES,
        name: String = "",
        language: Language? = null,
        country: Country? = null,
        offset: Int = 0,
        limit: Int = 10
    ): SearchParameters = SearchParameters(
        playerId = 1L,
        language = language,
        country = country,
        name = name,
        sort = sort,
        offset = offset,
        limit = limit
    )

    private fun givenSavedEpisodes(episodes: List<Episode>) {
        episodes.forEach { episode ->
            LocalOpenSearch.saveToOpenSearch(
                osClient,
                indexNamePerTest,
                EpisodeItem.buildPartitionKey(episode.id),
                EpisodeItem.from(episode)
            )
        }
    }

    private suspend fun whenMatch(params: SearchParameters): Boolean = topRatedSearch.match(params)

    private suspend fun whenSearch(params: SearchParameters): List<Episode> = topRatedSearch.search(params)

    private companion object {
        const val REQUIRED_LIKES = 100
        const val REQUIRED_VIEWS = 1000
    }
}
