package com.etermax.preguntados.episodes.core.infrastructure.ranking

import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankedPlayer
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Ranking
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingEntry
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import io.github.resilience4j.circuitbreaker.CircuitBreaker
import io.github.resilience4j.retry.Retry
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class HttpRankingRepositoryShould {

    private lateinit var httpClient: HttpClient

    private var error: Throwable? = null
    private var rankingResult: Ranking? = null
    private var rankingForPlayersResult: List<RankedPlayer>? = null

    @Test
    fun `return null when fails finding ranking`() = runTest {
        givenAFailingClient()

        whenFind()

        thenThereIsNoRanking()
    }

    @Test
    fun `find player ranking`() = runTest {
        givenAClient(URL_FIND)

        whenFind()

        assertThat(rankingResult).isEqualTo(RANKING_RESPONSE)
    }

    @Test
    fun `retrieve no player when service is empty`() = runTest {
        givenAClient(URL_FIND, statusCode = HttpStatusCode.Created)

        whenFind()

        thenThereIsNoRanking()
    }

    @Test
    fun `return null when fails finding ranking for players`() = runTest {
        givenAFailingClient()

        whenFindForPlayers()

        thenThereIsNoRankingForPlayers()
    }

    @Test
    fun `find ranking for players`() = runTest {
        givenAClient(URL_PLAYERS, REQUEST, RESPONSE_FOR_PLAYERS)

        whenFindForPlayers()

        assertThat(rankingForPlayersResult).isEqualTo(RANKING_FOR_PLAYERS_RESPONSE)
    }

    @Test
    fun `retrieve empty players list when service is empty`() = runTest {
        givenAClient(URL_PLAYERS, REQUEST, RESPONSE_FOR_PLAYERS, statusCode = HttpStatusCode.Created)

        whenFindForPlayers()

        thenThereIsNoRankingForPlayers()
    }

    @Test
    fun `increment score`() = runTest {
        givenAClient(URL_SCORE)

        whenScore()

        assertThat(error).isNull()
    }

    private fun givenAFailingClient() {
        httpClient = HttpClient(
            MockEngine {
                respondError(HttpStatusCode.BadRequest, "this is the body of the error response")
            }
        ) {
            install(ContentNegotiation) {
                json(Json { this.ignoreUnknownKeys = true })
            }
            install(HttpTimeout) {
                requestTimeoutMillis = 3000
            }
        }
    }

    private fun givenAClient(url: String, bodyRequest: String? = null, response: String = RESPONSE, statusCode: HttpStatusCode = HttpStatusCode.OK) {
        httpClient = HttpClient(
            MockEngine {
                assert(it.url.encodedPathAndQuery == url) { "Unexpected URL: $url" }

                bodyRequest?.let { request ->
                    val requestBody = it.body.toByteArray().toString(Charsets.UTF_8)
                    assert(requestBody == request) { "Unexpected body: $requestBody" }
                }

                request {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    expectSuccess = true
                }

                respond(
                    response,
                    statusCode,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        ) {
            install(ContentNegotiation) {
                json(Json { this.ignoreUnknownKeys = true })
            }
            install(HttpTimeout) {
                requestTimeoutMillis = 3000
            }
        }
    }

    private suspend fun whenFind() {
        val service = HttpRankingRepository(httpClient, RESILIENCE_BUNDLE, GAME_ID)
        rankingResult = service.find(PLAYER_ID, DOMAIN, PLAYERS_COUNT)
    }

    private suspend fun whenFindForPlayers() {
        val service = HttpRankingRepository(httpClient, RESILIENCE_BUNDLE, GAME_ID)
        rankingForPlayersResult = service.findForPlayers(PLAYER_ID, DOMAIN, PLAYERS)
    }

    private suspend fun whenScore() {
        val service = HttpRankingRepository(httpClient, RESILIENCE_BUNDLE, GAME_ID)
        error = kotlin.runCatching {
            service.incrementScore(PLAYER_ID, DOMAIN, SCORE)
        }.exceptionOrNull()
    }

    private fun thenThereIsNoRanking() {
        assertThat(rankingResult).isNull()
    }

    private fun thenThereIsNoRankingForPlayers() {
        assertThat(rankingForPlayersResult).isEmpty()
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val GAME_ID = "P2_DEV"
        val DOMAIN = Domain("ABC-123")
        const val PLAYERS_COUNT = 26
        val PLAYERS = listOf(PLAYER_ID, 20, 30)
        const val SCORE = 250
        const val SCOPE = "tc:ep:%s"
        const val URL_BASE = "/games/%s/users/%s/global-ranking"
        const val URL = "/games/%s/users/%s/global-ranking?scope=%s&from=1&amount=%d"
        val RESILIENCE_BUNDLE = EndpointResilienceBundle(CircuitBreaker.ofDefaults("p"), Retry.ofDefaults("s"))
        const val RESPONSE = """
            {
                "userRanking": [
                    {
                        "position": 10,
                        "userData": {
                            "userId": "$PLAYER_ID"
                        },
                        "score": 200
                    }
                ],
                "userRank": {
                    "position": 10,
                    "userData": {
                        "userId": "$PLAYER_ID"
                    },
                    "score": 200
                }
            }
        """
        const val RESPONSE_FOR_PLAYERS = """
            [
                {
                    "position": 1,
                    "userData": {
                        "userId": "$PLAYER_ID"
                    },
                    "score": 300
                },
                {
                    "position": 2,
                    "userData": {
                        "userId": "20"
                    },
                    "score": 200
                },
                {
                    "position": 3,
                    "userData": {
                        "userId": "30"
                    },
                    "score": 100
                }
            ]
        """
        val REQUEST = """
            {
                "players_id": [
                    {"id": $PLAYER_ID},
                    {"id": 20},
                    {"id": 30}
                ]
            }
        """.trimIndent().replace(Regex("\\s+"), "")

        val RANKED_PLAYER = RankedPlayer(PLAYER_ID, RankingEntry(10, 200))
        val RANKING_RESPONSE = Ranking(listOf(RANKED_PLAYER), RANKED_PLAYER)
        val RANKING_FOR_PLAYERS_RESPONSE = listOf(
            RankedPlayer(PLAYER_ID, RankingEntry(position = 1, points = 300)),
            RankedPlayer(20, RankingEntry(position = 2, points = 200)),
            RankedPlayer(30, RankingEntry(position = 3, points = 100))
        )
        val URL_FIND = URL.format(GAME_ID, PLAYER_ID, SCOPE.format(DOMAIN.value), PLAYERS_COUNT)
        val URL_PLAYERS = "$URL_BASE/players?scope=%s".format(GAME_ID, PLAYER_ID, SCOPE.format(DOMAIN.value))
        val URL_SCORE = "$URL_BASE/addScore".format(GAME_ID, PLAYER_ID)
    }
}
