package com.etermax.preguntados.episodes.core.domain.quality.service

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelScoreRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.quality.likerate.EpisodeLikeRateAvgCalculator
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ChannelScoreOnErrorShould {

    private lateinit var channelEpisodesRepository: ChannelEpisodesRepository
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var channelRepository: ChannelRepository
    private lateinit var defaultQualityService: DefaultQualityService
    private lateinit var episodeLikeRateAvgCalculator: EpisodeLikeRateAvgCalculator
    private lateinit var channelScoreRepository: ChannelScoreRepository

    @BeforeEach
    fun setUp() {
        channelEpisodesRepository = mockk()
        episodeRepository = mockk()
        channelRepository = mockk()
        episodeLikeRateAvgCalculator = EpisodeLikeRateAvgCalculator()
        channelScoreRepository = mockk()
        defaultQualityService =
            DefaultQualityService(
                channelEpisodesRepository,
                episodeRepository,
                channelRepository,
                episodeLikeRateAvgCalculator,
                channelScoreRepository,
                minimumRates = 30,
                minimumViews = 100
            )
    }

    @Test
    fun `should not calculate channel score when episode without channel ID`() = runTest {
        // given
        val episode = givenEpisodeWithRates(likes = 20, dislikes = 10)
        val episodeWithoutChannel = episode.copy(channelId = null)

        // when
        whenToProcessChannelScore(
            episodeBeforeUpdate = episodeWithoutChannel,
            episodeAfterUpdate = episodeWithoutChannel
        )

        // then
        coVerify(exactly = 0) { channelEpisodesRepository.findAllEpisodeIdsFrom(any()) }
        coVerify(exactly = 0) { episodeRepository.findByIds(any()) }
        coVerify(exactly = 0) { channelRepository.put(any()) }
        coVerify(exactly = 0) { channelScoreRepository.updateScore(any(), any()) }
    }

    @Test
    fun `should handle channel repository put error`() = runTest {
        // given
        val episode = givenEpisodeWithRates(likes = 20, dislikes = 10) // 66% like rate
        val episodeWithOneView = givenLikeRatedEpisodeFrom(episode) // Inc likes to 21, 67% like rate
        givenRepositoriesWith(episodeWithOneView)
        coEvery { channelScoreRepository.updateScore(any(), any()) } throws RuntimeException("Failed to update channel")

        // when
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode,
            episodeAfterUpdate = episodeWithOneView
        )

        // then
        // No exception should be thrown, error should be logged
        thenChannelScoreIsUpdatedTo(score = 67)
    }

    @Test
    fun `should handle negative like rates`() = runTest {
        // given
        val episode = EpisodeMother.buildEpisode(
            id = "episode-1",
            channelId = CHANNEL.id,
            likes = -10, // Negative likes
            dislikes = 40,
            views = 150
        )
        val episodeAfterLikeRate = givenLikeRatedEpisodeFrom(episode)
        givenRepositoriesWith(episode)

        // when
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode,
            episodeAfterUpdate = episodeAfterLikeRate
        )

        // then
        thenChannelScoreIsUpdatedTo(score = 0)
    }

    @Test
    fun `should handle negative dislike rates`() = runTest {
        // given
        val episode = EpisodeMother.buildEpisode(
            id = "episode-1",
            channelId = CHANNEL.id,
            likes = 50, // Negative likes
            dislikes = -40,
            views = 150
        )
        val episodeAfterLikeRate = givenLikeRatedEpisodeFrom(episode)
        givenRepositoriesWith(episode)

        // when
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode,
            episodeAfterUpdate = episodeAfterLikeRate
        )

        // then
        thenChannelScoreIsUpdatedTo(score = 100)
    }

    @Test
    fun `should handle negative views`() = runTest {
        // given
        val episode = EpisodeMother.buildEpisode(
            id = "episode-1",
            channelId = CHANNEL.id,
            likes = 1,
            dislikes = 2,
            views = -50 // Negative views
        )
        val episodeAfterLikeRate = givenLikeRatedEpisodeFrom(episode)
        givenRepositoriesWith(episode)

        // when
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode,
            episodeAfterUpdate = episodeAfterLikeRate
        )

        // then
        thenChannelScoreIsNotUpdated()
    }

    /**
     * ***************
     * ***************
     * Given
     * ***************
     * ***************
     */

    private fun givenRepositoriesWith(episode: Episode) {
        coEvery { channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL.id) } returns listOf(episode.id)
        coEvery { episodeRepository.findByIds(listOf(episode.id)) } returns listOf(episode)
        coEvery { channelRepository.findById(CHANNEL.id) } returns CHANNEL
    }

    private fun givenLikeRatedEpisodeFrom(episode: Episode) =
        episode.copy(rate = episode.rate.copy(_likes = episode.rate.likes + 1))

    private fun givenEpisodeWithRates(likes: Long, dislikes: Long) =
        EpisodeMother.buildEpisode(
            id = EPISODE_ID_1,
            channelId = CHANNEL.id,
            likes = likes,
            dislikes = dislikes,
            views = 50
        )

    /**
     * ***************
     * ***************
     * When to process channel score
     * ***************
     * ***************
     */
    private suspend fun whenToProcessChannelScore(episodeBeforeUpdate: Episode, episodeAfterUpdate: Episode) {
        defaultQualityService.calculateChannelScore(
            episodeBeforeUpdate = episodeBeforeUpdate,
            episodeAfterUpdate = episodeAfterUpdate
        )
    }

    /**
     * ***************
     * ***************
     * Then channel score is not updated
     * ***************
     * ***************
     */
    private fun thenChannelScoreIsNotUpdated() {
        coVerify(exactly = 0) { channelScoreRepository.updateScore(any(), any()) }
    }

    private fun thenChannelScoreIsUpdatedTo(score: Int) {
        coVerify { channelScoreRepository.updateScore(channelId = CHANNEL.id, score = score) }
    }

    private companion object {
        val CHANNEL = ChannelMother.aChannel(id = "channel-id")
        val EPISODE_ID_1 = "episode-id-1"
    }
}
