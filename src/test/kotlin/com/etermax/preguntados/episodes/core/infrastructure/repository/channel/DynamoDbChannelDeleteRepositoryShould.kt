package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.AssignChannelIdToEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.RemoveChannelFromEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelEpisodesCountItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.DynamoDBEpisodeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeByOwnerItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.services.dynamodb.model.*

class DynamoDbChannelDeleteRepositoryShould {
    private lateinit var channelRepository: DynamoDBChannelRepository
    private lateinit var episodesRepository: DynamoDBEpisodeRepository
    private lateinit var channelEpisodesRepository: DynamoChannelEpisodesRepository
    private lateinit var addEpisodesToChannelRepositories: DynamoDbAddEpisodesToChannelRepository
    private lateinit var deleteRepository: DynamoDbChannelDeleteRepository

    private var channel: Channel? = null

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        val channelTable = createChannelTable()
        val channelEpisodesTable = createChannelEpisodeTable()

        createEpisodesTable()
        channelRepository = DynamoDBChannelRepository(dynamoDbEnhancedClient, channelTable)
        episodesRepository =
            DynamoDBEpisodeRepository(dynamoDbEnhancedClient, dynamoDbAsyncEpisodesTable(), episodeByOwnerIndex())

        channelEpisodesRepository = DynamoChannelEpisodesRepository(
            dynamoDbEnhancedClient,
            createUpdateChannelEpisodesCountTable(),
            channelEpisodesTable,
            channelEpisodesOrderNormalizer = mockk(relaxed = true)
        )

        deleteRepository = DynamoDbChannelDeleteRepository(
            dynamoDbEnhancedClient,
            channelTable,
            channelEpisodesTable,
            createRemoveChannelIdFromEpisodeTable(),
            channelEpisodesRepository
        )

        addEpisodesToChannelRepositories = DynamoDbAddEpisodesToChannelRepository(
            dynamoDbEnhancedClient,
            createAddChannelToEpisodesTable(),
            channelTable,
            channelEpisodesTable
        )
    }

    @Test
    fun `remove channel`() = runTest {
        givenAChannel()
        whenDelete()
        thenChannelIsDeleted()
    }

    @Test
    fun `remove channel episodes link`() = runTest {
        givenAChannel()
        givenEpisodesFromChannel()

        whenDelete()

        thenChannelEpisodesAreDeleted()
    }

    @Test
    fun `remove channelId from episodes items`() = runTest {
        givenAChannel()
        givenEpisodesFromChannel()

        whenDelete()

        thenChannelIdIsRemovedFromEpisodes()
    }

    @Test
    fun `remove channel with more than 100 items`() = runTest {
        givenAChannel()
        given60EpisodesFromChannel()

        whenDelete()

        thenAllItemsFromChannelAreRemoved()
    }

    private suspend fun givenAChannel() {
        channelRepository.add(ChannelMother.aChannel(CHANNEL_ID))
    }

    private suspend fun givenEpisodesFromChannel() {
        addEpisodesToChannelRepositories.add(ChannelMother.aChannel(CHANNEL_ID), CHANNEL_EPISODES)
        EPISODES.forEach { episodesRepository.save(it) }
    }

    private suspend fun given60EpisodesFromChannel() {
        val channelsEpisodes = List(60) { ChannelMother.aChannelEpisode(episodeId = "episode_$it") }
        val episodes = List(60) { EpisodeMother.buildEpisode(id = "episode_$it") }

        addEpisodesToChannelRepositories.add(ChannelMother.aChannel(CHANNEL_ID), channelsEpisodes.toSet())
        episodes.forEach { episodesRepository.save(it) }
    }

    private suspend fun whenDelete() {
        deleteRepository.delete(CHANNEL_ID)
    }

    private suspend fun thenChannelIsDeleted() {
        channel = channelRepository.findById(CHANNEL_ID)
        assertThat(channel).isNull()
    }

    private suspend fun thenChannelEpisodesAreDeleted() {
        val channelEpisodes = channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL_ID)
        assertThat(channelEpisodes).isEmpty()
    }

    private suspend fun thenChannelIdIsRemovedFromEpisodes() {
        val episodes = episodesRepository.findByIds(EPISODES.map { it.id })
        assertThat(episodes.count()).isEqualTo(3)
        assertThat(episodes).containsExactlyInAnyOrder(*EPISODES_WITHOUT_CHANNEL_ID.toTypedArray())
    }

    private suspend fun thenAllItemsFromChannelAreRemoved() {
        thenChannelIsDeleted()

        val episodesIds = List(60) { "episode_$it" }
        val episodes = episodesRepository.findByIds(episodesIds)
        assertThat(episodes.count()).isEqualTo(60)

        val episodesChannelsIds = episodes.mapNotNull { it.channelId }
        assertThat(episodesChannelsIds).isEmpty()

        val channelEpisodes = channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL_ID)
        assertThat(channelEpisodes).isEmpty()
    }

    private fun createChannelTable(): DynamoDbAsyncTable<ChannelItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(ChannelItem::class.java))
    }

    private fun createChannelEpisodeTable(): DynamoDbAsyncTable<ChannelEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_EPISODES_TABLE_NAME, TableSchema.fromBean(ChannelEpisodeItem::class.java))
    }

    private fun createAddChannelToEpisodesTable(): DynamoDbAsyncTable<AssignChannelIdToEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(
                EPISODES_TABLE_NAME,
                TableSchema.fromBean(
                    AssignChannelIdToEpisodeItem::class.java
                )
            )
    }

    private fun createUpdateChannelEpisodesCountTable(): DynamoDbAsyncTable<UpdateChannelEpisodesCountItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(
                CHANNELS_TABLE_NAME,
                TableSchema.fromBean(
                    UpdateChannelEpisodesCountItem::class.java
                )
            )
    }

    private fun createRemoveChannelIdFromEpisodeTable(): DynamoDbAsyncTable<RemoveChannelFromEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(
                EPISODES_TABLE_NAME,
                TableSchema.fromBean(
                    RemoveChannelFromEpisodeItem::class.java
                )
            )
    }

    private fun dynamoDbAsyncEpisodesTable(): DynamoDbAsyncTable<EpisodeItem> {
        return dynamoDbEnhancedClient.table(EPISODES_TABLE_NAME, TableSchema.fromBean(EpisodeItem::class.java))
    }

    private fun episodeByOwnerIndex(): DynamoDbAsyncIndex<EpisodeByOwnerItem> {
        return dynamoDbEnhancedClient.table(
            EPISODES_TABLE_NAME,
            TableSchema.fromBean(
                EpisodeByOwnerItem::class.java
            )
        )
            .index(EPISODE_INDEX_NAME)
    }

    private fun createEpisodesTable() {
        val request = CreateTableRequest.builder()
            .tableName(EPISODES_TABLE_NAME)
            .keySchema(
                listOf(
                    KeySchemaElement.builder().attributeName("PK").keyType(KeyType.HASH).build(),
                    KeySchemaElement.builder().attributeName("SK").keyType(KeyType.RANGE).build()
                )
            )
            .attributeDefinitions(
                listOf(
                    AttributeDefinition.builder().attributeName("PK").attributeType(ScalarAttributeType.S).build(),
                    AttributeDefinition.builder().attributeName("SK").attributeType(ScalarAttributeType.S).build(),
                    AttributeDefinition.builder().attributeName("owner_id").attributeType(ScalarAttributeType.N)
                        .build()
                )
            )
            .globalSecondaryIndexes(
                listOf(
                    GlobalSecondaryIndex.builder()
                        .indexName(EPISODE_INDEX_NAME)
                        .keySchema(
                            listOf(
                                KeySchemaElement.builder().attributeName("owner_id").keyType(KeyType.HASH).build()
                            )
                        )
                        .projection(
                            Projection.builder()
                                .projectionType(ProjectionType.INCLUDE)
                                .nonKeyAttributes("PK")
                                .build()
                        )
                        .provisionedThroughput(
                            ProvisionedThroughput.builder()
                                .readCapacityUnits(10)
                                .writeCapacityUnits(10)
                                .build()
                        )
                        .build()
                )
            )
            .provisionedThroughput(
                ProvisionedThroughput.builder()
                    .readCapacityUnits(10)
                    .writeCapacityUnits(10)
                    .build()
            )
            .build()

        dynamoDbTestServer.buildClient().createTable(request)
    }

    private companion object {
        const val TABLE_PREFIX = "dev"
        const val EPISODES_TABLE_NAME = "${TABLE_PREFIX}_episodes"
        const val CHANNELS_TABLE_NAME = "${TABLE_PREFIX}_channels"
        const val CHANNELS_EPISODES_TABLE_NAME = "${TABLE_PREFIX}_channels_episodes"

        const val EPISODE_INDEX_NAME = "by-owner-episode-index"

        const val CHANNEL_ID = ChannelMother.ID

        val CHANNEL_EPISODES = setOf(
            ChannelMother.aChannelEpisode(episodeId = "episode_1"),
            ChannelMother.aChannelEpisode(episodeId = "episode_2"),
            ChannelMother.aChannelEpisode(episodeId = "episode_3")
        )

        val EPISODES = setOf(
            EpisodeMother.buildEpisode(id = "episode_1", channelId = CHANNEL_ID),
            EpisodeMother.buildEpisode(id = "episode_2", channelId = CHANNEL_ID),
            EpisodeMother.buildEpisode(id = "episode_3", channelId = CHANNEL_ID)
        )

        val EPISODES_WITHOUT_CHANNEL_ID = setOf(
            EpisodeMother.buildEpisode(id = "episode_1", channelId = null),
            EpisodeMother.buildEpisode(id = "episode_2", channelId = null),
            EpisodeMother.buildEpisode(id = "episode_3", channelId = null)
        )

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                ChannelItem::class.java to CHANNELS_TABLE_NAME,
                ChannelEpisodeItem::class.java to CHANNELS_EPISODES_TABLE_NAME
            )
        )
    }
}
