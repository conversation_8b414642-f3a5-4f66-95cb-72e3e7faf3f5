package com.etermax.preguntados.episodes.core.infrastructure.ranking

import com.etermax.preguntados.episodes.core.domain.episode.ranking.CalculatorInfo
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class TimeRankingPointsCalculatorShould {

    private var result: Int = -1
    private lateinit var info: CalculatorInfo

    @Test
    fun `no points when no total time`() {
        givenInfo(totalTime = null)

        whenCalculate()

        thenPointsAre(points = NO_POINTS)
    }

    @Test
    fun `calculate points`() {
        givenInfo(totalTime = TOTAL_TIME)

        whenCalculate()

        thenPointsAre(points = 25)
    }

    @Test
    fun `calculate max possible points`() {
        givenInfo(elapsedTime = 0, totalTime = TOTAL_TIME)

        whenCalculate()

        thenPointsAre(points = TOTAL_POINTS)
    }

    @Test
    fun `calculate min possible points`() {
        givenInfo(elapsedTime = TOTAL_TIME, totalTime = TOTAL_TIME)

        whenCalculate()

        thenPointsAre(points = NO_POINTS)
    }

    @Test
    fun `no points when elapsed time is bigger than total time`() {
        givenInfo(elapsedTime = TOTAL_TIME * 3, totalTime = TOTAL_TIME)

        whenCalculate()

        thenPointsAre(points = NO_POINTS)
    }

    private fun givenInfo(elapsedTime: Int = ELAPSED_TIME, totalTime: Int?) {
        info = CalculatorInfo(elapsedTime, totalTime)
    }

    private fun whenCalculate() {
        val calculator = TimeRankingPointsCalculator()
        result = calculator.calculate(info)
    }

    private fun thenPointsAre(points: Int) {
        assertThat(result).isEqualTo(points)
    }

    private companion object {
        const val NO_POINTS = 0
        const val TOTAL_TIME = 10000
        const val ELAPSED_TIME = 5000
        const val TOTAL_POINTS = 50
    }
}
