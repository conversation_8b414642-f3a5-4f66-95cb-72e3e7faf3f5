package com.etermax.preguntados.episodes.core.domain.quality.likerate

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class EpisodeLikeRateAvgCalculatorShould {

    private lateinit var calculator: EpisodeLikeRateAvgCalculator

    @BeforeEach
    fun setUp() {
        calculator = EpisodeLikeRateAvgCalculator()
    }

    @Test
    fun `should return 0 when episodes list is empty`() {
        // when
        val result = calculator.calculateAverage(CHANNEL.id, emptyList())

        // then
        assertEquals(0, result)
    }

    @Test
    fun `should calculate correct average with single episode`() {
        // given
        val episode = givenEpisodeWithRates(likes = 80, dislikes = 20)

        // when
        val result = calculator.calculateAverage(CHANNEL.id, listOf(episode))

        // then
        assertEquals(80, result) // (80/100) * 100 = 80
    }

    @Test
    fun `should calculate correct average with multiple episodes`() {
        // given
        val episodes = listOf(
            givenEpisodeWithRates(likes = 80, dislikes = 20), // 80%
            givenEpisodeWithRates(likes = 60, dislikes = 40), // 60%
            givenEpisodeWithRates(likes = 40, dislikes = 60) // 40%
        )

        // when
        val result = calculator.calculateAverage(CHANNEL.id, episodes)

        // then
        assertEquals(60, result) // (80 + 60 + 40) / 3 = 60
    }

    @Test
    fun `should handle episodes with zero rates`() {
        // given
        val episodes = listOf(
            givenEpisodeWithRates(likes = 0, dislikes = 0), // 0%
            givenEpisodeWithRates(likes = 80, dislikes = 20) // 80%
        )

        // when
        val result = calculator.calculateAverage(CHANNEL.id, episodes)

        // then
        assertEquals(40, result) // (0 + 80) / 2 = 40
    }

    @Test
    fun `should handle episodes with only likes`() {
        // given
        val episodes = listOf(
            givenEpisodeWithRates(likes = 100, dislikes = 0), // 100%
            givenEpisodeWithRates(likes = 50, dislikes = 0) // 100%
        )

        // when
        val result = calculator.calculateAverage(CHANNEL.id, episodes)

        // then
        assertEquals(100, result) // (100 + 100) / 2 = 100
    }

    @Test
    fun `should handle episodes with only dislikes`() {
        // given
        val episodes = listOf(
            givenEpisodeWithRates(likes = 0, dislikes = 100), // 0%
            givenEpisodeWithRates(likes = 0, dislikes = 50) // 0%
        )

        // when
        val result = calculator.calculateAverage(CHANNEL.id, episodes)

        // then
        assertEquals(0, result) // (0 + 0) / 2 = 0
    }

    @Test
    fun `should handle negative rates by converting to zero`() {
        // given
        val episodes = listOf(
            givenEpisodeWithRates(likes = -10, dislikes = 20), // 0%
            givenEpisodeWithRates(likes = 80, dislikes = -5) // 80%
        )

        // when
        val result = calculator.calculateAverage(CHANNEL.id, episodes)

        // then
        assertEquals(50, result) // (0 + 80) / 2 = 40
    }

    @Test
    fun `should handle episodes with equal likes and dislikes`() {
        // given
        val episodes = listOf(
            givenEpisodeWithRates(likes = 50, dislikes = 50), // 50%
            givenEpisodeWithRates(likes = 30, dislikes = 30) // 50%
        )

        // when
        val result = calculator.calculateAverage(CHANNEL.id, episodes)

        // then
        assertEquals(50, result) // (50 + 50) / 2 = 50
    }

    @Test
    fun `should handle episodes with maximum possible rates`() {
        // given
        val episodes = listOf(
            givenEpisodeWithRates(likes = Long.MAX_VALUE, dislikes = 0), // 100%
            givenEpisodeWithRates(likes = 0, dislikes = Long.MAX_VALUE) // 0%
        )

        // when
        val result = calculator.calculateAverage(CHANNEL.id, episodes)

        // then
        assertEquals(50, result) // (100 + 0) / 2 = 50
    }

    @Test
    fun `should handle episodes with very small rates`() {
        // given
        val episodes = listOf(
            givenEpisodeWithRates(likes = 1, dislikes = 1), // 50%
            givenEpisodeWithRates(likes = 2, dislikes = 2) // 50%
        )

        // when
        val result = calculator.calculateAverage(CHANNEL.id, episodes)

        // then
        assertEquals(50, result) // (50 + 50) / 2 = 50
    }

    private fun givenEpisodeWithRates(likes: Long, dislikes: Long): Episode {
        return EpisodeMother.buildEpisode(
            id = "episode-1",
            channelId = CHANNEL.id,
            likes = likes,
            dislikes = dislikes,
            views = 150
        )
    }

    private companion object {
        val CHANNEL = ChannelMother.aChannel(id = "channel-id")
    }
}
