package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother.BANNER
import com.etermax.preguntados.episodes.core.EpisodeMother.CONTENTS
import com.etermax.preguntados.episodes.core.EpisodeMother.COUNTRY
import com.etermax.preguntados.episodes.core.EpisodeMother.COVER
import com.etermax.preguntados.episodes.core.EpisodeMother.EPISODE_STATUS
import com.etermax.preguntados.episodes.core.EpisodeMother.EPISODE_TYPE
import com.etermax.preguntados.episodes.core.EpisodeMother.LANGUAGE
import com.etermax.preguntados.episodes.core.EpisodeMother.NAME
import com.etermax.preguntados.episodes.core.EpisodeMother.SEQUENCE_ID
import com.etermax.preguntados.episodes.core.EpisodeMother.START_DATE
import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.action.episode.CreateEpisode.ActionData
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.*
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus.DRAFT
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus.PUBLISHED
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNameEmptyException
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNameNotAllowedException
import com.etermax.preguntados.episodes.core.domain.exception.MandatoryAttributeValidateException
import com.etermax.preguntados.episodes.core.domain.moderation.ModerationService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother.aProfile
import com.etermax.preguntados.episodes.core.infrastructure.sequencer.UUIDSequencer
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class CreateEpisodeShould {

    private lateinit var newEpisodeRepository: NewEpisodeRepository
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var uuidSequencer: UUIDSequencer
    private lateinit var profileService: ProfileService
    private lateinit var moderationService: ModerationService
    private lateinit var channelEpisodesService: ChannelEpisodesService
    private lateinit var clock: Clock

    private lateinit var result: EpisodeSummary

    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk(relaxed = true)
        uuidSequencer = mockk()
        givenASequencer()
        profileService = mockk(relaxed = true)
        moderationService = mockk(relaxed = true)
        givenAModerator()
        clock = mockk(relaxed = true)
        givenAClock()
        newEpisodeRepository = mockk(relaxed = true)
        coEvery { newEpisodeRepository.find(any()) }.returns(listOf())
        channelEpisodesService = mockk(relaxed = true)

        coEvery { channelEpisodesService.canAddToChannel(CHANNEL_ID, any()) } returns true
    }

    @Test
    fun `save episode`() = runTest {
        whenSave()

        thenEpisodeIsSaved()
    }

    @Test
    fun `retrieve episode summary`() = runTest {
        givenAProfile()

        whenSave()

        thenSummaryIsRetrieved()
    }

    @Test
    fun `do not set start time when episode has it`() = runTest {
        whenSave()

        thenStartTimeIsNotSet()
    }

    @Test
    fun `set start time when episode has not it`() = runTest {
        whenSave(startDate = null)

        thenStartTimeIsSet()
    }

    @Test
    fun `avoid empty or blank name`() = runTest {
        whenSave(name = "             ")

        assertThat(error).isExactlyInstanceOf(EpisodeNameEmptyException::class.java)
    }

    @Test
    fun `avoid inappropriate name`() = runTest {
        givenAModerator(failWith = "__inappropriate__")

        whenSave(name = "__inappropriate__")

        assertThat(error).isExactlyInstanceOf(EpisodeNameNotAllowedException::class.java)
    }

    @Test
    fun `do not set start date on draft episode`() = runTest {
        whenSave(status = DRAFT)

        val episode = buildEpisode(likes = 0, dislikes = 0, reports = 0, startDate = null, status = DRAFT)
        coVerify(exactly = 1) { episodeRepository.save(episode) }
    }

    @Test
    fun `avoid empty cover`() = runTest {
        whenSave(cover = null, status = PUBLISHED)

        assertThat(error).isExactlyInstanceOf(MandatoryAttributeValidateException::class.java)
    }

    @Test
    fun `avoid blank cover`() = runTest {
        whenSave(cover = "             ", status = PUBLISHED)

        assertThat(error).isExactlyInstanceOf(MandatoryAttributeValidateException::class.java)
    }

    @Test
    fun `avoid empty banner`() = runTest {
        whenSave(banner = null, status = PUBLISHED)

        assertThat(error).isExactlyInstanceOf(MandatoryAttributeValidateException::class.java)
    }

    @Test
    fun `avoid blank banner`() = runTest {
        whenSave(banner = "             ", status = PUBLISHED)

        assertThat(error).isExactlyInstanceOf(MandatoryAttributeValidateException::class.java)
    }

    @Test
    fun `let empty cover on draft episode`() = runTest {
        whenSave(cover = null, status = DRAFT)

        val episode = buildEpisode(likes = 0, dislikes = 0, reports = 0, cover = "", startDate = null, status = DRAFT)
        coVerify(exactly = 1) { episodeRepository.save(episode) }
    }

    @Test
    fun `let empty banner on draft episode`() = runTest {
        whenSave(banner = null, status = DRAFT)

        val episode = buildEpisode(likes = 0, dislikes = 0, reports = 0, banner = "", startDate = null, status = DRAFT)
        coVerify(exactly = 1) { episodeRepository.save(episode) }
    }

    @Test
    fun `not add episode to channel when channelId is null`() = runTest {
        whenSave(channelId = null, status = DRAFT)

        thenSummaryHasNoChannelId()
        thenEpisodeIsSaved(channelId = null, status = DRAFT, startDate = null)
        thenNotCheckIfCanAddEpisodeToChannel()
    }

    @Test
    fun `not add episode to channel when channelId is not null but is not DRAFT`() = runTest {
        whenSave(channelId = CHANNEL_ID, status = PUBLISHED)

        thenSummaryHasNoChannelId()
        thenEpisodeIsSaved(channelId = null)
        thenNotCheckIfCanAddEpisodeToChannel()
    }

    @Test
    fun `add episode to channel`() = runTest {
        whenSave(channelId = CHANNEL_ID, status = DRAFT)

        assertThat(result.channelId).isEqualTo(CHANNEL_ID)
        thenEpisodeIsSaved(channelId = CHANNEL_ID, status = DRAFT, startDate = null)
    }

    private fun givenASequencer() {
        coEvery { uuidSequencer.next() } returns SEQUENCE_ID
    }

    private fun givenAModerator(failWith: String = "") {
        coEvery { moderationService.isTextAllowed(any(), any(), any()) } returns true
        coEvery { moderationService.isTextAllowed(any(), any(), eq(failWith)) } returns false
    }

    private fun givenAClock() {
        coEvery { clock.now() } returns NOW
    }

    private fun givenAProfile() {
        coEvery { profileService.find(PLAYER_ID) } returns PROFILE
    }

    private suspend fun whenSave(
        name: String = NAME,
        startDate: String? = START_DATE,
        cover: String? = COVER,
        banner: String? = BANNER,
        status: EpisodeStatus = EPISODE_STATUS,
        channelId: String? = null
    ) {
        error = runCatching {
            val actionData = ActionData(
                PLAYER_ID,
                name,
                LANGUAGE,
                COUNTRY,
                EPISODE_TYPE,
                startDate,
                cover,
                banner,
                CONTENTS,
                status,
                channelId
            )
            val createEpisode =
                CreateEpisode(
                    episodeRepository,
                    uuidSequencer,
                    profileService,
                    moderationService,
                    newEpisodeRepository,
                    channelEpisodesService,
                    clock
                )
            result = createEpisode(actionData)
        }.exceptionOrNull()
    }

    private fun thenEpisodeIsSaved(
        channelId: String? = null,
        status: EpisodeStatus = EPISODE_STATUS,
        startDate: String? = START_DATE
    ) {
        val parsedStartDate = startDate?.let { OffsetDateTime.parse(it) }
        val episode = buildEpisode(
            likes = 0,
            dislikes = 0,
            reports = 0,
            channelId = channelId,
            status = status,
            startDate = parsedStartDate
        )
        coVerify(exactly = 1) { episodeRepository.save(episode) }
    }

    private fun thenSummaryIsRetrieved() {
        val episode = buildEpisode(likes = 0, dislikes = 0, reports = 0)
        val summary = EpisodeSummary.from(episode, PROFILE)
        assertThat(result).isEqualTo(summary)
    }

    private fun thenStartTimeIsNotSet() {
        coVerify(exactly = 0) { clock.now() }
    }

    private fun thenStartTimeIsSet() {
        coVerify(exactly = 1) { clock.now() }
    }

    private fun thenNotCheckIfCanAddEpisodeToChannel() {
        coVerify(exactly = 0) { channelEpisodesService.canAddToChannel(any<String>(), any()) }
    }

    private fun thenSummaryHasNoChannelId() {
        assertThat(result.channelId).isNull()
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val CHANNEL_ID = ChannelMother.ID
        val NOW: OffsetDateTime = OffsetDateTime.now()
        val PROFILE = aProfile(playerId = PLAYER_ID)
    }
}
