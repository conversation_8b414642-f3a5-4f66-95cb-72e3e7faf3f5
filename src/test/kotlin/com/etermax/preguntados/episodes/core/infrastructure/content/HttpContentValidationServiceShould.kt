package com.etermax.preguntados.episodes.core.infrastructure.content

import com.etermax.preguntados.episodes.core.domain.content.Content
import com.etermax.preguntados.episodes.core.domain.content.ContentStatus
import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import io.github.resilience4j.circuitbreaker.CircuitBreaker
import io.github.resilience4j.retry.Retry
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class HttpContentValidationServiceShould {

    private lateinit var httpClient: HttpClient

    private var error: Throwable? = null
    private var result: List<Content>? = null

    @Test
    fun `empty content when fails`() = runTest {
        givenAFailingClient()

        whenValidate()

        thenValidateNothing()
    }

    @Test
    fun `empty content when code is not OK`() = runTest {
        givenAClient(HttpStatusCode.NoContent)

        whenValidate()

        thenValidateNothing()
    }

    @Test
    fun `validate content`() = runTest {
        givenAClient(HttpStatusCode.OK)

        whenValidate()

        assertThat(result).isEqualTo(CONTENT_RESPONSE)
    }

    private fun givenAFailingClient() {
        httpClient = HttpClient(
            MockEngine {
                respondError(HttpStatusCode.BadRequest, "this is the body of the error response")
            }
        ) {
            install(ContentNegotiation) {
                json(Json { this.ignoreUnknownKeys = true })
            }
            install(HttpTimeout) {
                requestTimeoutMillis = 3000
            }
        }
    }

    private fun givenAClient(statusCode: HttpStatusCode) {
        httpClient = HttpClient(
            MockEngine {
                assert(it.url.encodedPathAndQuery == URL) { "Unexpected URL: $URL" }

                val requestBody = it.body.toByteArray().toString(Charsets.UTF_8)
                assert(requestBody == REQUEST) { "Unexpected body: $requestBody" }

                request {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    expectSuccess = true
                }

                respond(
                    RESPONSE,
                    statusCode,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        ) {
            install(ContentNegotiation) {
                json(Json { this.ignoreUnknownKeys = true })
            }
            install(HttpTimeout) {
                requestTimeoutMillis = 3000
            }
        }
    }

    private suspend fun whenValidate() {
        val service = HttpContentValidationService(httpClient, RESILIENCE_BUNDLE, ADMIN_PASSWORD)
        error = kotlin.runCatching {
            result = service.validate(PLAYER_ID, CONTENTS)
        }.exceptionOrNull()
    }

    private fun thenValidateNothing() {
        assertThat(result).isEmpty()
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val URL = "/api/content-creation/content-moderation-statuses"
        const val ADMIN_PASSWORD = "passwords"
        const val CONTENT_1 = "CONTENT#1"
        const val CONTENT_2 = "CONTENT#2"
        const val CONTENT_3 = "CONTENT#3"
        const val CONTENT_4 = "CONTENT#4"
        val CONTENTS = listOf(CONTENT_1, CONTENT_2, CONTENT_3, CONTENT_4)
        val RESILIENCE_BUNDLE = EndpointResilienceBundle(CircuitBreaker.ofDefaults("p"), Retry.ofDefaults("s"))
        val REQUEST = """
            {
                "content_ids": ["$CONTENT_1", "$CONTENT_2", "$CONTENT_3", "$CONTENT_4"]
            }
        """.trimIndent().replace(Regex("\\s+"), "")
        const val RESPONSE = """
            {
                "statuses": [
                    {"content_id": "$CONTENT_1", "status": "APPROVED"},
                    {"content_id": "$CONTENT_2", "status": "REJECTED"},
                    {"content_id": "$CONTENT_3", "status": "PENDING"},
                    {"content_id": "$CONTENT_4", "status": "UNKNOWN"}
                ]
            }
        """
        val CONTENT_RESPONSE = listOf(
            Content(CONTENT_1, ContentStatus.APPROVED),
            Content(CONTENT_2, ContentStatus.REJECTED),
            Content(CONTENT_3, ContentStatus.PENDING),
            Content(CONTENT_4, ContentStatus.UNKNOWN)
        )
    }
}
