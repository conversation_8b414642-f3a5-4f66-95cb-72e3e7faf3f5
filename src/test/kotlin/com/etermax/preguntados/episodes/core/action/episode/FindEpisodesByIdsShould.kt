package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.EpisodeMother.buildEpisode
import com.etermax.preguntados.episodes.core.domain.SummaryService
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.doubles.ChannelUnpublishedEpisodesFactory
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother.aProfile
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FindEpisodesByIdsShould {

    private lateinit var episodesRepository: EpisodeRepository
    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService
    private lateinit var summaryService: SummaryService

    private lateinit var result: List<EpisodeSummary>

    @BeforeEach
    fun setUp() {
        episodesRepository = mockk(relaxed = true)
        profileService = mockk(relaxed = true)
        unpublishedEpisodesService = ChannelUnpublishedEpisodesFactory.mock()
        summaryService = SummaryService(profileService, unpublishedEpisodesService)
    }

    @Test
    fun `find none episode`() = runTest {
        givenNoneEpisode()

        whenFind()

        thenThereIsNoneEpisode()
    }

    @Test
    fun `find episodes`() = runTest {
        givenEpisodes()
        givenAProfile()

        whenFind()

        thenEpisodesAreFound()
    }

    @Test
    fun `filter duplicated episodes`() = runTest {
        givenAProfile()

        whenFind(EPISODES_ID.plus(EPISODE_ID_2))

        coVerify(exactly = 1) { episodesRepository.findByIds(EPISODES_ID) }
    }

    private fun givenNoneEpisode() {
        coEvery { episodesRepository.findByIds(any()) } returns emptyList()
    }

    private fun givenEpisodes() {
        coEvery { episodesRepository.findByIds(EPISODES_ID) } returns EPISODES
    }

    private fun givenAProfile() {
        coEvery { profileService.find(any()) } returns PROFILE
    }

    private suspend fun whenFind(episodesId: List<String> = EPISODES_ID) {
        println(episodesId)
        val actionData = FindEpisodesByIds.ActionData(episodesId)
        val findEpisodesByIds = FindEpisodesByIds(episodesRepository, summaryService)
        result = findEpisodesByIds(actionData)
    }

    private fun thenThereIsNoneEpisode() {
        assertThat(result).isEmpty()
    }

    private fun thenEpisodesAreFound() {
        assertThat(result).containsExactlyInAnyOrder(
            EpisodeSummary.from(EPISODE_1, PROFILE),
            EpisodeSummary.from(EPISODE_2, PROFILE)
        )
    }

    private companion object {
        const val EPISODE_ID_1 = "E_123"
        const val EPISODE_ID_2 = "E_456"
        val EPISODES_ID = listOf(EPISODE_ID_1, EPISODE_ID_2)
        val EPISODE_1 = buildEpisode(id = EPISODE_ID_1)
        val EPISODE_2 = buildEpisode(id = EPISODE_ID_2)
        val EPISODES = listOf(EPISODE_1, EPISODE_2)
        val PROFILE = aProfile(playerId = EPISODE_1.ownerId)
    }
}
