package com.etermax.preguntados.episodes.core.infrastructure.search.channels

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.CREATION_DATE
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.time.toMillis
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.DynamoDBChannelRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.utils.LocalOpenSearch
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import java.time.OffsetDateTime
import java.util.*

abstract class AbstractChannelsSearchIntegrationShould {
    protected val tableNamePerTest = "$collectionName-${System.currentTimeMillis()}"
    protected val indexNamePerTest = tableNamePerTest
    val dbClient = dynamoDbTestServer.buildEnhancedAsyncClient(dynamoDbTestServer.buildAsyncClient())
    val dbTable = dbClient.table(collectionName, TableSchema.fromBean(ChannelItem::class.java))

    protected val osClient = LocalOpenSearch.buildOpenSearch(
        LocalOpenSearch.Config(
            indexNamePerTest,
            "opensearch/mapping/channels-index-mapping-v1.1.json"
        )
    )

    protected val repository: ChannelRepository =
        DynamoDBChannelRepository(dbClient, dbTable)

    protected suspend fun givenAChannel(
        id: String = UUID.randomUUID().toString(),
        name: String = "Some great channel",
        language: Language = Language.EN,
        episodesCount: Int = 0,
        creationDate: OffsetDateTime = CREATION_DATE,
        lastModificationDate: OffsetDateTime = CREATION_DATE,
        embedding: FloatArray = FloatArray(0)
    ): Channel {
        val channel = ChannelMother.aChannel(
            id = id,
            name = name,
            language = language,
            episodesCount = episodesCount,
            creationDate = creationDate,
            lastModificationDate = lastModificationDate
        )
        saveToDynamoDB(channel)
        LocalOpenSearch.saveToOpenSearch(
            osClient,
            indexNamePerTest,
            channel.toDocumentId(),
            channel.toDocument(embedding)
        )
        return channel
    }

    private suspend fun saveToDynamoDB(channel: Channel) {
        repository.add(channel)
    }

    protected fun Channel.toDocumentId(): String {
        return "C#$id"
    }

    protected fun Channel.toDocument(embedding: FloatArray): Map<String, Any?> {
        val document = mutableMapOf<String, Any?>(
            "PK" to toDocumentId(),
            "channel_id" to id,
            "name" to name,
            "description" to description,
            "type" to type,
            "language" to language,
            "owner_id" to ownerId,
            "cover_url" to coverUrl,
            "website" to website,
            "subscribed" to subscribed,
            "owner_id" to ownerId,
            "episodes_count" to statistics.episodes,
            "subscribers_count" to statistics.subscribers,
            "creation_date" to creationDate.toMillis(),
            "ch_modification_date" to lastModificationDate.toMillis(),
            "order" to order
        )

        if (embedding.isNotEmpty()) {
            document["embedding"] = embedding
        }

        return document
    }

    protected companion object {
        private val collectionName = "channels"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(ChannelItem::class.java to collectionName))
    }
}
