package com.etermax.preguntados.episodes.core.domain.channel.episode

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UpdateEpisodeProcessDraftPostActionShould {
    private lateinit var action: UpdateChannelEpisodeProcessDraftPostAction
    private lateinit var repository: ChannelUnpublishedEpisodesRepository

    private var episode: Episode? = null
    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        repository = mockk()
        action = UpdateChannelEpisodeProcessDraftPostAction(repository, onError = {
            throw RuntimeException("on error callback")
        })

        episode = null
        error = null
    }

    @Test
    fun `remove old episode from unpublished`() = runTest {
        whenExecute()

        val item = ChannelUnpublishedEpisode(CHANNEL_ID, episode!!.id)
        coVerify(exactly = 1) { repository.delete(item) }
    }

    @Test
    fun `do nothing when previous channelId is null`() = runTest {
        whenExecute(oldChannelId = null)
        thenDoNothing()
    }

    @Test
    fun `do nothing when new status is REJECTED`() = runTest {
        whenExecute(newStatus = EpisodeStatus.REJECTED)
        thenDoNothing()
    }

    @Test
    fun `do nothing when new status is PUBLISHED`() = runTest {
        whenExecute(newStatus = EpisodeStatus.PUBLISHED)
        thenDoNothing()
    }

    @Test
    fun `do nothing when new status is PENDING`() = runTest {
        whenExecute(newStatus = EpisodeStatus.PENDING)
        thenDoNothing()
    }

    @Test
    fun `do nothing when new status is null`() = runTest {
        whenExecute(newStatus = null)
        thenDoNothing()
    }

    @Test
    fun `do nothing when previous status is DRAFT`() = runTest {
        whenExecute(oldStatus = EpisodeStatus.DRAFT)
        thenDoNothing()
    }

    @Test
    fun `do nothing when previous status is PUBLISHED`() = runTest {
        whenExecute(oldStatus = EpisodeStatus.PUBLISHED)
        thenDoNothing()
    }

    @Test
    fun `do nothing when previous status is REJECTED`() = runTest {
        whenExecute(oldStatus = EpisodeStatus.REJECTED)
        thenDoNothing()
    }

    private suspend fun whenExecute(
        oldChannelId: String? = CHANNEL_ID,
        newChannelId: String? = null,
        oldStatus: EpisodeStatus = EpisodeStatus.PENDING,
        newStatus: EpisodeStatus? = EpisodeStatus.DRAFT
    ) {
        error = runCatching {
            episode = EpisodeMother.buildEpisode(status = oldStatus, channelId = oldChannelId)
            action.execute(episode!!, UpdateChannelEpisodePostActionData(status = newStatus, channelId = newChannelId))
        }.exceptionOrNull()
    }

    private fun thenDoNothing() {
        coVerify(exactly = 0) { repository.delete(any()) }
        assertThat(error).isNull()
    }

    private companion object {
        const val CHANNEL_ID = ChannelMother.ID
    }
}
