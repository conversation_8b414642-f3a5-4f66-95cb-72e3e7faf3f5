package com.etermax.preguntados.episodes.core.infrastructure.search.repository

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.core.domain.search.PlayerRecentSearchRepository
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import kotlinx.coroutines.future.await
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration

abstract class PlayerRecentSearchRepositoryShould {

    private lateinit var repository: PlayerRecentSearchRepository
    private var result: Int? = null

    abstract fun getRepository(): PlayerRecentSearchRepository

    @BeforeEach
    fun setUp() {
        repository = getRepository()
    }

    @Test
    fun `save limit`() = runTest {
        whenSave()

        thenLimitIsSaved()
    }

    @Test
    fun `find limits`() = runTest {
        givenLimit()

        whenFind()

        thenLimitIsReturned()
    }

    @Test
    fun `return null when limit does not exist`() = runTest {
        whenFind()

        thenLimitDoesNotExist()
    }

    private suspend fun givenLimit() {
        repository.save(PLAYER_ID, ANOTHER_LIMIT)
    }

    internal suspend fun whenSave() {
        repository.save(PLAYER_ID, LIMIT)
    }

    private suspend fun whenFind() {
        result = repository.find(PLAYER_ID)
    }

    private suspend fun thenLimitIsSaved() {
        val limit = repository.find(PLAYER_ID)
        assertThat(limit).isEqualTo(LIMIT)
    }

    private fun thenLimitIsReturned() {
        assertThat(result).isEqualTo(ANOTHER_LIMIT)
    }

    private fun thenLimitDoesNotExist() {
        assertThat(result).isNull()
    }

    internal companion object {
        const val PLAYER_ID = 123L
        private const val LIMIT = 5
        private const val ANOTHER_LIMIT = 8
    }
}

@ExtendWith(RedisTestServer::class)
class RedisPlayerRecentSearchRepositoryShould : PlayerRecentSearchRepositoryShould() {
    private val redisAsync = EmbeddedRedisUtils.buildClient()

    override fun getRepository(): PlayerRecentSearchRepository {
        return RedisPlayerRecentSearchRepository(redisAsync, Duration.parse(TEN_SECONDS))
    }

    @Test
    fun `set ttl when adding search term`() = runTest {
        whenSave()

        thenTtlIsSet()
    }

    private suspend fun thenTtlIsSet() {
        val ttl = redisAsync.ttl("$KEY:$PLAYER_ID").await()
        assertThat(ttl).isGreaterThan(1)
    }

    private companion object {
        const val TEN_SECONDS = "PT10S"
        const val KEY = "pr:e:rs"
    }
}
