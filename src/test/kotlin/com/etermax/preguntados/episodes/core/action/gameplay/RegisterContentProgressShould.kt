package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.EpisodeMother.LANGUAGE
import com.etermax.preguntados.episodes.core.action.gameplay.RegisterContentProgress.ActionData
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentService
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.domain.quality.QualityService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class RegisterContentProgressShould {

    private lateinit var updatedEpisode: EpisodeSummary
    private lateinit var profileService: ProfileService
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var progressContentService: ProgressContentService
    private lateinit var qualityService: QualityService
    private lateinit var actionData: ActionData

    @BeforeEach
    fun setUp() {
        progressContentService = mockk(relaxed = true)
        episodeRepository = mockk(relaxed = true)
        coEvery { episodeRepository.findById(any()) } returns EpisodeMother.buildEpisode(EPISODE_ID)

        profileService = mockk(relaxed = true)
        coEvery { profileService.find(any()) } returns ProfileMother.aProfile(PLAYER_ID)

        qualityService = mockk(relaxed = true)
    }

    @Test
    fun `save progress content`() = runTest {
        givenAContentToSave(CONTENT_ID)

        whenRegisterProgressContent()

        thenContentSavedIs(CONTENT_ID)
    }

    @Test
    fun `save another progress content`() = runTest {
        givenAContentToSave(ANOTHER_CONTENT_ID)

        whenRegisterProgressContent()

        thenContentSavedIs(ANOTHER_CONTENT_ID)
    }

    @Test
    fun `increment episode views when registering content for the first time`() = runTest {
        givenAContentToSave(CONTENT_ID)
        givenNoPreviousProgress()

        whenRegisterProgressContent()

        thenEpisodeViewsIncremented()
    }

    @Test
    fun `not increment episode views when progress already exists`() = runTest {
        givenAContentToSave(CONTENT_ID)
        givenExistingProgress()

        whenRegisterProgressContent()

        thenEpisodeViewsNotIncremented()
    }

    @Test
    fun `increment episode views only when isIncrementViewsEnabled is true`() = runTest {
        givenAContentToSave(CONTENT_ID)
        givenNoPreviousProgress()

        whenRegisterProgressContent()

        thenEpisodeViewsIncremented()
    }

    @Test
    fun `not increment episode views when isIncrementViewsEnabled is false`() = runTest {
        givenAContentToSave(CONTENT_ID)
        givenNoPreviousProgress()

        whenRegisterProgressContent(isIncrementViewsEnabled = false)

        thenEpisodeViewsNotIncremented()
    }

    @Test
    fun `should calculate channel score when incrementing views`() = runTest {
        givenAContentToSave(CONTENT_ID)
        givenNoPreviousProgress()

        whenRegisterProgressContent()

        thenCalculateChannelScore()
    }

    private fun givenAContentToSave(contentId: String) {
        actionData = ActionData(PLAYER_ID, EPISODE_ID, contentId)
    }

    private suspend fun whenRegisterProgressContent(isIncrementViewsEnabled: Boolean = true) {
        val registerContentProgress = RegisterContentProgress(
            progressContentService,
            episodeRepository,
            profileService,
            qualityService,
            isIncrementViewsEnabled = { isIncrementViewsEnabled }
        )
        updatedEpisode = registerContentProgress(actionData)
    }

    private fun thenContentSavedIs(contentId: String) {
        coVerify(exactly = 1) { progressContentService.registerProgress(EPISODE_ID, PLAYER_ID, contentId, LANGUAGE.name, false) }
    }

    private fun givenNoPreviousProgress() {
        coEvery { progressContentService.findProgress(any(), any()) } returns null
    }

    private fun givenExistingProgress() {
        coEvery { progressContentService.findProgress(any(), any()) } returns mockk(relaxed = true)
    }

    private fun thenEpisodeViewsIncremented() {
        coVerify(exactly = 1) { episodeRepository.updateView(EPISODE.id) }
        assertEquals(
            EPISODE.views + 1,
            updatedEpisode.views
        )
    }

    private fun thenEpisodeViewsNotIncremented() {
        coVerify(exactly = 0) { episodeRepository.updateItem(any()) }
    }

    private fun thenCalculateChannelScore() {
        coVerify {
            qualityService.calculateChannelScore(
                episodeBeforeUpdate = match { it.id == EPISODE_ID && it.views == EPISODE.views },
                episodeAfterUpdate = match { it.id == EPISODE_ID && it.views == EPISODE.views + 1 }
            )
        }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "ABC-123"
        const val CONTENT_ID = "9876543"
        const val ANOTHER_CONTENT_ID = "123456"
        val EPISODE = EpisodeMother.buildEpisode(EPISODE_ID)
    }
}
