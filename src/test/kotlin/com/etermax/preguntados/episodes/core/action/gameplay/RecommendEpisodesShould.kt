package com.etermax.preguntados.episodes.core.action.gameplay

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.recommendation.FriendsEpisode
import com.etermax.preguntados.episodes.core.domain.episode.recommendation.FriendsRecommendationService
import com.etermax.preguntados.episodes.core.domain.episode.recommendation.RecommendedEpisode
import com.etermax.preguntados.episodes.core.domain.profile.Profile
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class RecommendEpisodesShould {

    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var profileService: ProfileService
    private lateinit var friendsRecommendationService: FriendsRecommendationService
    private lateinit var recommendEpisodes: RecommendEpisodes
    private lateinit var result: List<RecommendedEpisode>

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk()
        profileService = mockk()
        friendsRecommendationService = mockk()
        recommendEpisodes = RecommendEpisodes(episodeRepository, profileService, friendsRecommendationService)
    }

    @Test
    fun `return empty list when no recommendations found`() = runTest {
        givenNoRecommendations()

        whenRecommendEpisodes()

        thenNoEpisodesRecommended()
    }

    @Test
    fun `return empty list when episodes not found`() = runTest {
        givenRecommendations()
        givenNoEpisodesFound()
        givenFriendProfiles()

        whenRecommendEpisodes()

        thenNoEpisodesRecommended()
    }

    @Test
    fun `return empty list when friend profiles not found`() = runTest {
        givenRecommendations()
        givenEpisodesFound()
        givenNoFriendProfiles()

        whenRecommendEpisodes()

        thenNoEpisodesRecommended()
    }

    @Test
    fun `only recommend episodes with profiles`() = runTest {
        givenRecommendations()
        givenEpisodesFound()
        givenFriendProfiles(profiles = listOf(FRIEND_2_PROFILE))

        whenRecommendEpisodes()

        thenEpisodesWithoutProfilesAreFiltered()
    }

    @Test
    fun `return recommended episodes with friend profiles`() = runTest {
        givenRecommendations()
        givenEpisodesFound()
        givenFriendProfiles()

        whenRecommendEpisodes()

        thenEpisodesAreRecommended()
    }

    @Test
    fun `recommend at most 10 episodes`() = runTest {
        givenManyRecommendations()
        givenManyEpisodesFound()
        givenFriendProfiles(friendIds = listOf(FRIEND_1_ID))

        whenRecommendEpisodes()

        thenMaxEpisodesAreRecommended()
    }

    private fun givenNoRecommendations() {
        coEvery { friendsRecommendationService.recommend(PLAYER_ID) } returns emptyList()
    }

    private fun givenRecommendations() {
        coEvery { friendsRecommendationService.recommend(PLAYER_ID) } returns RECOMMENDATIONS
    }

    private fun givenNoEpisodesFound() {
        coEvery { episodeRepository.findByIds(EPISODE_IDS) } returns emptyList()
    }

    private fun givenEpisodesFound() {
        coEvery { episodeRepository.findByIds(EPISODE_IDS) } returns EPISODES
    }

    private fun givenManyRecommendations() {
        val recommendations = (1..20).map {
            FriendsEpisode("EPISODE_$it", listOf(FRIEND_1_ID))
        }
        coEvery { friendsRecommendationService.recommend(PLAYER_ID) } returns recommendations
    }

    private fun givenManyEpisodesFound() {
        val episodes = (1..20).map {
            EpisodeMother.buildEpisode(id = "EPISODE_$it")
        }
        coEvery { episodeRepository.findByIds(any()) } returns episodes
    }

    private fun givenNoFriendProfiles() {
        coEvery { profileService.findMany(FRIEND_IDS) } returns emptyList()
    }

    private fun givenFriendProfiles(friendIds: List<Long> = FRIEND_IDS, profiles: List<Profile> = FRIEND_PROFILES) {
        coEvery { profileService.findMany(friendIds) } returns profiles
    }

    private suspend fun whenRecommendEpisodes() {
        val actionData = RecommendEpisodes.ActionData(PLAYER_ID)
        result = recommendEpisodes(actionData)
    }

    private fun thenNoEpisodesRecommended() {
        assertThat(result).isEmpty()
    }

    private fun thenEpisodesAreRecommended() {
        assertThat(result).hasSize(2)

        val firstRecommendation = result.find { it.episode.id == EPISODE_1.id }
        assertThat(firstRecommendation).isNotNull
        assertThat(firstRecommendation!!.players).containsExactlyInAnyOrder(FRIEND_1_PROFILE, FRIEND_2_PROFILE)

        val secondRecommendation = result.find { it.episode.id == EPISODE_2.id }
        assertThat(secondRecommendation).isNotNull
        assertThat(secondRecommendation!!.players).containsExactly(FRIEND_3_PROFILE)
    }

    private fun thenEpisodesWithoutProfilesAreFiltered() {
        assertThat(result).hasSize(1)

        val firstRecommendation = result.find { it.episode.id == EPISODE_1.id }
        assertThat(firstRecommendation).isNotNull
        assertThat(firstRecommendation!!.players).containsExactlyInAnyOrder(FRIEND_2_PROFILE)

        val noSecondRecommendation = result.find { it.episode.id == EPISODE_2.id }
        assertThat(noSecondRecommendation).isNull()
    }

    private fun thenMaxEpisodesAreRecommended() {
        assertThat(result).hasSize(LIMIT)
    }

    private companion object {
        const val PLAYER_ID = 123L
        const val LIMIT = 10
        const val FRIEND_1_ID = 456L
        const val FRIEND_2_ID = 789L
        const val FRIEND_3_ID = 101112L

        val EPISODE_1 = EpisodeMother.buildEpisode(id = "episode-1")
        val EPISODE_2 = EpisodeMother.buildEpisode(id = "episode-2")

        val FRIEND_1_PROFILE = ProfileMother.aProfile(playerId = FRIEND_1_ID)
        val FRIEND_2_PROFILE = ProfileMother.aProfile(playerId = FRIEND_2_ID)
        val FRIEND_3_PROFILE = ProfileMother.aProfile(playerId = FRIEND_3_ID)

        val RECOMMENDATIONS = listOf(
            FriendsEpisode(EPISODE_1.id, listOf(FRIEND_1_ID, FRIEND_2_ID)),
            FriendsEpisode(EPISODE_2.id, listOf(FRIEND_3_ID))
        )

        val EPISODE_IDS = listOf(EPISODE_1.id, EPISODE_2.id)
        val EPISODES = listOf(EPISODE_1, EPISODE_2)
        val FRIEND_IDS = listOf(FRIEND_1_ID, FRIEND_2_ID, FRIEND_3_ID)
        val FRIEND_PROFILES = listOf(FRIEND_1_PROFILE, FRIEND_2_PROFILE, FRIEND_3_PROFILE)
    }
}
