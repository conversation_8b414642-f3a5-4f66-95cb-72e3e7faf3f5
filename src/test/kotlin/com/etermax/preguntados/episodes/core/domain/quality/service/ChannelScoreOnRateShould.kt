package com.etermax.preguntados.episodes.core.domain.quality.service

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelScoreRepository
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.quality.likerate.EpisodeLikeRateAvgCalculator
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ChannelScoreOnRateShould {

    private lateinit var channelEpisodesRepository: ChannelEpisodesRepository
    private lateinit var episodeRepository: EpisodeRepository
    private lateinit var channelRepository: ChannelRepository
    private lateinit var defaultQualityService: DefaultQualityService
    private lateinit var episodeLikeRateAvgCalculator: EpisodeLikeRateAvgCalculator
    private lateinit var channelScoreRepository: ChannelScoreRepository

    @BeforeEach
    fun setUp() {
        channelEpisodesRepository = mockk()
        episodeRepository = mockk()
        channelRepository = mockk()
        episodeLikeRateAvgCalculator = EpisodeLikeRateAvgCalculator()
        channelScoreRepository = mockk()
        defaultQualityService =
            DefaultQualityService(
                channelEpisodesRepository,
                episodeRepository,
                channelRepository,
                episodeLikeRateAvgCalculator,
                channelScoreRepository,
                minimumRates = 30,
                minimumViews = 100
            )
    }

    @Test
    fun `should calculate channel score when user like one episode and meet minimum rate threshold`() = runTest {
        // given
        val episode = givenEpisodeWithRates(EPISODE_ID_1, likes = 20, dislikes = 10) // 80% like rate
        val episodeWithOneMoreLike = givenLikeRatedEpisodeFrom(episode) // Inc likes to 21
        givenRepositoriesWith(episodeWithOneMoreLike)

        // when rated episode
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode,
            episodeAfterUpdate = episodeWithOneMoreLike
        )

        // then
        thenChannelScoreIsUpdatedTo(67)
    }

    @Test
    fun `should calculate channel score when user dislike one episode and meet minimum rate threshold`() = runTest {
        // given
        val episode = givenEpisodeWithRates(EPISODE_ID_1, likes = 0, dislikes = 30) // 0% like rate
        val episodeWithOneMoreDislike = givenDislikeRatedEpisodeFrom(episode) // Inc dislikes to 31
        givenRepositoriesWith(episodeWithOneMoreDislike)

        // when rated episode
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode,
            episodeAfterUpdate = episodeWithOneMoreDislike
        )

        // then
        thenChannelScoreIsUpdatedTo(0)
    }

    @Test
    fun `should not calculate channel score when user like one episode and no meet minimum rates threshold`() =
        runTest {
            // given
            val episode = givenEpisodeWithRates(EPISODE_ID_1, likes = 0, dislikes = 0) // 0% like rate
            val episodeWithOneMoreLike = givenLikeRatedEpisodeFrom(episode) // Inc likes to 1
            givenRepositoriesWith(episodeWithOneMoreLike)

            // when rated episode
            whenToProcessChannelScore(
                episodeBeforeUpdate = episode,
                episodeAfterUpdate = episodeWithOneMoreLike
            )

            // then
            thenChannelScoreIsNotUpdated()
        }

    @Test
    fun `should calculate channel score when user like an episode & channel have all valid episodes`() = runTest {
        // given
        val episode1 = givenEpisodeWithRates(EPISODE_ID_1, likes = 80, dislikes = 20) // 80% like rate
        val episode2 = givenEpisodeWithRates(EPISODE_ID_2, likes = 60, dislikes = 40) // 60% like rate
        val episode3 = givenEpisodeWithRates(EPISODE_ID_3, likes = 40, dislikes = 60) // 40% like rate
        val episode1WithOneMoreView = givenLikeRatedEpisodeFrom(episode1) // Inc likes by 1 -> 80% like rate
        val episodes = listOf(episode1WithOneMoreView, episode2, episode3)
        givenRepositoriesWith(episodes)

        // when rated episode 1
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode1,
            episodeAfterUpdate = episode1WithOneMoreView
        )

        // then
        thenChannelScoreIsUpdatedTo(score = 60) // (80 + 60 + 40) / 3 = 60
    }

    @Test
    fun `should calculate channel score when user like an episode & channel have mixed valid and invalid episodes`() =
        runTest {
            // given
            val validEpisode1 = givenEpisodeWithRates(id = EPISODE_ID_1, likes = 80, dislikes = 20) // 80% like rate
            val validEpisode2 = givenEpisodeWithEnoughViews(EPISODE_ID_2) // 0% like rate
            val validEpisode2WithOneMoreLike =
                givenLikeRatedEpisodeFrom(validEpisode2) // Inc likes by 1 -> 100% like rate
            val invalidEpisode3 = givenEpisodeWithNotEnoughRatesAndViews(EPISODE_ID_3) // No cumple con ningún requisito
            val invalidEpisode4 = givenEpisodeWithNotEnoughRatesAndViews(EPISODE_ID_4) // No cumple con ningún requisito
            val allEpisodes = listOf(validEpisode1, validEpisode2WithOneMoreLike, invalidEpisode3, invalidEpisode4)
            givenRepositoriesWith(allEpisodes)

            // when rated episode 2
            whenToProcessChannelScore(
                episodeBeforeUpdate = validEpisode2,
                episodeAfterUpdate = validEpisode2WithOneMoreLike
            )

            // then
            thenChannelScoreIsUpdatedTo(score = 90) // (80 + 100) / 2 = 90
        }

    @Test
    fun `should no calculate channel score when user like an episode & channel have all invalid episodes`() = runTest {
        // given
        val episode1 =
            givenEpisodeWithRates(EPISODE_ID_1, likes = 0, dislikes = 0) // 0% like rate, not enough rates and views
        val episode2 =
            givenEpisodeWithRates(EPISODE_ID_2, likes = 0, dislikes = 2) // 0% like rate, not enough rates and views
        val episode3 =
            givenEpisodeWithRates(EPISODE_ID_3, likes = 0, dislikes = 3) // 0% like rate, not enough rates and views
        val episode1WithOneMoreLike = givenLikeRatedEpisodeFrom(episode1) // Inc likes to 1, not enough rates and views
        val episodes = listOf(episode1WithOneMoreLike, episode2, episode3)
        givenRepositoriesWith(episodes)

        // when rated episode 1
        whenToProcessChannelScore(
            episodeBeforeUpdate = episode1,
            episodeAfterUpdate = episode1WithOneMoreLike
        )

        // then
        thenChannelScoreIsNotUpdated()
    }

    @Test
    fun `should skip channel score calculation for same episodes that meet minimum rate thresholds`() =
        runTest {
            // given
            val sameEpisodes = givenEpisodeWithRates(id = EPISODE_ID_1, likes = 20, dislikes = 10)
            givenRepositoriesWith(sameEpisodes)

            // when
            whenToProcessChannelScore(
                episodeBeforeUpdate = sameEpisodes,
                episodeAfterUpdate = sameEpisodes
            )

            // then
            thenChannelScoreIsNotUpdated()
        }

    @Test
    fun `should not skip channel score calculation for same episodes that meet minimum rate and view thresholds `() =
        runTest {
            // given
            val sameEpisodes = givenEpisodeWithRatesAndView(id = EPISODE_ID_1, likes = 20, dislikes = 10, views = 120)
            givenRepositoriesWith(sameEpisodes)

            // when
            whenToProcessChannelScore(
                episodeBeforeUpdate = sameEpisodes,
                episodeAfterUpdate = sameEpisodes
            )

            // then
            thenChannelScoreIsUpdatedTo(score = 66)
        }

    /**
     * ***************
     * ***************
     * Given like/dislike rated episode
     * ***************
     * ***************
     */
    private fun givenRepositoriesWith(allEpisodes: List<Episode>) {
        coEvery { channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL.id) } returns allEpisodes.map { it.id }
        coEvery { episodeRepository.findByIds(allEpisodes.map { it.id }) } returns allEpisodes
        coEvery { channelRepository.findById(CHANNEL.id) } returns CHANNEL
    }

    private fun givenEpisodeWithRates(id: String, likes: Long, dislikes: Long) =
        EpisodeMother.buildEpisode(
            id = id,
            channelId = CHANNEL.id,
            likes = likes,
            dislikes = dislikes,
            views = 50
        )

    private fun givenEpisodeWithRatesAndView(id: String, likes: Long, dislikes: Long, views: Long) =
        EpisodeMother.buildEpisode(
            id = id,
            channelId = CHANNEL.id,
            likes = likes,
            dislikes = dislikes,
            views = views
        )

    private fun givenEpisodeWithEnoughViews(id: String) =
        EpisodeMother.buildEpisode(
            id = id,
            channelId = CHANNEL.id,
            likes = 0,
            dislikes = 0,
            views = 150
        )

    private fun givenEpisodeWithNotEnoughRatesAndViews(id: String) =
        EpisodeMother.buildEpisode(
            id = id,
            channelId = CHANNEL.id,
            likes = 0,
            dislikes = 0,
            views = 50
        )

    private fun givenLikeRatedEpisodeFrom(episode: Episode) =
        episode.copy(rate = episode.rate.copy(_likes = episode.rate.likes + 1))

    private fun givenDislikeRatedEpisodeFrom(episode: Episode) =
        episode.copy(rate = episode.rate.copy(_dislikes = episode.rate.dislikes + 1))

    private fun givenRepositoriesWith(episode: Episode) {
        coEvery { channelEpisodesRepository.findAllEpisodeIdsFrom(CHANNEL.id) } returns listOf(
            episode.id
        )
        coEvery { episodeRepository.findByIds(listOf(episode.id)) } returns listOf(episode)
        coEvery { channelRepository.findById(CHANNEL.id) } returns CHANNEL
    }

    /**
     * ***************
     * ***************
     * When to process channel score
     * ***************
     * ***************
     */
    private suspend fun whenToProcessChannelScore(episodeBeforeUpdate: Episode, episodeAfterUpdate: Episode) {
        defaultQualityService.calculateChannelScore(
            episodeBeforeUpdate = episodeBeforeUpdate,
            episodeAfterUpdate = episodeAfterUpdate
        )
    }

    /**
     * ***************
     * ***************
     * Then channel score is updated to
     * ***************
     * ***************
     */
    private fun thenChannelScoreIsUpdatedTo(score: Int) {
        coVerify { channelScoreRepository.updateScore(channelId = CHANNEL.id, score = score) }
    }

    private fun thenChannelScoreIsNotUpdated() {
        coVerify(exactly = 0) { channelScoreRepository.updateScore(any(), any()) }
    }

    private companion object {
        val CHANNEL = ChannelMother.aChannel(id = "channel-id")
        val EPISODE_ID_1 = "episode-id-1"
        val EPISODE_ID_2 = "episode-id-2"
        val EPISODE_ID_3 = "episode-id-3"
        val EPISODE_ID_4 = "episode-id-4"
    }
}
