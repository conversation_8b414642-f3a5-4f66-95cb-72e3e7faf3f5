package com.etermax.preguntados.episodes.core.action.episode

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.EpisodeMother.COUNTRY
import com.etermax.preguntados.episodes.core.EpisodeMother.LANGUAGE
import com.etermax.preguntados.episodes.core.EpisodeMother.NAME
import com.etermax.preguntados.episodes.core.EpisodeMother.OWNER_ID
import com.etermax.preguntados.episodes.core.action.episode.FindAllEpisodes.ActionData
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FindAllEpisodesShould {

    private lateinit var result: List<EpisodeSummary>
    private lateinit var actionData: ActionData
    private lateinit var episodeRepository: EpisodeRepository

    @BeforeEach
    fun setUp() {
        episodeRepository = mockk(relaxed = true)
    }

    @Test
    fun `find episodes by filtering`() = runTest {
        givenFilters()

        whenFindAll()

        coVerify(exactly = 1) { episodeRepository.findAll(OWNER_ID, LANGUAGE, NAME, COUNTRY) }
    }

    @Test
    fun `retrieve episodes found`() = runTest {
        givenFilters()
        givenAnEpisodes()

        whenFindAll()

        thenEpisodesAreFound()
    }

    @Test
    fun `do not find episodes without at least one filter`() = runTest {
        givenNoFilters()
        givenAnEpisodes()

        whenFindAll()

        thenNoEpisodesAreReturned()
    }

    private fun givenAnEpisodes() {
        coEvery { episodeRepository.findAll(any(), any(), any(), any()) } returns EPISODES
    }

    private fun givenFilters() {
        actionData = ActionData(OWNER_ID, LANGUAGE, NAME, COUNTRY)
    }

    private fun givenNoFilters() {
        actionData = ActionData(null, null, null, null)
    }

    private suspend fun whenFindAll() {
        val findAllEpisodes = FindAllEpisodes(episodeRepository)
        result = findAllEpisodes(actionData)
    }

    private fun thenEpisodesAreFound() {
        assertThat(result).containsExactlyInAnyOrder(
            *EPISODES.map { EpisodeSummary.from(it, null) }.toTypedArray()
        )
    }

    private fun thenNoEpisodesAreReturned() {
        assertThat(result).isEmpty()
    }

    private companion object {
        val EPISODES = listOf(
            EpisodeMother.buildEpisode(id = "E_123"),
            EpisodeMother.buildEpisode(id = "E_456")
        )
    }
}
