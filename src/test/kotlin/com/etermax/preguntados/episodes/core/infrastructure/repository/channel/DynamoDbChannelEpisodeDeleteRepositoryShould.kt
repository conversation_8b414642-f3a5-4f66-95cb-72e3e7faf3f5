package com.etermax.preguntados.episodes.core.infrastructure.repository.channel

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.CREATION_DATE
import com.etermax.preguntados.episodes.core.ChannelMother.MODIFICATION_DATE
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.ChannelItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.AssignChannelIdToEpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.channel.tables.items.update.UpdateChannelEpisodesCountItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.DynamoDBEpisodeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.DynamoDBProgressRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.progress.tables.ProgressItem
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema

class DynamoDbChannelEpisodeDeleteRepositoryShould {
    private lateinit var repository: DynamoDbChannelEpisodeDeleteRepository

    private lateinit var episodesRepository: DynamoDBEpisodeRepository
    private lateinit var progressRepository: DynamoDBProgressRepository
    private lateinit var channelRepository: DynamoDBChannelRepository
    private lateinit var addEpisodesToChannelRepository: DynamoDbAddEpisodesToChannelRepository
    private lateinit var channelEpisodesRepository: DynamoChannelEpisodesRepository

    private val client: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        val episodeTable = createEpisodeTable()
        val channelTable = createChannelTable()
        val channelEpisodeTable = createChannelEpisodeTable()
        val episodeProgressTable = createEpisodeProgressTable()
        val updateChannelEpisodesCountTable = createUpdateChannelEpisodesCountTable()
        val assignChannelIdToEpisodeTable = createAssignChannelIdToEpisodeTable()

        episodesRepository = DynamoDBEpisodeRepository(client, episodeTable, mockk())
        progressRepository = DynamoDBProgressRepository(client, episodeProgressTable)
        channelRepository = DynamoDBChannelRepository(client, channelTable)
        addEpisodesToChannelRepository = DynamoDbAddEpisodesToChannelRepository(
            client,
            assignChannelIdToEpisodeTable,
            channelTable,
            channelEpisodeTable
        )
        channelEpisodesRepository = DynamoChannelEpisodesRepository(
            client,
            updateChannelEpisodesCountTable,
            channelEpisodeTable,
            channelEpisodesOrderNormalizer = mockk(relaxed = true)
        )

        repository = DynamoDbChannelEpisodeDeleteRepository(
            client,
            episodeTable,
            updateChannelEpisodesCountTable,
            channelEpisodeTable,
            episodesRepository
        )
    }

    @Test
    fun `remove all items from episode without channel`() = runTest {
        givenEpisode(channelId = null)
        givenEpisodeProgress()

        whenDeleteWithoutChannel()

        thenRemoveEpisode()
        thenRemoveEpisodeItems()
    }

    @Test
    fun `remove only items from episodes when channel not specified`() = runTest {
        givenEpisode(channelId = CHANNEL_ID)
        givenEpisodeProgress()
        givenChannelForEpisode()

        whenDeleteWithoutChannel()

        thenRemoveEpisode()
        thenRemoveEpisodeItems()
        thenNotUpdateChannel()
        thenNotRemoveChannelEpisodeItem()
    }

    @Test
    fun `remove all items from episode with channel`() = runTest {
        givenEpisode(channelId = CHANNEL_ID)
        givenEpisodeProgress()
        givenChannelForEpisode()

        whenDeleteWithChannel()

        thenRemoveEpisode()
        thenRemoveEpisodeItems()
        thenUpdateChannel()
        thenRemoveChannelEpisodeItem()
    }

    private suspend fun givenEpisode(channelId: String?) {
        val episode = EpisodeMother.buildEpisode(channelId = channelId)
        episodesRepository.save(episode)
    }

    private suspend fun givenEpisodeProgress() {
        val progress1 = ProgressContent(EPISODE_ID, PLAYER_1, lastContentId = null, language = null)
        val progress2 = ProgressContent(EPISODE_ID, PLAYER_2, lastContentId = null, language = null)
        progressRepository.save(progress1)
        progressRepository.save(progress2)
    }

    private suspend fun givenChannelForEpisode() {
        val channel = ChannelMother.aChannel(id = CHANNEL_ID, episodesCount = 1)
        channelRepository.add(channel)

        val channelEpisode = ChannelMother.aChannelEpisode(channelId = CHANNEL_ID, episodeId = EPISODE_ID)
        val anotherChannelEpisode =
            ChannelMother.aChannelEpisode(channelId = CHANNEL_ID, episodeId = ANOTHER_EPISODE_ID)
        addEpisodesToChannelRepository.add(channel, setOf(channelEpisode, anotherChannelEpisode))
    }

    private suspend fun whenDeleteWithoutChannel() {
        repository.delete(EPISODE_ID, channel = null)
    }

    private suspend fun whenDeleteWithChannel() {
        val channel = ChannelMother.aChannel(
            id = CHANNEL_ID,
            episodesCount = 0,
            lastModificationDate = MODIFICATION_DATE
        )
        repository.delete(EPISODE_ID, channel)
    }

    private suspend fun thenRemoveEpisode() {
        val episode = episodesRepository.findById(EPISODE_ID)
        assertThat(episode).isNull()
    }

    private suspend fun thenRemoveEpisodeItems() {
        val progress1 = progressRepository.findBy(EPISODE_ID, PLAYER_1)
        val progress2 = progressRepository.findBy(EPISODE_ID, PLAYER_2)
        assertThat(progress1).isNull()
        assertThat(progress2).isNull()
    }

    private suspend fun thenRemoveChannelEpisodeItem() {
        val channelEpisodes = channelEpisodesRepository.findAllFrom(CHANNEL_ID)
        assertThat(channelEpisodes.items.count()).isEqualTo(1)
        assertThat(channelEpisodes.items.first()).isEqualTo(
            ChannelMother.aChannelEpisode(
                channelId = CHANNEL_ID,
                episodeId = ANOTHER_EPISODE_ID
            )
        )
    }

    private suspend fun thenUpdateChannel() {
        val channel = channelRepository.findById(CHANNEL_ID)!!
        assertThat(channel.episodesCount).isEqualTo(0)
        assertThat(channel.lastModificationDate).isEqualTo(MODIFICATION_DATE)
    }

    private suspend fun thenNotUpdateChannel() {
        val channel = channelRepository.findById(CHANNEL_ID)!!
        assertThat(channel.episodesCount).isEqualTo(1)
        assertThat(channel.lastModificationDate).isEqualTo(CREATION_DATE)
    }

    private suspend fun thenNotRemoveChannelEpisodeItem() {
        val channelEpisodes = channelEpisodesRepository.findAllFrom(CHANNEL_ID)

        val items = channelEpisodes.items
        assertThat(items.count()).isEqualTo(2)
        assertThat(items.find { it.episodeId == EPISODE_ID }).isEqualTo(
            ChannelMother.aChannelEpisode(
                channelId = CHANNEL_ID,
                episodeId = EPISODE_ID
            )
        )
        assertThat(items.find { it.episodeId == ANOTHER_EPISODE_ID }).isEqualTo(
            ChannelMother.aChannelEpisode(
                channelId = CHANNEL_ID,
                episodeId = ANOTHER_EPISODE_ID
            )
        )
    }

    private fun createEpisodeTable(): DynamoDbAsyncTable<EpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(EPISODES_TABLE_NAME, TableSchema.fromBean(EpisodeItem::class.java))
    }

    private fun createEpisodeProgressTable(): DynamoDbAsyncTable<ProgressItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(EPISODES_TABLE_NAME, TableSchema.fromBean(ProgressItem::class.java))
    }

    private fun createChannelTable(): DynamoDbAsyncTable<ChannelItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(ChannelItem::class.java))
    }

    private fun createUpdateChannelEpisodesCountTable(): DynamoDbAsyncTable<UpdateChannelEpisodesCountItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(UpdateChannelEpisodesCountItem::class.java))
    }

    private fun createChannelEpisodeTable(): DynamoDbAsyncTable<ChannelEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(ChannelEpisodeItem::class.java))
    }

    private fun createAssignChannelIdToEpisodeTable(): DynamoDbAsyncTable<AssignChannelIdToEpisodeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(CHANNELS_TABLE_NAME, TableSchema.fromBean(AssignChannelIdToEpisodeItem::class.java))
    }

    private companion object {
        const val EPISODE_ID = EpisodeMother.SEQUENCE_ID
        const val ANOTHER_EPISODE_ID = "another_episode_id"
        const val CHANNEL_ID = ChannelMother.ID
        const val PLAYER_1 = 100L
        const val PLAYER_2 = 200L

        const val TABLE_PREFIX = "dev"
        const val EPISODES_TABLE_NAME = "${TABLE_PREFIX}_episodes"
        const val CHANNELS_TABLE_NAME = "${TABLE_PREFIX}_channels"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                EpisodeItem::class.java to EPISODES_TABLE_NAME,
                ChannelItem::class.java to CHANNELS_TABLE_NAME
            )
        )
    }
}
