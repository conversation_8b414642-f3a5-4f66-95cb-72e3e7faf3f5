package com.etermax.preguntados.episodes.core.infrastructure.search.filter

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema.fromBean
import kotlin.test.assertContentEquals
import kotlin.test.assertTrue

class DynamoDBFilterDataRepositoryShould {
    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        dynamoDbTestServer.buildEnhancedAsyncClient(dynamoDbTestServer.buildAsyncClient())
    private val table: DynamoDbAsyncTable<DynamoDBFilterDataItem> =
        dynamoDbEnhancedClient.table(TABLE_NAME, fromBean(DynamoDBFilterDataItem::class.java))

    private val repository = DynamoDBFilterDataRepository(dynamoDbEnhancedClient, table)

    @Test
    fun `save and retrieve filter data`() = runTest {
        val scope = "test-scope"
        val data = "stored-test-data".toByteArray()
        repository.save(scope, data)

        val retrievedData = repository.getByScope(scope)

        assertContentEquals(data, retrievedData)
    }

    @Test
    fun `retrieve non-existent data returns empty byte array`() = runTest {
        val retrievedData = repository.getByScope("non-existent-scope")

        assertTrue(retrievedData.isEmpty())
    }

    private companion object {
        const val TABLE_NAME = "dev_episodes"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(DynamoDBFilterDataItem::class.java to TABLE_NAME))
    }
}
