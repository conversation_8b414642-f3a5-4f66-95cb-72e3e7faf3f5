package com.etermax.preguntados.episodes.core.domain.episode.ranking

import com.etermax.preguntados.episodes.core.EpisodeMother.OWNER_ID
import com.etermax.preguntados.episodes.core.domain.profile.PlayerFriendsService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class RankingServiceShould {

    private lateinit var result: DeliveryRanking
    private lateinit var rankingRepository: RankingRepository
    private lateinit var deliveryRankingService: DeliveryRankingService
    private lateinit var playerFriendsService: PlayerFriendsService

    private lateinit var rankingService: RankingService

    @BeforeEach
    fun setUp() {
        rankingRepository = mockk()
        deliveryRankingService = mockk()
        playerFriendsService = mockk()
        rankingService = RankingService(rankingRepository, deliveryRankingService, playerFriendsService)
    }

    @Test
    fun `return default ranking when it has no players`() = runTest {
        givenAnEmptyRanking()

        whenFindRanking()

        thenRankingIs(EMPTY_RANKING)
    }

    @Test
    fun `find ranking`() = runTest {
        givenARanking()

        whenFindRanking()

        thenRankingIs(DELIVERY_RANKING)
    }

    @Test
    fun `find ranking with friends`() = runTest {
        givenFollowedPlayers()
        givenARankingWithFriends()

        whenFindRankingWithFriends()

        thenRankingWithFriendsIs(DELIVERY_RANKING)
    }

    private fun givenAnEmptyRanking() {
        coEvery { rankingRepository.find(PLAYER_ID, DOMAIN, RANKING_SIZE) } returns null
    }

    private fun givenARanking() {
        coEvery { rankingRepository.find(PLAYER_ID, DOMAIN, RANKING_SIZE) } returns RANKING
        coEvery { deliveryRankingService.complete(RANKING) } returns DELIVERY_RANKING
    }

    private fun givenFollowedPlayers() {
        coEvery { playerFriendsService.findFollowedIds(PLAYER_ID) } returns FOLLOWED_PLAYERS
    }

    private fun givenARankingWithFriends() {
        coEvery { rankingRepository.findForPlayers(PLAYER_ID, DOMAIN, FOLLOWED_PLAYERS.plus(PLAYER_ID)) } returns listOf(RANKED_PLAYER)
        coEvery { deliveryRankingService.complete(RANKING) } returns DELIVERY_RANKING
    }

    private suspend fun whenFindRanking() {
        result = rankingService.findRanking(PLAYER_ID, DOMAIN)
    }

    private suspend fun whenFindRankingWithFriends() {
        result = rankingService.findRankingWithFriends(PLAYER_ID, DOMAIN)
    }

    private fun thenRankingIs(ranking: DeliveryRanking) {
        assertThat(result).isEqualTo(ranking)
    }

    private fun thenRankingWithFriendsIs(ranking: DeliveryRanking) {
        assertThat(result).isEqualTo(ranking)
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "ABC-123"
        const val RANKING_SIZE = 100
        val FOLLOWED_PLAYERS = listOf<Long>(10, 20, 30, 40, 50)
        val PROFILE = ProfileMother.aProfile(playerId = OWNER_ID)
        val DOMAIN = Domain(EPISODE_ID)
        val RANKING_ENTRY = RankingEntry(10, 200)
        val EMPTY_RANKING = DeliveryRanking(emptyList(), RankingEntry(-1, 0))
        val RANKED_PLAYER = RankedPlayer(PLAYER_ID, RANKING_ENTRY)
        val RANKING = Ranking(listOf(RANKED_PLAYER), RANKED_PLAYER)
        val DELIVERY_RANKING = DeliveryRanking(listOf(DeliveryRankedPlayer(PROFILE, RANKING_ENTRY)), RANKING_ENTRY)
    }
}
