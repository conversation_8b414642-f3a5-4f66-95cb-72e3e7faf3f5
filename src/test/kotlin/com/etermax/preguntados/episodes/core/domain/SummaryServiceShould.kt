package com.etermax.preguntados.episodes.core.domain

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.ChannelSummary
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelUnpublishedEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeSummary
import com.etermax.preguntados.episodes.core.domain.profile.ProfileService
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SummaryServiceShould {

    private lateinit var profileService: ProfileService
    private lateinit var unpublishedEpisodesService: ChannelUnpublishedEpisodesService
    private lateinit var summaryService: SummaryService

    private var episodeSummary: EpisodeSummary? = null
    private var channelSummary: ChannelSummary? = null
    private var episodesSummary: List<EpisodeSummary> = emptyList()
    private var channelsSummary: List<ChannelSummary> = emptyList()

    @BeforeEach
    fun setUp() {
        profileService = mockk()
        unpublishedEpisodesService = mockk()
        summaryService = SummaryService(profileService, unpublishedEpisodesService)

        coEvery { unpublishedEpisodesService.count(CHANNEL.id) } returns 0
        coEvery { unpublishedEpisodesService.count(ANOTHER_CHANNEL.id) } returns 5
    }

    @Test
    fun `return null when episode predicate returns null`() = runTest {
        whenToEpisodeSummaryWithNullPredicate()

        thenEpisodeSummaryIsNull()
    }

    @Test
    fun `return null when channel predicate returns null`() = runTest {
        whenToChannelSummaryWithNullPredicate()

        thenChannelSummaryIsNull()
    }

    @Test
    fun `convert episode to summary`() = runTest {
        givenProfileForOwner()

        whenToEpisodeSummaryWithEpisode()

        thenEpisodeSummaryIsCreated()
    }

    @Test
    fun `convert channel to summary`() = runTest {
        givenProfileForOwner()

        whenToChannelSummaryWithChannel()

        thenChannelSummaryIsCreated()
    }

    @Test
    fun `return empty list when episodes predicate returns null`() = runTest {
        whenToEpisodesSummaryWithNullPredicate()

        thenEpisodesSummaryIsEmpty()
    }

    @Test
    fun `return empty list when channels predicate returns null`() = runTest {
        whenToChannelsSummaryWithNullPredicate()

        thenChannelsSummaryIsEmpty()
    }

    @Test
    fun `convert episodes to summaries`() = runTest {
        givenProfilesForOwners()

        whenToEpisodesSummaryWithEpisodes()

        thenEpisodesSummariesAreCreated()
    }

    @Test
    fun `convert channels to summaries`() = runTest {
        givenProfilesForOwners()

        whenToChannelsSummaryWithEpisodes()

        thenChannelsSummariesAreCreated()
    }

    @Test
    fun `fetch profiles only once for multiple episodes with same owner`() = runTest {
        givenProfilesForOwners()

        whenToEpisodesSummaryWithDuplicateOwners()

        thenProfilesAreFetchedOnce()
    }

    @Test
    fun `fetch profiles only once for multiple channels with same owner`() = runTest {
        givenProfilesForOwners()
        coEvery { unpublishedEpisodesService.count("third-channel") } returns ChannelMother.UNPUBLISHED_EPISODES

        whenToChannelsSummaryWithDuplicateOwners()

        thenProfilesAreFetchedOnce()
    }

    @Test
    fun `handle missing profile for episode`() = runTest {
        givenMissingProfileForOwner()

        whenToEpisodesSummaryWithEpisodes()

        thenEpisodeSummaryHasNullProfile()
    }

    @Test
    fun `handle missing profile for channel`() = runTest {
        givenMissingProfileForOwner()

        whenToChannelsSummaryWithEpisodes()

        thenChannelSummaryHasNullProfile()
    }

    private fun givenProfileForOwner() {
        coEvery { profileService.find(OWNER_ID) } returns OWNER_PROFILE
    }

    private fun givenProfilesForOwners() {
        coEvery {
            profileService.findMany(listOf(OWNER_ID, ANOTHER_OWNER_ID))
        } returns listOf(OWNER_PROFILE, ANOTHER_OWNER_PROFILE)
    }

    private fun givenMissingProfileForOwner() {
        coEvery {
            profileService.findMany(listOf(OWNER_ID, ANOTHER_OWNER_ID))
        } returns listOf(ANOTHER_OWNER_PROFILE)
    }

    private suspend fun whenToEpisodeSummaryWithNullPredicate() {
        episodeSummary = summaryService.toEpisodeSummary(null)
    }

    private suspend fun whenToChannelSummaryWithNullPredicate() {
        channelSummary = summaryService.toChannelSummary(null)
    }

    private suspend fun whenToEpisodeSummaryWithEpisode() {
        episodeSummary = summaryService.toEpisodeSummary(EPISODE)
    }

    private suspend fun whenToChannelSummaryWithChannel() {
        channelSummary = summaryService.toChannelSummary(CHANNEL)
    }

    private suspend fun whenToEpisodesSummaryWithNullPredicate() {
        episodesSummary = summaryService.toEpisodesSummary(null)
    }

    private suspend fun whenToChannelsSummaryWithNullPredicate() {
        channelsSummary = summaryService.toChannelsSummary(null)
    }

    private suspend fun whenToEpisodesSummaryWithEpisodes() {
        episodesSummary = summaryService.toEpisodesSummary(listOf(EPISODE, ANOTHER_EPISODE))
    }

    private suspend fun whenToChannelsSummaryWithEpisodes() {
        channelsSummary = summaryService.toChannelsSummary(listOf(CHANNEL, ANOTHER_CHANNEL))
    }

    private suspend fun whenToEpisodesSummaryWithDuplicateOwners() {
        val episodes = listOf(EPISODE, ANOTHER_EPISODE, EPISODE.copy(id = "third-episode"))
        episodesSummary = summaryService.toEpisodesSummary(episodes)
    }

    private suspend fun whenToChannelsSummaryWithDuplicateOwners() {
        val channels = listOf(CHANNEL, ANOTHER_CHANNEL, CHANNEL.copy(id = "third-channel"))
        channelsSummary = summaryService.toChannelsSummary(channels)
    }

    private fun thenEpisodeSummaryIsNull() {
        assertThat(episodeSummary).isNull()
    }

    private fun thenChannelSummaryIsNull() {
        assertThat(channelSummary).isNull()
    }

    private fun thenEpisodeSummaryIsCreated() {
        assertThat(episodeSummary).isEqualTo(EpisodeSummary.from(EPISODE, OWNER_PROFILE))
    }

    private fun thenChannelSummaryIsCreated() {
        assertThat(channelSummary).isEqualTo(ChannelSummary.from(CHANNEL, OWNER_PROFILE, unpublishedEpisodes = 0))
    }

    private fun thenEpisodeSummaryHasNullProfile() {
        assertThat(episodesSummary).containsExactlyInAnyOrder(
            EpisodeSummary.from(EPISODE, null),
            EpisodeSummary.from(ANOTHER_EPISODE, ANOTHER_OWNER_PROFILE)
        )
    }

    private fun thenChannelSummaryHasNullProfile() {
        assertThat(channelsSummary).containsExactlyInAnyOrder(
            ChannelSummary.from(CHANNEL, ownerProfile = null, unpublishedEpisodes = 0),
            ChannelSummary.from(ANOTHER_CHANNEL, ANOTHER_OWNER_PROFILE, unpublishedEpisodes = 5)
        )
    }

    private fun thenEpisodesSummaryIsEmpty() {
        assertThat(episodesSummary).isEmpty()
    }

    private fun thenChannelsSummaryIsEmpty() {
        assertThat(channelsSummary).isEmpty()
    }

    private fun thenEpisodesSummariesAreCreated() {
        assertThat(episodesSummary).containsExactlyInAnyOrder(
            EpisodeSummary.from(EPISODE, OWNER_PROFILE),
            EpisodeSummary.from(ANOTHER_EPISODE, ANOTHER_OWNER_PROFILE)
        )
    }

    private fun thenChannelsSummariesAreCreated() {
        assertThat(channelsSummary).containsExactlyInAnyOrder(
            ChannelSummary.from(CHANNEL, OWNER_PROFILE, unpublishedEpisodes = 0),
            ChannelSummary.from(ANOTHER_CHANNEL, ANOTHER_OWNER_PROFILE, unpublishedEpisodes = 5)
        )
    }

    private fun thenProfilesAreFetchedOnce() {
        coVerify(exactly = 1) { profileService.findMany(any()) }
    }

    private companion object {
        const val OWNER_ID = 123L
        const val ANOTHER_OWNER_ID = 456L

        val OWNER_PROFILE = ProfileMother.aProfile(playerId = OWNER_ID)
        val ANOTHER_OWNER_PROFILE = ProfileMother.aProfile(playerId = ANOTHER_OWNER_ID)

        val EPISODE = EpisodeMother.buildEpisode(id = "episode-1", ownerId = OWNER_ID)
        val ANOTHER_EPISODE = EpisodeMother.buildEpisode(id = "episode-2", ownerId = ANOTHER_OWNER_ID)

        val CHANNEL = ChannelMother.aChannel(id = "episode-1", ownerId = OWNER_ID)
        val ANOTHER_CHANNEL = ChannelMother.aChannel(id = "episode-2", ownerId = ANOTHER_OWNER_ID)
    }
}
