package com.etermax.preguntados.episodes.core.action.channel

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.ChannelMother.COVER_URL
import com.etermax.preguntados.episodes.core.ChannelMother.DESCRIPTION
import com.etermax.preguntados.episodes.core.ChannelMother.LANGUAGE
import com.etermax.preguntados.episodes.core.ChannelMother.NAME
import com.etermax.preguntados.episodes.core.ChannelMother.PLAYER_ID
import com.etermax.preguntados.episodes.core.ChannelMother.WEBSITE
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.exception.*
import com.etermax.preguntados.episodes.core.domain.moderation.ModerationService
import com.etermax.preguntados.episodes.core.domain.moderation.UrlValidatorService
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ChannelValidatorServiceShould {

    private lateinit var moderationService: ModerationService
    private lateinit var urlValidatorService: UrlValidatorService
    private lateinit var validator: ChannelValidatorService

    @BeforeEach
    fun setUp() {
        moderationService = mockk()
        urlValidatorService = mockk()
        validator = ChannelValidatorService(moderationService, urlValidatorService)

        givenAValidUrl()
        givenATextAllowed()
    }

    @Test
    fun `validate create - throw exception when name is empty`() = runTest {
        whenValidateCreate(name = "")

        thenThrowsException<ChannelNameEmptyException>()
    }

    @Test
    fun `validate create - throw exception when name is blank`() = runTest {
        whenValidateCreate(name = "   ")

        thenThrowsException<ChannelNameEmptyException>()
    }

    @Test
    fun `validate create - throw exception when name is longer than 30 characters`() = runTest {
        whenValidateCreate(name = 'A'.repeat(31))

        thenThrowsException<ChannelNameLongerTooLongException>()
    }

    @Test
    fun `validate create - throw exception when name is not allowed`() = runTest {
        givenATextNotAllowed("not allowed name")
        whenValidateCreate(name = "not allowed name")

        thenThrowsException<ChannelNameNotAllowedException>()
    }

    @Test
    fun `validate create - throw exception when description is longer than 80 characters`() = runTest {
        whenValidateCreate(description = 'A'.repeat(81))

        thenThrowsException<ChannelDescriptionLongerTooLongException>()
    }

    @Test
    fun `validate create - throw exception when description is not allowed`() = runTest {
        givenATextNotAllowed("not allowed description")
        whenValidateCreate(description = "not allowed description")

        thenThrowsException<ChannelDescriptionNotAllowedException>()
    }

    @Test
    fun `validate create - throw exception when website is invalid`() = runTest {
        givenAnInvalidUrl()
        whenValidateCreate(website = "invalid-url")

        thenThrowsException<ChannelInvalidWebsiteException>()
    }

    @Test
    fun `validate create - throw exception when cover url is invalid`() = runTest {
        givenAnInvalidUrl()
        whenValidateCreate(coverUrl = "invalid-url", website = null)

        thenThrowsException<ChannelInvalidCoverUrlException>()
    }

    @Test
    fun `validate create - not validate description when is null`() = runTest {
        whenValidateCreate(description = null)

        thenDoesNotModerateDescriptionInCreate()
    }

    @Test
    fun `validate create - not validate website when is null`() = runTest {
        whenValidateCreate(website = null)

        thenDoesNotValidateWebsiteInCreate()
    }

    @Test
    fun `validate update - throw exception when name is empty`() = runTest {
        whenValidateUpdate(name = "")

        thenThrowsException<ChannelNameEmptyException>()
    }

    @Test
    fun `validate update - throw exception when name is blank`() = runTest {
        whenValidateUpdate(name = "   ")

        thenThrowsException<ChannelNameEmptyException>()
    }

    @Test
    fun `validate update - throw exception when name is longer than 30 characters`() = runTest {
        whenValidateUpdate(name = 'A'.repeat(31))

        thenThrowsException<ChannelNameLongerTooLongException>()
    }

    @Test
    fun `validate update - throw exception when name is not allowed`() = runTest {
        givenATextNotAllowed("not allowed name")
        whenValidateUpdate(name = "not allowed name")

        thenThrowsException<ChannelNameNotAllowedException>()
    }

    @Test
    fun `validate update - throw exception when description is longer than 80 characters`() = runTest {
        whenValidateUpdate(description = 'A'.repeat(81))

        thenThrowsException<ChannelDescriptionLongerTooLongException>()
    }

    @Test
    fun `validate update - throw exception when description is not allowed`() = runTest {
        givenATextNotAllowed("not allowed description")
        whenValidateUpdate(description = "not allowed description")

        thenThrowsException<ChannelDescriptionNotAllowedException>()
    }

    @Test
    fun `validate update - throw exception when website is invalid`() = runTest {
        givenAnInvalidUrl()
        whenValidateUpdate(website = "invalid-url")

        thenThrowsException<ChannelInvalidWebsiteException>()
    }

    @Test
    fun `validate update - throw exception when cover url is invalid`() = runTest {
        givenAnInvalidUrl()
        whenValidateUpdate(coverUrl = "invalid-url")

        thenThrowsException<ChannelInvalidCoverUrlException>()
    }

    @Test
    fun `validate update - not validate description when is null`() = runTest {
        whenValidateUpdate(description = null)

        thenDoesNotModerateDescriptionInUpdate()
    }

    @Test
    fun `validate update - not validate website when is null`() = runTest {
        whenValidateUpdate(website = null)

        thenDoesNotValidateWebsiteInUpdate()
    }

    @Test
    fun `validate update - not validate when values are the same`() = runTest {
        whenValidateUpdate(
            name = NAME,
            description = DESCRIPTION,
            website = WEBSITE,
            coverUrl = COVER_URL
        )

        thenDoesNotValidateAnything()
    }

    @Test
    fun `validate update - throw exception when player is not owner`() = runTest {
        val channel = ChannelMother.aChannel().copy(ownerId = 999L)
        whenValidateUpdate(channel = channel)

        thenThrowsException<InvalidUpdateNotOwnChannelException>()
    }

    @Test
    fun `validate update - throw exception when player is not owner with different player id`() = runTest {
        val channel = ChannelMother.aChannel()
        whenValidateUpdate(channel = channel, playerId = 999L)

        thenThrowsException<InvalidUpdateNotOwnChannelException>()
    }

    @Test
    fun `validate update - validate name with channel language when available`() = runTest {
        val channel = ChannelMother.aChannel().copy(language = LANGUAGE)
        whenValidateUpdate(channel = channel, name = "new name")

        thenModeratesNameWithLanguage(LANGUAGE)
    }

    @Test
    fun `validate update - validate name with moderation language when channel language is null`() = runTest {
        val channel = ChannelMother.aChannel().copy(language = null)
        whenValidateUpdate(channel = channel, name = "new name")

        thenModeratesNameWithLanguage(LANGUAGE)
    }

    private fun givenAValidUrl() {
        every { urlValidatorService.isValid(any()) } returns true
    }

    private fun givenAnInvalidUrl() {
        every { urlValidatorService.isValid(any()) } returns false
    }

    private fun givenATextAllowed() {
        coEvery { moderationService.isTextAllowed(any(), any(), any()) } returns true
    }

    private fun givenATextNotAllowed(text: String) {
        coEvery { moderationService.isTextAllowed(any(), any(), text) } returns false
    }

    private suspend fun whenValidateCreate(
        name: String = NAME,
        description: String? = DESCRIPTION,
        website: String? = WEBSITE,
        coverUrl: String = COVER_URL
    ) {
        try {
            validator.validateCreate(
                data = ActionData(
                    playerId = PLAYER_ID,
                    _name = name,
                    _description = description,
                    _website = website,
                    type = null,
                    coverUrl = coverUrl,
                    moderationLanguage = LANGUAGE
                )
            )
        } catch (e: RuntimeException) {
            thrownException = e
        }
    }

    private suspend fun whenValidateUpdate(
        name: String = NAME,
        description: String? = DESCRIPTION,
        website: String? = WEBSITE,
        coverUrl: String = COVER_URL,
        channel: Channel = ChannelMother.aChannel(),
        playerId: Long = PLAYER_ID
    ) {
        val actionData = ActionData(
            playerId = playerId,
            _name = name,
            _description = description,
            _website = website,
            type = null,
            coverUrl = coverUrl,
            moderationLanguage = LANGUAGE
        )

        try {
            validator.validateUpdate(
                data = actionData,
                channel = channel
            )
        } catch (e: RuntimeException) {
            thrownException = e
        }
    }

    private fun thenDoesNotModerateDescriptionInUpdate() {
        coVerify(exactly = 0) { moderationService.isTextAllowed(any(), any(), any()) }
    }

    private fun thenDoesNotModerateDescriptionInCreate() {
        coVerify(exactly = 1) { moderationService.isTextAllowed(any(), any(), any()) }
    }

    private fun thenDoesNotValidateWebsiteInUpdate() {
        coVerify(exactly = 0) { urlValidatorService.isValid(any()) }
    }

    private fun thenDoesNotValidateWebsiteInCreate() {
        coVerify(exactly = 1) { urlValidatorService.isValid(any()) }
    }

    private fun thenDoesNotValidateAnything() {
        coVerify(exactly = 0) { moderationService.isTextAllowed(any(), any(), any()) }
        coVerify(exactly = 0) { urlValidatorService.isValid(any()) }
    }

    private fun thenModeratesNameWithLanguage(language: Language) {
        coVerify { moderationService.isTextAllowed(PLAYER_ID, language, any()) }
    }

    private inline fun <reified T : RuntimeException> thenThrowsException() {
        assertThat(thrownException)
            .isNotNull
            .isInstanceOf(T::class.java)
    }

    private fun Char.repeat(count: Int): String = this.toString().repeat(count)

    private var thrownException: RuntimeException? = null
}
