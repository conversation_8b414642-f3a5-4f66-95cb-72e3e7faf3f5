package com.etermax.preguntados.episodes.core

import com.etermax.preguntados.episodes.core.domain.channel.*
import com.etermax.preguntados.episodes.core.infrastructure.profile.ProfileMother
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import java.time.OffsetDateTime

object ChannelEpisodeMother {
    const val PLAYER_ID = ProfileMother.PLAYER_ID
    private const val CHANNEL_ID = "channel_id"
    const val EPISODE_ID = "episode_id"
    val LANGUAGE = Language.EN
    private val CREATION_DATE: OffsetDateTime = OffsetDateTime.parse("2025-01-01T18:03:00.000Z")

    fun aChannelEpisode(
        channelId: String = CHANNEL_ID,
        episodeId: String = EPISODE_ID,
        dateAdded: OffsetDateTime = CREATION_DATE,
        episodeOrder: Long = 100
    ): ChannelEpisode {
        return ChannelEpisode(
            channelId,
            episodeId,
            dateAdded,
            episodeOrder
        )
    }
}
