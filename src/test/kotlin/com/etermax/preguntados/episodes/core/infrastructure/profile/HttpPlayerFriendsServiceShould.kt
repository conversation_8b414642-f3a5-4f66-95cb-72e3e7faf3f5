package com.etermax.preguntados.episodes.core.infrastructure.profile

import com.etermax.preguntados.episodes.core.infrastructure.resilience.EndpointResilienceBundle
import io.github.resilience4j.circuitbreaker.CircuitBreaker
import io.github.resilience4j.retry.Retry
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class HttpPlayerFriendsServiceShould {

    private lateinit var httpClient: HttpClient
    private lateinit var result: List<Long>

    @Test
    fun `return null when fails finding followed players`() = runTest {
        givenAFailingClient()

        whenFindFollowedIds()

        thenThereIsNoFollowedPlayers()
    }

    @Test
    fun `find followed players`() = runTest {
        givenAClient()

        whenFindFollowedIds()

        assertThat(result).containsExactlyInAnyOrder(*FOLLOWED_PLAYERS.toTypedArray())
    }

    private fun givenAFailingClient() {
        httpClient = HttpClient(
            MockEngine {
                respondError(HttpStatusCode.BadRequest, "this is the body of the error response")
            }
        ) {
            install(ContentNegotiation) {
                json(Json { this.ignoreUnknownKeys = true })
            }
            install(HttpTimeout) {
                requestTimeoutMillis = 3000
            }
        }
    }

    private fun givenAClient() {
        httpClient = HttpClient(
            MockEngine {
                assert(it.url.encodedPathAndQuery == URL) { "Unexpected URL: $URL" }

                assert(it.headers.contains(ADMIN_PASS_HEADER, ADMIN_PASSWORD)) { "Password header not found" }

                request {
                    contentType(ContentType.Application.Json)
                    header(ADMIN_PASS_HEADER, ADMIN_PASSWORD)
                    accept(ContentType.Application.Json)
                    expectSuccess = true
                }

                respond(
                    RESPONSE,
                    HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        ) {
            install(ContentNegotiation) {
                json(Json { this.ignoreUnknownKeys = true })
            }
            install(HttpTimeout) {
                requestTimeoutMillis = 3000
            }
        }
    }

    private suspend fun whenFindFollowedIds() {
        val service = HttpPlayerFriendsService(httpClient, RESILIENCE_BUNDLE, ADMIN_PASSWORD)
        result = service.findFollowedIds(PLAYER_ID)
    }

    private fun thenThereIsNoFollowedPlayers() {
        assertThat(result).isEmpty()
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val URL = "/api/users/$PLAYER_ID/favorites-ids"
        const val ADMIN_PASSWORD = "password"
        const val ADMIN_PASS_HEADER = "god-password"
        val RESILIENCE_BUNDLE = EndpointResilienceBundle(CircuitBreaker.ofDefaults("p"), Retry.ofDefaults("s"))
        val FOLLOWED_PLAYERS = listOf<Long>(50, 51, 52, 53, 100, 101, 102, 103)
        val RESPONSE = """
            {
                "userIds": $FOLLOWED_PLAYERS
            }
        """
    }
}
