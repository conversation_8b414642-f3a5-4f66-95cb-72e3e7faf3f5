package com.etermax.preguntados.episodes.core.infrastructure.repository.challenge

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.ChallengeMother
import com.etermax.preguntados.episodes.core.ChallengeMother.aChallenge
import com.etermax.preguntados.episodes.core.domain.challenge.Challenge
import com.etermax.preguntados.episodes.core.domain.challenge.ChallengeRepository
import com.etermax.preguntados.episodes.core.infrastructure.repository.challenge.tables.ChallengeItem
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import kotlin.test.assertNotNull

class DynamoDBChallengeRepositoryShould {
    private lateinit var repository: ChallengeRepository
    private var challenge: Challenge? = null

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()

    @BeforeEach
    fun setUp() {
        repository = DynamoDBChallengeRepository(dynamoDbEnhancedClient, createTable())
    }

    @Test
    fun `Save a challenge`() = runTest {
        givenAChallenge()
        thenIsAdded()
    }

    private suspend fun givenAChallenge() {
        repository.save(aChallenge())
    }

    private suspend fun thenIsAdded() {
        challenge = repository.find(CHALLENGE_ID)
        assertNotNull(challenge)
    }

    private fun createTable(): DynamoDbAsyncTable<ChallengeItem> {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
            .table(TABLE_NAME, TableSchema.fromBean(ChallengeItem::class.java))
    }

    private companion object {
        const val TABLE_NAME = "dev_challenges"
        const val CHALLENGE_ID = ChallengeMother.ID
        const val ANOTHER_CHALLENGE_ID = "another_challenge_id"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(ChallengeItem::class.java to TABLE_NAME))
    }
}
