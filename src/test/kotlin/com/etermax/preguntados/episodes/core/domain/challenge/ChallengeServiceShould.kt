package com.etermax.preguntados.episodes.core.domain.challenge

import com.etermax.preguntados.episodes.core.ChallengeMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContent
import com.etermax.preguntados.episodes.core.domain.episode.progress.ProgressContentService
import com.etermax.preguntados.episodes.core.domain.episode.ranking.DeliveryRanking
import com.etermax.preguntados.episodes.core.domain.episode.ranking.Domain
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingEntry
import com.etermax.preguntados.episodes.core.domain.episode.ranking.RankingService
import com.etermax.preguntados.episodes.core.domain.exception.ChallengeNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.EpisodeNotFoundException
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

@Suppress("SameParameterValue")
class ChallengeServiceShould {
    private lateinit var challengeService: ChallengeService
    private lateinit var progressContentService: ProgressContentService
    private lateinit var rankingService: RankingService
    private lateinit var challengesRepository: ChallengeRepository
    private lateinit var challengePlayerRepository: ChallengePlayerRepository
    private lateinit var episodesRepository: EpisodeRepository

    @BeforeEach
    fun setUp() {
        challengesRepository = mockk()
        progressContentService = mockk()
        challengePlayerRepository = mockk()
        episodesRepository = mockk()
        rankingService = mockk()

        challengeService = ChallengeService(
            progressContentService,
            rankingService,
            challengesRepository,
            challengePlayerRepository,
            episodesRepository
        )
    }

    @Test
    fun `get challenge details successfully`() = runTest {
        val challenge = givenAChallenge(CHALLENGE_ID, EPISODE_ID)
        val episode = givenAnEpisode(EPISODE_ID)
        val players = givenPlayers(PLAYER_ID, CHALLENGE_ID)
        val ranking = givenRanking()

        val details = challengeService.getChallengeDetailsByUser(PLAYER_ID)

        assertEquals(details.size, 1)
        assertEquals(details.first().challenge, challenge)
        assertEquals(details.first().episode, episode)
        assertEquals(details.first().players, players)
        assertEquals(details.first().ranking, ranking)
    }

    @Test
    fun `get challenge context successfully`() = runTest {
        val episode = givenAnEpisode(EPISODE_ID)
        val challenge = givenAChallenge(CHALLENGE_ID, EPISODE_ID)
        val progress = givenAProgress()

        val context = challengeService.getChallengeContext(CHALLENGE_ID, PLAYER_ID)

        assertEquals(context.challenge, challenge)
        assertEquals(context.episode, episode)
        assertEquals(context.scope, "${EPISODE_ID}_$CHALLENGE_ID")
        assertEquals(context.progress, progress)
    }

    @Test
    fun `get challenge context with no progress`() = runTest {
        val episode = givenAnEpisode(EPISODE_ID)
        val challenge = givenAChallenge(CHALLENGE_ID, EPISODE_ID)
        givenNoProgress()

        val context = challengeService.getChallengeContext(CHALLENGE_ID, PLAYER_ID)

        assertEquals(context.challenge, challenge)
        assertEquals(context.episode, episode)
        assertEquals(context.scope, "${EPISODE_ID}_$CHALLENGE_ID")
        assertEquals(context.progress, null)
    }

    @Test
    fun `get challenge context with no challenge`() = runTest {
        givenANotExistingChallenge()

        kotlin.runCatching {
            challengeService.getChallengeContext(CHALLENGE_ID, PLAYER_ID)
        }.onFailure {
            assertThat(it).isExactlyInstanceOf(ChallengeNotFoundException::class.java)
        }
    }

    @Test
    fun `get challenge context with no episode`() = runTest {
        givenANotExistingEpisode(EPISODE_ID)
        givenAChallenge(CHALLENGE_ID, EPISODE_ID)

        kotlin.runCatching {
            challengeService.getChallengeContext(CHALLENGE_ID, PLAYER_ID)
        }.onFailure {
            assertThat(it).isExactlyInstanceOf(EpisodeNotFoundException::class.java)
        }
    }

    @Test
    fun `initialize player in ranking and update status to playing when there is no previous progress`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = null
        )
        coEvery { rankingService.incrementScore(any(), any(), any()) } returns Unit
        coEvery { challengePlayerRepository.save(any<ChallengePlayer>()) } returns Unit

        // When
        challengeService.ensurePlayerInitialized(CHALLENGE_ID, PLAYER_ID, context)

        // Then
        coVerify(exactly = 1) { rankingService.incrementScore(PLAYER_ID, Domain(context.scope), INITIAL_SCORE) }
        coVerify(exactly = 1) {
            challengePlayerRepository.save(
                ChallengePlayer(
                    PLAYER_ID,
                    CHALLENGE_ID,
                    ChallengePlayer.Status.PLAYING
                )
            )
        }
    }

    @Test
    fun `do not initialize player nor update status in ranking when there is previous progress`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, "C1", null, false)
        )

        // When
        challengeService.ensurePlayerInitialized(CHALLENGE_ID, PLAYER_ID, context)

        // Then
        coVerify(exactly = 0) { rankingService.incrementScore(any(), any(), any()) }
        coVerify(exactly = 0) { challengePlayerRepository.save(any<ChallengePlayer>()) }
    }

    @Test
    fun `return all contents when there is no previous content`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = null
        )

        // When
        val remainingContent = challengeService.calculateRemainingContent(context)

        // Then
        assertThat(remainingContent).containsExactly("C1", "C2", "C3")
    }

    @Test
    fun `return all contents when there is previous content but challenge is finished`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, "C1", null, true)
        )

        // When
        val remainingContent = challengeService.calculateRemainingContent(context)

        // Then
        assertThat(remainingContent).containsExactly("C1", "C2", "C3")
    }

    @Test
    fun `return remaining contents when there is previous content`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, "C1", null, false)
        )

        // When
        val remainingContent = challengeService.calculateRemainingContent(context)

        // Then
        assertThat(remainingContent).containsExactly("C2", "C3")
    }

    @Test
    fun `register progress and update status to finished when challenge is finished`() = runTest {
        // Given a ChallengeContext
        val context = ChallengeService.ChallengeContext(
            scope = "${EPISODE_ID}_$CHALLENGE_ID",
            episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = listOf("C1", "C2", "C3")),
            challenge = ChallengeMother.aChallenge(id = CHALLENGE_ID, episodeId = EPISODE_ID),
            progress = ProgressContent(EPISODE_ID, PLAYER_ID, "C1", null, false)
        )
        coEvery { progressContentService.registerProgress(any(), any(), any(), any(), any()) } returns Unit
        coEvery { challengePlayerRepository.save(any<ChallengePlayer>()) } returns Unit

        // When
        challengeService.finishChallenge(CHALLENGE_ID, PLAYER_ID, context)

        // Then
        coVerify(exactly = 1) {
            progressContentService.registerProgress(
                context.scope,
                PLAYER_ID,
                "C3",
                context.episode.language.name,
                true
            )
        }
        coVerify(exactly = 1) {
            challengePlayerRepository.save(
                ChallengePlayer(
                    PLAYER_ID,
                    CHALLENGE_ID,
                    ChallengePlayer.Status.FINISHED
                )
            )
        }
    }

    private fun givenAnEpisode(episodeId: String): Episode {
        val episode = EpisodeMother.buildEpisode(id = episodeId)
        coEvery { episodesRepository.findById(episodeId) } returns episode
        return episode
    }

    private fun givenAChallenge(challengeId: String, episodeId: String): Challenge {
        val challenge = ChallengeMother.aChallenge(id = challengeId, episodeId = episodeId)
        coEvery { challengesRepository.find(challengeId) } returns challenge
        return challenge
    }

    private fun givenAProgress(
        challengeId: String = CHALLENGE_ID,
        episodeId: String = EPISODE_ID,
        playerId: Long = PLAYER_ID,
        hasFinishedEpisode: Boolean = false
    ): ProgressContent {
        val progress = ProgressContent("${episodeId}_$challengeId", playerId, "C1", null, hasFinishedEpisode)
        coEvery { progressContentService.findProgress(any(), any()) } returns progress
        return progress
    }

    private fun givenPlayers(playerId: Long = PLAYER_ID, challengeId: String = CHALLENGE_ID): List<ChallengePlayer> {
        val result = listOf(ChallengePlayer(playerId, challengeId, ChallengePlayer.Status.PLAYING))

        coEvery { challengePlayerRepository.findAllByPlayerId(any()) } returns result
        coEvery { challengePlayerRepository.findAllByChallengeId(any()) } returns result

        return result
    }

    private fun givenRanking(): DeliveryRanking {
        val deliveryRanking = DeliveryRanking(
            emptyList(),
            RankingEntry(1, 100)
        )
        coEvery { rankingService.findRanking(any(), any()) } returns deliveryRanking

        return deliveryRanking
    }

    private fun givenNoProgress() {
        coEvery { progressContentService.findProgress(any(), any()) } returns null
    }

    private fun givenANotExistingEpisode(episodeId: String) {
        coEvery { episodesRepository.findById(episodeId) } returns null
    }

    private fun givenANotExistingChallenge() {
        coEvery { challengesRepository.find(any()) } returns null
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val CHALLENGE_ID = "CHA-123"
        const val EPISODE_ID = "EPI-123"
        const val INITIAL_SCORE = 0
    }
}
