package com.etermax.preguntados.episodes.core.domain.channel.episode

import com.etermax.preguntados.episodes.core.ChannelMother
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.channel.Channel
import com.etermax.preguntados.episodes.core.domain.channel.ChannelUnpublishedEpisode
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelRepository
import com.etermax.preguntados.episodes.core.domain.channel.repository.ChannelUnpublishedEpisodesRepository
import com.etermax.preguntados.episodes.core.domain.channel.service.ChannelEpisodesService
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus.PENDING
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeStatus.PUBLISHED
import com.etermax.preguntados.episodes.core.domain.episode.update.UpdateChannelEpisodePostActionData
import com.etermax.preguntados.external.services.core.domain.api.profile.Language
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UpdateEpisodeProcessPublishedPostActionShould {
    private lateinit var action: UpdateChannelEpisodeProcessPublishedPostAction
    private lateinit var channelRepository: ChannelRepository
    private lateinit var channelEpisodesService: ChannelEpisodesService
    private lateinit var unpublishedEpisodesRepository: ChannelUnpublishedEpisodesRepository

    private var channel: Channel? = null
    private var episode: Episode? = null
    private var error: Throwable? = null

    @BeforeEach
    fun setUp() {
        channelRepository = mockk()
        channelEpisodesService = mockk(relaxUnitFun = true)
        unpublishedEpisodesRepository = mockk(relaxUnitFun = true)
        action = UpdateChannelEpisodeProcessPublishedPostAction(
            channelRepository,
            channelEpisodesService,
            unpublishedEpisodesRepository
        )

        channel = null
        episode = null
        error = null
    }

    @Test
    fun `add episode to channel`() = runTest {
        givenAChannel()
        whenExecute()
        thenAddEpisodesToChannel()
    }

    @Test
    fun `remove unpublished episode from channel`() = runTest {
        givenAChannel()
        whenExecute()
        thenRemoveUnpublishedFromChannel()
    }

    @Test
    fun `do nothing when episode has no new channel id and old episode not have channel id`() = runTest {
        givenAChannel()
        whenExecute(newChannelId = null)
        thenNotAddEpisodeToChannel()
    }

    @Test
    fun `remove old channel id when episode has no new channel id and old episode and have channel id`() = runTest {
        givenAChannel()
        whenExecute(newChannelId = null, oldChannelId = CHANNEL_ID)
        thenRemoveOldChannelId()
    }

    @Test
    fun `do nothing when status has not changed`() = runTest {
        givenAChannel()
        whenExecute(newStatus = null)
        thenNotAddEpisodeToChannel()
    }

    @Test
    fun `do nothing when changed status is the same`() = runTest {
        givenAChannel()
        whenExecute(episodeStatus = NEW_STATUS, newStatus = NEW_STATUS)
        thenNotAddEpisodeToChannel()
    }

    @Test
    fun `do nothing when changed status is not PUBLISHED`() = runTest {
        val status = EpisodeStatus.values().toMutableList()
        status.remove(PUBLISHED)

        status.forEach { s ->
            givenAChannel()
            whenExecute(episodeStatus = PUBLISHED, newStatus = s)
            thenNotAddEpisodeToChannel()
        }
    }

    @Test
    fun `do nothing when new channel is empty and old channelId is null`() = runTest {
        givenAChannel()
        whenExecute(oldChannelId = null, newChannelId = "")
        thenNotAddEpisodeToChannel()
        coVerify(exactly = 0) { unpublishedEpisodesRepository.delete(any()) }
    }

    @Test
    fun `remove previous channel from unpublished when new channel is empty and old channelId is not null`() = runTest {
        givenAChannel()

        whenExecute(oldChannelId = CHANNEL_ID, newChannelId = "")

        thenNotAddEpisodeToChannel()
        thenRemoveOldChannelId()
    }

    @Test
    fun `not throw error when fail`() = runTest {
        givenAFailure()
        givenAChannel()

        whenExecute()

        thenNotThrowError()
    }

    @Test
    fun `remove episode from channel when channel not exist`() = runTest {
        givenNoChannel()
        whenExecute()
        thenRemoveChannelFromEpisode()
    }

    @Test
    fun `remove episode from channel when languages do not match`() = runTest {
        givenAChannel(language = ANOTHER_LANGUAGE)
        whenExecute()
        thenRemoveChannelFromEpisode()
    }

    @Test
    fun `remove unpublished episode from channel when channel not exist`() = runTest {
        givenNoChannel()
        whenExecute()
        thenRemoveUnpublishedFromChannel()
    }

    private fun givenAChannel(language: Language? = null) {
        channel = ChannelMother.aChannel(id = CHANNEL_ID, language = language)
        coEvery { channelRepository.findById(CHANNEL_ID) } returns channel!!
    }

    private fun givenNoChannel() {
        coEvery { channelRepository.findById(any<String>()) } returns null
    }

    private fun givenAFailure() {
        coEvery { channelEpisodesService.addEpisodesToChannel(any(), any(), any()) } throws RuntimeException("error")
    }

    private suspend fun whenExecute(
        oldChannelId: String? = null,
        newChannelId: String? = CHANNEL_ID,
        episodeStatus: EpisodeStatus = PENDING,
        newStatus: EpisodeStatus? = NEW_STATUS,
        language: Language = EpisodeMother.LANGUAGE
    ) {
        error = runCatching {
            episode = EpisodeMother.buildEpisode(status = episodeStatus, channelId = oldChannelId, language = language)
            val data = UpdateChannelEpisodePostActionData(status = newStatus, channelId = newChannelId)
            action.execute(episode!!, data)
        }.exceptionOrNull()
    }

    private fun thenAddEpisodesToChannel() {
        coVerify(exactly = 1) { channelEpisodesService.addEpisodeToChannel(channel!!, episode!!) }
    }

    private fun thenNotAddEpisodeToChannel() {
        coVerify(exactly = 0) { channelEpisodesService.addEpisodeToChannel(any<Channel>(), any()) }
    }

    private fun thenNotThrowError() {
        assertThat(error).isNull()
    }

    private fun thenRemoveChannelFromEpisode() {
        coVerify(exactly = 1) { channelEpisodesService.removeChannelFromEpisode(PERSISTED_EPISODE.id) }
    }

    private fun thenRemoveUnpublishedFromChannel() {
        val item = ChannelUnpublishedEpisode(CHANNEL_ID, EpisodeMother.SEQUENCE_ID)
        coVerify(exactly = 1) { unpublishedEpisodesRepository.delete(item) }
    }

    private fun thenRemoveOldChannelId() {
        val item = ChannelUnpublishedEpisode(CHANNEL_ID, episode!!.id)
        coVerify(exactly = 1) { unpublishedEpisodesRepository.delete(item) }
    }

    private companion object {
        const val CHANNEL_ID = ChannelMother.ID
        val NEW_STATUS = PUBLISHED
        val PERSISTED_EPISODE = EpisodeMother.buildEpisode(channelId = CHANNEL_ID, status = NEW_STATUS)
        val ANOTHER_LANGUAGE = Language.FR
    }
}
