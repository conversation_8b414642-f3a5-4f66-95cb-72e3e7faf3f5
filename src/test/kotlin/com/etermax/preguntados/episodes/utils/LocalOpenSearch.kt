package com.etermax.preguntados.episodes.utils

import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.hc.core5.http.HttpHost
import org.opensearch.client.opensearch.OpenSearchAsyncClient
import org.opensearch.client.opensearch._types.Refresh
import org.opensearch.client.transport.httpclient5.ApacheHttpClient5TransportBuilder
import org.testcontainers.DockerClientFactory
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.wait.strategy.Wait
import java.io.File

object LocalOpenSearch : GenericContainer<LocalOpenSearch>("opensearchproject/opensearch:2.13.0") {
    private const val CONTAINER_NAME = "opensearch-integration-test"

    private var client: OpenSearchAsyncClient

    init {
        stopPreviousContainerIfItsStillAlive()

        println("Creating testContainer")
        withCreateContainerCmdModifier { cmd -> cmd.withName(CONTAINER_NAME) }
        withEnv("discovery.type", "single-node")
        withEnv("plugins.security.disabled", "true")
        addExposedPort(9200)
        withEnv("OPENSEARCH_INITIAL_ADMIN_PASSWORD", "Luna1234$")
        waitingFor(Wait.forHttp("/").forPort(9200).forStatusCode(200))
        println("Finished testContainer creation from object")

        start()

        client = buildClient()
    }

    fun buildOpenSearch(indexMappings: List<Config>): OpenSearchAsyncClient {
        indexMappings.forEach { config ->
            buildOpenSearch(config)
        }
        return client
    }

    fun buildOpenSearch(config: Config): OpenSearchAsyncClient {
        val mappingFileObj = File(config.mappingFile)
        val jsonNode = ObjectMapper().readTree(mappingFileObj)
        val settingsJson = jsonNode.get("settings").toString()
        val mappingsJson = jsonNode.get("mappings").toString()

        println("Creating index: ${config.index}")
        println("Settings JSON: $settingsJson")
        println("Mappings JSON: $mappingsJson")

        // Create index with separate settings and mappings
        client.indices().create { builder ->
            builder.index(config.index)
            builder.settings { settings ->
                settings.withJson(settingsJson.reader())
            }
            builder.mappings { mappings ->
                mappings.withJson(mappingsJson.reader())
            }
        }.get()
        return client
    }

    private fun buildClient(): OpenSearchAsyncClient {
        val url = "http://${this.host}:${this.firstMappedPort}"
        val client = OpenSearchAsyncClient(ApacheHttpClient5TransportBuilder.builder(HttpHost.create(url)).build())
        return client
    }

    fun <TDocument> saveToOpenSearch(osClient: OpenSearchAsyncClient, index: String, id: String, document: TDocument) {
        osClient.index {
            it.index(index)
            it.id(id)
            it.document(document)
            it.refresh(Refresh.True)
        }.get()
    }

    private fun stopPreviousContainerIfItsStillAlive() {
        try {
            println("Trying to cleanup containers")
            val docker = DockerClientFactory.instance().client()
            val containers = docker.listContainersCmd().withShowAll(true).exec()

            containers.filter { it.names.any { name -> name.contains(CONTAINER_NAME) } }.forEach { container ->
                println("Cleaning up orphaned OpenSearch container: ${container.id}")
                docker.removeContainerCmd(container.id).withForce(true).exec()
            }
            println("Cleaned containers")
        } catch (e: Exception) {
            println("Warning: Could not clean up containers: ${e.message}")
        }
    }

    data class Config(val index: String, val mappingFile: String)
}
