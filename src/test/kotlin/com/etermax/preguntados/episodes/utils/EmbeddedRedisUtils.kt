package com.etermax.preguntados.episodes.utils

import com.etermax.embedded.redis.RedisTestServer
import io.lettuce.core.ClientOptions
import io.lettuce.core.RedisClient
import io.lettuce.core.RedisURI
import io.lettuce.core.api.async.RedisAsyncCommands

object EmbeddedRedisUtils {
    // In order to work, make sure you use @Test from "jupiter" junit in your tests
    fun buildClient(): RedisAsyncCommands<String, String> {
        val uri = RedisURI.create("redis://localhost:${RedisTestServer.port}")
        val redisAsync = RedisClient.create(uri).also {
            it.options = ClientOptions.builder().autoReconnect(false).build()
        }.connect().async()
        return redisAsync
    }
}
