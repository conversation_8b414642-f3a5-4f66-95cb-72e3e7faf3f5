package com.etermax.preguntados.analytics.actions

import com.etermax.devices.domain.DeviceType
import com.etermax.preguntados.analytics.service.*
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import io.mockk.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class TrackEpisodeAnalyticsShould {
    private val analyticsTracker: AnalyticsTracker = mockk {
        coEvery { track(any<ViewedEvent>()) } returns Unit
        coEvery { track(any<PlayedEvent>()) } returns Unit
        coEvery { track(any<FinishedEvent>()) } returns Unit
        coEvery { track(any<FollowEvent>()) } returns Unit
    }
    private val episodeRepository: EpisodeRepository = mockk()

    @Test
    fun `do not track when it is not enabled`() {
        val action = givenAction(false)

        val playerId = 1234L
        val episodeId = "E#9999"
        val ownerId = 9999L
        givenEpisode(episodeId, ownerId)

        val viewedEvent = ViewedEvent(playerId, episodeId)
        action.invoke(viewedEvent)

        coVerify(exactly = 0) { analyticsTracker.track(viewedEvent) }
    }

    @Test
    fun `do not track when player is owner`() {
        val action = givenAction(true)

        val playerId = 1234L
        val episodeId = "E#9999"
        val ownerId = playerId
        givenEpisode(episodeId, ownerId)

        val viewedEvent = ViewedEvent(playerId, episodeId)
        action.invoke(viewedEvent)

        coVerify(exactly = 0) { analyticsTracker.track(viewedEvent) }
    }

    @Test
    fun `track viewed`() {
        val action = givenAction(true)

        val playerId = 1234L
        val episodeId = "E#9999"
        val ownerId = 9999L
        givenEpisode(episodeId, ownerId)
        val viewedEvent = ViewedEvent(playerId, episodeId)
        action.invoke(viewedEvent)

        coVerify { analyticsTracker.track(viewedEvent) }
    }

    @Test
    fun `track played`() {
        val action = givenAction(true)

        val playedEvent = PlayedEvent(
            1234L,
            "E#9999",
            5555L,
            Country.MX,
            DeviceType.IPHONE,
            false
        )
        action.invoke(playedEvent)

        coVerify { analyticsTracker.track(playedEvent) }
    }

    @Test
    fun `track finished`() {
        val action = givenAction(true)

        val viewedEvent = FinishedEvent(1234L, "E#9999", 9999L)
        action.invoke(viewedEvent)

        coVerify { analyticsTracker.track(viewedEvent) }
    }

    @Test
    fun `track follow`() {
        val action = givenAction(true)

        val viewedEvent = FollowEvent(9999L, "E#9999")
        action.invoke(viewedEvent)

        coVerify { analyticsTracker.track(viewedEvent) }
    }

    private fun givenEpisode(episodeId: String, ownerId: Long) {
        coEvery { episodeRepository.findById(episodeId) } returns EpisodeMother.buildEpisode(ownerId = ownerId)
    }

    private fun givenAction(enabled: Boolean): TrackEpisodeAnalytics =
        spyk(TrackEpisodeAnalytics(analyticsTracker, episodeRepository, enabled)) {
            every { fireAndForget(any()) } answers {
                val params = firstArg<suspend CoroutineScope.() -> Unit>()
                runBlocking { params.invoke(this) }
            }
        }
}
