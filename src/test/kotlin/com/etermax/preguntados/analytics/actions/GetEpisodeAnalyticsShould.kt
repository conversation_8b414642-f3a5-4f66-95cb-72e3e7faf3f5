package com.etermax.preguntados.analytics.actions

import com.etermax.preguntados.analytics.service.AnalyticsTracker
import com.etermax.preguntados.analytics.service.EpisodeAnalytics
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.base.EntityNotFoundException
import com.etermax.preguntados.episodes.core.domain.exception.base.ForbiddenException
import com.etermax.preguntados.reports.action.EpisodeReportsSummary
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class GetEpisodeAnalyticsShould {
    private val analyticsTracker: AnalyticsTracker = mockk {
        coEvery { getEpisodeAnalytics(any(), any()) } returns dummyAnalytics()
    }

    private val episodeRepository: EpisodeRepository = mockk()

    val action = GetEpisodeAnalytics(analyticsTracker, episodeRepository)

    @Test
    fun `throw exception when no episode`() = runTest {
        coEvery { episodeRepository.findById(any()) } returns null

        assertThrows<EntityNotFoundException> {
            val actionData = GetEpisodeAnalytics.ActionData(1234L, "#E9292")
            action(actionData)
        }
    }

    @Test
    fun `throw exception when it's not your episode`() = runTest {
        val ownerId = 999999L
        coEvery { episodeRepository.findById(any()) } returns EpisodeMother.buildEpisode(ownerId = ownerId)

        val userId = 1234L
        assertThrows<ForbiddenException> {
            val actionData = GetEpisodeAnalytics.ActionData(userId, "#E9292")
            action(actionData)
        }
    }

    @Test
    fun `get analytics`() = runTest {
        val creatorId = 1234L
        val episodeId = "#E9292"
        val episode = EpisodeMother.buildEpisode(id = episodeId, ownerId = creatorId)
        coEvery { episodeRepository.findById(episodeId) } returns episode

        val actionData = GetEpisodeAnalytics.ActionData(creatorId, episodeId)

        val result = action(actionData)

        assertEquals(dummyAnalytics(), result)
    }

    private fun dummyAnalytics(): EpisodeAnalytics = EpisodeAnalytics(
        listOf(1, 2, 3, 4, 5),
        listOf(9, 8, 7, 6),
        listOf(1, 1, 1),
        listOf(),
        null,
        null,
        null,
        EpisodeReportsSummary(episode = mapOf(), contents = listOf())
    )
}
