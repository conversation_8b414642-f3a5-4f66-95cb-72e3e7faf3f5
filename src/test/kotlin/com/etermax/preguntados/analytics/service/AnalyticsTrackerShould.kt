package com.etermax.preguntados.analytics.service

import com.etermax.devices.domain.DeviceType
import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.analytics.enums.MetricDeviceType
import com.etermax.preguntados.analytics.repository.*
import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.profile.PlayerFriendsService
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.core.infrastructure.repository.episode.tables.EpisodeItem
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import com.etermax.preguntados.reports.action.EpisodeReportsSummary
import com.etermax.preguntados.reports.action.GetEpisodeReports
import com.etermax.preguntados.reports.domain.ReportReason
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import java.time.OffsetDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNull

@ExtendWith(RedisTestServer::class)
class AnalyticsTrackerShould {
    private val redisAsync = EmbeddedRedisUtils.buildClient()

    private var nowDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z")
    private val clock: Clock = mockk {
        coEvery { now() } answers { nowDate }
    }

    val episodesRepository: EpisodeRepository = mockk()

    val getEpisodeReports: GetEpisodeReports = mockk {
        coEvery { this@mockk(any()) } returns EpisodeReportsSummary(mapOf(), listOf())
    }

    val playerFriendsService: PlayerFriendsService = mockk {
        coEvery { findFollowedIds(any()) } returns emptyList()
    }

    private val dynamoDbEnhancedClient by lazy {
        DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(dynamoDbTestServer.buildAsyncClient())
            .build()
    }

    private val contentMetricsTable by lazy {
        dynamoDbEnhancedClient.table(metricsTableName, TableSchema.fromBean(EpisodeMetricsItem::class.java))
    }

    private val creatorMetricsTable by lazy {
        dynamoDbEnhancedClient.table(metricsTableName, TableSchema.fromBean(CreatorUserMetricsItem::class.java))
    }

    private val contentMetricsRepository by lazy {
        DynamoDBEpisodeCountersAnalyticsRepository(dynamoDbEnhancedClient, contentMetricsTable, clock)
    }

    private val creatorMetricsRepository by lazy {
        DynamoDBCreatorUserAnalyticsRepository(dynamoDbEnhancedClient, creatorMetricsTable, clock)
    }

    private val histogramRepository = RedisHistogramAnalyticsRepository(redisAsync, clock)

    private val tracker by lazy {
        AnalyticsTracker(
            clock,
            contentMetricsRepository,
            creatorMetricsRepository,
            histogramRepository,
            getEpisodeReports,
            playerFriendsService
        )
    }

    private val USER_ID = 1L
    private val CREATOR_ID = 999999999L

    private val EPISODE_ID = "E#2222"

    @Test
    fun `get users by episode by day (unique users)`() = runTest {
        val episode = givenAnEpisode()

        val userId1 = 1L
        val userId2 = 2L

        setClockDate("2025-01-01") // Day before yesterday: 1 user
        givenPlayedEvent(userId1)
        givenPlayedEvent(userId1)
        givenPlayedEvent(userId1) // Three times, same user

        setClockDate("2025-01-02") // Yesterday: 2 users
        givenPlayedEvent(userId1)
        givenPlayedEvent(userId1) // Two times, same user
        givenPlayedEvent(userId2)

        setClockDate("2025-01-03") // Today: 1 user
        givenPlayedEvent(userId2)
        givenPlayedEvent(userId2) // Two times, same user

        val metric = tracker.getEpisodeAnalytics(CREATOR_ID, episode).playersByDay

        assertEquals(1, metric[29]) // Today: 1 user
        assertEquals(2, metric[28]) // Yesterday: 2 users
        assertEquals(1, metric[27]) // Day before yesterday: 1 user
        (0..26).forEach { assertEquals(0, metric[it]) }
    }

    @Test
    fun `get play conversion by episode by day (unique users)`() = runTest {
        val userId1 = 1L
        val userId2 = 2L
        val episode = givenAnEpisode()

        setClockDate("2025-01-01") // 100% conversion
        tracker.track(ViewedEvent(userId1, EPISODE_ID))
        tracker.track(ViewedEvent(userId1, EPISODE_ID))
        givenPlayedEvent(userId1)

        tracker.track(ViewedEvent(userId2, EPISODE_ID))
        givenPlayedEvent(userId2)
        givenPlayedEvent(userId2)
        givenPlayedEvent(userId2)

        setClockDate("2025-01-02") // Yesterday: 50% conversion
        tracker.track(ViewedEvent(userId1, EPISODE_ID))
        givenPlayedEvent(userId1)
        givenPlayedEvent(userId1)

        tracker.track(ViewedEvent(userId2, EPISODE_ID))

        setClockDate("2025-01-03") // Today: Corner case with less viewed than played --> 100% conversion
        givenPlayedEvent(userId2)

        val metric = tracker.getEpisodeAnalytics(CREATOR_ID, episode).playConversionByDay

        assertEquals(100, metric[29]) // Today: 1 user
        assertEquals(50, metric[28]) // Yesterday: 2 users
        assertEquals(100, metric[27]) // Day before yesterday: 1 user
        (0..26).forEach { assertEquals(0, metric[it]) }
    }

    @Test
    fun `get followers by episode by day (unique users)`() = runTest {
        val userId1 = 1L
        val userId2 = 2L
        val episode = givenAnEpisode()

        setClockDate("2025-01-01") // Day before yesterday: 1 user
        givenFollowEvent(userId1)
        givenFollowEvent(userId1)
        givenFollowEvent(userId1)

        setClockDate("2025-01-02") // Yesterday: 2 users
        givenFollowEvent(userId1)
        givenFollowEvent(userId1)
        givenFollowEvent(userId2)

        setClockDate("2025-01-03") // Today: 1 user
        givenFollowEvent(userId2)
        givenFollowEvent(userId2)

        val metric = tracker.getEpisodeAnalytics(CREATOR_ID, episode).followersByDay

        assertEquals(1, metric[29]) // Today: 1 user
        assertEquals(2, metric[28]) // Yesterday: 2 users
        assertEquals(1, metric[27]) // Day before yesterday: 1 user
        (0..26).forEach { assertEquals(0, metric[it]) }
    }

    @Test
    fun `get most played times`() = runTest {
        val episode = givenAnEpisode()

        // Day 1 --> it will be ignored because this metric only takes last 5 days
        setClockDate("2025-01-01", "15", "30")
        givenPlayedEvent()
        givenPlayedEvent()
        // Day 2
        setClockDate("2025-01-02", "15", "30")
        givenPlayedEvent()
        setClockDate("2025-01-02", "16", "30")
        givenPlayedEvent()
        setClockDate("2025-01-02", "17", "30")
        givenPlayedEvent()
        // Day 3
        setClockDate("2025-01-03", "16", "30")
        givenPlayedEvent()
        // Day 4: Nothing
        // Day 5: Nothing
        // Day 6
        setClockDate("2025-01-06", "20", "30")
        givenPlayedEvent()

        val metric = tracker.getEpisodeAnalytics(CREATOR_ID, episode).mostPlayedTimes

        val expectedHoursWithActivity = mapOf(
            15 to 20.0,
            16 to 40.0,
            17 to 20.0,
            20 to 20.0
        )
        val expectedResult = List(24) { index -> expectedHoursWithActivity.getOrDefault(index, 0.0) }

        assertEquals(expectedResult, metric)
    }

    @Test
    fun `get likes, dislikes and reports`() = runTest {
        val likes = 30L
        val dislikes = 5L
        val reports = 2L
        val episode = EpisodeMother.buildEpisode(id = EPISODE_ID, likes = likes, dislikes = dislikes, reports = reports)
        givenAnEpisode(episode)

        val metric = tracker.getEpisodeAnalytics(CREATOR_ID, episode).feedbacks!!

        assertEquals(likes, metric.likes)
        assertEquals(dislikes, metric.dislikes)
    }

    @Test
    fun `get device demography by episode (percentage by total plays)`() = runTest {
        val episode = givenAnEpisode()

        givenPlayedEvent(device = DeviceType.ANDROID, isTablet = false)
        givenPlayedEvent(device = DeviceType.ANDROID, isTablet = false)
        givenPlayedEvent(device = DeviceType.ANDROID, isTablet = true)

        givenPlayedEvent(device = DeviceType.AMAZON, isTablet = true)
        givenPlayedEvent(device = DeviceType.UNAVAILABLE, isTablet = true)
        givenPlayedEvent(device = DeviceType.UNAVAILABLE, isTablet = false)

        givenPlayedEvent(device = DeviceType.IPHONE, isTablet = false)
        givenPlayedEvent(device = DeviceType.IPHONE, isTablet = true)

        val metric = tracker.getEpisodeAnalytics(CREATOR_ID, episode).demography!!

        val expectedDevices = mapOf(
            MetricDeviceType.OTHER to 100.0 * 3 / 8,
            MetricDeviceType.ANDROID_MOBILE to 100.0 * 2 / 8,
            MetricDeviceType.IOS_MOBILE to 100.0 * 1 / 8,
            MetricDeviceType.ANDROID_TABLET to 100.0 * 1 / 8,
            MetricDeviceType.IOS_IPAD to 100.0 * 1 / 8
        )
        assertEquals(expectedDevices, metric.devices)
    }

    @Test
    fun `get site demography by episode (percentage by total plays)`() = runTest {
        val episode = givenAnEpisode()

        givenPlayedEvent(site = Country.AR)
        givenPlayedEvent(site = Country.AR)
        givenPlayedEvent(site = Country.AR)
        givenPlayedEvent(site = Country.AR)
        givenPlayedEvent(site = Country.AR)

        givenPlayedEvent(site = Country.BR)

        givenPlayedEvent(site = Country.US)
        givenPlayedEvent(site = Country.US)

        val metric = tracker.getEpisodeAnalytics(CREATOR_ID, episode).demography!!

        val expectedCountries = mapOf(
            Country.AR to 100.0 * 5 / 8,
            Country.US to 100.0 * 2 / 8,
            Country.BR to 100.0 * 1 / 8
        )
        assertEquals(expectedCountries, metric.countries)
    }

    @Test
    fun `get user type demography by episode (percentage by total plays)`() = runTest {
        val creatorId = 999L
        val follower1 = 100L
        val follower2 = 101L
        val nonFollower1 = 200L
        val nonFollower2 = 201L
        val nonFollower3 = 202L

        val episode = EpisodeMother.buildEpisode(id = EPISODE_ID, ownerId = creatorId)
        givenAnEpisode(episode)

        // Mock the followers service to return follower1 and follower2 as followers of the creator
        coEvery { playerFriendsService.findFollowedIds(follower1) } returns listOf(creatorId)
        coEvery { playerFriendsService.findFollowedIds(follower2) } returns listOf(creatorId)

        // Followers play the episode
        givenPlayedEvent(userId = follower1, ownerId = creatorId)
        givenPlayedEvent(userId = follower1, ownerId = creatorId) // Same user, should count as 2 plays
        givenPlayedEvent(userId = follower2, ownerId = creatorId)

        // Non-followers play the episode
        givenPlayedEvent(userId = nonFollower1, ownerId = creatorId)
        givenPlayedEvent(userId = nonFollower2, ownerId = creatorId)
        givenPlayedEvent(userId = nonFollower2, ownerId = creatorId) // Same user, should count as 2 plays
        givenPlayedEvent(userId = nonFollower3, ownerId = creatorId)
        givenPlayedEvent(userId = nonFollower3, ownerId = creatorId) // Same user, should count as 2 plays

        val metric = tracker.getEpisodeAnalytics(CREATOR_ID, episode).demography!!

        // Total plays: 3 from followers + 5 from non-followers = 8 plays
        val expectedUserTypes = UserTypes(
            100.0 * 3 / 8, // 37.5%
            100.0 * 5 / 8 // 62.5%
        )
        assertEquals(expectedUserTypes, metric.userTypes)
    }

    @Test
    fun `get user type demography when creator has no followers`() = runTest {
        val creatorId = 999L
        val nonFollower1 = 200L
        val nonFollower2 = 201L

        val episode = EpisodeMother.buildEpisode(id = EPISODE_ID, ownerId = creatorId)
        givenAnEpisode(episode)

        // Mock the followers service to return empty list (creator has no followers)
        coEvery { playerFriendsService.findFollowedIds(creatorId) } returns emptyList()

        // Only non-followers play the episode
        givenPlayedEvent(userId = nonFollower1, ownerId = creatorId)
        givenPlayedEvent(userId = nonFollower2, ownerId = creatorId)
        givenPlayedEvent(userId = nonFollower2, ownerId = creatorId) // Same user plays again

        val metric = tracker.getEpisodeAnalytics(CREATOR_ID, episode).demography!!

        // All plays should be from non-followers
        val expectedUserTypes = UserTypes(0.0, 100.0)
        assertEquals(expectedUserTypes, metric.userTypes)
    }

    @Test
    fun `track episode start and finish rates when no one played`() = runTest {
        val creatorId = 90001L
        val episodeId1 = "E#episode1"
        val episodeId2 = "E#episode2"
        val episode1 = givenAnEpisode(episodeId1, creatorId)
        val episode2 = givenAnEpisode(episodeId2, creatorId)

        val metrics1 = tracker.getEpisodeAnalytics(creatorId, episode1)
        val metrics2 = tracker.getEpisodeAnalytics(creatorId, episode2)

        assertNull(metrics1.finishRate)
        assertNull(metrics2.finishRate)
    }

    @Test
    fun `track episode start and finish rates for multiple episodes`() = runTest {
        val player1 = 100L
        val player2 = 101L

        // Given creators and their episodes
        val creatorId1 = 90001L
        val episodeId1 = "E#episodeId1"
        val episodeId2 = "E#episodeId2"
        val episode1 = givenAnEpisode(episodeId1, creatorId1)
        val episode2 = givenAnEpisode(episodeId2, creatorId1)

        val creatorId2 = 90002L
        val episodeId3 = "E#episodeId3"
        val episode3 = givenAnEpisode(episodeId3, creatorId2)

        // Episode 1: 3 starts, 2 finishes (66.67% finish rate)
        givenPlayedEvent(userId = player1, episodeId = episodeId1, ownerId = creatorId1)
        givenPlayedEvent(userId = player1, episodeId = episodeId1, ownerId = creatorId1)
        givenPlayedEvent(userId = player2, episodeId = episodeId1, ownerId = creatorId1)
        tracker.track(FinishedEvent(player1, episodeId1, creatorId1))
        tracker.track(FinishedEvent(player2, episodeId1, creatorId1))

        // Episode 2: 2 starts, 0 finishes (0% finish rate)
        givenPlayedEvent(userId = player1, episodeId = episodeId2, ownerId = creatorId1)
        givenPlayedEvent(userId = player2, episodeId = episodeId2, ownerId = creatorId1)

        // Episode 3: 1 starts, 2 finishes (100% finish rate, border case)
        givenPlayedEvent(userId = player1, episodeId = episodeId3, ownerId = creatorId2)
        tracker.track(FinishedEvent(player1, episodeId3, creatorId2))
        tracker.track(FinishedEvent(player2, episodeId3, creatorId2))

        // Episode 4: 0 starts, 0 finishes

        val metrics1 = tracker.getEpisodeAnalytics(creatorId1, episode1).finishRate!!
        val creator1AverageRate = 100.0 * 2 / 5
        assertEquals(100.0 * 2 / 3, metrics1.thisEpisode, 0.01)
        assertEquals(creator1AverageRate, metrics1.creatorAverage, 0.01)

        val metrics2 = tracker.getEpisodeAnalytics(creatorId1, episode2).finishRate!!
        assertEquals(0.0, metrics2.thisEpisode, 0.01)
        assertEquals(creator1AverageRate, metrics2.creatorAverage, 0.01)

        val metrics3 = tracker.getEpisodeAnalytics(creatorId2, episode3).finishRate!!
        assertEquals(100.0, metrics3.thisEpisode, 0.01)
        assertEquals(100.0, metrics3.creatorAverage, 0.01)
    }

    @Test
    fun `include reports summary`() = runTest {
        val episode = givenAnEpisode()

        val reports = EpisodeReportsSummary(
            episode = mapOf(
                ReportReason.VIOLENCE to 3,
                ReportReason.OTHER to 5
            ),
            contents = episode.contents.map {
                mapOf(ReportReason.INAPPROPRIATE to 1)
            }
        )
        coEvery {
            getEpisodeReports.invoke(GetEpisodeReports.ActionData(episode.ownerId, episode.id))
        } returns reports

        val metrics = tracker.getEpisodeAnalytics(episode.ownerId, episode)

        assertEquals(reports, metrics.reports)
    }

    fun setClockDate(stringDate: String, hour: String = "10", minute: String = "00") {
        nowDate = OffsetDateTime.parse("${stringDate}T$hour:$minute:00.000Z")
    }

    private suspend fun givenPlayedEvent(
        userId: Long = USER_ID,
        episodeId: String = EPISODE_ID,
        ownerId: Long = USER_ID,
        site: Country = Country.AR,
        device: DeviceType = DeviceType.ANDROID,
        isTablet: Boolean = true
    ) {
        tracker.track(PlayedEvent(userId, episodeId, ownerId, site, device, isTablet))
    }

    private suspend fun givenFollowEvent(userId1: Long) {
        tracker.track(FollowEvent(userId1, EPISODE_ID))
    }

    private fun givenAnEpisode(episode: Episode = EpisodeMother.buildEpisode(id = EPISODE_ID)): Episode {
        coEvery {
            episodesRepository.findById(episode.id)
        } returns episode
        return episode
    }

    private fun givenAnEpisode(episodeId: String, creatorId: Long = 99999L): Episode {
        val episode = EpisodeMother.buildEpisode(id = episodeId, ownerId = creatorId)
        return givenAnEpisode(episode)
    }

    protected companion object {
        private val episodesTableName = "episodes"
        private val metricsTableName = "metrics"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(
            mapOf(
                EpisodeItem::class.java to episodesTableName,
                EpisodeMetricsItem::class.java to metricsTableName,
                CreatorUserMetricsItem::class.java to metricsTableName
            )
        )
    }
}
