package com.etermax.preguntados.analytics.repository

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.analytics.service.CreatorsCountersAnalyticsRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import java.time.OffsetDateTime
import java.time.temporal.ChronoUnit
import kotlin.test.assertNull

class DynamoDBCreatorUserMetricsRepositoryTest {
    private var nowDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z")

    private val clock: Clock = mockk {
        coEvery { now() } answers { nowDate }
    }
    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        dynamoDbTestServer.buildEnhancedAsyncClient(dynamoDbTestServer.buildAsyncClient())
    private val table: DynamoDbAsyncTable<CreatorUserMetricsItem> =
        dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(CreatorUserMetricsItem::class.java))
    private val repository: CreatorsCountersAnalyticsRepository = DynamoDBCreatorUserAnalyticsRepository(dynamoDbEnhancedClient, table, clock)

    @Test
    fun `when play, expiration time is set 6 months from now`() = runTest {
        val creatorId = 1234L

        val nothing = repository.getCreatorFinishRate(creatorId)
        assertNull(nothing)

        repository.registerPlayedContentOf(creatorId)

        val result = repository.getCreatorFinishRate(creatorId)

        val expected: Long = nowDate.plus(6, ChronoUnit.MONTHS).toEpochSecond()
        assertEquals(expected, result?.expiration)
    }

    @Test
    fun `when finish, expiration time is set 6 months from now`() = runTest {
        val creatorId = 1234L

        val nothing = repository.getCreatorFinishRate(creatorId)
        assertNull(nothing)

        repository.registerFinishedContentOf(creatorId)

        val result = repository.getCreatorFinishRate(creatorId)

        val expected: Long = nowDate.plus(6, ChronoUnit.MONTHS).toEpochSecond()
        assertEquals(expected, result?.expiration)
    }

    private companion object {
        const val TABLE_NAME = "dev_episodes"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(CreatorUserMetricsItem::class.java to TABLE_NAME))
    }
}
