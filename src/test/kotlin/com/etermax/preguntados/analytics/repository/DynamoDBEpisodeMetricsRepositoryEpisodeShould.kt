package com.etermax.preguntados.analytics.repository

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.analytics.enums.MetricDeviceType
import com.etermax.preguntados.analytics.enums.MetricUserType
import com.etermax.preguntados.analytics.service.EpisodeCountersAnalyticsRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.external.services.core.domain.api.profile.Country
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import java.time.OffsetDateTime
import java.time.temporal.ChronoUnit
import kotlin.test.assertEquals
import kotlin.test.assertNull

class DynamoDBEpisodeMetricsRepositoryEpisodeShould {
    private var nowDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z")

    private val clock: Clock = mockk {
        coEvery { now() } answers { nowDate }
    }
    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        dynamoDbTestServer.buildEnhancedAsyncClient(dynamoDbTestServer.buildAsyncClient())
    private val table: DynamoDbAsyncTable<EpisodeMetricsItem> =
        dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(EpisodeMetricsItem::class.java))
    private val repository: EpisodeCountersAnalyticsRepository = DynamoDBEpisodeCountersAnalyticsRepository(dynamoDbEnhancedClient, table, clock)

    @Test
    fun `when play, expiration time is set 6 months from now`() = runTest {
        val episodeId = "E#1234"

        val nothing = repository.getContentMetrics(episodeId)
        assertNull(nothing)

        repository.registerPlayedFrom(
            episodeId,
            Country.AR,
            MetricDeviceType.ANDROID_TABLET,
            MetricUserType.NOT_FOLLOWER
        )

        val result = repository.getContentMetrics(episodeId)

        val expected: Long = nowDate.plus(6, ChronoUnit.MONTHS).toEpochSecond()
        assertEquals(expected, result?.expiration)
    }

    @Test
    fun `when finish, expiration time is set 6 months from now`() = runTest {
        val episodeId = "E#1234"

        val nothing = repository.getContentMetrics(episodeId)
        assertNull(nothing)

        repository.registerEpisodeFinished(episodeId)

        val result = repository.getContentMetrics(episodeId)

        val expected: Long = nowDate.plus(6, ChronoUnit.MONTHS).toEpochSecond()
        assertEquals(expected, result?.expiration)
    }

    private companion object {
        const val TABLE_NAME = "dev_episodes"
        const val EPISODE_ID = "ABC-666"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(EpisodeMetricsItem::class.java to TABLE_NAME))
    }
}
