package com.etermax.preguntados.analytics.repository

import com.etermax.embedded.redis.RedisTestServer
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.utils.EmbeddedRedisUtils
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.future.await
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.OffsetDateTime

@ExtendWith(RedisTestServer::class)
class RedisHistogramAnalyticsRepositoryShould {
    private lateinit var nowDate: OffsetDateTime

    private val clock: Clock = mockk {
        coEvery { now() } answers { nowDate }
    }

    private val redisAsync = EmbeddedRedisUtils.buildClient()
    private val repository = RedisHistogramAnalyticsRepository(redisAsync, clock)

    @Test
    fun `save views (unique users) with ttl by day`() = runTest {
        val episodeId = "E#12345"

        nowDate = OffsetDateTime.parse("2025-01-01T10:25:00.000Z")
        repository.registerViewed(episodeId, 8888L)
        repository.registerViewed(episodeId, 9999L)
        repository.registerViewed(episodeId, 9999L)

        val usersThatPlaysKey = "views:$episodeId:20250101"
        val usersThatPlayedCount: Long? = redisAsync.pfcount(usersThatPlaysKey).await()
        val expiration: Long? = redisAsync.ttl(usersThatPlaysKey).await()

        assertEquals(2, usersThatPlayedCount)
        val expected31Days: Long = 31 * 24 * 60 * 60
        assertEquals(expected31Days, expiration)
    }

    @Test
    fun `save plays (unique users) with ttl by day`() = runTest {
        val episodeId = "E#12345"

        nowDate = OffsetDateTime.parse("2025-01-01T10:25:00.000Z")
        repository.registerPlayed(episodeId, 8888L)
        repository.registerPlayed(episodeId, 9999L)
        repository.registerPlayed(episodeId, 9999L)

        val usersThatPlaysKey = "plays:$episodeId:20250101"
        val usersThatPlayedCount: Long? = redisAsync.pfcount(usersThatPlaysKey).await()
        val expiration: Long? = redisAsync.ttl(usersThatPlaysKey).await()

        assertEquals(2, usersThatPlayedCount)
        val expected31Days: Long = 31 * 24 * 60 * 60
        assertEquals(expected31Days, expiration)
    }

    @Test
    fun `save plays by hour with ttl`() = runTest {
        val episodeId = "E#12345"
        val expected6Days: Long = 6 * 24 * 60 * 60

        nowDate = OffsetDateTime.parse("2025-01-01T10:25:00.000Z")
        repository.registerPlayed(episodeId, 8888L)
        repository.registerPlayed(episodeId, 9999L)
        repository.registerPlayed(episodeId, 9999L)

        nowDate = OffsetDateTime.parse("2025-01-02T13:25:00.000Z")
        repository.registerPlayed(episodeId, 9999L)

        val dayOne = 20250101
        val playsByHourKey = "plays:$episodeId:$dayOne:hours"
        val playsOneDayCount: Long? = redisAsync.hget(playsByHourKey, "10").await()?.toLongOrNull()
        val expiration1: Long? = redisAsync.ttl(playsByHourKey).await()
        assertEquals(3, playsOneDayCount)
        assertEquals(expected6Days, expiration1)

        val dayTwo = 20250102
        val playsByHourKey2 = "plays:$episodeId:$dayTwo:hours"
        val playsOneDayCount2: Long? = redisAsync.hget(playsByHourKey2, "13").await()?.toLongOrNull()
        val expiration2: Long? = redisAsync.ttl(playsByHourKey2).await()
        assertEquals(1, playsOneDayCount2)
        assertEquals(expected6Days, expiration2)
    }

    @Test
    fun `save follows (unique users) with ttl`() = runTest {
        val episodeId = "E#12345"

        nowDate = OffsetDateTime.parse("2025-01-01T10:25:00.000Z")
        repository.registerFollowed(episodeId, 8888L)
        repository.registerFollowed(episodeId, 9999L)
        repository.registerFollowed(episodeId, 9999L)

        val usersThatPlaysKey = "follows:$episodeId:20250101"
        val usersThatPlayedCount: Long? = redisAsync.pfcount(usersThatPlaysKey).await()
        val expiration: Long? = redisAsync.ttl(usersThatPlaysKey).await()

        assertEquals(2, usersThatPlayedCount)
        val expected31Days: Long = 31 * 24 * 60 * 60
        assertEquals(expected31Days, expiration)
    }
}
