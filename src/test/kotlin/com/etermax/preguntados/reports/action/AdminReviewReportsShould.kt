package com.etermax.preguntados.reports.action

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.Episode
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.episodes.job.UpdateStatusService
import com.etermax.preguntados.reports.domain.*
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class AdminReviewReportsShould {
    private val getReports: GetFullEpisodeReports = mockk(relaxed = true)
    private val reportsRepository: ReportRepository = mockk(relaxed = true)
    private val episodeRepository: EpisodeRepository = mockk(relaxed = true)
    private val updateStatus: UpdateStatusService = mockk(relaxed = true)

    private var nowDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z")
    private val clock: Clock = mockk { coEvery { now() } answers { nowDate } }

    private val report1 = Report(
        clock.now(),
        PLAYER_ID,
        ReportEntityType.EPISODE,
        EPISODE_ID,
        ReportReason.VIOLENCE,
        "I don't like it!!",
        ReportStatus.OPEN
    )

    private val report2 = Report(
        clock.now(),
        PLAYER_ID,
        ReportEntityType.CONTENT,
        "some_content",
        ReportReason.LEGAL,
        "This content is mine!!",
        ReportStatus.OPEN
    )

    private val action = ReviewReports(getReports, updateStatus, reportsRepository, episodeRepository, clock)

    @Test
    fun `dismiss reports`() = runTest {
        givenReports()

        nowDate = OffsetDateTime.parse("2025-12-31T10:00:00.000Z")
        val actionTaken = ReportActionTaken.DISMISSED
        val reviewerComment = "No valid reason to ban this episode"
        val actionData = ReviewReports.ActionData(ADMIN_ID, EPISODE_ID, actionTaken, reviewerComment)
        action(actionData)

        val updated = nowDate.toInstant().toEpochMilli()
        val expectedUpdate1 = report1.close(ADMIN_ID, actionTaken, reviewerComment, updated)
        val expectedUpdate2 = report2.close(ADMIN_ID, actionTaken, reviewerComment, updated)
        coVerify { reportsRepository.save(expectedUpdate1) }
        coVerify { reportsRepository.save(expectedUpdate2) }

        coVerify(exactly = 0) { updateStatus.updateToRejected(any()) }
    }

    @Test
    fun `accept reports and ban the episode`() = runTest {
        val episode = givenReports()

        nowDate = OffsetDateTime.parse("2025-12-31T10:00:00.000Z")
        val actionTaken = ReportActionTaken.BANNED
        val reviewerComment = "Terminated. Kaput. Finished."
        val actionData = ReviewReports.ActionData(ADMIN_ID, EPISODE_ID, actionTaken, reviewerComment)
        action(actionData)

        val updated = nowDate.toInstant().toEpochMilli()
        val expectedUpdate1 = report1.close(ADMIN_ID, actionTaken, reviewerComment, updated)
        val expectedUpdate2 = report2.close(ADMIN_ID, actionTaken, reviewerComment, updated)
        coVerify { reportsRepository.save(expectedUpdate1) }
        coVerify { reportsRepository.save(expectedUpdate2) }

        coVerify { updateStatus.updateToRejected(episode) }
    }

    private fun givenReports(): Episode {
        val contents = listOf("content#1", "content#2", "content#3")
        val episode = EpisodeMother.buildEpisode(id = EPISODE_ID, contents = contents)

        coEvery { episodeRepository.findById(EPISODE_ID) } returns episode

        coEvery { getReports(GetFullEpisodeReports.ActionData(ADMIN_ID, EPISODE_ID)) } returns EpisodeReports(
            episode = episode,
            episodeReports = listOf(report1),
            contentToReports = mapOf(
                "content#1" to listOf(),
                "content#2" to listOf(report2),
                "content#3" to listOf()
            )
        )
        return episode
    }

    private companion object {
        const val ADMIN_ID = 1234L
        const val PLAYER_ID = 654L
        const val EPISODE_ID = "ABC-123"
    }
}
