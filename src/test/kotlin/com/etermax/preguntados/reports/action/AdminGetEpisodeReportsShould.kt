package com.etermax.preguntados.reports.action

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.reports.domain.Report
import com.etermax.preguntados.reports.domain.ReportEntityType
import com.etermax.preguntados.reports.domain.ReportReason
import com.etermax.preguntados.reports.domain.ReportRepository
import com.etermax.preguntados.reports.domain.ReportStatus
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import kotlin.test.assertEquals

class AdminGetEpisodeReportsShould {
    private val reportRepository: ReportRepository = mockk()
    private val episodeRepository: EpisodeRepository = mockk()
    private val action = GetFullEpisodeReports(reportRepository, episodeRepository)

    @Test
    fun `return reports made by user from repository`() = runTest {
        val adminId = 1234L
        val episodeId = "E#some-uuid"
        val contentId1 = "content#1"
        val contentId2 = "content#2"
        val contentId3 = "content#3"
        val contentIds = listOf(contentId1, contentId2, contentId3)
        val episode = EpisodeMother.buildEpisode(id = episodeId, contents = contentIds)
        coEvery { episodeRepository.findById(episodeId) } returns episode

        val expectedEpisodeReports = listOf(
            Report(
                creationDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z"),
                reporterId = adminId,
                entityType = ReportEntityType.EPISODE,
                reportedId = "episode1",
                reason = ReportReason.VIOLENCE,
                comment = "Inappropriate content",
                status = ReportStatus.OPEN
            )
        )
        val expectedReportsForContent1 = listOf<Report>()
        val expectedReportsForContent2 = listOf(
            Report(
                creationDate = OffsetDateTime.parse("2025-01-02T10:00:00.000Z"),
                reporterId = adminId,
                entityType = ReportEntityType.CONTENT,
                reportedId = "content#2",
                reason = ReportReason.LEGAL,
                comment = "Copyright violation",
                status = ReportStatus.CLOSED
            ),
            Report(
                creationDate = OffsetDateTime.parse("2025-01-03T10:00:00.000Z"),
                reporterId = adminId,
                entityType = ReportEntityType.CHANNEL,
                reportedId = "content#2",
                reason = ReportReason.UNKNOWN,
                comment = null,
                status = ReportStatus.OPEN
            )
        )
        val expectedReportsForContent3 = listOf<Report>()

        coEvery {
            reportRepository.getReportsMadeTo(
                ReportEntityType.EPISODE,
                episodeId
            )
        } returns expectedEpisodeReports
        coEvery {
            reportRepository.getReportsMadeTo(
                ReportEntityType.CONTENT,
                contentId1
            )
        } returns expectedReportsForContent1
        coEvery {
            reportRepository.getReportsMadeTo(
                ReportEntityType.CONTENT,
                contentId2
            )
        } returns expectedReportsForContent2
        coEvery {
            reportRepository.getReportsMadeTo(
                ReportEntityType.CONTENT,
                contentId3
            )
        } returns expectedReportsForContent3

        val actionData = GetFullEpisodeReports.ActionData(adminId, episodeId)
        val result = action(actionData)

        assertEquals(expectedEpisodeReports, result.episodeReports)
        assertEquals(expectedReportsForContent1, result.contentToReports[contentId1])
        assertEquals(expectedReportsForContent2, result.contentToReports[contentId2])
        assertEquals(expectedReportsForContent3, result.contentToReports[contentId3])
    }
}
