package com.etermax.preguntados.reports.action

import com.etermax.preguntados.reports.domain.*
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import kotlin.test.assertEquals

class GetMyReportsShould {
    private val reportRepository: ReportRepository = mockk()
    private val action = GetMyReports(reportRepository)

    @Test
    fun `return reports made by user from repository`() = runTest {
        val userId = 1234L
        val expectedReports = listOf(
            Report(
                creationDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z"),
                reporterId = userId,
                entityType = ReportEntityType.EPISODE,
                reportedId = "episode1",
                reason = ReportReason.VIOLENCE,
                comment = "Inappropriate content",
                status = ReportStatus.OPEN
            ),
            Report(
                creationDate = OffsetDateTime.parse("2025-01-02T10:00:00.000Z"),
                reporterId = userId,
                entityType = ReportEntityType.CONTENT,
                reportedId = "content#2",
                reason = ReportReason.LEGAL,
                comment = "Copyright violation",
                status = ReportStatus.CLOSED
            ),
            Report(
                creationDate = OffsetDateTime.parse("2025-01-03T10:00:00.000Z"),
                reporterId = userId,
                entityType = ReportEntityType.CHANNEL,
                reportedId = "content#2",
                reason = ReportReason.UNKNOWN,
                comment = null,
                status = ReportStatus.OPEN
            )
        )

        coEvery { reportRepository.getReportsMadeBy(userId) } returns expectedReports

        val actionData = GetMyReports.ActionData(userId)
        val result = action(actionData)

        assertEquals(expectedReports, result)
    }
}
