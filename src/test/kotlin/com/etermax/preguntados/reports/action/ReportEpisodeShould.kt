package com.etermax.preguntados.reports.action

import com.etermax.preguntados.episodes.core.EpisodeMother
import com.etermax.preguntados.episodes.core.domain.episode.EpisodeRepository
import com.etermax.preguntados.episodes.core.domain.exception.base.ConflictException
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.reports.domain.Report
import com.etermax.preguntados.reports.domain.ReportEntityType
import com.etermax.preguntados.reports.domain.ReportReason
import com.etermax.preguntados.reports.domain.ReportRepository
import com.etermax.preguntados.reports.domain.ReportStatus
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.OffsetDateTime

class ReportEpisodeShould {
    private val reportRepository: ReportRepository = mockk(relaxed = true)
    private val episodeRepository: EpisodeRepository = mockk(relaxed = true) {
        coEvery { findById(EPISODE_ID) } returns EpisodeMother.buildEpisode(id = EPISODE_ID)
    }
    private val reportComment = null
    private val reportReason = ReportReason.VIOLENCE

    private var nowDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z")
    private val clock: Clock = mockk { coEvery { now() } answers { nowDate } }

    private val report = ReportEpisode(reportRepository, episodeRepository, clock)

    @Test
    fun `increment reports`() = runTest {
        givenNoExistingReport()

        val playerId1 = 1234L
        val actionData1 = givenAReport(playerId1)
        report(actionData1)
        thenUserReportIsSaved(playerId1)

        val playerId2 = 4321L
        val actionData2 = givenAReport(playerId2)
        report(actionData2)
        thenUserReportIsSaved(playerId2)

        thenEpisodeIsUpdated(2)
    }

    @Test
    fun `reject multiple reports from same user`() = runTest {
        givenExistingReport()
        val actionData = givenAReport(PLAYER_ID)

        assertThrows<ConflictException> {
            report(actionData)
        }
    }

    private fun givenNoExistingReport() {
        coEvery { reportRepository.userHasReported(any(), ReportEntityType.EPISODE, EPISODE_ID) } returns null
    }

    private fun givenExistingReport() {
        coEvery { reportRepository.userHasReported(PLAYER_ID, ReportEntityType.EPISODE, EPISODE_ID) } returns true
    }

    private fun givenAReport(playerId: Long = PLAYER_ID): ReportEpisode.ActionData =
        ReportEpisode.ActionData(playerId, EPISODE_ID, reportReason, reportComment)

    private fun thenUserReportIsSaved(playerId: Long = PLAYER_ID) {
        val expectedReport = Report(
            clock.now(),
            playerId,
            ReportEntityType.EPISODE,
            EPISODE_ID,
            reportReason,
            reportComment,
            ReportStatus.OPEN
        )
        coVerify { reportRepository.save(expectedReport) }
    }

    private fun thenEpisodeIsUpdated(times: Int = 1) {
        coVerify(exactly = times) { episodeRepository.plusOneReport(EPISODE_ID) }
    }

    private companion object {
        const val PLAYER_ID = 666L
        const val EPISODE_ID = "ABC-123"
    }
}
