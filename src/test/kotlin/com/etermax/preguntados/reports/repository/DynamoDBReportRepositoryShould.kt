package com.etermax.preguntados.reports.repository

import com.etermax.embedded.dynamodb.DynamoDBTestServer
import com.etermax.preguntados.episodes.core.domain.time.Clock
import com.etermax.preguntados.reports.domain.Report
import com.etermax.preguntados.reports.domain.ReportEntityType
import com.etermax.preguntados.reports.domain.ReportReason
import com.etermax.preguntados.reports.domain.ReportRepository
import com.etermax.preguntados.reports.domain.ReportStatus
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import java.time.OffsetDateTime
import kotlin.test.assertEquals

class DynamoDBReportRepositoryShould {
    private var nowDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z")
    private val clock: Clock = mockk { coEvery { now() } answers { nowDate } }

    private val dynamoDbEnhancedClient: DynamoDbEnhancedAsyncClient =
        dynamoDbTestServer.buildEnhancedAsyncClient(dynamoDbTestServer.buildAsyncClient())
    private val table: DynamoDbAsyncTable<ReportItem> =
        dynamoDbEnhancedClient.table(TABLE_NAME, TableSchema.fromBean(ReportItem::class.java))
    private val repository: ReportRepository = DynamoDBReportRepository(dynamoDbEnhancedClient, table)

    @Test
    fun `save report`() = runTest {
        val playerId1 = 12345L
        val playerId2 = 9999L
        verifyReportIs(playerId1, false)

        val reason = ReportReason.VIOLENCE
        val comment = "This is too much awesome!"
        val report = givenAReport(playerId1, reason, comment, EPISODE_ID)
        repository.save(report)

        verifyReportIs(playerId1, true)
        verifyReportIs(playerId2, false)

        val actualReport = repository.getReport(playerId1, ReportEntityType.EPISODE, EPISODE_ID)!!
        assertEquals(report, actualReport)
    }

    @Test
    fun `get all reports made by player`() = runTest {
        val playerId = 5L
        verifyReportIs(playerId, false)

        nowDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z")
        val report1 = givenAReport(playerId, ReportReason.VIOLENCE, "This is too much awesome!", "some-uuid-1")
        repository.save(report1)

        nowDate = OffsetDateTime.parse("2025-01-02T10:00:00.000Z")
        val report2 = givenAReport(playerId, ReportReason.LEGAL, "Meeeh", "some-uuid-2")
        repository.save(report2)

        nowDate = OffsetDateTime.parse("2025-01-03T10:00:00.000Z")
        val report3 = givenAReport(playerId, ReportReason.UNKNOWN, null, "some-uuid-3")
        repository.save(report3)

        val actualReports = repository.getReportsMadeBy(playerId)
        assertEquals(listOf(report1, report2, report3), actualReports)
    }

    @Test
    fun `get all reports made to some episode`() = runTest {
        val playerId = 5L
        verifyReportIs(playerId, false)

        nowDate = OffsetDateTime.parse("2025-01-01T10:00:00.000Z")
        val report1 = givenAReport(playerId, ReportReason.VIOLENCE, "This is too much awesome!", EPISODE_ID)
        repository.save(report1)

        nowDate = OffsetDateTime.parse("2025-01-02T10:00:00.000Z")
        val report2 = givenAReport(playerId, ReportReason.LEGAL, "Meeeh", EPISODE_ID)
        repository.save(report2)

        nowDate = OffsetDateTime.parse("2025-01-03T10:00:00.000Z")
        val reportToAnotherEpisode = givenAReport(playerId, ReportReason.UNKNOWN, null, "another-episode-uuid")
        repository.save(reportToAnotherEpisode)

        val actualReports = repository.getReportsMadeTo(ReportEntityType.EPISODE, EPISODE_ID)
        assertEquals(listOf(report1, report2), actualReports)
    }

    private fun givenAReport(playerId1: Long, reason: ReportReason, comment: String?, entityId: String): Report =
        Report(
            clock.now(),
            playerId1,
            ReportEntityType.EPISODE,
            entityId,
            reason,
            comment,
            ReportStatus.OPEN
        )

    private suspend fun verifyReportIs(playerId: Long, expected: Boolean) {
        val result = repository.userHasReported(playerId, ReportEntityType.EPISODE, EPISODE_ID)
        assertEquals(expected, result)
    }

    private companion object {
        const val TABLE_NAME = "dev_trivia_reports"
        const val EPISODE_ID = "ABC-666"

        @RegisterExtension
        @JvmField
        val dynamoDbTestServer = DynamoDBTestServer(mapOf(ReportItem::class.java to TABLE_NAME))
    }
}
