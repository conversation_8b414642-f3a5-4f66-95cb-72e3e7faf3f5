plugins {
    id "java"
    id "application"
    id "org.jetbrains.kotlin.jvm" version "1.8.21"
    id "org.jetbrains.kotlin.plugin.serialization" version "1.8.21"
    id "com.github.ben-manes.versions" version "0.39.0"
    id "jacoco"
    id "org.jetbrains.kotlinx.kover" version "0.6.0-Beta"
}

group "com.etermax.preguntados.episodes"
version '0.77.0'

mainClassName = "com.etermax.preguntados.episodes.MainKt"

repositories {
    maven { url "https://mavenrepo.etermax.com/artifactory/repo" }
}

ext {
    ktorV = "2.3.1"
    kotlinV = "1.8.21"
    junitV = '5.9.3'
    prometheusV = "0.15.0"
    mockkV = '1.13.5'
    hopliteVersion = "2.7.4"
    log4JVersion = '2.20.0'
    sentryVersion = "6.34.0"
    lettuceVersion= '6.2.4.RELEASE'
    kotlinxJsonVersion = "1.3.2"
    ktorPluginVersion = "4.1.0"
    awsVersion = "2.31.33"
    embebbedRedisVersion = "1.1.0"
    embebbedDynamoDbVersion = "2.0.0"
    externalServiceManagerVersion = "0.37.0"
    openSearchVersion = "2.23.0"
    openSearchRestClientVersion = "2.12.0"
    httpClient5Version = "5.2.1"
    resilience4jVersion = "1.7.1"
    jacksonModuleVersion = "2.14.2"
    luceneVersion = "9.10.0"
}

kotlin {
    //it alsa set jvm compatibility
    jvmToolchain(17)
}

dependencies {
    //BOMs
    implementation platform("org.jetbrains.kotlin:kotlin-bom:$kotlinV")
    implementation platform("io.ktor:ktor-bom:$ktorV")
    implementation platform("software.amazon.awssdk:bom:$awsVersion")

    //Kotlin
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"

    //Ktor Server
    implementation "io.ktor:ktor-server-core"
    implementation "io.ktor:ktor-server-netty"
    implementation "io.ktor:ktor-server-content-negotiation"
    implementation "io.ktor:ktor-serialization-kotlinx-json"
    implementation "io.ktor:ktor-server-default-headers"
    implementation "io.ktor:ktor-server-cors"
    implementation "io.ktor:ktor-server-call-logging"
    implementation "io.ktor:ktor-server-compression"
    implementation "io.ktor:ktor-server-status-pages"
    implementation "io.ktor:ktor-server-forwarded-header"
    implementation "io.ktor:ktor-server-call-id"
    implementation "io.ktor:ktor-server-double-receive"

    //Ktor Client
    implementation "io.ktor:ktor-client-core"
    implementation "io.ktor:ktor-client-apache"
    implementation "io.ktor:ktor-client-content-negotiation"

    // Redis client
    implementation "io.lettuce:lettuce-core:$lettuceVersion"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-reactive"
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-slf4j'

    // Boom filter
    implementation "com.google.guava:guava:33.4.8-jre"

    //DynamoDb
    implementation("software.amazon.awssdk:dynamodb")
    implementation("software.amazon.awssdk:netty-nio-client")
    implementation("software.amazon.awssdk:dynamodb-enhanced")
    implementation('software.amazon.awssdk:sts')

    implementation "com.etermax.ktor:ktor-plugins:$ktorPluginVersion"

    // Metrics
    implementation "io.prometheus:simpleclient:$prometheusV"

    // OpenSearch
    implementation "org.opensearch.client:opensearch-java:$openSearchVersion"
    implementation "org.apache.httpcomponents.client5:httpclient5:$httpClient5Version"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin:$jacksonModuleVersion"

    //Text Analyzer
    implementation "org.apache.lucene:lucene-core:$luceneVersion"
    implementation "org.apache.lucene:lucene-analysis-common:$luceneVersion"

    // Etermax dependencies
    implementation "com.etermax.preguntados:external-services-manager:$externalServiceManagerVersion"
    implementation "com.etermax.preguntados:preguntados-eteragent:2.1.0"
    implementation "com.etermax.ktor:ktor-sentry-plugin:1.1.1"
    implementation "com.etermax.preguntados:sortable-games:1.0.1"

    //Resilience4j
    implementation "io.github.resilience4j:resilience4j-kotlin:$resilience4jVersion"
    implementation "io.github.resilience4j:resilience4j-circuitbreaker:$resilience4jVersion"
    implementation "io.github.resilience4j:resilience4j-retry:$resilience4jVersion"
    implementation "io.github.resilience4j:resilience4j-prometheus:$resilience4jVersion"
}

dependencies {
    // Configuration loader
    implementation "com.sksamuel.hoplite:hoplite-core:$hopliteVersion"
    implementation "com.sksamuel.hoplite:hoplite-yaml:$hopliteVersion"
}

dependencies {
    implementation "org.apache.logging.log4j:log4j-api:$log4JVersion"
    implementation "org.apache.logging.log4j:log4j-core:$log4JVersion"
    implementation "org.apache.logging.log4j:log4j-slf4j-impl:$log4JVersion"
    implementation "io.sentry:sentry-log4j2:$sentryVersion"
    implementation "io.sentry:sentry-kotlin-extensions:$sentryVersion"
}


dependencies {
    // Test
    testImplementation "org.junit.jupiter:junit-jupiter-api:$junitV"
    testImplementation "org.junit.jupiter:junit-jupiter-engine:$junitV"
    testImplementation "org.junit.jupiter:junit-jupiter-params:$junitV"
    testRuntimeOnly "org.junit.vintage:junit-vintage-engine:$junitV"

    testImplementation "org.jetbrains.kotlin:kotlin-test-junit"
    testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test"
    testImplementation "io.mockk:mockk:$mockkV"
    testImplementation "org.assertj:assertj-core:3.22.0"

    // HTTP test support
    testImplementation "io.ktor:ktor-client-mock"
    testImplementation "io.ktor:ktor-server-test-host"

    // Redis for tests
    testImplementation "com.etermax.kotlin:embedded-redis:$embebbedRedisVersion"

    // DynamoDB for tests
    testImplementation "com.etermax.kotlin:embedded-dynamodb:$embebbedDynamoDbVersion"

    // OpenSearch for tests
    testImplementation("org.testcontainers:elasticsearch:1.19.1")

    testImplementation "org.testcontainers:junit-jupiter:1.19.1"
}

dependencies {
    // Architecture checks
    testImplementation 'com.tngtech.archunit:archunit-junit5:1.0.1'
}

compileKotlin {
    kotlinOptions.freeCompilerArgs += [
            "-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi",
            "-Xopt-in=kotlinx.serialization.ExperimentalSerializationApi",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi"
    ]

}
compileTestKotlin {
    kotlinOptions.freeCompilerArgs += [
            "-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi",
            "-Xopt-in=kotlinx.serialization.ExperimentalSerializationApi",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi"
    ]
}

test {
    useJUnitPlatform{
    }

    testLogging {
        events("passed", "skipped", "failed")
    }

    minHeapSize = "256M"
    maxHeapSize = "512M"

    maxParallelForks = 1
    forkEvery = 0
    setForkEvery(0)
    jvmArgs("-Dorg.gradle.test.worker=1")
}

apply from: 'dependencies.gradle.kts'

// Versions plugins Configuration
def isNonStable = { String version ->
    def stableKeyword = ['RELEASE', 'FINAL', 'GA'].any { it -> version.toUpperCase().contains(it) }
    def regex = /^[0-9,.v-]+(-r)?$/
    return !stableKeyword && !(version ==~ regex)
}

tasks.named("dependencyUpdates").configure {
    outputFormatter = "json"
    // reject all non stable versions
    rejectVersionIf {
        isNonStable(it.candidate.version)
    }
}

jacocoTestReport {
    dependsOn "test"
    reports {
        xml.required = true
        html.required = true
    }
}

tasks.named("processResources", Copy).configure {
    doFirst{
        filesMatching('**/sentry.properties') {
            filter {
                it.replace('@app.version@', project.version.toString())
            }
        }
    }
}
