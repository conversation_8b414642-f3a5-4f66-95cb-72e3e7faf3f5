apiVersion: apps/v1
kind: Deployment
metadata:
  name: episode
spec:
  replicas: 1
  template:
    spec:
      serviceAccountName: stg-episodes
      containers:
      - name: episode
        env:
        - name: ENVIRONMENT
          value: 'stg'
        - name: L4J_LEVEL
          value: 'INFO'
        - name: L4J_LEVEL_ETERMAX
          value: 'INFO'
        - name: L4J_APPENDER
          value: 'Console'
        - name: L4J_ERROR_APPENDER
          value: 'Sentry'
        - name: SENTRY_DSN
          value: 'https://<EMAIL>/176'
        - name: SENTRY_ENVIRONMENT
          value: 'stg'
        - name: platformUrl
          value: 'https://api.platform-staging.etermax.com'
        - name: PLATFORM_GAME_ID
          value: P2
        - name: delivery__requiredLikesForTopRated
          value: '3'
        - name: delivery__requiredViewsForTopRated
          value: '5'
        - name: delivery__requiredMinimumQueryLength
          value: '2'
        - name: delivery__useBlackList
          value: 'false'
        - name: OS_ENDPOINT
          value: 'https://vpc-stg-episodes-vaaiu4c63i2mch4eidwv5hkyhm.us-east-1.es.amazonaws.com'
        - name: openSearch__episodesIndex
          value: episodes-index
        - name: openSearch__channelsIndex
          value: channels-index
        - name: openSearch__playerEpisodesIndex
          value: player-episodes-index
        - name: pendingEpisodesJobConfig__isEnabled
          value: 'true'
        - name: dynamoDBSearch__isEnabled
          value: 'true'
        - name: isUpdateRateCountersEnabled
          value: 'true'
        - name: isIncrementViewsEnabled
          value: 'true'
        - name: ANALYTICS_RELEASE_TOGGLE_ENABLED
          value: 'true'
