apiVersion: apps/v1
kind: Deployment
metadata:
  name: episode
spec:
  replicas: 1
  template:
    metadata:
      labels:
        app: episode
    spec:
      serviceAccountName: dev-episodes
      containers:
      - name: episode
        env:
        - name: ENVIRONMENT
          value: 'dev'
        - name: L4J_LEVEL
          value: 'DEBUG'
        - name: L4J_LEVEL_ETERMAX
          value: 'DEBUG'
        - name: L4J_APPENDER
          value: 'Console'
        - name: L4J_ERROR_APPENDER
          value: 'Sentry'
        - name: SENTRY_DSN
          value: 'https://<EMAIL>/176'
        - name: SENTRY_ENVIRONMENT
          value: 'dev'
        - name: platformUrl
          value: 'https://api.platform-staging.etermax.com'
        - name: OS_ENDPOINT
          value: 'https://vpc-dev-episodes-5z6ehtxeev7pkamf5g2fwjxivm.us-east-1.es.amazonaws.com'
        - name: openSearch__episodesIndex
          value: episodes-index
        - name: openSearch__channelsIndex
          value: channels-index
        - name: openSearch__playerEpisodesIndex
          value: player-episodes-index
        - name: PLATFORM_GAME_ID
          value: P2DEV
        - name: delivery__requiredLikesForTopRated
          value: '3'
        - name: delivery__requiredViewsForTopRated
          value: '5'
        - name: delivery__requiredMinimumQueryLength
          value: '2'
        - name: delivery__useBlackList
          value: 'false'
        - name: pendingEpisodesJobConfig__isEnabled
          value: 'true'
        - name: pendingEpisodesJobConfig__processDelayInSeconds
          value: '60'
        - name: notificationsConfiguration__played__isEnabled
          value: 'true'
        - name: notificationsConfiguration__liked__isEnabled
          value: 'true'
        - name: eventGroupsConfiguration__groups__LIKE__isEnabled
          value: 'true'
        - name: eventGroupsConfiguration__groups__PLAY__isEnabled
          value: 'true'
        - name: dynamoDBSearch__isEnabled
          value: 'true'
        - name: isUpdateRateCountersEnabled
          value: 'true'
        - name: isIncrementViewsEnabled
          value: 'true'
        - name: ANALYTICS_RELEASE_TOGGLE_ENABLED
          value: 'true'
        - name: notificationsConfiguration__inviterCanvasId
          value: 'e249e7c4-a326-4138-bdd3-61b08dec4f61'
        - name: notificationsConfiguration__played__singleCanvasId
          value: 'bfe58b9c-38b2-4940-8f48-093ec90c2252'
        - name: notificationsConfiguration__played__pluralCanvasId
          value: '22eecbcb-065c-49dd-a03e-63ea58124033'
        - name: notificationsConfiguration__liked__singleCanvasId
          value: '30fc4500-8007-494c-95a3-5157c56826f2'
        - name: notificationsConfiguration__liked__pluralCanvasId
          value: '994620b7-0319-4af4-8a19-1a13510d6805'
        - name: PLAYERS_BY_OWNER_EXPIRATION_TIME
          value: 'PT10M'
        - name: IS_CHANNEL_EPISODES_NORMALIZER_ENABLED
          value: 'true'