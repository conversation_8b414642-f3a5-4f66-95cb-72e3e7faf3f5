apiVersion: apps/v1
kind: Deployment
metadata:
  name: episode
spec:
  template:
    metadata:
      annotations:
        sidecar.istio.io/proxyCPU: 100m
        sidecar.istio.io/proxyCPULimit: 100m
        sidecar.istio.io/proxyMemory: 200Mi
        sidecar.istio.io/proxyMemoryLimit: 200Mi
      labels:
        app: episode
    spec:
      serviceAccountName: episodes
      affinity:
          podAntiAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
            - topologyKey: kubernetes.io/hostname
              labelSelector:
                matchLabels:
                  app: episode
      containers:
      - name: episode
        env:
        - name: ENVIRONMENT
          value: 'prod'
        - name: L4J_LEVEL
          value: 'INFO'
        - name: L4J_APPENDER
          value: 'Console'
        - name: L4J_ERROR_APPENDER
          value: 'Sentry'
        - name: SENTRY_DSN
          value: 'https://<EMAIL>/179'
        - name: platformUrl
          value: 'https://api.platform.etermax.com'
        - name: persistence__cache__useTLS
          value: "true"
        - name: PLATFORM_GAME_ID
          value: P2
        - name: delivery__requiredLikesForTopRated
          value: '10'
        - name: delivery__requiredViewsForTopRated
          value: '50'
        - name: delivery__requiredMinimumQueryLength
          value: '2'
        - name: delivery__isSortedByQuality
          value: 'false'
        - name: OS_ENDPOINT
          value: 'https://vpc-prod-episodes-e2fzwzwasdtyazwwvssd5kgeki.us-east-1.es.amazonaws.com'
        - name: openSearch__episodesIndex
          value: episodes-index
        - name: openSearch__channelsIndex
          value: channels-index
        - name: openSearch__playerEpisodesIndex
          value: player-episodes-index
        - name: persistence__cache__useTLS
          value: 'true'
        - name: pendingEpisodesJobConfig__isEnabled
          value: 'true'
        - name: dynamoDBSearch__isEnabled
          value: 'true'
        - name: isUpdateRateCountersEnabled
          value: 'true'
        - name: isIncrementViewsEnabled
          value: 'true'
        - name: notificationsConfiguration__inviterCanvasId
          value: 'adeb9dcd-4c08-4d2b-a9af-5565766a9052'
        - name: ANALYTICS_RELEASE_TOGGLE_ENABLED
          value: 'true'
        - name: notificationsConfiguration__played__singleCanvasId
          value: '56c664e3-5f1f-48a7-9a5f-6a212d255c1e'
        - name: notificationsConfiguration__played__pluralCanvasId
          value: '3c0fe4ef-0b9c-4253-bf31-94e43ca56475'
        - name: notificationsConfiguration__liked__singleCanvasId
          value: '679aab4a-9c85-4209-9158-538b3dff433c'
        - name: notificationsConfiguration__liked__pluralCanvasId
          value: 'd7930de2-39dd-4bed-b272-26dc8253daee'
        - name: notificationsConfiguration__creatorToPlayersCanvasId
          value: '58281063-c8b8-468b-ad26-e3f8311c42cb'
        - name: IS_CHANNEL_EPISODES_NORMALIZER_ENABLED
          value: 'true'
        resources:
          requests:
            memory: 1024M
            cpu: 500m
          limits:
            memory: 1024M
            cpu: 500m
