# Episode Project - Local Environment Setup

This guide explains how to start a local environment for the Episode Project using Docker Compose.

## Prerequisites
- Docker installed on your machine: [Get Docker](https://docs.docker.com/get-docker/)
- Docker Compose installed (comes with Docker Desktop)
- IntelliJ IDEA with Kotlin support

## Steps to Start the Local Environment

1. **Navigate to the Docker Compose Directory:**
```bash
cd docker/local
```

2. **Start the Services:**
```bash
docker-compose -f docker-compose.yml up
```
This command pulls the necessary images (if not already pulled) and starts the containers.

3. **Start the API:**
- Open the project in IntelliJ IDEA.
- Locate the `Main.kt` file (usually in `src/main/kotlin` or equivalent path).
- Right-click `Main.kt` and select **Run 'MainKt'**.

4. **Access the Application:**
- The application should be accessible at: [http://localhost:8080](http://localhost:8080).

5. **Stop the Services:**
   To stop the containers, press `Ctrl + C` or run:
```bash
docker-compose -f docker-compose.yml down
```

## Channels

To create DynamoDB table for channels, execute 

```shell
sh ./create-channels-table.sh
```

## Troubleshooting
- If ports are already in use, update the `ports` section in the `docker-compose.yml`.
- Ensure Docker Desktop is running before executing the commands.

---
Your local environment should now be up and running. Happy coding! 🚀

