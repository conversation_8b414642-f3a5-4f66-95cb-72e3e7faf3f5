#!/bin/bash

echo "Importing episodes data from data.json to DynamoDB and OpenSearch..."

# Check if import-data-episodes.json exists
if [ ! -f "import-data-episodes.json" ]; then
    echo "Error: import-data-episodes.json file not found!"
    exit 1
fi

# Check if import-data-channels.json exists
if [ ! -f "import-data-channels.json" ]; then
    echo "Error: import-data-channels.json file not found!"
    exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo "Error: jq is required but not installed. Please install jq first."
    exit 1
fi

# Check if DynamoDB is running
echo "Checking if DynamoDB is available..."
while ! curl -s http://localhost:8000 > /dev/null; do
    echo "Waiting for DynamoDB to be ready..."
    sleep 2
done

# Check if OpenSearch is running
echo "Checking if OpenSearch is available..."
while ! curl -s http://localhost:9200 > /dev/null; do
    echo "Waiting for OpenSearch to be ready..."
    sleep 2
done

# Set AWS credentials for local DynamoDB
export AWS_ACCESS_KEY_ID=dummy
export AWS_SECRET_ACCESS_KEY=dummy
export AWS_DEFAULT_REGION=us-east-1

# Process each item in import-data-episodes.json
echo "Processing items from import-data-episodes.json..."
jq -c '.[]' import-data-episodes.json | while read -r item; do
    # Extract ID for logging
    id=$(echo $item | jq -r '.PK | @uri')
    echo "Processing episode with ID: $id"

    # Prepare item for DynamoDB (assuming item is already in correct format)
    dynamo_item=$(echo "$item" | jq -c '.' | sed "s/\"/'/g")

    # Insert into DynamoDB
    echo "Inserting into DynamoDB dev_episodes table..."
    aws dynamodb execute-statement \
        --statement "INSERT INTO dev_episodes VALUE $dynamo_item" \
        --endpoint-url http://localhost:8000 \
        --region us-east-1

    # Prepare item for OpenSearch (may need transformation depending on your schema)
    opensearch_item=$(echo $item | jq -c '.')

    # Insert into OpenSearch
    echo "Inserting into OpenSearch episodes-index..."
    curl -X POST "http://localhost:9200/episodes-index/_doc/$id" \
        -H "Content-Type: application/json" \
        -d "$opensearch_item"

    echo "Item $id processed successfully."
    # Small delay to avoid overwhelming the services
    sleep 0.5
done

# Process each item in import-data-channels.json
echo "Processing items from import-data-channels.json..."
jq -c '.[]' import-data-channels.json | while read -r item; do
    # Extract ID for logging
    id=$(echo $item | jq -r '.PK | @uri')
    echo "Processing channel with ID: $id"

    # Prepare item for DynamoDB (assuming item is already in correct format)
    dynamo_item=$(echo "$item" | jq -c '.' | sed "s/\"/'/g")

    # Insert into DynamoDB
    echo "Inserting into DynamoDB dev_channels table..."
    aws dynamodb execute-statement \
        --statement "INSERT INTO dev_channels VALUE $dynamo_item" \
        --endpoint-url http://localhost:8000 \
        --region us-east-1

    # Prepare item for OpenSearch (may need transformation depending on your schema)
    opensearch_item=$(echo $item | jq -c '.')

    sk=$(echo "$item" | jq -r '.SK')
    if [ "$sk" != "CHANNEL" ]; then
        continue
    fi

    # Insert into OpenSearch
    echo "Inserting into OpenSearch channels-index..."
    curl -X POST "http://localhost:9200/channels-index/_doc/$id" \
        -H "Content-Type: application/json" \
        -d "$opensearch_item"

    echo "Item $id processed successfully."
    # Small delay to avoid overwhelming the services
    sleep 0.5
done

echo "Import completed successfully!"