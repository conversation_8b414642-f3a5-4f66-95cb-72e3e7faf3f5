#!/bin/bash

echo "Waiting for DynamoDB to be ready..."
while ! curl -s http://localhost:8000 > /dev/null; do
    sleep 1
done

echo "Creating dev-channels table..."
export AWS_ACCESS_KEY_ID=dummy
export AWS_SECRET_ACCESS_KEY=dummy
aws dynamodb create-table \
    --endpoint-url http://localhost:8000 \
    --region us-east-1 \
    --table-name dev_episodes \
    --attribute-definitions \
        AttributeName=PK,AttributeType=S \
        AttributeName=SK,AttributeType=S \
        AttributeName=owner_id,AttributeType=N \
        AttributeName=player_id,AttributeType=N \
        AttributeName=challenge_id,AttributeType=S \
    --key-schema \
        AttributeName=PK,KeyType=HASH \
        AttributeName=SK,KeyType=RANGE \
    --global-secondary-indexes \
        "[
            {
                \"IndexName\": \"by-owner-episode-index\",
                \"KeySchema\": [
                    {\"AttributeName\": \"owner_id\", \"KeyType\": \"HASH\"},
                    {\"AttributeName\": \"PK\", \"KeyType\": \"RANGE\"}
                ],
                \"Projection\": {
                    \"ProjectionType\": \"INCLUDE\",
                    \"NonKeyAttributes\": [\"type\", \"language\", \"start_date\", \"status\", \"channel_id\"]
                },
                \"ProvisionedThroughput\": {
                    \"ReadCapacityUnits\": 5,
                    \"WriteCapacityUnits\": 5
                }
            },
            {
                \"IndexName\": \"by-player-challenge-index\",
                \"KeySchema\": [
                    {\"AttributeName\": \"player_id\", \"KeyType\": \"HASH\"},
                    {\"AttributeName\": \"challenge_id\", \"KeyType\": \"RANGE\"}
                ],
                \"Projection\": {
                    \"ProjectionType\": \"ALL\"
                },
                \"ProvisionedThroughput\": {
                    \"ReadCapacityUnits\": 5,
                    \"WriteCapacityUnits\": 5
                }
            }
        ]" \
    --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5

echo "Table created successfully!"