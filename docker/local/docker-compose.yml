version: '2'

services:

  redis-6379:
    image: redis:6.2.10
    restart: "unless-stopped"
    container_name: redis-6379
    ports:
      - 6379:6379
    command: redis-server --requirepass local --appendonly yes
    networks:
      - etermax

  dynamodb:
    image: docker.etermax.net/preguntados/dynamodb:2.0
    #try with this image if you have problems and you have an aarch processor (M1,M2,MX)
    #image: docker.etermax.net/preguntados/dynamodb:2.0-aarch
    ports:
      - 8000:8000
    environment:
      - DYNAMO_OPTS=-sharedDb -dbPath "/opt/dynamodb"
    restart: "unless-stopped"
    container_name: preguntados-dynamodb
    networks:
      - etermax

  dynamodb-ui:
    image: aaronshaf/dynamodb-admin
    ports:
      - 8001:8001
    environment:
      - DYNAMO_ENDPOINT=http://preguntados-dynamodb:8000
    restart: "unless-stopped"
    container_name: preguntados-dynamodb-ui
    networks:
      - etermax

  opensearch:
    image: opensearchproject/opensearch:2.19.2
    ports:
      - "9200:9200"
      - "9600:9600"
    environment:
      - discovery.type=single-node
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=Luna1234$
      - plugins.security.disabled=true
    restart: unless-stopped

  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:2.19.2
    container_name: opensearch-dashboards
    ports:
      - "5601:5601"
    environment:
      - OPENSEARCH_HOSTS=http://opensearch:9200
      - DISABLE_SECURITY_DASHBOARDS_PLUGIN=true
    depends_on:
      - opensearch
    restart: unless-stopped

networks:
  etermax:
    external:
      name: etermax
