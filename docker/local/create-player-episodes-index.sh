#!/bin/bash

echo "Creating player-episodes-index in OpenSearch..."

# Check if OpenSearch is running
echo "Checking if OpenSearch is available..."
while ! curl -s http://localhost:9200 > /dev/null; do
    echo "Waiting for OpenSearch to be ready..."
    sleep 2
done

# Create the index using the mapping file
echo "Creating index with mapping..."
curl -X PUT "http://localhost:9200/player-episodes-index" \
  -H "Content-Type: application/json" \
  -d @../../opensearch/mapping/player-episodes-index-mapping-v1.0.json

# Check if the index was created successfully
if [ $? -eq 0 ]; then
    echo "Index created successfully!"
    echo "Verifying index..."
    curl -X GET "http://localhost:9200/player-episodes-index?pretty"
else
    echo "Failed to create index. Please check the error message above."
fi