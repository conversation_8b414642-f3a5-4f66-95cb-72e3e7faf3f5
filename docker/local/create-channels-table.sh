#!/bin/bash

echo "Waiting for DynamoDB to be ready..."
while ! curl -s http://localhost:8000 > /dev/null; do
    sleep 1
done

echo "Creating dev-channels table..."
export AWS_ACCESS_KEY_ID=dummy
export AWS_SECRET_ACCESS_KEY=dummy
aws dynamodb create-table \
    --endpoint-url http://localhost:8000 \
    --region us-east-1 \
    --table-name dev_channels \
    --attribute-definitions \
        AttributeName=PK,AttributeType=S \
        AttributeName=SK,AttributeType=S \
        AttributeName=ep_order,AttributeType=S \
        AttributeName=ep_date_added,AttributeType=N \
        AttributeName=owner_id,AttributeType=N \
        AttributeName=ch_order,AttributeType=S \
        AttributeName=ch_modification_date,AttributeType=N \
    --key-schema \
        AttributeName=PK,KeyType=HASH \
        AttributeName=SK,KeyType=RANGE \
    --local-secondary-indexes \
        "[
            {
                \"IndexName\": \"by-id-and-order-index\",
                \"KeySchema\": [
                    {\"AttributeName\": \"PK\", \"KeyType\": \"HASH\"},
                    {\"AttributeName\": \"ep_order\", \"KeyType\": \"RANGE\"}
                ],
                \"Projection\": {
                    \"ProjectionType\": \"ALL\"
                }
            },
            {
                \"IndexName\": \"by-id-and-episodes-added-date-index\",
                \"KeySchema\": [
                    {\"AttributeName\": \"PK\", \"KeyType\": \"HASH\"},
                    {\"AttributeName\": \"ep_date_added\", \"KeyType\": \"RANGE\"}
                ],
                \"Projection\": {
                    \"ProjectionType\": \"ALL\"
                }
            }
        ]" \
    --global-secondary-indexes \
        "[
            {
                \"IndexName\": \"by-owner-and-order-index\",
                \"KeySchema\": [
                    {\"AttributeName\": \"owner_id\", \"KeyType\": \"HASH\"},
                    {\"AttributeName\": \"ch_order\", \"KeyType\": \"RANGE\"}
                ],
                \"Projection\": {
                    \"ProjectionType\": \"ALL\"
                },
                \"ProvisionedThroughput\": {
                    \"ReadCapacityUnits\": 5,
                    \"WriteCapacityUnits\": 5
                }
            },
            {
                \"IndexName\": \"by-owner-and-modification-index\",
                \"KeySchema\": [
                    {\"AttributeName\": \"owner_id\", \"KeyType\": \"HASH\"},
                    {\"AttributeName\": \"ch_modification_date\", \"KeyType\": \"RANGE\"}
                ],
                \"Projection\": {
                    \"ProjectionType\": \"ALL\"
                },
                \"ProvisionedThroughput\": {
                    \"ReadCapacityUnits\": 5,
                    \"WriteCapacityUnits\": 5
                }
            }
        ]" \
    --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5

echo "Table created successfully!"