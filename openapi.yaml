openapi: 3.0.0
info:
  title: Preguntados Episodes API
  version: 1.0.0
servers:
- url: https://api.{environment}.tc.etermax.com/api
  variables:
    environment:
      enum:
      - dev
      - stg
      - prod
      default: dev
      description: The environment to use

tags:
- name: Channels

paths:
  /users/{user_id}/channel-episodes/create:
    post:
      tags:
      - Channels
      summary: "Create a new channel"
      operationId: createChannel
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateChannelRequest"
      responses:
        "201":
          description: Channel created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelSummary"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /users/{user_id}/channel-episodes/search:
    get:
      tags:
      - Channels
      summary: "Search user's channels"
      operationId: searchChannels
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - name: lastEvaluatedKey
        in: query
        required: false
        description: Last evaluated key for pagination
        schema:
          $ref: "#/components/schemas/String"
      - name: profileOwnerId
        in: query
        required: false
        description: Owner ID of channels
        schema:
          $ref: "#/components/schemas/String"
      responses:
        "200":
          description: Paginated list of channels
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedChannelSummaries"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /users/{user_id}/channel-episodes/search/by-language/{language}:
    get:
      tags:
      - Channels
      summary: "Search user's channels by language"
      operationId: searchChannelsByLanguage
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - $ref: "#/components/parameters/LanguagePathParam"
      - name: lastEvaluatedKey
        in: query
        required: false
        description: Last evaluated key for pagination
        schema:
          $ref: "#/components/schemas/String"
      responses:
        "200":
          description: Paginated list of channels for language
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedChannelByLanguageSummaries"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /users/{user_id}/channel-episodes/{channel_id}/episodes/add:
    post:
      tags:
      - Channels
      summary: "Add episodes to a channel"
      operationId: addEpisodesToChannel
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - $ref: "#/components/parameters/ChannelIdPathParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddEpisodesRequest"
      responses:
        "200":
          description: Episodes added successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelSummary"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /users/{user_id}/channel-episodes/{channel_id}/episodes/remove:
    post:
      tags:
      - Channels
      summary: "Remove episodes from channel"
      operationId: removeEpisodesFromChannel
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - $ref: "#/components/parameters/ChannelIdPathParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RemoveEpisodesRequest"
      responses:
        "200":
          description: Episodes removed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelSummary"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /users/{user_id}/channel-episodes/{channel_id}/episodes/order:
    put:
      tags:
      - Channels
      summary: "Update episodes order in channel"
      operationId: UpdateChannelEpisodesOrder
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - $ref: "#/components/parameters/ChannelIdPathParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateChannelEpisodesOrderRequest"
      responses:
        "200":
          description: Episodes order updated successfully
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /users/{user_id}/channel-episodes/{channel_id}:
    get:
      tags:
      - Channels
      summary: "Find Channel by ID"
      operationId: getChannel
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - $ref: "#/components/parameters/ChannelIdPathParam"
      responses:
        "200":
          description: Channel summary
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelSummary"
        "204":
          description: Channel not found for ID
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    put:
      tags:
      - Channels
      summary: "Update channel"
      operationId: updateChannel
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - $ref: "#/components/parameters/ChannelIdPathParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateChannelRequest"
      responses:
        "200":
          description: Channel updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelSummary"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

    delete:
      tags:
      - Channels
      summary: "Delete channel"
      operationId: deleteChannel
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - $ref: "#/components/parameters/ChannelIdPathParam"
      responses:
        "200":
          description: Channel deleted successfully
          content: {}
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /users/{user_id}/channel-episodes/{channel_id}/reduced:
    get:
      tags:
      - Channels
      summary: "Find Channel by ID with reduced response"
      operationId: getReducedChannel
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - $ref: "#/components/parameters/ChannelIdPathParam"
      responses:
        "200":
          description: Channel summary with reduced response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelSummaryReduced"
        "204":
          description: Channel not found for ID
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /users/{user_id}/channel-episodes/{channel_id}/order-type:
    put:
      tags:
      - Channels
      summary: "Update Channel OrderType"
      operationId: putOrderType
      parameters:
      - $ref: "#/components/parameters/UserIdPathParam"
      - $ref: "#/components/parameters/ChannelIdPathParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateChannelOrderTypeRequest"
      responses:
        "200":
          description: Order type updated
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

components:
  schemas:
    ErrorResponse:
      type: object
      required:
      - code
      - message
      properties:
        code:
          type: string
          description: Error code
        message:
          type: string
          description: Error message

    CreateChannelRequest:
      type: object
      required:
      - name
      - cover_url
      - language
      properties:
        name:
          type: string
          description: Channel name (max 30 characters)
        description:
          type: string
          description: Channel description (max 80 characters)
        cover_url:
          type: string
          description: URL of the channel cover image
        language:
          type: string
          description: Channel language
        website:
          type: string
          nullable: true
          description: Channel website (max 30 characters)
        type:
          type: string
          nullable: true
          description: Channel type

    UpdateChannelRequest:
      type: object
      required:
      - name
      - cover_url
      - moderation_language
      properties:
        name:
          type: string
          description: Channel name (max 30 characters)
        description:
          type: string
          description: Channel description (max 80 characters)
        cover_url:
          type: string
          description: URL of the channel cover image
        moderation_language:
          type: string
          description: Channel language
        website:
          type: string
          nullable: true
          description: Channel website (max 30 characters)
        type:
          type: string
          nullable: true
          description: Channel type

    AddEpisodesRequest:
      type: object
      required:
      - episodes_ids
      properties:
        episodes_language:
          type: string
          description: Episodes language
        episodes_ids:
          type: array
          items:
            type: string
          description: List of episode IDs to add to the channel

    RemoveEpisodesRequest:
      type: object
      required:
      - episodes_ids
      properties:
        episodes_ids:
          type: array
          items:
            type: string
          description: List of episode IDs to remove from the channel

    UpdateChannelEpisodesOrderRequest:
      type: object
      required:
      - new_order
      properties:
        new_order:
          type: object
          additionalProperties:
            type: string
          description: Map of episode position (integer key) to episode ID (string value)

    UpdateChannelOrderTypeRequest:
      type: object
      required:
      - order_type
      properties:
        order_type:
          type: string
          description: Order type

    SearchChannelSummary:
      type: object
      properties:
        channel:
          $ref: "#/components/schemas/ChannelSummary"
        episodes_covers_url:
          type: array
          items:
            type: string
          description: List of episodes Cover URL

    ChannelSummary:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/String"
        name:
          $ref: "#/components/schemas/String"
        description:
          $ref: "#/components/schemas/StringOrNull"
        cover_url:
          $ref: "#/components/schemas/String"
        subscribed:
          $ref: "#/components/schemas/Boolean"
        owner:
          $ref: "#/components/schemas/ProfileOrNull"
        create_date_in_millis:
          $ref: "#/components/schemas/Long"
        last_modification_date_in_millis:
          $ref: "#/components/schemas/Long"
        statistics:
          $ref: "#/components/schemas/ChannelStatistics"
        type:
          $ref: "#/components/schemas/ChannelType"
        language:
          $ref: "#/components/schemas/StringOrNull"
        website:
          $ref: "#/components/schemas/StringOrNull"
        order_type:
          $ref: "#/components/schemas/ChannelOrderType"

    PaginatedChannelSummaries:
      type: object
      properties:
        channels:
          type: object
          properties:
            last_evaluated_key:
              $ref: "#/components/schemas/StringOrNull"
            items:
              type: array
              items:
                $ref: "#/components/schemas/SearchChannelSummary"

    ChannelByLanguageSummary:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/String"
        name:
          $ref: "#/components/schemas/String"
        cover_url:
          $ref: "#/components/schemas/String"

    PaginatedChannelByLanguageSummaries:
      type: object
      properties:
        channels:
          type: object
          properties:
            last_evaluated_key:
              $ref: "#/components/schemas/StringOrNull"
            items:
              type: array
              items:
                $ref: "#/components/schemas/ChannelByLanguageSummary"

    ChannelSummaryReduced:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/String"
        name:
          $ref: "#/components/schemas/String"
        cover_url:
          $ref: "#/components/schemas/String"

    ChannelStatistics:
      type: object
      properties:
        subscribers:
          $ref: "#/components/schemas/Int"
        episodes:
          $ref: "#/components/schemas/Int"
        unpublished_episodes:
          $ref: "#/components/schemas/Int"

    ProfileOrNull:
      type: object
      nullable: true
      properties:
        id:
          $ref: "#/components/schemas/Int"
        name:
          $ref: "#/components/schemas/String"
        photo_url:
          $ref: "#/components/schemas/StringOrNull"
        social_data:
          $ref: '#/components/schemas/SocialProfileRepresentationOrNull'
        restriction:
          $ref: '#/components/schemas/RestrictionRepresentationOrNull'

    ChannelType:
      type: string
      enum:
      - PUBLIC
      - PRIVATE

    ChannelOrderType:
      type: string
      enum:
      - DATE_ADDED
      - CUSTOM_ORDER

    Int:
      type: integer
      format: int32

    IntOrNull:
      type: integer
      format: int32
      nullable: true

    Long:
      type: integer
      format: int64

    String:
      type: string

    StringOrNull:
      type: string
      nullable: true

    Boolean:
      type: boolean

    SocialProfileRepresentationOrNull:
      type: object
      nullable: true
      properties:
        network:
          type: string
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true

    RestrictionRepresentationOrNull:
      type: object
      nullable: true
      properties:
        type:
          type: string

  parameters:
    UserIdPathParam:
      name: user_id
      in: path
      required: true
      examples:
        user_id:
          value: 284
        user_id2:
          value: 456
      schema:
        $ref: "#/components/schemas/Int"
    ChannelIdPathParam:
      name: channel_id
      in: path
      required: true
      examples:
        channel_id:
          value: "channel-123"
        channel_id2:
          value: "channel-456"
      schema:
        type: string
    LanguagePathParam:
      name: language
      in: path
      required: true
      examples:
        channel_id:
          value: "es"
        channel_id2:
          value: "en"
      schema:
        type: string
